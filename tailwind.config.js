import defaultTheme from "tailwindcss/defaultTheme";
import headlessuiPlugin from "@headlessui/tailwindcss";
import tailwindcssAnimatePlugin from "tailwindcss-animate";

/** @type {import('tailwindcss').Config} */
const config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/@tremor/**/*.{js,ts,jsx,tsx}",
	'./modules/**/*.{ts,tsx}', // Tremor module
  ],
  darkMode: ["class", "class"],
  theme: {
  	transparent: 'transparent',
  	fontFamily: {
  		sans: [
  			'Inter',
                ...defaultTheme.fontFamily.sans
            ]
  	},
  	screens: {
  		'2xsm': '375px',
  		xsm: '425px',
  		'3xl': '2000px',
            ...defaultTheme.screens
  	},
  	extend: {
  		colors: {
  			current: 'currentColor',
  			transparent: 'transparent',
  			white: '#FFFFFF',
  			black: '#1C2434',
  			'black-2': '#010101',
  			body: '#64748B',
  			bodydark: '#AEB7C0',
  			bodydark1: '#DEE4EE',
  			bodydark2: '#8A99AF',
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			stroke: '#E2E8F0',
  			gray: '#EFF4FB',
  			graydark: '#333A48',
  			'gray-2': '#F7F9FC',
  			'gray-3': '#FAFAFA',
  			whiten: '#F1F5F9',
  			whiter: '#F5F7FD',
  			boxdark: '#24303F',
  			'boxdark-2': '#1A222C',
  			strokedark: '#2E3A47',
  			'form-strokedark': '#3d4d60',
  			'form-input': '#1d2a39',
  			'meta-1': '#DC3545',
  			'meta-2': '#EFF2F7',
  			'meta-3': '#10B981',
  			'meta-4': '#313D4A',
  			'meta-5': '#259AE6',
  			'meta-6': '#FFBA00',
  			'meta-7': '#FF6766',
  			'meta-8': '#F0950C',
  			'meta-9': '#E5E7EB',
  			success: '#219653',
  			danger: '#D34053',
  			warning: '#FFA70B',
  			tremor: {
  				brand: {
  					faint: '#eff6ff',
  					muted: '#bfdbfe',
  					subtle: '#60a5fa',
  					DEFAULT: '#3b82f6',
  					emphasis: '#1d4ed8',
  					inverted: '#ffffff'
  				},
  				background: {
  					muted: '#f9fafb',
  					subtle: '#f3f4f6',
  					DEFAULT: '#ffffff',
  					emphasis: '#374151'
  				},
  				border: {
  					DEFAULT: '#e5e7eb'
  				},
  				ring: {
  					DEFAULT: '#e5e7eb'
  				},
  				content: {
  					subtle: '#9ca3af',
  					DEFAULT: '#6b7280',
  					emphasis: '#374151',
  					strong: '#111827',
  					inverted: '#ffffff'
  				}
  			},
  			'dark-tremor': {
  				brand: {
  					faint: '#0B1229',
  					muted: '#172554',
  					subtle: '#1e40af',
  					DEFAULT: '#3b82f6',
  					emphasis: '#60a5fa',
  					inverted: '#030712'
  				},
  				background: {
  					muted: '#131A2B',
  					subtle: '#1f2937',
  					DEFAULT: '#111827',
  					emphasis: '#d1d5db'
  				},
  				border: {
  					DEFAULT: '#1f2937'
  				},
  				ring: {
  					DEFAULT: '#1f2937'
  				},
  				content: {
  					subtle: '#4b5563',
  					DEFAULT: '#6b7280',
  					emphasis: '#e5e7eb',
  					strong: '#f9fafb',
  					inverted: '#000000'
  				}
  			},
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			},
  			sidebar: {
  				DEFAULT: 'hsl(var(--sidebar-background))',
  				foreground: 'hsl(var(--sidebar-foreground))',
  				primary: 'hsl(var(--sidebar-primary))',
  				'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
  				accent: 'hsl(var(--sidebar-accent))',
  				'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
  				border: 'hsl(var(--sidebar-border))',
  				ring: 'hsl(var(--sidebar-ring))'
  			}
  		},
  		boxShadow: {
  			'1': '0px 1px 3px rgba(0, 0, 0, 0.08)',
  			'2': '0px 1px 4px rgba(0, 0, 0, 0.12)',
  			'3': '0px 1px 5px rgba(0, 0, 0, 0.14)',
  			'4': '0px 4px 10px rgba(0, 0, 0, 0.12)',
  			'5': '0px 1px 1px rgba(0, 0, 0, 0.15)',
  			'6': '0px 3px 15px rgba(0, 0, 0, 0.1)',
  			'7': '-5px 0 0 #313D4A, 5px 0 0 #313D4A',
  			'8': '1px 0 0 #313D4A, -1px 0 0 #313D4A, 0 1px 0 #313D4A, 0 -1px 0 #313D4A, 0 3px 13px rgb(0 0 0 / 8%)',
  			default: '0px 8px 13px -3px rgba(0, 0, 0, 0.07)',
  			card: '0px 1px 3px rgba(0, 0, 0, 0.12)',
  			'card-2': '0px 1px 2px rgba(0, 0, 0, 0.05)',
  			switcher: '0px 2px 4px rgba(0, 0, 0, 0.2), inset 0px 2px 2px #FFFFFF, inset 0px -1px 1px rgba(0, 0, 0, 0.1)',
  			'switch-1': '0px 0px 5px rgba(0, 0, 0, 0.15)',
  			'tremor-input': '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  			'tremor-card': '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  			'tremor-dropdown': '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  			'dark-tremor-input': '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  			'dark-tremor-card': '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  			'dark-tremor-dropdown': '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)'
  		},
  		keyframes: {
  			rotating: {
  				'0%, 100%': {
  					transform: 'rotate(360deg)'
  				},
  				'50%': {
  					transform: 'rotate(0deg)'
  				}
  			},
  			typing: {
  				from: {
  					width: '0'
  				},
  				to: {
  					width: '6ch'
  				}
  			},
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			},
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			}
  		},
  		animation: {
  			'ping-once': 'ping 5s cubic-bezier(0, 0, 0.2, 1)',
  			rotating: 'rotating 30s linear infinite',
  			'spin-1.5': 'spin 1.5s linear infinite',
  			'spin-2': 'spin 2s linear infinite',
  			'spin-3': 'spin 3s linear infinite',
  			typing: 'typing 2s steps(6), blink 1s infinite',
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out',
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out'
  		},
  		borderRadius: {
  			'tremor-small': '0.375rem',
  			'tremor-default': '0.5rem',
  			'tremor-full': '9999px',
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		fontSize: {
  			'title-xxl': [
  				'44px',
  				'55px'
  			],
  			'title-xl': [
  				'36px',
  				'45px'
  			],
  			'title-xl2': [
  				'33px',
  				'45px'
  			],
  			'title-lg': [
  				'28px',
  				'35px'
  			],
  			'title-md': [
  				'24px',
  				'30px'
  			],
  			'title-md2': [
  				'26px',
  				'30px'
  			],
  			'title-sm': [
  				'20px',
  				'26px'
  			],
  			'title-xsm': [
  				'18px',
  				'24px'
  			],
  			'tremor-label': [
  				'0.75rem'
  			],
  			'tremor-default': [
  				'0.875rem',
  				{
  					lineHeight: '1.25rem'
  				}
  			],
  			'tremor-title': [
  				'1.125rem',
  				{
  					lineHeight: '1.75rem'
  				}
  			],
  			'tremor-metric': [
  				'1.875rem',
  				{
  					lineHeight: '2.25rem'
  				}
  			]
  		},
  		spacing: {
  			'11': '2.75rem',
  			'13': '3.25rem',
  			'14': '3.5rem',
  			'15': '3.75rem',
  			'16': '4rem',
  			'17': '4.25rem',
  			'18': '4.5rem',
  			'19': '4.75rem',
  			'21': '5.25rem',
  			'22': '5.5rem',
  			'25': '6.25rem',
  			'26': '6.5rem',
  			'27': '6.75rem',
  			'29': '7.25rem',
  			'30': '7.5rem',
  			'31': '7.75rem',
  			'34': '8.5rem',
  			'35': '8.75rem',
  			'39': '9.75rem',
  			'40': '10rem',
  			'44': '11rem',
  			'45': '11.25rem',
  			'46': '11.5rem',
  			'49': '12.25rem',
  			'50': '12.5rem',
  			'52': '13rem',
  			'54': '13.5rem',
  			'55': '13.75rem',
  			'59': '14.75rem',
  			'60': '15rem',
  			'65': '16.25rem',
  			'67': '16.75rem',
  			'70': '17.5rem',
  			'73': '18.25rem',
  			'75': '18.75rem',
  			'90': '22.5rem',
  			'94': '23.5rem',
  			'95': '23.75rem',
  			'100': '25rem',
  			'115': '28.75rem',
  			'125': '31.25rem',
  			'150': '37.5rem',
  			'180': '45rem',
  			'203': '50.75rem',
  			'230': '57.5rem',
  			'4.5': '1.125rem',
  			'5.5': '1.375rem',
  			'6.5': '1.625rem',
  			'7.5': '1.875rem',
  			'8.5': '2.125rem',
  			'9.5': '2.375rem',
  			'10.5': '2.625rem',
  			'11.5': '2.875rem',
  			'12.5': '3.125rem',
  			'13.5': '3.375rem',
  			'14.5': '3.625rem',
  			'15.5': '3.875rem',
  			'16.5': '4.125rem',
  			'17.5': '4.375rem',
  			'18.5': '4.625rem',
  			'19.5': '4.875rem',
  			'21.5': '5.375rem',
  			'22.5': '5.625rem',
  			'24.5': '6.125rem',
  			'25.5': '6.375rem',
  			'27.5': '6.875rem',
  			'29.5': '7.375rem',
  			'32.5': '8.125rem',
  			'34.5': '8.625rem',
  			'36.5': '9.125rem',
  			'37.5': '9.375rem',
  			'39.5': '9.875rem',
  			'42.5': '10.625rem',
  			'47.5': '11.875rem',
  			'52.5': '13.125rem',
  			'54.5': '13.625rem',
  			'55.5': '13.875rem',
  			'62.5': '15.625rem',
  			'67.5': '16.875rem',
  			'72.5': '18.125rem',
  			'132.5': '33.125rem',
  			'171.5': '42.875rem',
  			'187.5': '46.875rem',
  			'242.5': '60.625rem'
  		},
  		maxWidth: {
  			'3': '0.75rem',
  			'4': '1rem',
  			'11': '2.75rem',
  			'13': '3.25rem',
  			'14': '3.5rem',
  			'15': '3.75rem',
  			'25': '6.25rem',
  			'30': '7.5rem',
  			'34': '8.5rem',
  			'35': '8.75rem',
  			'40': '10rem',
  			'44': '11rem',
  			'45': '11.25rem',
  			'70': '17.5rem',
  			'90': '22.5rem',
  			'94': '23.5rem',
  			'125': '31.25rem',
  			'150': '37.5rem',
  			'180': '45rem',
  			'203': '50.75rem',
  			'230': '57.5rem',
  			'270': '67.5rem',
  			'280': '70rem',
  			'2.5': '0.625rem',
  			'22.5': '5.625rem',
  			'42.5': '10.625rem',
  			'132.5': '33.125rem',
  			'142.5': '35.625rem',
  			'242.5': '60.625rem',
  			'292.5': '73.125rem'
  		},
  		maxHeight: {
  			'35': '8.75rem',
  			'70': '17.5rem',
  			'90': '22.5rem',
  			'300': '18.75rem',
  			'550': '34.375rem'
  		},
  		minWidth: {
  			'75': '18.75rem',
  			'22.5': '5.625rem',
  			'42.5': '10.625rem',
  			'47.5': '11.875rem'
  		},
  		zIndex: {
  			'1': '1',
  			'9': '9',
  			'99': '99',
  			'999': '999',
  			'9999': '9999',
  			'99999': '99999',
  			'999999': '999999'
  		},
  		opacity: {
  			'65': '.65'
  		},
  		backgroundImage: {
  			video: 'url("./images/video/video.png")'
  		},
  		content: {
  			'icon-copy': 'url("./images/icon/icon-copy-alt.svg")'
  		},
  		transitionProperty: {
  			width: 'width',
  			stroke: 'stroke'
  		},
  		borderWidth: {
  			'6': '6px'
  		},
  		dropShadow: {
  			'1': '0px 1px 0px #E2E8F0',
  			'2': '0px 1px 4px rgba(0, 0, 0, 0.12)'
  		}
  	}
  },
  safelist: [
    {
      pattern:
        /^(bg-(?:slate|gray|zinc|neutral|stone|red|orange|amber|yellow|lime|green|emerald|teal|cyan|sky|blue|indigo|violet|purple|fuchsia|pink|rose)-(?:50|100|200|300|400|500|600|700|800|900|950))$/,
      variants: ["hover", "ui-selected"],
    },
    {
      pattern:
        /^(text-(?:slate|gray|zinc|neutral|stone|red|orange|amber|yellow|lime|green|emerald|teal|cyan|sky|blue|indigo|violet|purple|fuchsia|pink|rose)-(?:50|100|200|300|400|500|600|700|800|900|950))$/,
      variants: ["hover", "ui-selected"],
    },
    {
      pattern:
        /^(border-(?:slate|gray|zinc|neutral|stone|red|orange|amber|yellow|lime|green|emerald|teal|cyan|sky|blue|indigo|violet|purple|fuchsia|pink|rose)-(?:50|100|200|300|400|500|600|700|800|900|950))$/,
      variants: ["hover", "ui-selected"],
    },
    {
      pattern:
        /^(ring-(?:slate|gray|zinc|neutral|stone|red|orange|amber|yellow|lime|green|emerald|teal|cyan|sky|blue|indigo|violet|purple|fuchsia|pink|rose)-(?:50|100|200|300|400|500|600|700|800|900|950))$/,
    },
    {
      pattern:
        /^(stroke-(?:slate|gray|zinc|neutral|stone|red|orange|amber|yellow|lime|green|emerald|teal|cyan|sky|blue|indigo|violet|purple|fuchsia|pink|rose)-(?:50|100|200|300|400|500|600|700|800|900|950))$/,
    },
    {
      pattern:
        /^(fill-(?:slate|gray|zinc|neutral|stone|red|orange|amber|yellow|lime|green|emerald|teal|cyan|sky|blue|indigo|violet|purple|fuchsia|pink|rose)-(?:50|100|200|300|400|500|600|700|800|900|950))$/,
    },
  ],
  plugins: [headlessuiPlugin, tailwindcssAnimatePlugin],
};

export default config;
