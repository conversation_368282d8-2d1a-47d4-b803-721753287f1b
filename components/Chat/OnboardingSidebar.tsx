import { useState, useEffect, useRef } from "react";
import {
  Stepper,
  StepperItem,
  StepperIndicator,
  StepperSeparator,
  StepperTrigger,
} from "@/components/ui/stepper";
import { Button } from "@/components/ui/button";
import { useRouter, useSearchParams } from "next/navigation";
import { useAppDispatch } from "@/store/jobHook";
import {
  SidebarStateData,
  PlanDetails,
  SummaryItem,
  UserGoals,
} from "@/app/api/onboarding/state";
import { OnboardingPlanCalendarView } from "@/components/Onboarding/OnboardingPlanCalendarView";
import React from "react";

interface OnboardingSidebarProps {
  sidebarData?: SidebarStateData | null;
  selectedSport?: string | null;
}

const OnboardingSidebar: React.FC<OnboardingSidebarProps> = ({
  sidebarData = null,
  selectedSport = null,
}) => {
  const [activeStep, setActiveStep] = useState(3);
  const router = useRouter();
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const steps = [
    { step: 1, title: "Welcome", content: "Tell us about yourself..." },
    { step: 2, title: "Gathering Info", content: "Let's chat..." },
    {
      step: 3,
      title: "Reviewing Info",
      content: "Making sure we have everything...",
    },
    {
      step: 4,
      title: "Finalizing Plan",
      content: "Review your personalized plan outline and calendar.",
    },
  ];

  useEffect(() => {
    let newStep = 1;
    if (sidebarData) {
      console.log(
        "[OnboardingSidebar] useEffect - Processing sidebarData:",
        sidebarData,
      );
      if (sidebarData.generatedPlan) {
        console.log("[OnboardingSidebar] Plan detected, setting step to 4.");
        newStep = 4;
      } else if (
        sidebarData.summaryItems &&
        sidebarData.summaryItems.length > 0
      ) {
        console.log(
          "[OnboardingSidebar] Summary items detected, setting step to 3.",
        );
        newStep = 3;
      } else if (
        sidebarData.goals?.exists ||
        sidebarData.currentStage === "gathering" ||
        sidebarData.currentStage === "reviewing"
      ) {
        console.log(
          "[OnboardingSidebar] Goals/Gathering/Reviewing detected, setting step to 2.",
        );
        newStep = 2;
      } else {
        console.log("[OnboardingSidebar] Defaulting to step 1.");
      }
    }
    setActiveStep(newStep);
  }, [sidebarData]);

  // Scroll to bottom when content changes
  useEffect(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop =
        scrollContainerRef.current.scrollHeight;
    }
  }, [
    activeStep,
    sidebarData?.summaryItems,
    sidebarData?.goals,
    sidebarData?.selectedSports,
    sidebarData?.generatedPlan,
  ]);

  useEffect(() => {
    // alert(JSON.stringify(sidebarData))
    if (sidebarData) {
      console.log("[OnboardingSidebar] SidebarData details:", {
        currentStage: sidebarData.currentStage,
        goalsExist: sidebarData.goals?.exists,
        goalsList: sidebarData.goals?.list,
        summaryItemsCount: sidebarData.summaryItems?.length ?? 0,
        planExists: !!sidebarData.generatedPlan,
        planName: sidebarData.generatedPlan?.name,
        selectedSports: sidebarData.selectedSports,
      });
    }
  }, [sidebarData]);

  const chatId = window.location.pathname.split("/").pop();

  const handleSkip = () => {
    const newUrl = `/chat/${chatId}`;
    router.push(newUrl);
  };

  const renderInformationSoFar = (summaryItems?: SummaryItem[]) => {
    if (
      (!sidebarData?.goals?.list || sidebarData.goals.list.length === 0) &&
      (!sidebarData?.selectedSports ||
        sidebarData.selectedSports.length === 0) &&
      (!summaryItems || summaryItems.length === 0)
    ) {
      console.log(
        "[OnboardingSidebar] No goals, sports, or summary items to render.",
      );
      return null;
    }

    const goals = sidebarData?.goals;
    const hasGoals = goals?.exists && goals.list && goals.list.length > 0;
    const hasSports =
      sidebarData?.selectedSports && sidebarData.selectedSports.length > 0;
    const hasSummaryItems = summaryItems && summaryItems.length > 0;

    const importantCategoryKeywords = [
      "injury",
      "illness",
      "dietary",
      "event",
      "medical",
      "condition",
      // "sport" and "activity" are handled by hasSports section directly
    ];

    const getPillClassName = (isImportant: boolean) => {
      let baseClasses =
        "rounded-lg border bg-gray-100 px-3 py-1.5 text-sm text-gray-700 shadow-sm";
      if (isImportant) {
        return `${baseClasses} border-purple-600`;
      }
      return `${baseClasses} border-gray-300`;
    };

    return (
      <React.Fragment>
        <h4 className="mb-3 mt-4 text-sm font-medium">
          Your Information So Far:
        </h4>
        <div className="flex flex-wrap gap-2">
          {hasGoals &&
            goals.list.map((goal, idx) => {
              const isImportant = false; // Goals are not important by default
              return (
                <div
                  key={`goal-pill-${idx}`}
                  className={getPillClassName(isImportant)}
                >
                  <span className="font-semibold">Goals: </span>
                  {goal}
                </div>
              );
            })}

          {hasSports &&
            sidebarData?.selectedSports?.map((sport, idx) => {
              const isImportant = true; // Sports/Activities are important
              return (
                <div
                  key={`sport-pill-${idx}`}
                  className={getPillClassName(isImportant)}
                >
                  <span className="font-semibold">Sports/Activities: </span>
                  {sport}
                </div>
              );
            })}

          {hasSummaryItems &&
            summaryItems?.map((section, index) => {
              const categoryLower = section.category.toLowerCase();
              const isImportant = importantCategoryKeywords.some((keyword) =>
                categoryLower.includes(keyword),
              );
              return (
                <div
                  key={`summary-pill-${index}`}
                  className={getPillClassName(isImportant)}
                >
                  <span className="font-semibold">{section.category}: </span>
                  {section.details}
                </div>
              );
            })}
        </div>
      </React.Fragment>
    );
  };

  const renderGeneratedPlan = () => {
    const plan = sidebarData?.generatedPlan;
    if (plan) {
      console.log("[OnboardingSidebar] Rendering generated plan:", plan);
      return (
        <div className="mt-4 rounded-md border border-green-200 bg-green-50 p-4">
          <h4 className="mb-2 text-sm font-medium text-green-800">
            Generated Plan Outline:
          </h4>
          <div className="space-y-2 text-sm">
            <p>
              <strong className="font-semibold">Name:</strong> {plan.name}
            </p>
            <p>
              <strong className="font-semibold">Description:</strong>{" "}
              {plan.description}
            </p>
            <p>
              <strong className="font-semibold">Duration:</strong>{" "}
              {plan.duration}
            </p>
            <p>
              <strong className="font-semibold">Level:</strong> {plan.level}
            </p>
            <p>
              <strong className="font-semibold">Plan Type:</strong>{" "}
              {plan.planType}
            </p>
            {plan.phases && plan.phases.length > 0 && (
              <div>
                <strong className="font-semibold">Phases:</strong>
                <ul className="mt-1 list-disc pl-5">
                  {plan.phases.map(
                    (phase, index) =>
                      phase.phaseName && (
                        <li key={index} className="text-gray-700">
                          {phase.phaseName} ({phase.duration}):{" "}
                          {phase.description}
                        </li>
                      ),
                  )}
                </ul>
              </div>
            )}
          </div>
        </div>
      );
    }
    return null;
  };

  const renderContentOrLoading = () => {
    const currentStepDetails = steps.find((s) => s.step === activeStep);

    const showInitialContent =
      !sidebarData ||
      ((!sidebarData.summaryItems || sidebarData.summaryItems.length === 0) &&
        (!sidebarData.goals?.list || sidebarData.goals.list.length === 0) &&
        (!sidebarData.selectedSports ||
          sidebarData.selectedSports.length === 0) &&
        !sidebarData.generatedPlan);

    console.log("[OnboardingSidebar] Content render state:", {
      activeStep,
      currentStage: sidebarData?.currentStage,
      hasSummaryItems: (sidebarData?.summaryItems?.length ?? 0) > 0,
      hasPlan: !!sidebarData?.generatedPlan,
      showInitialContent,
    });

    if (showInitialContent) {
      return (
        <div className="mb-4">
          <p className="text-gray-500 mb-1 text-sm">Onboarding Step 1:</p>
          <h1 className="mb-6 text-3xl font-bold">Welcome to Athlea!</h1>

          <p className="mb-5">
            Athlea is your intelligent coaching companion — designed to help you
            move more, feel stronger, and stay consistent with your physical
            activity goals. Whether you&apos;re just getting started or already
            on your journey, Athlea adapts to you.
          </p>

          <h2 className="mb-4 text-xl font-bold">How Athlea Works:</h2>

          <ol className="mb-5 space-y-4">
            <li>
              <span className="font-bold">1. Set your goals.</span> During
              onboarding, you&apos;ll answer a few quick questions so we can
              understand your current habits, preferences, and goals. This helps
              Athlea personalise your experience right from the start.
            </li>
            <li>
              <span className="font-bold">2. Get your plan.</span> Based on your
              input, Athlea will create a tailored coaching plan that fits your
              lifestyle — with suggestions on movement, training sessions, and
              wellbeing tips.
            </li>
            <li>
              <span className="font-bold">3. Stay flexible.</span> Life changes
              — and so should your plan. As you interact with Athlea and
              complete sessions, we&apos;ll adjust your recommendations over
              time. The more you use it, the smarter it gets.
            </li>
            <li>
              <span className="font-bold">4. Full transparency.</span>{" "}
              You&apos;ll always be able to see what&apos;s influencing your
              coaching plan. We&apos;ll show you what we&apos;re tracking and
              why — no surprises.
            </li>
          </ol>

          <p className="font-medium">Now, let&apos;s get started!</p>
          <p>
            Tap &quot;Next&quot; to begin your journey — we&apos;re here to
            support you every step of the way.
          </p>
        </div>
      );
    }

    return (
      <div className="mb-4">
        <div className="text-gray-500 mb-2 text-sm">
          Onboarding Stage: {sidebarData?.currentStage ?? `Step ${activeStep}`}
        </div>
        <h2 className="mb-4 text-2xl font-bold">
          {currentStepDetails?.title ?? "Onboarding"}
        </h2>
        <p className="text-gray-600">
          {currentStepDetails?.content ??
            "Please follow the chat instructions."}
        </p>

        {!sidebarData ||
        (sidebarData.summaryItems &&
          sidebarData.summaryItems.length === 0 &&
          !sidebarData.generatedPlan) ? (
          <div className="bg-gray-50 mt-4 rounded-md border p-4 text-center">
            <p className="text-gray-500">Loading onboarding status...</p>
          </div>
        ) : (
          <>
            {activeStep >= 2 &&
              renderInformationSoFar(sidebarData?.summaryItems)}
            {activeStep === 4 && renderGeneratedPlan()}
            {activeStep === 4 && sidebarData?.generatedPlan && (
              <OnboardingPlanCalendarView plan={sidebarData.generatedPlan} />
            )}
          </>
        )}
        {/* <>
            {activeStep=== 1 &&
              renderInformationSoFar(sidebarData?.summaryItems)}
            {activeStep === 4 && renderGeneratedPlan()}
          </> */}
      </div>
    );
  };

  return (
    <div className="relative flex h-full flex-col bg-white">
      <div
        ref={scrollContainerRef}
        className="flex flex-1 flex-col overflow-y-auto px-6 pb-24 pt-6"
      >
        <div className="mb-6">
          <Stepper value={activeStep - 1} className="w-full">
            {steps.map((stepInfo, index) => (
              <StepperItem
                key={index}
                step={index}
                // className="[&:not(:last-child)]:flex-1"
                completed={index < activeStep - 1}
              >
                <StepperTrigger>
                  <StepperIndicator className="data-[state=active]:border-purple-600 data-[state=completed]:border-purple-600 data-[state=active]:bg-purple-600 data-[state=completed]:bg-purple-600" />
                </StepperTrigger>
                {/* {index < steps.length - 1 && (
                  <StepperSeparator className="data-[state=completed]:bg-purple-600" />
                )} */}
              </StepperItem>
            ))}
          </Stepper>
        </div>

        {renderContentOrLoading()}
      </div>

      <div className="absolute bottom-6 right-6 flex gap-4">
        <Button
          variant="default"
          // onClick={handleSkip}
          className="bg-purple-600 px-8 text-white hover:bg-purple-700"
        >
          Next Step
        </Button>
        <Button
          variant="outline"
          onClick={handleSkip}
          className="border-purple-600 px-8 text-purple-600 hover:bg-purple-100 hover:text-purple-700"
        >
          Skip
        </Button>
      </div>
    </div>
  );
};

export default OnboardingSidebar;
