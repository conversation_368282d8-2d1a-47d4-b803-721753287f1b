import NutritionGraph from "@/components/Graphs/NutritionGraph";
import React, { FC } from "react";
import {
  Draggable,
  DraggableProvided,
  DraggableStateSnapshot,
} from "react-beautiful-dnd";
import { useDispatch } from "react-redux";
import { Button } from "@/components/ui/button";
import { Calendar } from "lucide-react";
import toast from "react-hot-toast";
import { v4 as uuidv4 } from "uuid";
import useCalendarToggle from "@/hooks/useCalendarToggle";
import { CronofyEventUtil } from "@/utils/CronofyEventUtil";
import { RootState } from "@/store/store";
import { useAppSelector } from "@/store/jobHook";
import ScientificSources, { ScientificSource } from "@/components/ScientificSources";
import * as d3 from "d3";

interface MealIngredient {
  name: string;
  quantity: string;
  unit?: string;
  calories: number;
}

interface MealItem {
  description: string;
  ingredients: MealIngredient[];
  preparation?: string;
  total_calories: number;
}

interface Meal {
  items: MealItem[];
  meal_calories: number;
}

interface DailyPlan {
  breakfast: Meal;
  morning_snack: Meal;
  lunch: Meal;
  afternoon_snack: Meal;
  dinner: Meal;
  daily_total_calories: number;
}

interface Macronutrients {
  protein?: string;
  carbohydrates?: string;
  fat?: string;
}

interface NutritionTaskProps {
  task: {
    session_type: string;
    caloric_intake?: string;
    macronutrients?: Macronutrients;
    meals?: DailyPlan;
    day?: string;
    rationale?: string;
  };
  msgId?: number;
  index?: number;
}

const parseValue = (value?: string) => {
  if (!value) return 0;
  return value.includes("%")
    ? parseInt(value.replace("%", ""), 10)
    : parseInt(value, 10);
};

// Inline D3 graph for Impact on Target
const impactData = [
  { label: 'Calories', value: 13, color: '#3A7BFF' },
  { label: 'Protein', value: 21, color: '#1BC47D' },
  { label: 'Fat', value: 9, color: '#FF8C3A' },
  { label: 'Fat', value: 9, color: '#FF8C3A' },
  { label: 'Carbs', value: 10, color: '#B3B3B3' },
];

const ImpactOnTargetGraph: React.FC = () => {
  const ref = React.useRef<SVGSVGElement | null>(null);
  const [dimensions, setDimensions] = React.useState({ width: 120, height: 520 });

  React.useEffect(() => {
    function handleResize() {
      if (ref.current) {
        const parent = ref.current.parentElement;
        if (parent) {
          setDimensions({ width: parent.clientWidth, height: 520 });
        }
      }
    }
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  React.useEffect(() => {
    if (!ref.current) return;
    const svg = d3.select(ref.current);
    svg.selectAll("*").remove();
    const { width, height } = dimensions;
    const circleRadius = 28;
    const verticalGap = 44;
    const circleDiameter = circleRadius * 2;
    const totalHeight = impactData.length * circleDiameter + (impactData.length - 1) * verticalGap;
    const startY = (height - totalHeight) / 2 + circleRadius;
    const centerX = width / 2;

    impactData.forEach((d, i) => {
      const y = startY + i * (circleDiameter + verticalGap);
      // Circle
      svg.append("circle")
        .attr("cx", centerX)
        .attr("cy", y)
        .attr("r", circleRadius)
        .attr("stroke", d.color)
        .attr("stroke-width", 5)
        .attr("fill", '#fff');
      // Value
      svg.append("text")
        .attr("x", centerX)
        .attr("y", y + 5)
        .attr("text-anchor", "middle")
        .attr("font-size", 14)
        .attr("font-weight", 600)
        .attr("fill", "#000")
        .text(`${d.value}%`);
      // Label
      svg.append("text")
        .attr("x", centerX)
        .attr("y", y + circleRadius + 18)
        .attr("text-anchor", "middle")
        .attr("font-size", 12)
        .attr("fill", "#8C8C8C")
        .text(d.label);
    });
  }, [dimensions]);

  return (
    <div style={{ width: "100%", overflowX: "auto" }}>
      <svg ref={ref} width={dimensions.width} height={dimensions.height} />
    </div>
  );
};

const NutritionTask: FC<NutritionTaskProps> = ({ task, msgId, index = 0 }) => {
  const dispatch = useDispatch();
  const { taskDivRef } = useCalendarToggle();
  const userId = useAppSelector(
    (state) => state.user.userData?.user_id,
  );
  const { cronofyCalendars } = useAppSelector(
    (state: RootState) => state.calendar,
  );

  // Use the meals data if available, or fall back to the caloric_intake and macronutrients
  const totalCalories =
    task.meals?.daily_total_calories ||
    parseInt(task.caloric_intake || "0", 10);

  // Determine macros from either the structured data or the legacy format
  const protein = task.macronutrients?.protein || "";
  const carbs = task.macronutrients?.carbohydrates || "";
  const fats = task.macronutrients?.fat || "";

  const graphsData = [
    {
      label: "Energy",
      value: totalCalories,
      unit: "kcal",
      max: 2900,
      color: "#88D26F",
      markers: [2900],
    },
    {
      label: "Carbs",
      value: parseValue(carbs),
      max: 480,
      color: "#0D99FF",
      markers: [360, 480],
    },
    {
      label: "Protein",
      value: parseValue(protein),
      unit: "g",
      max: 130,
      color: "#3FDAE1",
      markers: [110, 130],
    },
    {
      label: "Fats",
      value: parseValue(fats),
      unit: "g",
      max: 71,
      color: "#FFA629",
      markers: [17, 71],
    },
  ];

  const handleAddToCalendar = async() => {
    try {
      // Get today's date as default
      const today = new Date();
      const sessionDate = today.toISOString().split("T")[0]; // Format as YYYY-MM-DD

      // Create the session task with current task data
      const sessionTask = {
        ...task,
        session_type: "nutrition",
        id: uuidv4(),
        isTemp: true,
      };

      // Dispatch the action to add the session to the calendar
      dispatch({
        type: "calendar/addSession",
        payload: {
          session: sessionTask,
          coach: "nutrition_coach",
          sessionDate,
        },
      });

      toast.success("Nutrition plan added to calendar");
          // Create Cronofy event using the utility class
          const calendarId = cronofyCalendars?.[0]?.calendar_id;
          if (calendarId) {
            await CronofyEventUtil.createEvent(userId, sessionTask, calendarId);
          } else {
            console.error('No calendar ID available');
            toast.error('Failed to add event to calendar - no calendar selected');
          }
    } catch (error) {
      console.error("Failed to add nutrition plan to calendar:", error);
      toast.error("Failed to add nutrition plan to calendar");
    }
  };

  // Helper function to render a meal section
  const renderMeal = (meal: Meal, title: string) => {
    if (!meal || !meal.items || meal.items.length === 0) return null;

    return (
      <div className="mb-6">
        <h4 className="mb-2 text-lg font-medium">{title}</h4>
        <div className="pl-4">
          {meal.items.map((item, idx) => (
            <div key={idx} className="mb-4 rounded-md bg-slate-50 p-3">
              <div className="mb-1 font-medium">{item.description}</div>
              {item.preparation && (
                <div className="mb-2 text-sm text-slate-600">
                  <span className="font-medium">Preparation: </span>
                  {item.preparation}
                </div>
              )}
              <div className="mb-2 text-sm text-slate-600">
                <span className="font-medium">Calories: </span>
                {item.total_calories} kcal
              </div>
              <div className="text-sm">
                <span className="font-medium">Ingredients:</span>
                <ul className="mt-1 list-disc pl-5">
                  {item.ingredients.map((ingredient, ingIdx) => (
                    <li key={ingIdx}>
                      {ingredient.name} - {ingredient.quantity}
                      {ingredient.unit ? ` ${ingredient.unit}` : ""} (
                      {ingredient.calories} kcal)
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Use a unique draggable ID based on msgId or index
  const draggableId = `draggable-nutrition-task-${msgId || index}`;

  return (
    // -------------------- Old Design (Commented Out) --------------------
    /*
    <div ref={taskDivRef} className="w-full">
      <div className="mb-6 flex flex-row justify-between align-bottom">
        <h3 className="text-xl font-semibold dark:text-white">Nutrition</h3>
        <Button
          size="sm"
          onClick={handleAddToCalendar}
          className="flex items-center gap-1"
        >
          <Calendar className="h-4 w-4" />
          Add to Calendar
        </Button>
      </div>

      {task.rationale && (
        <div className="mb-4 rounded-md bg-slate-50 p-3">
          <div className="mb-1 font-medium">Rationale:</div>
          <div className="text-sm text-slate-600">{task.rationale}</div>
        </div>
      )}

      <div className="mb-6">
        <h4 className="mb-4 text-lg font-medium">Macronutrients</h4>
        <div className="space-y-4">
          {graphsData.map((data, index) => (
            <div key={index} className="flex flex-col gap-2">
              <div className="flex justify-between">
                <span className="font-medium">{data.label}:</span>
                <span>
                  {data.value} {data.unit} / {data.max} {data.unit}
                </span>
              </div>
              <NutritionGraph
                value={data.value}
                max={data.max}
                color={data.color}
                markers={data.markers}
                label={data.label}
              />
            </div>
          ))}
        </div>
      </div>

      {task.meals && (
        <div>
          <h4 className="mb-4 text-xl font-semibold">Daily Meal Plan</h4>
          {renderMeal(task.meals.breakfast, "Breakfast")}
          {renderMeal(task.meals.morning_snack, "Morning Snack")}
          {renderMeal(task.meals.lunch, "Lunch")}
          {renderMeal(task.meals.afternoon_snack, "Afternoon Snack")}
          {renderMeal(task.meals.dinner, "Dinner")}
          <div className="text-right font-medium">
            Total Daily Calories: {task.meals.daily_total_calories} kcal
          </div>
        </div>
      )}
    </div>
    */
    // -------------------- New Design (Based on Provided Image) --------------------
    <div
      ref={taskDivRef}
      className="w-full max-w-2xl mx-auto bg-white rounded-2xl shadow-lg overflow-hidden border border-[#E6E6E6]"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Header Image */}
      <div className="w-full h-48 md:h-64 bg-[#F6F6F6] flex items-center justify-center overflow-hidden">
        {/* Placeholder for food image, replace with actual image if available */}
        <img
          src="https://images.unsplash.com/photo-1504674900247-0877df9cc836?auto=format&fit=crop&w=800&q=80"
          alt="Chicken Salad"
          className="object-cover w-full h-full"
          style={{ maxHeight: '260px' }}
        />
      </div>
      {/* Title & Tags */}
      <div className="px-6 pt-6 pb-2 flex flex-col md:flex-row md:items-center md:justify-between gap-2">
        <div>
          <div className="text-xs text-[#8C8C8C] font-medium mb-1">{totalCalories} Calories</div>
          <h2 className="text-2xl font-bold text-[#222] mb-2">Chicken Salad</h2>
          <div className="flex flex-wrap gap-2">
            <span className="bg-[#F2F6FF] text-[#3A7BFF] text-xs font-semibold px-3 py-1 rounded-full">Carb free</span>
            <span className="bg-[#F2FFF6] text-[#1BC47D] text-xs font-semibold px-3 py-1 rounded-full">Lactose free</span>
            <span className="bg-[#FFF6F2] text-[#FF8C3A] text-xs font-semibold px-3 py-1 rounded-full">Gluten free</span>
          </div>
        </div>
        <Button
          onClick={handleAddToCalendar}
          className="bg-[#3A7BFF] hover:bg-[#2556B0] text-white font-semibold px-6 py-2 rounded-lg flex items-center gap-2 text-sm shadow-none"
        >
          <Calendar className="h-4 w-4" />
          Add to plan
        </Button>
      </div>
      {/* Description */}
      <div className="px-6 pt-4 pb-2">
        <div className="text-[#222] text-base font-semibold mb-1">Description:</div>
        <div className="text-[#666] text-sm leading-relaxed">
          A fresh and hearty chicken salad featuring tender grilled chicken breast, crisp mixed greens, juicy cherry tomatoes, crunchy cucumbers, thinly sliced red onions, and creamy avocado. Topped with a sprinkle of toasted seeds and drizzled with a zesty lemon-herb vinaigrette, this salad strikes the perfect balance between flavour and nutrition.
        </div>
      </div>
      {/* Nutrition Stats */}
      <div className="px-6 pt-4 pb-2 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-[#F6F6F6] rounded-xl p-4 flex flex-col items-center">
          <div className="text-xs text-[#8C8C8C] font-medium mb-1">Protein:</div>
          <div className="text-2xl font-bold text-[#222] mb-1">{graphsData[2].value}g</div>
          <div className="text-xs text-[#8C8C8C]">40% of RDA</div>
          <div className="text-xs text-[#B3B3B3] mt-1">{graphsData[2].value}g / {graphsData[2].max}g</div>
        </div>
        <div className="bg-[#F6F6F6] rounded-xl p-4 flex flex-col items-center">
          <div className="text-xs text-[#8C8C8C] font-medium mb-1">Fat:</div>
          <div className="text-2xl font-bold text-[#222] mb-1">{graphsData[3].value}g</div>
          <div className="text-xs text-[#8C8C8C]">40% of RDA</div>
          <div className="text-xs text-[#B3B3B3] mt-1">{graphsData[3].value}g / {graphsData[3].max}g</div>
        </div>
        <div className="bg-[#F6F6F6] rounded-xl p-4 flex flex-col items-center">
          <div className="text-xs text-[#8C8C8C] font-medium mb-1">Carbs:</div>
          <div className="text-2xl font-bold text-[#222] mb-1">{graphsData[1].value}g</div>
          <div className="text-xs text-[#8C8C8C]">40% of RDA</div>
          <div className="text-xs text-[#B3B3B3] mt-1">{graphsData[1].value}g / {graphsData[1].max}g</div>
        </div>
      </div>
      {/* Impact on Target */}
      <div className="px-6 pt-4 pb-2">
        <div className="text-[#222] text-base font-semibold mb-2">Impact on Target (13%)</div>
        <ImpactOnTargetGraph />
      </div>
      {/* Breakdown */}
      <div className="px-6 pt-4 pb-2">
        <div className="text-[#222] text-base font-semibold mb-2">Breakdown</div>
        <div className="flex flex-col gap-2">
          <button className="flex justify-between items-center px-4 py-2 bg-[#F6F6F6] rounded-lg text-[#222] text-sm font-medium hover:bg-[#E6E6E6] transition">Protein <span className="text-[#8C8C8C]">&gt;</span></button>
          <button className="flex justify-between items-center px-4 py-2 bg-[#F6F6F6] rounded-lg text-[#222] text-sm font-medium hover:bg-[#E6E6E6] transition">Fats <span className="text-[#8C8C8C]">&gt;</span></button>
          <button className="flex justify-between items-center px-4 py-2 bg-[#F6F6F6] rounded-lg text-[#222] text-sm font-medium hover:bg-[#E6E6E6] transition">Carbs <span className="text-[#8C8C8C]">&gt;</span></button>
        </div>
      </div>
      {/* Scientific Sources */}
      <div className="px-6 pt-4 pb-2">
        <ScientificSources
          sources={[
            {
              title: "5 of the best ways to eat chicken salad",
              description: "Publication, 2024 – You might call swimming the perfect workout — it's one of the few activities that combines full-body movement, relaxation, and low-impact resistance. Whether you're pushing for performance or just...",
              tags: ["Nutrition", "Health", "Gluten free and Low Carb"],
            },
            {
              title: "Why is chicken such an essential protein?",
              description: "Publication, 2024 – You might call swimming the perfect workout — it's one of the few activities that combines full-body movement, relaxation, and low-impact resistance. Whether you're pushing for performance or just...",
              tags: ["Nutrition", "Health", "Gluten free and Low Carb"],
            },
            {
              title: "The power of health and chicken salads",
              description: "Publication, 2024 – You might call swimming the perfect workout — it's one of the few activities that combines full-body movement, relaxation, and low-impact resistance. Whether you're pushing for performance or just...",
              tags: ["Nutrition", "Health", "Gluten free and Low Carb"],
            },
          ]}
        />
      </div>
      {/* Search from the Web */}
      <div className="px-6 pt-4 pb-6">
        <div className="text-[#222] text-base font-semibold mb-2">Search from the Web</div>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 bg-[#F6F6F6] rounded-lg p-4">
            <div className="text-xs text-[#3A7BFF] font-semibold mb-2">Best Chicken Salad Recipe: How To Make Classic ...</div>
            <div className="text-xs text-[#222] mb-2">Our best chicken salad recipe combines tender shredded chicken with celery, Greek yogurt, and tons of fresh herbs.</div>
            <div className="text-xs text-[#8C8C8C]">People also ask :</div>
            <ul className="list-disc pl-5 text-xs text-[#222]">
              <li>What ingredients go in chicken salad?</li>
              <li>How to give chicken salad more flavor?</li>
              <li>What is a good dressing for chicken salad?</li>
              <li>What to have with a chicken salad?</li>
            </ul>
          </div>
          <div className="w-full md:w-48 flex-shrink-0 flex flex-col items-center justify-center">
            <img
              src="https://images.unsplash.com/photo-1670237735381-ac5c7fa72c51?q=80&w=2006&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
              alt="Chicken salad thumbnail"
              className="rounded-lg w-full h-32 object-cover mb-2"
            />
            <div className="text-xs text-[#222] text-center">Chicken salad is a leafy salad with chicken as a main protein. Recipes include: mixed salad greens, celery, onions, pepper, pickles and a variety of dressings. Wikipedia.</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NutritionTask;
