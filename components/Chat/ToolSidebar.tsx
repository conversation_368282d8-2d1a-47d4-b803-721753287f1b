import { Wrench, ExternalLink } from "lucide-react";
import React, { useRef, useState, useEffect, useMemo } from "react";
import dynamic from "next/dynamic";
import { DomainAvatar, getDomainBgColor, getDomainColor } from "./DomainUtils"; // Extension change will be handled by TypeScript resolution
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"; // Import Tabs components
import { cn } from "@/lib/utils"; // Import cn utility
import {
  Draggable,
  DraggableProvided,
  DraggableStateSnapshot,
  Droppable, // Import Droppable
  DragDropContext, // Import DragDropContext (may be needed if not higher up)
} from "react-beautiful-dnd";

// Import Session Components - ensure paths are correct
import StrengthTask from "@/components/Chat/Sessions/StrengthTask";
import RunningTask from "@/components/Chat/Sessions/RunningTask";
import CyclingTask from "@/components/Chat/Sessions/CyclingTask";
import NutritionTask from "@/components/Chat/Sessions/NutritionTask";
import RecoveryText from "@/components/Chat/TaskText/RecoveryText";

// Dynamically import LocationMap with ssr: false - ensure path is correct
const LocationMap = dynamic(() => import("@/components/Chat/Map/LocationMap"), {
  ssr: false,
  loading: () => <p>Loading map...</p>,
});

// Define types locally or import if shared
type MessageDomain =
  | "strength"
  | "cardio"
  | "running"
  | "cycling"
  | "nutrition"
  | "recovery"
  | "mental"
  | "general"
  | "finalizer"
  | "map"
  | "head"
  | "planner"
  | "reasoning"
  | "azure_search"; // Add azure_search domain type if needed

interface ToolCall {
  id: string;
  domain: MessageDomain;
  toolName: string;
  timestamp: number;
  content?: any;
  expanded?: boolean;
}

// Define Location type used for map data
interface Location {
  name: string;
  category: string;
  address: string;
  distance: string;
  coordinates: string;
}

// Define a type for the source documents
export interface SourceDocument {
  id?: string; // Optional: if your documents have a unique ID from the source
  docId?: string; // Optional: another possible ID field
  title: string;
  content: string;
  url?: string;
  score?: number; // Can be relevance, quality, or a combined score
  qualityScore?: number;
  relevanceScore?: number;
  metadata_storage_path?: string;
  // Contextual information from the tool call
  _sourceDomain: string;
  _sourceTimestamp: number;
  _sourceToolName: string;
}

interface ToolSidebarProps {
  setSourceDocuments: any;
  sourceDocuments: any;
  toolCalls: ToolCall[];
  toggleToolCallExpanded: (id: string) => void;
  getFormattedTime: (timestamp: number) => string;
}

const domainColors: { [key: string]: string } = {
  cycling: "#37C6C4",
  general: "#6580F4",
  strength: "#D343DB",
  running: "rgba(247, 181, 0, 0.1)",
  // "#F7B500",
  nutrition: "#63D571",
  recovery: "#FD8C5B",
  phase: "#A1A1AA", // Example color for phase
};

// --- Helper Functions (Keep relevant ones or move to DomainUtils) ---

// Map of tool names to display titles
const formatTitle = (name: string): string => {
  const titles: Record<string, string> = {
    azure_maps_tools: "Map Search",
    google_tools: "Web Search",
    azure_search: "Knowledge Search", // Keep this mapping
    azure_search_tools: "Knowledge Search", // Add this variant
    azure_cognitive_search: "Knowledge Search", // Add this variant
    search: "Knowledge Search", // Add this variant
    elevation: "Elevation Profile",
    generate_strength_session: "Strength Session",
    generate_running_session: "Running Session",
    generate_cycling_session: "Cycling Session",
    generate_nutrition_plan: "Nutrition Plan",
    generate_recovery_session: "Recovery Session",
    // Add more mappings as needed
  };
  // Basic formatting for unmapped names
  const formatted = name
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
  return titles[name] || formatted;
};

const getToolCallBorderColor = (domain: string): string => {
  const domainLower = domain.toLowerCase();
  if (domainLower.includes("strength")) return "border-amber-200";
  if (domainLower.includes("cardio")) return "border-red-200";
  if (domainLower.includes("running")) return "border-emerald-200";
  if (domainLower.includes("cycling")) return "border-sky-200";
  if (domainLower.includes("nutrition")) return "border-green-200";
  if (domainLower.includes("recovery")) return "border-blue-200";
  if (domainLower.includes("mental")) return "border-purple-200";
  if (domainLower.includes("map")) return "border-indigo-200";
  if (domainLower.includes("search") || domainLower.includes("azure_cognitive"))
    return "border-blue-200"; // Use blue for search for now
  return "border-slate-300"; // Changed from border-gray-200 for better visibility
};

// --- Tool Content Rendering ---
const renderToolContent = (
  toolCall: ToolCall,
  toolCallIndex: number,
): React.ReactNode | Location[] | null => {
  try {
    let parsedContent = toolCall.content;
    // Attempt to parse content if it's a string (might be stringified JSON)
    if (typeof toolCall.content === "string") {
      try {
        parsedContent = JSON.parse(toolCall.content);
      } catch (e) {
        console.warn(
          "[ToolSidebar] Failed to parse toolCall.content string, proceeding with original string:",
          toolCall.content,
          e,
        );
        // Keep parsedContent as the original string if parsing fails
      }
    }

    // Session Generation tools
    switch (toolCall.toolName) {
      case "generate_strength_session":
        console.log(
          "[ToolSidebar] Processing tool: generate_strength_session, Original Content:",
          toolCall.content,
          "Parsed Content:",
          parsedContent,
        );
        return typeof parsedContent === "object" && parsedContent !== null ? (
          <StrengthTask
            task={parsedContent}
            index={toolCallIndex}
            msgId={parseInt(toolCall.id, 10)}
          />
        ) : (
          <p className="text-xs text-red-500">Invalid strength session data</p>
        );
      case "generate_running_session":
        console.log(
          "[ToolSidebar] Processing tool: generate_running_session, Original Content:",
          toolCall.content,
          "Parsed Content:",
          parsedContent,
        );
        return typeof parsedContent === "object" && parsedContent !== null ? (
          <RunningTask
            task={parsedContent}
            index={toolCallIndex}
            msgId={parseInt(toolCall.id, 10)}
          />
        ) : (
          <p className="text-xs text-red-500">Invalid running session data</p>
        );
      case "generate_cycling_session":
        console.log(
          "[ToolSidebar] Processing tool: generate_cycling_session, Original Content:",
          toolCall.content,
          "Parsed Content:",
          parsedContent,
        );
        return typeof parsedContent === "object" && parsedContent !== null ? (
          <CyclingTask
            task={parsedContent}
            index={toolCallIndex}
            msgId={parseInt(toolCall.id, 10)}
          />
        ) : (
          <p className="text-xs text-red-500">Invalid cycling session data</p>
        );
      case "generate_nutrition_plan":
        console.log(
          "[ToolSidebar] Processing tool: generate_nutrition_plan, Original Content:",
          toolCall.content,
          "Parsed Content:",
          parsedContent,
        );
        return typeof parsedContent === "object" && parsedContent !== null ? (
          <NutritionTask
            task={parsedContent}
            msgId={parseInt(toolCall.id, 10)}
          />
        ) : (
          <p className="text-xs text-red-500">Invalid nutrition plan data</p>
        );
      case "generate_recovery_session":
        console.log(
          "[ToolSidebar] Processing tool: generate_recovery_session, Original Content:",
          toolCall.content,
          "Parsed Content:",
          parsedContent,
        );
        return typeof parsedContent === "object" && parsedContent !== null ? (
          <RecoveryText
            task={parsedContent}
            index={toolCallIndex}
            msgId={parseInt(toolCall.id, 10)}
          />
        ) : (
          <p className="text-xs text-red-500">Invalid recovery session data</p>
        );
    }

    // Map results (azure_maps_tools) - Return data, not component
    if (toolCall.toolName === "azure_maps_tools") {
      const locationsData = Array.isArray(parsedContent) ? parsedContent : [];
      const locations = locationsData
        .map((poi: any) => ({
          name: poi.poi?.name || "Unknown location",
          category: poi.poi?.categories?.join(", ") || "Location",
          address: poi.address?.freeformAddress || "",
          distance: poi.dist ? `${(poi.dist / 1000).toFixed(1)} km` : "Unknown",
          coordinates: `${poi.position.lat},${poi.position.lon}`,
        }))
        .filter(Boolean);
      return locations.length > 0 ? locations : null;
    }

    // Azure Cognitive Search results - Handled in the "Sources" tab now
    // if (
    //   (toolCall.toolName === "azure_search" ||
    //     toolCall.toolName === "search" ||
    //     toolCall.toolName === "azure_cognitive_search" ||
    //     toolCall.toolName === "azure_search_tools") &&
    //   Array.isArray(toolCall.content)
    // ) {
    //    // Logic moved to Sources Tab
    //    return null; // Don't render here
    // }

    // Default: Stringify for unhandled or simple cases
    // If parsedContent is already a string (e.g. error string from tool, or failed parse), display as is.
    // Otherwise, stringify the object for display.
    const displayContent =
      typeof parsedContent === "string"
        ? parsedContent
        : JSON.stringify(parsedContent, null, 2);

    return (
      <pre className="bg-gray-50 overflow-auto whitespace-pre-wrap rounded-md p-2 text-xs">
        {displayContent}
      </pre>
    );
  } catch (error) {
    console.error("Error rendering tool content:", error);
    return (
      <div className="text-xs text-red-500">
        Error rendering tool result: {String(error)}
      </div>
    );
  }
};

// --- Source Document URL Helpers ---
const getDisplayUrl = (doc: SourceDocument): string | null => {
  return doc.url || doc.metadata_storage_path || null;
};

const formatDisplayUrl = (url: string): string => {
  try {
    const parsedUrl = new URL(url);
    return parsedUrl.origin + parsedUrl.pathname;
  } catch (e) {
    return url; // Return original if not a valid URL
  }
};
// --- End Source Document URL Helpers ---

// --- Main Component ---
/**
 * ToolSidebar - A resizable sidebar component for displaying tool calls and source documents
 *
 * Features:
 * - Built-in resizable functionality using shadcn/ui resizable component
 * - Drag-to-resize with visual feedback and handle
 * - Panel size persistence across sessions using localStorage
 * - Keyboard accessibility with ARIA attributes
 * - Visual indicators during resize operation
 * - Configurable min/max size constraints (20%-40% of viewport)
 * - Smooth resize animations and transitions
 *
 * The resizing is handled by the parent component (chat page) using ResizablePanelGroup,
 * which provides a more robust and accessible resizing experience.
 *
 * @param props - Component props
 * @param props.setSourceDocuments - Function to update source documents
 * @param props.sourceDocuments - Array of source documents
 * @param props.toolCalls - Array of tool call objects
 * @param props.toggleToolCallExpanded - Function to toggle tool call expansion
 * @param props.getFormattedTime - Function to format timestamps
 */
const ToolSidebar: React.FC<ToolSidebarProps> = ({
  sourceDocuments,
  setSourceDocuments,
  toolCalls,
  toggleToolCallExpanded,
  getFormattedTime,
}) => {
  const toolCallsEndRef = useRef<HTMLDivElement>(null);
  const prevToolCallsLengthRef = useRef<number>(toolCalls.length);
  console.log("---toolCalls---", toolCalls);

  // State for tab management and new content indication
  const [activeTab, setActiveTab] = useState<"sources" | "artifacts">(
    "sources",
  );
  const [hasNewSources, setHasNewSources] = useState<boolean>(false);
  const [hasNewArtifacts, setHasNewArtifacts] = useState<boolean>(false);

  // State for managing expanded source document content
  const [expandedSources, setExpandedSources] = useState<
    Record<string, boolean>
  >({});

  // Define tool names that are considered sources
  const azureSearchToolNames = [
    "azure_search",
    "search",
    "azure_cognitive_search",
    "azure_search_tools",
    "azure_search_graph",
    "azure_search_retriever",
  ];

  // Filter tool calls for each tab
  const sourceToolCalls = toolCalls.filter((tc) =>
    azureSearchToolNames.includes(tc.toolName),
  );
  const artifactToolCalls = toolCalls.filter(
    (tc) => !azureSearchToolNames.includes(tc.toolName),
  );

  const validSourceDocuments: SourceDocument[] = useMemo(() => {
    return sourceDocuments.length
      ? sourceDocuments
      : sourceToolCalls.reduce((acc, toolCall) => {
          let individualDocs: any[] = [];

          if (
            toolCall.toolName === "azure_search_graph" ||
            toolCall.toolName === "azure_search_retriever"
          ) {
            // For azure_search_graph and azure_search_retriever, content is an object with a 'results' array
            if (
              typeof toolCall.content === "object" &&
              toolCall.content !== null &&
              Array.isArray(toolCall.content.results)
            ) {
              individualDocs = toolCall.content.results;
            } else if (typeof toolCall.content === "string") {
              // Fallback: if content is somehow still a string, try to parse it
              try {
                const parsed = JSON.parse(toolCall.content);
                if (
                  typeof parsed === "object" &&
                  parsed !== null &&
                  Array.isArray(parsed.results)
                ) {
                  individualDocs = parsed.results;
                }
              } catch (e) {
                console.warn(
                  "[ToolSidebar] Could not parse string content for azure_search_retriever/azure_search_graph to extract results array:",
                  toolCall.content,
                );
              }
            }
          } else {
            // For other/older azure_search tool names, content might be directly an array or stringified array
            if (Array.isArray(toolCall.content)) {
              individualDocs = toolCall.content;
            } else if (typeof toolCall.content === "string") {
              try {
                const parsed = JSON.parse(toolCall.content);
                if (Array.isArray(parsed)) {
                  individualDocs = parsed;
                }
              } catch (e) {
                console.warn(
                  "[ToolSidebar] Could not parse string content for other search tool:",
                  toolCall.content,
                );
              }
            }
          }

          const documentsWithContext = individualDocs
            .map((doc): SourceDocument | null => {
              if (
                doc &&
                (doc.title ||
                  doc.content ||
                  doc.score !== undefined ||
                  doc.qualityScore !== undefined)
              ) {
                return {
                  id: doc.id || undefined,
                  docId: doc.docId || doc.id || undefined,
                  title: doc.title,
                  content: doc.content,
                  url: doc.url,
                  score:
                    doc.qualityScore !== undefined
                      ? doc.qualityScore
                      : doc.score,
                  metadata_storage_path: doc.metadata_storage_path,
                  _sourceDomain: toolCall.domain,
                  _sourceTimestamp: toolCall.timestamp,
                  _sourceToolName: toolCall.toolName,
                };
              }
              return null;
            })
            .filter((doc): doc is SourceDocument => doc !== null);

          return acc.concat(documentsWithContext);
        }, [] as SourceDocument[]);
  }, [sourceToolCalls, sourceDocuments]);

  useEffect(() => {
    if (validSourceDocuments.length) {
      setSourceDocuments(validSourceDocuments);
    }
  }, [validSourceDocuments]);

  const PREVIEW_CHAR_LIMIT = 250; // Character limit for preview

  // Toggle function for expanding/collapsing source content
  const toggleSourceExpansion = (sourceId: string) => {
    setExpandedSources((prev) => ({
      ...prev,
      [sourceId]: !prev[sourceId],
    }));
  };

  // Effect to detect new tool calls and update indicators
  useEffect(() => {
    const previousLength = prevToolCallsLengthRef.current;
    const currentLength = toolCalls.length;

    if (currentLength > previousLength) {
      const newToolCalls = toolCalls.slice(previousLength);
      let newSourcesFound = false;
      let newArtifactsFound = false;

      newToolCalls.forEach((call) => {
        if (azureSearchToolNames.includes(call.toolName)) {
          newSourcesFound = true;
        } else {
          newArtifactsFound = true;
        }
      });

      if (newSourcesFound && activeTab !== "sources") {
        console.log("New sources detected while sources tab inactive.");
        setHasNewSources(true);
      }
      if (newArtifactsFound && activeTab !== "artifacts") {
        console.log("New artifacts detected while artifacts tab inactive.");
        setHasNewArtifacts(true);
      }
    }

    // Update the ref for the next render AFTER comparison
    prevToolCallsLengthRef.current = currentLength;
  }, [toolCalls, activeTab]);

  // Handler for changing tabs
  const handleTabChange = (value: string) => {
    setActiveTab(value as "sources" | "artifacts");
    // Reset the 'new' indicator for the tab being activated
    if (value === "sources") {
      setHasNewSources(false);
    } else if (value === "artifacts") {
      setHasNewArtifacts(false);
    }
  };

  // Placeholder onDragEnd - replace with actual logic if reordering is needed
  const onDragEnd = (result: any) => {
    console.log("ToolSidebar onDragEnd:", result);
  };

  return (
    <div className="bg-gray-50 border-gray-200 scrollbar-hide flex h-full w-full flex-col overflow-y-auto border-l">
      <div className="p-4">
        <Tabs
          value={activeTab}
          onValueChange={handleTabChange}
          className="flex flex-1 flex-col"
        >
          <TabsList className="mb-4 grid w-full grid-cols-2">
            <TabsTrigger
              value="sources"
              className={cn("relative", hasNewSources && "text-blue-600")}
            >
              Sources
              {hasNewSources && (
                <span className="absolute right-1 top-1 block h-2 w-2 rounded-full bg-blue-500" />
              )}
            </TabsTrigger>
            <TabsTrigger
              value="artifacts"
              className={cn("relative", hasNewArtifacts && "text-blue-600")}
              style={{ color: domainColors[`${toolCalls[0]?.domain}`] }}
            >
              Artifacts
              {hasNewArtifacts && (
                <span className="absolute right-1 top-1 block h-2 w-2 rounded-full bg-blue-500" />
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent
            value="sources"
            className="flex-1 space-y-2 overflow-y-auto pr-2"
          >
            {validSourceDocuments.length > 0 ? (
              <div className="space-y-2">
                {validSourceDocuments.map((doc, index) => {
                  const displayUrl = getDisplayUrl(doc);
                  const formattedUrl = displayUrl
                    ? formatDisplayUrl(displayUrl)
                    : null;
                  const docKey = doc.docId || doc.id || `source-${index}`;
                  const isExpanded = expandedSources[docKey] || false;
                  const canTruncate =
                    doc.content && doc.content.length > PREVIEW_CHAR_LIMIT;
                  const displayContent =
                    isExpanded || !canTruncate
                      ? doc.content
                      : `${doc.content.substring(0, PREVIEW_CHAR_LIMIT)}...`;

                  return (
                    <div
                      key={docKey}
                      className="rounded-md border bg-white p-3 text-sm shadow-sm"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex min-w-0 items-center gap-2">
                          <div className="flex-shrink-0">
                            <DomainAvatar domain={doc._sourceDomain} />
                          </div>
                          <div className="min-w-0 flex-1">
                            <p className="text-gray-800 truncate font-medium">
                              {doc.title || doc.docId || `Source ${index + 1}`}
                            </p>
                            <p className="text-gray-500 text-xs">
                              {getFormattedTime(doc._sourceTimestamp ?? 0)}
                            </p>
                          </div>
                        </div>
                        {doc.score !== undefined && (
                          <span className="ml-2 flex-shrink-0 rounded bg-blue-100 px-1.5 py-0.5 text-xs font-medium text-blue-700">
                            {doc.score.toFixed(1)}
                          </span>
                        )}
                      </div>
                      {doc.content && (
                        <p className="text-gray-600 mt-2 whitespace-pre-wrap text-xs">
                          {displayContent}
                        </p>
                      )}
                      {canTruncate && (
                        <button
                          onClick={() => toggleSourceExpansion(docKey)}
                          className="mt-1 block text-xs text-blue-600 hover:text-blue-800 hover:underline"
                        >
                          {isExpanded ? "Show less" : "Show more"}
                        </button>
                      )}
                      {displayUrl && (
                        <a
                          href={displayUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="mt-2 block items-center text-xs text-blue-600 hover:text-blue-800 hover:underline"
                          title={displayUrl}
                        >
                          View Source
                          <ExternalLink className="ml-1 inline-block h-3 w-3" />
                        </a>
                      )}
                      {!doc.content && !displayUrl && (
                        <p className="text-gray-500 mt-2 text-xs italic">
                          No preview or link available.
                        </p>
                      )}
                    </div>
                  );
                })}
              </div>
            ) : (
              <p className="text-gray-500 text-center text-sm">
                No sources found.
              </p>
            )}
          </TabsContent>

          <TabsContent
            value="artifacts"
            className="flex-1 space-y-2 overflow-y-auto pr-2"
          >
            {artifactToolCalls.length > 0 ? (
              <Droppable
                droppableId="tool-artifacts-droppable"
                isDropDisabled={false}
              >
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="space-y-2"
                  >
                    {artifactToolCalls.map((toolCall, toolCallIndex) => {
                      const agentDomain = toolCall.domain;
                      const titleToDisplay = formatTitle(toolCall.toolName);
                      const borderColor = getToolCallBorderColor(agentDomain);
                      const bgColor = getDomainBgColor(agentDomain);
                      const textColor = getDomainColor(agentDomain);
                      const isMapTool =
                        toolCall.toolName === "azure_maps_tools";

                      const renderedContentOrData = renderToolContent(
                        toolCall,
                        toolCallIndex,
                      );

                      const handleToolToggle = () => {
                        toggleToolCallExpanded(toolCall.id);
                      };

                      const mapLocations =
                        isMapTool && Array.isArray(renderedContentOrData)
                          ? (renderedContentOrData as Location[])
                          : null;

                      const otherRenderedContent =
                        !isMapTool &&
                        renderedContentOrData !== null &&
                        !Array.isArray(renderedContentOrData)
                          ? (renderedContentOrData as React.ReactNode)
                          : null;

                      return (
                        <div
                          key={toolCall.id}
                          className={`rounded-lg border-[1px] p-3 text-sm shadow-sm ${bgColor} ${borderColor}`}
                        >
                          <div
                            className="flex cursor-pointer items-center justify-between"
                            onClick={handleToolToggle}
                          >
                            <div className="flex items-center gap-2">
                              <DomainAvatar domain={agentDomain} />
                              <span className={`font-medium ${textColor}`}>
                                {titleToDisplay}
                              </span>
                            </div>
                            <div className="flex items-center">
                              <span className="text-gray-500 mr-2 text-xs">
                                {getFormattedTime(toolCall.timestamp)}
                              </span>
                              {toolCall.expanded ? (
                                <span className="text-xs">▼</span>
                              ) : (
                                <span className="text-xs">▶</span>
                              )}
                            </div>
                          </div>
                          <div
                            className={cn(
                              "mt-3 max-h-96 overflow-y-auto border-t pt-3 text-sm",
                              borderColor,
                              !toolCall.expanded && "hidden",
                            )}
                          >
                            {isMapTool ? (
                              mapLocations ? (
                                <div className="border-gray-200 overflow-hidden rounded-lg border">
                                  <LocationMap locations={mapLocations} />
                                </div>
                              ) : (
                                <p className="text-gray-500 text-xs">
                                  No map locations found.
                                </p>
                              )
                            ) : otherRenderedContent ? (
                              otherRenderedContent
                            ) : (
                              <p className="text-gray-500 text-xs italic">
                                No content available.
                              </p>
                            )}
                          </div>
                        </div>
                      );
                    })}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            ) : (
              <p className="text-gray-500 text-center text-sm">
                No artifacts generated yet.
              </p>
            )}
            <div ref={toolCallsEndRef} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default ToolSidebar;
