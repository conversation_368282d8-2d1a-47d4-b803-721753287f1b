import { getWebSocket } from "@/app/lib/websocketmanager";
import { useSuggestionContext } from "@/context/SuggestionContext";
import useAzurSpeechtoText from "@/hooks/useAzurSpeechtoText";
import { useAppDispatch, useAppSelector } from "@/store/jobHook";
import {
  addSystemMessage,
  addToChatMessage,
  clearSelectedSuggestions,
  initChatWebSocket,
  sendChatMessageAsync,
  sendFileMessage,
  setAnswer,
  setUploading,
  setUserInput,
  setPendingResponse,
  clearChatMessages,
  clearContentAndChats,
  clearLogArray,
} from "@/store/slices/chatSlice";
import {
  createTrainingPlanAndSendFileMessage,
  createTrainingPlanAndSendMessage,
  setIsSidebarModalOpen,
  setLastActiveCoach,
  setUserInputHeight,
  clearJobId,
  handleCreateNewChatClick,
} from "@/store/slices/jobSlice";
import { loadNotifications } from "@/store/slices/notificationSlice";
import store, { RootState } from "@/store/store";
import { Message } from "@/types/chat";
import IconButton from "@mui/material/IconButton";
import InputBase from "@mui/material/InputBase";
import Paper from "@mui/material/Paper";
import {
  Mic,
  MicOff,
  Paperclip,
  SendHorizonalIcon,
  File,
  FileX2,
  Waves,
  Search,
  Send,
  Loader2,
} from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import {
  ChangeEvent,
  FC,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useSelector } from "react-redux";
import ServiceDropdown from "./ServiceDropdown";
import ImagePreview from "./ImagePreview";
import axios from "axios";
import FilePreviewComponent from "./FilePreview/FilePreviewComponent";
import { DropEvent, FileRejection, useDropzone } from "react-dropzone";
import { useGlossary } from "@/context/GlossaryContext";
import toast from "react-hot-toast";
import { useAnimationMessage } from "@/context/AnimationMessageContext";
import { useTrainingProfile } from "@/context/TrainingProfileContext";
import { useStats } from "@/context/StatsContext";
import { TimelineRange } from "../Stats/StatsTimeline";
import { MetricDataPoint } from "@/types/stats";
import { processMetricData, processMetricGroups } from "@/utils/statsUtils";
import { toggleCalendar } from "@/store/slices/calendarSlice";
import {
  connectAudioWebSocket,
  startRecording,
  disconnectAudioWebSocket,
  setIsMicOn,
  addChatMessage,
  setCurrentEmotions,
} from "@/store/slices/audioSlice";
import { useVoice } from "@humeai/voice-react";
import { fetchEmotionalProfile, updateEmotion } from "@/store/slices/userSlice";
import { useEmotional } from "@/context/EmotionalContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/app/lib/utlis";

// Define the suggestion type based on the expected data
interface Suggestion {
  label: string;
  value: string;
}

// Add props interface
interface UserInputProps {
  onSubmitRegularMessage?: (message: string) => void;
  onSubmitWaitingInput?: (message: string) => void;
  onSubmitHomeMessage?: (message: string) => void;
  isLoading?: boolean;
  isStreaming?: boolean;
  waitingForInput?: boolean;
  inputPrompt?: string;
  isHomePage?: boolean;
  isCreatingChat?: boolean;
  onFocusChange?: (isFocused: boolean) => void;
  suggestions?: Suggestion[]; // Add the suggestions prop here
}

const findLastQuestionOrTask = (messages: Message[]): Message | null => {
  for (let i = 0; i <= messages.length - 1; i++) {
    console.log("LAST+++MEssage===>", messages[i]);
    if (messages[i].type === "question" || messages[i].type === "task") {
      console.log("Found last question or task:", messages[i]);
      return messages[i];
    }
  }
  return null;
};

const UserInput: FC<UserInputProps> = ({
  onSubmitRegularMessage,
  onSubmitWaitingInput,
  onSubmitHomeMessage,
  isLoading,
  isStreaming,
  waitingForInput,
  inputPrompt,
  isHomePage,
  isCreatingChat,
  onFocusChange,
  suggestions,
}) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const pathname = usePathname();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const [inputWidth, setInputWidth] = useState(0);
  const { showTrainingProfile, showCurrentTrainingProfile } =
    useTrainingProfile();

  const {
    setShowStats,
    setAvailableRanges,
    setSelectedRange,
    setData,
    setMetricPanels,
    setMetrics,
    availableRanges,
    data,
  } = useStats();

  const {
    myTranscript,
    recognizingTranscript,
    isListening,
    pauseListening,
    resumeListening,
    stopListening,
  } = useAzurSpeechtoText();

  // hume ai voice
  const { connect, disconnect, messages, lastUserMessage, lastVoiceMessage } =
    useVoice();

  const { isOpen: isCalendarOpen } = useAppSelector(
    (state: RootState) => state.calendar,
  );
  const { showGlossary } = useGlossary();
  const { isAnimationMessageExpanded } = useAnimationMessage();
  const { showStats } = useStats(); // Add stats context

  const {
    answer,
    pendingResponse,
    userInput,
    selectedSuggestions,
    chatMessages,
    ai_voice_messages,
    hasErrorMessage,
  } = useAppSelector((state: RootState) => state.chat);
  console.log(
    "myTranscript",
    myTranscript,
    isListening,
    "recognizingTranscript",
    recognizingTranscript,
  );

  const currWebSocket = getWebSocket();

  const {
    selectedJobId,
    coaches,
    lastActiveCoach,
    temporaryCoaches,
    isSidebarModalOpen,
  } = useAppSelector((state: RootState) => state.job);
  const [localInput, setLocalInput] = useState(userInput);
  console.log(
    "humeMessage",
    messages,
    selectedJobId,
    lastUserMessage,
    lastVoiceMessage,
  );

  const user_id = useAppSelector(
    (state: RootState) => state.user.userData?.user_id,
  );
  const { colorMode } = useSelector((state: any) => state.colormode);
  const isMicOn = useAppSelector((state: RootState) => state.audio?.isMicOn);
  console.log(
    "isListening ",
    isMicOn,
    isListening,
    myTranscript,
    recognizingTranscript,
  );

  const updateEmotionalData = useCallback(
    async (currentEmotions: any) => {
      console.log("Fetching elevation for userId:", user_id);
      if (Object.keys(currentEmotions).length > 0) {
        try {
          await axios.post(`/api/users/${user_id}/update-emotion`, {
            emotions: currentEmotions,
          });
          dispatch(updateEmotion(currentEmotions));
        } catch (error) {
          console.error("falied to post emotion");
        }
      }
    },
    [user_id],
  );

  useEffect(() => {
    if (messages && messages.length > 0) {
      if (lastUserMessage) {
        dispatch(
          addChatMessage({
            timestamp: new Date().toISOString(),
            role: lastActiveCoach,
            content: lastUserMessage.message?.content,
            emotions: lastUserMessage.models?.prosody?.scores,
            type: lastUserMessage.type,
          }),
        );

        if (lastUserMessage.models?.prosody?.scores) {
          dispatch(setCurrentEmotions(lastUserMessage.models?.prosody?.scores));
          (async () => {
            await updateEmotionalData(lastUserMessage.models?.prosody?.scores);
            await dispatch(fetchEmotionalProfile(user_id)).unwrap();
          })();
        }
      }
    }
  }, [lastUserMessage]);

  useEffect(() => {
    if (messages && messages.length > 0) {
      if (lastVoiceMessage) {
        dispatch(
          addChatMessage({
            timestamp: new Date().toISOString(),
            role: lastActiveCoach,
            content: lastVoiceMessage.message?.content,
            emotions: lastVoiceMessage.models?.prosody?.scores,
            type: lastVoiceMessage.type,
          }),
        );

        if (lastVoiceMessage.models?.prosody?.scores) {
          dispatch(
            setCurrentEmotions(lastVoiceMessage.models?.prosody?.scores),
          );
        }
      }
    }
  }, [lastVoiceMessage]);

  const coachNameSafe = lastActiveCoach || "default";

  const [lastMessage, setLastMessage] = useState<Message | null>(null);

  const [attachment, setAttachment] = useState<any[]>([]);
  const [previewUrl, setPreviewUrl] = useState<any>(null);
  const [isImageSelection, setIsImageSelection] = useState<boolean>(false);
  const [isFileUploading, setIfileUploading] = useState<boolean>(false);
  const [uploadedFile, setUploadedFile] = useState<{
    file: File;
    fileId: string;
  } | null>(null);
  const [isErrorFile, setIsErrorFile] = useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [fileSize, setFileSize] = useState<number>(0);
  const [cumulativeTranscript, setCumulativeTranscript] = useState("");
  // const [isMicOn, setIsMicOn] = useState(false);
  const [isEnterPressed, setIsEnterPressed] = useState<boolean>(false);
  const selectedCoach = useSelector(
    (state: RootState) => state.job.lastActiveCoach,
  );
  const { showEmotional, setShowEmotional } = useEmotional();

  // Determine if running within the LangGraph chat page context
  const isLangGraphChat = pathname?.includes("/chat/");

  const allowedTypes = [
    "text/plain",
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "text/csv",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "image/jpeg",
    "image/png",
    "image/webp",
  ];

  const handleFileUpload = useCallback(
    async (file: File) => {
      console.log("File selected:", file?.name);

      if (!file) {
        console.error("No file selected");
        return;
      }

      // User ID check logic
      let attempts = 0;
      const maxAttempts = 10;
      let currentUserId = user_id;

      while (!currentUserId && attempts < maxAttempts) {
        await new Promise((resolve) => setTimeout(resolve, 500));
        currentUserId = store.getState().user.userData?.user_id;
        attempts++;
      }

      if (!currentUserId) {
        console.error("User ID is missing after waiting");
        toast.error("Unable to get user ID. Please try again.");
        return;
      }

      // File validation
      if (!allowedTypes.includes(file.type)) {
        console.error("File type not allowed:", file.type);
        dispatch(setAnswer(`Error: File type ${file.type} is not allowed`));
        return;
      }

      console.log("File type allowed:", file.type);
      setIsImageSelection(file.type.startsWith("image/"));
      setFileSize(fileSize + file.size);
      setAttachment([...attachment, file]);

      if (file && fileSize + file.size > 5 * 1024 * 1024) {
        console.error("Can not upload file more than 5MB");
        setIsErrorFile(true);
        dispatch(setAnswer(`Error: File size more than 5MB is not allowed`));
        return;
      }

      setIfileUploading(true);

      // Handle image preview
      if (file.type.startsWith("image/")) {
        const reader = new FileReader();
        reader.onloadend = () => {
          console.log("Image preview generated");
          setPreviewUrl(reader.result);
        };
        reader.readAsDataURL(file);
      }

      const formData = new FormData();
      formData.append("file", file);
      formData.append("user_id", currentUserId);

      try {
        console.log("Starting file upload");
        dispatch(setUploading(true));

        const response = await fetch("/api/upload", {
          method: "POST",
          body: formData,
        });

        console.log("Upload response received:", response.status);

        if (response.ok) {
          const result = await response.json();
          console.log("File uploaded successfully:", result);
          setUploadedFile({ file, fileId: result.fileId });
          toast.success("File uploaded successfully");

          // Step 1: Determine if file has analyzable stats
          try {
            console.log("Checking if file contains analyzable stats");
            const determineResponse = await fetch("/api/analyze/determine", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                userId: currentUserId,
                fileName: file.name,
              }),
            });

            if (!determineResponse.ok) {
              throw new Error("Failed to determine stats potential");
            }

            const determineResult = await determineResponse.json();
            console.log("Stats determination result:", determineResult);

            // Step 2: If file has analyzable stats, perform analysis
            if (determineResult.hasStats) {
              console.log(
                "File contains analyzable stats, starting detailed analysis",
              );

              // Create new FormData for analysis
              const analysisFormData = new FormData();
              analysisFormData.append("file", file);
              // Optional: Add any additional context from determination
              analysisFormData.append(
                "determinationResult",
                JSON.stringify(determineResult),
              );
              analysisFormData.append("userId", currentUserId);
              analysisFormData.append("fileId", result.fileId);

              const analyzeResponse = await fetch("/api/analyze", {
                method: "POST",
                body: analysisFormData,
              });

              if (!analyzeResponse.ok) {
                throw new Error("Failed to analyze stats");
              }

              const analysisResult = await analyzeResponse.json();
              console.log("Stats analysis completed:", analysisResult);

              if (analysisResult.success) {
                try {
                  // Save stats to API
                  const saveResponse = await fetch(
                    `/api/stats?user_id=${currentUserId}`,
                    {
                      method: "POST",
                      headers: {
                        "Content-Type": "application/json",
                      },
                      body: JSON.stringify({
                        analysis: analysisResult.analysis,
                        metadata: analysisResult.metadata,
                        rawData: analysisResult.rawData,
                        dateRange: analysisResult.dateRange,
                      }),
                    },
                  );

                  if (saveResponse.ok) {
                    // Create timeline range object
                    const newRange: TimelineRange = {
                      startDate: new Date(analysisResult.dateRange.startDate),
                      endDate: new Date(analysisResult.dateRange.endDate),
                      rawData: analysisResult.rawData,
                      analysis: {
                        metricGroups: analysisResult.analysis.metricGroups,
                      },
                      metadata: {
                        fileName: analysisResult.metadata.fileName,
                        fileType: analysisResult.metadata?.fileType,
                        fileSize: analysisResult.metadata?.fileSize,
                        image_url: analysisResult.metadata?.image_url,
                      },
                    };

                    // Update ranges if not exists
                    const foundRange = availableRanges.find(
                      (item: TimelineRange) =>
                        item.metadata?.fileName ===
                        analysisResult.metadata.fileName,
                    );

                    // Update ranges if not exists
                    if (!foundRange) {
                      setAvailableRanges((prev) => [...prev, newRange]);
                    }

                    // Set selected range
                    setSelectedRange(newRange);

                    // Update dashboard data if not exists
                    const foundStats = data.find(
                      (item: MetricDataPoint) =>
                        item.fileName === analysisResult.metadata.fileName,
                    );

                    if (!foundStats) {
                      setData([
                        ...data,
                        ...[
                          {
                            ...analysisResult.rawData[0],
                            ...analysisResult.metadata,
                          },
                        ],
                      ]);
                    }

                    // Process and set metrics
                    const processedPanels = processMetricData(
                      analysisResult.rawData,
                      analysisResult,
                    );
                    const processedMetrics =
                      processMetricGroups(analysisResult);
                    setMetricPanels(processedPanels);
                    setMetrics(processedMetrics);

                    // Show success message
                    toast.success(
                      "Stats analysis completed and saved successfully",
                    );

                    // Show stats view
                    setShowStats(true);
                  } else {
                    throw new Error("Failed to save analysis");
                  }
                } catch (error) {
                  console.error("Failed to save stats:", error);
                  toast.error("Analysis completed but failed to save stats");
                }
              }
            } else {
              console.log("File does not contain analyzable stats");
            }
          } catch (statsError) {
            console.error("Stats processing failed:", statsError);
            // Don't show error to user since the upload succeeded
          }
        } else {
          const errorData = await response.json();
          console.error("File upload failed:", errorData.error);
          toast.error(`Upload failed: ${errorData.error}`);
        }
      } catch (error) {
        console.error("Error uploading file:", error);
        toast.error("Error uploading file. Please try again.");
      } finally {
        console.log("Upload process completed");
        dispatch(setUploading(false));
        setIfileUploading(false);
      }
    },
    [
      dispatch,
      user_id,
      setIsImageSelection,
      setIfileUploading,
      setAttachment,
      setPreviewUrl,
      attachment,
      fileSize,
      setShowStats,
      setAvailableRanges,
      setSelectedRange,
      setData,
      setMetricPanels,
      setMetrics,
      availableRanges,
      data,
      processMetricData,
      processMetricGroups,
    ],
  );
  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        handleFileUpload(acceptedFiles[0]);
      }
    },
    [handleFileUpload],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      "text/plain": [],
      "application/pdf": [],
      "application/msword": [],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        [],
      "text/csv": [],
      "application/vnd.ms-excel": [],
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [],
      "image/jpeg": [],
      "image/png": [],
      "image/webp": [],
    },
    maxFiles: 1,
    maxSize: 5 * 1024 * 1024,
    minSize: 0,
    onDrop,
    noClick: true, // Prevent opening file dialog on click
    noDragEventsBubbling: true, // Prevent drag events from bubbling up
  });

  useEffect(() => {
    if (attachment.length > 0) {
      const totalFileSize = attachment
        .map((item: File) => item.size)
        .reduce((prev: any, current: any) => prev + current);
      setFileSize(totalFileSize);
      if (totalFileSize > 5 * 1024 * 1024) {
        setIsErrorFile(true);
      } else {
        setIsErrorFile(false);
      }
    }
  }, [attachment]);

  const {
    setShowSuggestions,
    handleGenerateSuggestions,
    setIsLoadingSuggestions,
    autoShowSuggestions,
    setAutoShowSuggestions,
    setSuggestions,
  } = useSuggestionContext();

  const handleDelErroFile = useCallback(
    (index: number) => {
      let newAttachmnets = [...attachment];
      newAttachmnets = newAttachmnets.filter((_, idx: number) => idx !== index);
      setAttachment(newAttachmnets);
      setIsErrorFile(false);
    },
    [attachment],
  );

  const handleDeleteDoc = useCallback(
    async (docName: string, index: number) => {
      try {
        setIsDeleting(true);
        const { data } = await axios.delete(
          `/api/upload?docName=${docName}&userId=${user_id}`,
        );
        handleDelErroFile(index);
        // setAttachment([]);
        setPreviewUrl(null);
        setIsErrorFile(false);
        setIsDeleting(false);
      } catch (error) {
        setIsDeleting(false);
        setAttachment([]);
        setPreviewUrl(null);
      }
    },
    [uploadedFile, handleDelErroFile],
  );

  useEffect(() => {
    // Update local input when userInput in Redux changes
    setLocalInput(userInput);
  }, [userInput]);

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      setLocalInput(newValue);
      dispatch(setAnswer(newValue));
      dispatch(setUserInput(newValue));
      if (inputRef.current?.offsetHeight) {
        dispatch(setUserInputHeight(inputRef.current?.offsetHeight));
      }
    },
    [dispatch],
  );

  // useEffect(() => {
  //   let isMounted = true;
  //   setTimeout(() => {
  //     fetchMessage(isMounted);
  //   }, 2000);
  //   return () => {
  //     console.log("Cleanup: setting isMounted to false");
  //     isMounted = false;
  //   };
  // }, [selectedJobId]);

  useEffect(() => {
    if (pathname === "/") {
      // alert('mic off')
      if (isMicOn) {
        dispatch(setIsMicOn(false));
        // stopListening();
        setCumulativeTranscript("");
      }
    }
  }, [pathname]);

  const fetchMessage = async (isMounted: boolean) => {
    if (selectedJobId) {
      try {
        const response = await fetch(`/api/messages/${selectedJobId}`);
        if (response.ok) {
          const data = await response.json();
          if (isMounted && data.messageList) {
            const lastQuestionOrTask = findLastQuestionOrTask(data.messageList);
            setLastMessage(lastQuestionOrTask);
          } else {
            console.log("Component unmounted or no messageList in data");
          }
        } else {
          console.error(`Failed to fetch messages. Status: ${response.status}`);
        }
      } catch (error) {
        console.error("Error fetching messages:", error);
      }
    } else {
      console.log("No selectedJobId, skipping fetch");
    }
  };

  useEffect(() => {
    setShowSuggestions(false);
    setAutoShowSuggestions(false);
  }, [selectedJobId, setShowSuggestions, setAutoShowSuggestions]);

  const checkAndAddNote = async (message: string, date: string) => {
    try {
      const response = await axios.post("/api/coach-notes", {
        message,
        userId: user_id,
        date,
      });

      if (response.data.success) {
        toast.success("Training note added to calendar");
        // Open calendar if it's not already open
        if (!isCalendarOpen) {
          dispatch(toggleCalendar());
        }
      }
    } catch (error) {
      // Silently log error but don't show to user
      console.error("Error adding note:", error);
    }
  };

  const handleSubmitAnswer = async () => {
    if (!localInput.trim()) {
      toast.error("Please enter a message to continue.");
      return;
    }

    // Handle home page message
    if (isHomePage && onSubmitHomeMessage) {
      onSubmitHomeMessage(localInput.trim());
      setLocalInput("");
      dispatch(setUserInput(""));
      dispatch(setAnswer(""));
      return;
    }

    // Handle waiting for input
    if (waitingForInput && onSubmitWaitingInput) {
      onSubmitWaitingInput(localInput.trim());
      setLocalInput("");
      dispatch(setUserInput(""));
      dispatch(setAnswer(""));
      return;
    }

    // Handle regular message
    if (onSubmitRegularMessage) {
      onSubmitRegularMessage(localInput.trim());
      setLocalInput("");
      dispatch(setUserInput(""));
      dispatch(setAnswer(""));
      return;
    }

    // Original logic below for when no custom handlers are provided
    setIsEnterPressed(true);
    const messageToSend = answer.trim();
    const combinedMessage = [...selectedSuggestions, messageToSend]
      .filter(Boolean)
      .join(", ");

    // Guard clause: Do nothing if there's no message and no file
    if (combinedMessage.length === 0 && !uploadedFile) {
      setIsEnterPressed(false); // Reset enter press state
      return;
    }

    // Clear suggestions and input state from Redux
    dispatch(clearSelectedSuggestions());
    dispatch(setAnswer("")); // Clear Redux answer state
    dispatch(setUserInput("")); // Clear Redux user input state
    setLocalInput(""); // Clear local input state as well

    // Ensure the input field is cleared after Redux state updates
    setTimeout(() => {
      setLocalInput("");
    }, 50);

    // Pause microphone if active
    if (isMicOn) {
      // pauseListening(); // Keep this commented if not using Azure Speech directly here
    }

    try {
      // Original logic for non-LangGraph pages (e.g., WebSocket)
      console.log("UserInput: Sending message via WebSocket (original logic)");
      await checkAndAddNote(
        combinedMessage,
        new Date().toISOString().split("T")[0],
      );
      if (currWebSocket && currWebSocket.readyState === WebSocket.OPEN) {
        if (uploadedFile) {
          await dispatch(
            sendFileMessage({
              message: combinedMessage,
              fileName: uploadedFile.file.name,
              fileType: uploadedFile.file.type,
              fileId: uploadedFile.fileId,
            }),
          );
          setUploadedFile(null);
        } else {
          await dispatch(sendChatMessageAsync({ msg: combinedMessage }));
        }
        // Profile update logic remains for non-LangGraph context
        try {
          const response = await axios.post("/api/update-profile", {
            message: combinedMessage,
            userId: user_id,
          });
          if (
            response.data.updated &&
            Object.keys(response.data.cleanedProfile).length > 0
          ) {
            console.log("Profile updated:", response.data.cleanedProfile);
            toast.success("Profile has been updated");
            const updateMessage = Object.entries(response.data.cleanedProfile)
              .map(([domain, updates]) =>
                Object.entries(updates as Record<string, any>).map(
                  ([key, value]) => `${domain}.${key}: ${value}`,
                ),
              )
              .flat()
              .join(", ");
            const systemMessage = `Training profile updated: ${updateMessage}`;
            dispatch(
              addToChatMessage({
                msg: systemMessage,
                msg_from: "system",
                display: true,
                timestamp: new Date().toISOString(),
              }),
            );
            await dispatch(
              loadNotifications({ userId: user_id, unread_count: false }),
            ).unwrap();
          } else {
            console.log(
              "No fields were updated or profile update was not required.",
            );
          }
        } catch (error) {
          console.error("Error updating profile:", error);
        }
        // Suggestion logic remains for non-LangGraph context
        setIsLoadingSuggestions(true);
        setSuggestions([]);
        setTimeout(() => {
          handleCallMsgListAPIAfterSubmitANS();
        }, 2000);
      } else {
        console.error("Cannot send message: WebSocket is not connected.");
        toast.error("Cannot send message. Please check your connection.");
      }

      // Common cleanup logic (applies to all contexts)
      setCumulativeTranscript("");
      setAttachment([]);
      setPreviewUrl(null);
      setIsImageSelection(false);
      setIfileUploading(false);
      setUploadedFile(null); // Ensure uploaded file state is cleared
      setIsErrorFile(false);
      // Resume listening after a short delay if mic was on
      if (isMicOn) {
        setTimeout(() => {
          // resumeListening(); // Keep commented if not using Azure Speech directly
        }, 100);
      }
    } catch (error) {
      console.error("Error in handleSubmitAnswer:", error);
      toast.error("An error occurred while sending the message.");
    } finally {
      setIsEnterPressed(false); // Reset enter press state
    }
  };

  useEffect(() => {
    if (pendingResponse && isListening) {
      // pauseListening();
    } else if (
      !pendingResponse &&
      isMicOn &&
      !isListening &&
      pathname !== "/"
    ) {
      // resumeListening();
    }
  }, [pendingResponse, isListening, isMicOn, pauseListening, resumeListening]);

  const handleCallMsgListAPIAfterSubmitANS = async () => {
    const response = await fetch(`/api/messages/${selectedJobId}`);
    if (response.ok) {
      const data = await response.json();
      if (data.messageList) {
        const lastQuestionOrTask = findLastQuestionOrTask(data.messageList);
        setLastMessage(lastQuestionOrTask);
      }
    }
  };

  useEffect(() => {
    console.log(
      `pendingResponse: ${pendingResponse}, lastMessage: ${JSON.stringify(lastMessage)}`,
    );
    if (pendingResponse) {
      console.log(
        "Setting isLoadingSuggestions to true due to pendingResponse",
      );
      setIsLoadingSuggestions(true);
    } else if (lastMessage) {
      console.log(
        "Setting isLoadingSuggestions to false due to new lastMessage",
      );
      setIsLoadingSuggestions(false);
    }
  }, [pendingResponse, lastMessage, setIsLoadingSuggestions]);

  console.log("Pending Response", pendingResponse);
  useEffect(() => {
    if (myTranscript) {
      setCumulativeTranscript((prevTranscript) => {
        const newTranscript = prevTranscript
          ? `${prevTranscript} ${myTranscript}`
          : myTranscript;
        dispatch(setAnswer(newTranscript));
        dispatch(setUserInput(newTranscript));
        return newTranscript;
      });
    }
  }, [myTranscript, dispatch]);

  useEffect(() => {
    if (!isListening) {
      // Comment out problematic function call - we can restore it when proper imports are fixed
      // dispatch(setAiVoiceMessages([]));
      setShowSuggestions(false);
    }
  }, [isListening, dispatch, setShowSuggestions]);

  const hasInputOrSuggestions =
    answer.trim().length > 0 || selectedSuggestions.length > 0;

  const handleAttachmentClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  useEffect(() => {
    const generateSuggestionsForNewMessage = async () => {
      console.log(
        `Checking for new message. lastMessage: ${JSON.stringify(lastMessage)}, autoShowSuggestions: ${autoShowSuggestions}`,
      );
      if (
        lastMessage &&
        lastMessage.id &&
        (lastMessage.type === "question" || lastMessage.type === "task")
      ) {
        const lastGeneratedId = localStorage.getItem(
          "lastGeneratedSuggestionId",
        );
        console.log(`Last generated ID from localStorage: ${lastGeneratedId}`);

        if (
          (!lastGeneratedId || lastMessage.id.toString() !== lastGeneratedId) &&
          autoShowSuggestions
        ) {
          console.log("Generating suggestions for new message:", lastMessage);
          await handleGenerateSuggestions(lastMessage);
          localStorage.setItem(
            "lastGeneratedSuggestionId",
            lastMessage.id.toString(),
          );
          console.log(
            `Updated lastGeneratedSuggestionId in localStorage to ${lastMessage.id}`,
          );
        } else {
          console.log(
            "Skipping suggestion generation: same message ID or autoShowSuggestions is false",
          );
        }
      } else {
        console.log(
          "Cannot generate suggestions: invalid lastMessage type or no lastMessage",
        );
      }
    };

    generateSuggestionsForNewMessage();
  }, [lastMessage, autoShowSuggestions, handleGenerateSuggestions]);

  const shouldApplyExtraStyles = useMemo(() => {
    const otherStatesActive =
      isCalendarOpen ||
      isAnimationMessageExpanded ||
      showGlossary ||
      showStats ||
      showEmotional ||
      showCurrentTrainingProfile; // Add stats to other states

    return (
      (!isCalendarOpen &&
        !isAnimationMessageExpanded &&
        !showGlossary &&
        !showCurrentTrainingProfile &&
        !showStats && // Add stats to extra styles check
        !showTrainingProfile &&
        !showEmotional) ||
      (showTrainingProfile && otherStatesActive)
    );
  }, [
    isCalendarOpen,
    isAnimationMessageExpanded,
    showGlossary,
    showTrainingProfile,
    showStats, // Add stats to dependencies
    showCurrentTrainingProfile,
    showEmotional,
  ]);
  console.log("shouldApplyExtraStyles", shouldApplyExtraStyles);

  useEffect(() => {
    const updateInputWidth = () => {
      if (formRef.current) {
        setInputWidth(formRef.current.offsetWidth);
      }
    };

    // Initial measurement
    setTimeout(() => {
      updateInputWidth();
    }, 500);
    // Set up event listener for window resize
    window.addEventListener("resize", updateInputWidth);

    // Clean up
    return () => {
      window.removeEventListener("resize", updateInputWidth);
    };
  }, [shouldApplyExtraStyles]);

  // Also update the useEffect for calendar/animation changes
  useEffect(() => {
    setTimeout(() => {
      if (formRef.current && (isCalendarOpen || isAnimationMessageExpanded)) {
        setInputWidth(formRef.current.offsetWidth);
      }
    }, 500);
  }, [isCalendarOpen, isAnimationMessageExpanded]);

  const handleMicToggle = () => {
    if (isMicOn) {
      dispatch(setIsMicOn(false));
      // stopListening();
      setCumulativeTranscript("");
    } else {
      dispatch(setIsMicOn(true));
      if (!pendingResponse) {
        setCumulativeTranscript("");
        // resumeListening();
      }
    }
  };

  const handleMicPress = async () => {
    try {
      const sessionId = crypto.randomUUID();
      console.log("[UserInput] Starting mic press with sessionId:", sessionId);

      // Use the current selected coach instead of hardcoding
      const currentCoach = selectedCoach || "Cycling Coach"; // Fallback if no coach selected
      console.log("[UserInput] Current coach:", currentCoach);

      // Convert display name to backend format for WebSocket
      const backendCoachId =
        currentCoach.replace(" Coach", "").toLowerCase().split(" ").join("_") +
        "_coach";

      console.log("[UserInput] Using coach for WebSocket:", {
        displayName: currentCoach,
        backendId: backendCoachId,
      });

      // Keep the same display format in Redux
      dispatch(setLastActiveCoach(currentCoach));
      dispatch(setIsMicOn(true));
      connect()
        .then((value) => {
          console.log("hume conneted", value);
        })
        .catch((reason) => {
          console.log("hume error", reason);
        });

      console.log("[UserInput] Connecting WebSocket...");
      // await dispatch(
      //   connectAudioWebSocket({
      //     sessionId,
      //     userId: user_id,
      //     coach: backendCoachId, // Use the backend format for WebSocket
      //     onMessage: (message) => {
      //       console.log("[UserInput] WebSocket message received:", message);
      //     },
      //   }),
      // ).unwrap();
      console.log("[UserInput] WebSocket connected successfully");

      console.log("[UserInput] Navigating to audio page...");
      // if (pathname === "/") {
      // }
      router.push(`/audio/${sessionId}`);
    } catch (error) {
      console.error("[UserInput] Error in handleMicPress:", error);
      toast.error("Failed to start recording");
      dispatch(disconnectAudioWebSocket());
      dispatch(setIsMicOn(false));
    }
  };

  const [isInputFocused, setIsInputFocused] = useState<boolean>(false);

  return (
    <div className="flex w-full flex-col items-center ">
      {/* Suggestions Container */}
      {suggestions && suggestions.length > 0 && (
        <div className="mb-2 flex flex-wrap justify-center gap-2 px-4">
          {suggestions.map((suggestion) => (
            <Button
              key={suggestion.value}
              variant="outline"
              size="sm"
              className="h-auto rounded-full px-3 py-1 text-sm"
              onClick={() => {
                if (onSubmitRegularMessage) {
                  onSubmitRegularMessage(suggestion.value);
                }
                // Optionally clear input field or perform other actions
                setLocalInput("");
                dispatch(setUserInput(""));
                dispatch(setAnswer(""));
              }}
              // Prevent the button click from blurring the main input immediately
              onMouseDown={(e) => e.preventDefault()}
            >
              {suggestion.label}
            </Button>
          ))}
        </div>
      )}

      <form
        onSubmit={(e) => {
          e.preventDefault();
          handleSubmitAnswer();
        }}
        className={cn(
          "w-full max-w-sm items-center gap-2 border bg-white px-4 py-2 shadow-sm transition-all duration-200 sm:max-w-md md:max-w-lg lg:max-w-3xl",
          isInputFocused ? "shadow-md" : "shadow-sm",
          attachment.length > 0 ? "rounded-xl" : "rounded-full",
        )}
        ref={formRef}
      >
        {attachment.length > 0 && isImageSelection && previewUrl && (
          <div className="relative w-[80px] rounded-md border ">
            <ImagePreview previewUrl={previewUrl} />
            {isFileUploading && (
              <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 items-center justify-center">
                <div className="h-8 w-8 animate-spin rounded-full border-2 border-solid border-[#000000] border-t-transparent"></div>
              </div>
            )}
          </div>
        )}

        {attachment.length > 0 && !isImageSelection && (
          <div
            className=" left-0 flex flex-row overflow-scroll rounded-md"
            style={{ width: "auto" }}
          >
            {attachment.map((attachedfile: File, index: number) => (
              <FilePreviewComponent
                key={index}
                attachment={attachedfile}
                colorMode={colorMode}
                isFileUploading={isFileUploading}
                isErrorFile={isErrorFile}
                isDeleting={isDeleting}
                isLastIndex={
                  attachment.length > 0 && attachment.length - 1 === index
                    ? true
                    : false
                }
                onRemove={() => {
                  if (isErrorFile) {
                    handleDelErroFile(index);
                  } else if (attachedfile) {
                    handleDeleteDoc(attachedfile?.name, index);
                  }
                }}
                inputWidth={inputWidth}
              />
            ))}
          </div>
        )}
        <div className="flex w-full items-center">
          <Search className="text-gray-400 h-5 w-5" />
          <Input
            type="text"
            value={localInput}
            onChange={handleInputChange}
            placeholder={
              isHomePage
                ? "Ask about training, nutrition, or recovery..."
                : isLangGraphChat
                  ? waitingForInput
                    ? inputPrompt ||
                      "Please provide the requested information..."
                    : "Ask a fitness question..."
                  : selectedJobId
                    ? "Send message"
                    : "Ask about training, nutrition, or recovery..."
            }
            className="placeholder:text-gray-500 flex-1 border-none bg-transparent outline-none focus:ring-0"
            onFocus={() => {
              setIsInputFocused(true);
              if (onFocusChange) onFocusChange(true);
            }}
            onBlur={(e) => {
              const relatedTarget = e.relatedTarget;

              // Allow time for click events on suggestion pills to propagate
              // before deciding whether to remove focus
              setTimeout(() => {
                // Only lose focus if not clicking a suggestion pill
                const isClickingPill = document
                  .querySelector(".suggestion-pills-container")
                  ?.contains(relatedTarget as Node);

                if (
                  !formRef.current?.contains(relatedTarget as Node) &&
                  !isClickingPill
                ) {
                  setIsInputFocused(false);
                  if (onFocusChange) onFocusChange(false);
                  if (!isLangGraphChat) {
                    dispatch(setIsSidebarModalOpen(true));
                  }
                }
              }, 10);
            }}
            disabled={
              (isHomePage
                ? isCreatingChat
                : isLangGraphChat
                  ? (isLoading || isStreaming) && !waitingForInput
                  : pendingResponse) || hasErrorMessage
            }
          />

          <IconButton
            onClick={handleAttachmentClick}
            disabled={pendingResponse || isFileUploading}
            aria-label="attach file"
            sx={{
              color: "#90a4ae",
              borderRadius: "10%",
              p: "6px",
              "&:hover": {
                backgroundColor: "transparent",
                color: "#1976d2",
              },
            }}
          >
            <Paperclip size={20} />
          </IconButton>

          <Button
            type="submit"
            size="sm"
            variant="ghost"
            disabled={isLoading || !hasInputOrSuggestions || hasErrorMessage}
          >
            {isHomePage && isCreatingChat ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : isLangGraphChat && isLoading && !waitingForInput ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-5 w-5" />
            )}
          </Button>

          <input
            {...getInputProps()}
            type="file"
            accept="*"
            ref={fileInputRef}
            style={{ display: "none" }}
            onChange={(e) => {
              if (e.target.files && e.target.files[0]) {
                handleFileUpload(e.target.files[0]);
              }
            }}
          />
        </div>
      </form>

      {/* {attachment.length > 0 && isImageSelection && previewUrl && (
        <div className="absolute bottom-16 left-0 rounded-md border">
          <ImagePreview previewUrl={previewUrl} />
          {isFileUploading && (
            <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 items-center justify-center">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-solid border-[#000000] border-t-transparent"></div>
            </div>
          )}
        </div>
      )} */}

      {/*{attachment.length > 0 && !isImageSelection && (
        <div
          className="absolute bottom-16 left-0 flex flex-row overflow-scroll rounded-md"
          style={{ width: inputWidth > 0 ? inputWidth + 42 : "auto" }}
        >
          {attachment.map((attachedfile: File, index: number) => (
            <FilePreviewComponent
              key={index}
              attachment={attachedfile}
              colorMode={colorMode}
              isFileUploading={isFileUploading}
              isErrorFile={isErrorFile}
              isDeleting={isDeleting}
              isLastIndex={
                attachment.length > 0 && attachment.length - 1 === index
                  ? true
                  : false
              }
              onRemove={() => {
                if (isErrorFile) {
                  handleDelErroFile(index);
                } else if (attachedfile) {
                  handleDeleteDoc(attachedfile?.name, index);
                }
              }}
              inputWidth={inputWidth}
            />
          ))}
        </div>
      )}*/}
    </div>
  );
};

export default UserInput;
