import { Message, ToolCall, CodeExecution } from "@/app/types/chat";
import MessageContainer from "./MessageContainer";
import DomainAvatar, { getDomainBgColor } from "./DomainAvatar";
import MarkdownContent from "./MarkdownContent";
import ToolCallSection from "./ToolCallSection";
import ToolStepTracer, {
  StepItem,
  ToolStepTracerStep,
} from "@/components/ToolStepTracer/ToolStepTracer";
import { Loader2 } from "lucide-react";
import { useEffect } from "react";

// Utility function to convert StepItem[] to ToolStepTracerStep[]
const convertStepsToToolStepTracerSteps = (
  steps: StepItem[],
): ToolStepTracerStep[] => {
  return steps.map((step) => {
    const normalizedNumber =
      typeof step.number === "string"
        ? parseInt(step.number, 10) || 0
        : step.number;

    // Ensure content has the correct shape
    let normalizedContent: ToolStepTracerStep["content"] = {
      type: "text",
      value: "",
    };

    if (step.content) {
      if (typeof step.content === "object" && "type" in step.content) {
        // Handle different content types
        const contentType = step.content.type;

        if (contentType === "dots" && "props" in step.content) {
          normalizedContent = {
            type: contentType,
            props: step.content.props,
          };
        } else if (contentType === "items" && "props" in step.content) {
          normalizedContent = {
            type: contentType,
            props: step.content.props,
          };
        } else if (
          contentType === "relevance" &&
          "assessments" in step.content
        ) {
          normalizedContent = {
            type: contentType,
            assessments: step.content.assessments,
          };
        } else if (contentType === "text" && "value" in step.content) {
          normalizedContent = {
            type: contentType,
            value: step.content.value as string,
          };
        }
      } else if (typeof step.content === "string") {
        normalizedContent = {
          type: "text",
          value: step.content,
        };
      }
    }

    return {
      number: normalizedNumber,
      title: step.title,
      description: step.description,
      content: normalizedContent,
    };
  });
};

// User Message Component
export function UserMessage({ message }: { message: Message }) {
  return (
    <MessageContainer message={message}>
      <div className="group rounded-lg bg-slate-100 p-4">
        <MarkdownContent
          content={message.content}
          keywords={message.keywords}
        />
      </div>
    </MessageContainer>
  );
}

// AI Message Component
export function AIMessage({
  message,
  toolCalls,
  onToggleCollapse,
}: {
  message: Message;
  toolCalls?: ToolCall[];
  onToggleCollapse?: (messageId: string) => void;
}) {
  const isCollapsed = message.metadata?.isCollapsed ?? false;
  const domainBg = getDomainBgColor(message.domain);

  const bubbleClassName =
    message.domain === "reasoning"
      ? "group rounded-lg p-4 bg-slate-100"
      : `group rounded-lg p-4 ${domainBg} bg-opacity-20`;

  const avatarBgClassName =
    message.domain === "reasoning"
      ? `mt-1 flex h-8 w-8 items-center justify-center rounded-full bg-slate-100`
      : `mt-1 flex h-8 w-8 items-center justify-center rounded-full ${domainBg} bg-opacity-20`;

  return (
    <MessageContainer message={message}>
      <div className="flex-shrink-0">
        <div className={avatarBgClassName}>
          <DomainAvatar domain={message.domain} />
        </div>
      </div>
      <div className="flex-1 space-y-2">
        <div className={bubbleClassName}>
          {message.id.includes("typing-indicator") ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
            </>
          ) : (
            message.name && (
              <div className="mb-2 flex items-center justify-between">
                <div className="text-xs font-semibold opacity-80">
                  {message.name
                    .replace(/_/g, " ")
                    .replace(/\b\w/g, (c: string) => c.toUpperCase())}
                  {!(
                    message.domain === "reasoning" ||
                    message.domain === "planner" ||
                    message.domain === "Athlea"
                  ) && " Coach"}
                </div>
                {onToggleCollapse &&
                  (message.domain === "reasoning" ||
                    message.domain === "planner") && (
                    <button
                      onClick={() => onToggleCollapse(message.id)}
                      className="ml-4 text-xs text-blue-600 hover:underline focus:outline-none"
                    >
                      {isCollapsed ? "Show Details" : "Hide Details"}
                    </button>
                  )}
              </div>
            )
          )}
          {(!isCollapsed || !onToggleCollapse) && (
            <MarkdownContent
              content={message.content}
              keywords={message.keywords}
            />
          )}
          {message.inlineToolTrace &&
            message.inlineToolTrace.steps &&
            message.inlineToolTrace.steps.length > 0 && (
              <div className="mt-3">
                <ToolStepTracer
                  toolName={message.inlineToolTrace.toolName}
                  steps={convertStepsToToolStepTracerSteps(
                    message.inlineToolTrace.steps,
                  )}
                  summary={message.inlineToolTrace.summary}
                  triggerLabel={
                    message.inlineToolTrace.triggerLabel || "View details"
                  }
                  expanded={true}
                />
              </div>
            )}
        </div>
        {toolCalls && toolCalls.length > 0 && !message.inlineToolTrace && (
          <ToolCallSection toolCalls={toolCalls} domain={message.domain} />
        )}
      </div>
    </MessageContainer>
  );
}

// System Message Component
export function SystemMessage({ message }: { message: Message }) {
  return (
    <MessageContainer message={message}>
      <div className="bg-gray-100 text-gray-600 w-full rounded-lg p-3 text-sm">
        <MarkdownContent content={message.content} />
      </div>
    </MessageContainer>
  );
}

// Tool Message Component
export function ToolMessage({
  message,
  codeExecutions,
}: {
  message: Message;
  codeExecutions?: CodeExecution[];
}) {
  const domainBg = getDomainBgColor(message.domain);
  const avatarBgClassName = `mt-1 flex h-8 w-8 items-center justify-center rounded-full ${domainBg} bg-opacity-20`;

  return (
    <MessageContainer message={message}>
      <div className="flex-shrink-0">
        <div className={avatarBgClassName}>
          <DomainAvatar domain={message.domain} />
        </div>
      </div>
      <div className="flex-1 space-y-2">
        <div className={`group rounded-lg p-4 ${domainBg} bg-opacity-20`}>
          {message.name && (
            <div className="mb-2 text-xs font-semibold opacity-80">
              {message.name
                .replace(/_/g, " ")
                .replace(/\b\w/g, (c: string) => c.toUpperCase())}
            </div>
          )}
          <MarkdownContent content={message.content} />
        </div>
        {codeExecutions && codeExecutions.length > 0 && (
          <div className="mt-2 space-y-3">
            {codeExecutions.map((execution) => (
              <div
                key={execution.id}
                className={`border-gray-200 overflow-hidden rounded-md border ${domainBg} bg-opacity-20 p-3`}
              >
                <div className="text-gray-800 px-3 py-2 text-xs font-medium">
                  Code Execution
                </div>
                <div className="p-3">
                  <pre className="bg-gray-800 text-gray-200 overflow-x-auto rounded p-2 text-xs">
                    {execution.code}
                  </pre>
                  <div className="mt-2 text-xs">
                    <div className="text-gray-700 mb-1 font-medium">
                      Result:
                    </div>
                    <pre className="bg-gray-100 text-gray-700 rounded p-2">
                      {execution.result}
                    </pre>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </MessageContainer>
  );
}
