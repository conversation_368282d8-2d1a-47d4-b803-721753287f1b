// StatsDashboard.tsx
import React, { useState, useMemo, useEffect, useRef } from "react";
import { Line<PERSON>hart, Loader, SlidersHorizontal, X } from "lucide-react";
import MetricVisualizer, { MetricPanel } from "./MetricVisualizer";
import {
  APIResponse,
  AnalysisQuestion,
  DashboardMetricGroup,
  Dataset,
  MetricDataPoint,
  MetricGroup,
  TimelineAnalysis,
  isDashboardMetricGroup,
} from "@/types/stats";
import StatsRow from "./StatsRow";
import { CombinedCharts } from "./CombinedStatsPanel";
import { useAppDispatch, useAppSelector } from "@/store/jobHook";
import { StatsTimeline, TimelineRange } from "./StatsTimeline";
import toast from "react-hot-toast";
import { TimelineSkeleton } from "../Loaders/TimelineSkeleton";
import { StatsRowSkeleton } from "../Loaders/StatsRowSkeleton";
import { CombinedChartsSkeleton } from "../Loaders/CombinedChartsSkeleton";
import { AIQuestionModal } from "../Modals/AIQuestionModal";
import QualitativeAnalysisDisplay, {
  MultiQualitativeDisplay,
} from "./QualitativePanel";
import { useDropzone } from "react-dropzone";
import {
  processMetricData,
  processMetricGroups,
  processTrainingStats,
} from "@/utils/statsUtils";
import { useStats } from "@/context/StatsContext";
import {
  selectDailyStats,
  selectWeeklyStats,
  setDailyCyclingStats,
  setDailyNutritionStats,
  setDailyRecoveryStats,
  setDailyRunningStats,
  setDailyStrengthStats,
} from "@/store/slices/statsSlice";
import TrainingMetricVisualizer from "./TrainingMetricVisualizer";

const colors = [
  "#8884d8",
  "#82ca9d",
  "#ffc658",
  "#ff8042",
  "#af19ff",
  "#ff195e",
  "#19ff4f",
  "#ff8819",
  "#1984ff",
  "#ff19d4",
] as const;

type CategoryType =
  | "Heart Rate"
  | "Activity"
  | "Sleep"
  | "Recovery"
  | "Training"
  | "Other";
type GroupedPanels = Record<CategoryType, MetricPanel[]>;

const StatsDashboard: React.FC = () => {
  const {
    setShowStats,
    setAvailableRanges,
    setSelectedRange,
    setData,
    setMetricPanels,
    setMetrics,
    availableRanges,
    selectedRange,
    data,
    metricPanels,
    metrics,
  } = useStats();

  // Add training stats from Redux
  const dailyTrainingStats = useAppSelector(selectDailyStats);
  const weeklyTrainingStats = useAppSelector(selectWeeklyStats);
  const dispatch = useAppDispatch();

  const [uploading, setUploading] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [message, setMessage] = useState<string>("");
  const [trendData, setTrendData] = useState<any[]>([]);
  const [selectedMetrics, setSelectedMetrics] = useState<Set<string>>(
    new Set(),
  );
  const [currentQuestion, setCurrentQuestion] =
    useState<AnalysisQuestion | null>(null);
  const [pendingAnalysis, setPendingAnalysis] = useState<APIResponse | null>(
    null,
  );
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [filteredmetric, setFilteredmetric] = useState<any>(null);
  const [newfilteredPanels, setNewfilteredPanels] = useState<any>(null);

  // Add training stats state
  const [trainingStatsByDate, setTrainingStatsByDate] = useState<
    Record<string, any>
  >({});
  console.log("trainingStatsByDate", trainingStatsByDate);

  console.log(
    "availableRanges",
    availableRanges,
    trendData,
    selectedRange,
    data,
  );

  const fileInputRef = useRef<HTMLInputElement>(null);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      "application/json": [],
      "text/csv": [],
      "application/vnd.ms-excel": [],
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [],
      "image/jpeg": [],
      "image/png": [],
      "image/webp": [],
      "text/plain": [],
    },
    maxFiles: 1,
    maxSize: 5 * 1024 * 1024,
    minSize: 0,
    onDrop: (files, fileRejection, event) => {
      console.log("uploaded stat files", files, event);
      handleUpload(files[0]);
    },
    noClick: true, // Prevent opening file dialog on click
    noDragEventsBubbling: true, // Prevent drag events from bubbling up
  });

  const handleQuestionResponse = async (answer: string) => {
    if (!currentQuestion || !pendingAnalysis) return;

    try {
      const analysisResponse = await fetch("/api/analyze/refine", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          question: currentQuestion,
          answer,
          data: pendingAnalysis.rawData,
          previousAnalysis: pendingAnalysis.analysis,
        }),
      });

      if (!analysisResponse.ok) throw new Error("Failed to refine analysis");

      const result = await analysisResponse.json();

      if (result.success) {
        const finalAnalysis = {
          ...pendingAnalysis,
          analysis: {
            ...pendingAnalysis.analysis,
            insights: {
              ...pendingAnalysis.analysis.insights,
              ...result.refinedInsights,
            },
          },
        };

        const saveResponse = await fetch(`/api/stats?user_id=${user_id}`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            analysis: finalAnalysis.analysis,
            metadata: finalAnalysis.metadata,
            rawData: finalAnalysis.rawData,
            dateRange: finalAnalysis.dateRange,
          }),
        });

        if (saveResponse.ok) {
          // Create a new TimelineRange object for the new data
          const newRange: TimelineRange = {
            startDate: new Date(finalAnalysis.dateRange.startDate),
            endDate: new Date(finalAnalysis.dateRange.endDate),
            rawData: finalAnalysis.rawData,
            analysis: {
              metricGroups: finalAnalysis.analysis.metricGroups,
            },
          };

          // Update available ranges and select the new range
          setAvailableRanges((prev) => [...prev, newRange]);
          setSelectedRange(newRange);

          // Update the dashboard data
          setData(finalAnalysis.rawData);
          const processedPanels = processMetricData(
            finalAnalysis.rawData,
            finalAnalysis,
          );
          const processedMetrics = processMetricGroups(finalAnalysis);
          setMetricPanels(processedPanels);
          setMetrics(processedMetrics);

          toast.success("Data analyzed and saved successfully!");
        } else {
          throw new Error("Failed to save analysis");
        }
      }
    } catch (error) {
      console.error("Error processing analysis:", error);
      toast.error("Failed to process analysis");
    } finally {
      setCurrentQuestion(null);
      setPendingAnalysis(null);
    }
  };

  const user_id = useAppSelector((state) => state.user.userData?.user_id);

  const formatDate = (dateValue: string | number): string => {
    if (!dateValue) return "";
    try {
      if (typeof dateValue === "number") {
        const date = new Date(Math.round((dateValue - 25569) * 86400 * 1000));
        return date.toLocaleDateString("en-GB", {
          day: "2-digit",
          month: "2-digit",
        });
      }
      const date = new Date(dateValue);
      return date.toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
      });
    } catch {
      return String(dateValue);
    }
  };

  useEffect(() => {
    const fetchAvailableRanges = async () => {
      setLoading(true);
      try {
        const response = await fetch(`/api/stats?user_id=${user_id}`);
        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || "Failed to fetch data");
        }

        if (result.success && result.stats) {
          const ranges = result.stats.map((stat: any) => ({
            startDate: new Date(stat.startDate),
            endDate: new Date(stat.endDate),
            rawData: stat.rawData,
            analysis: stat.analysis,
            metadata: stat.metadata, // Add this line to include metadata
          }));
          setAvailableRanges(ranges);

          if (ranges.length > 0 && !selectedRange) {
            const mostRecent = ranges.reduce(
              (latest: TimelineRange, current: TimelineRange) =>
                latest.startDate > current.startDate ? latest : current,
            );
            setTimeout(() => {
              handleRangeSelect(mostRecent);
            }, 200);
          }
        }
      } catch (error) {
        console.error("Error fetching available ranges:", error);
        toast.error("Failed to fetch data ranges");
      } finally {
        setLoading(false);
      }
    };

    if (user_id) {
      fetchAvailableRanges();
    }
  }, [user_id]);

  const availableMetrics = useMemo(() => {
    const metrics = new Set<string>();
    metricPanels.forEach((panel) => {
      metrics.add(panel.title.replace(/\s/g, "_"));
    });
    return metrics;
  }, [metricPanels]);

  const isQualitativeData = (range: TimelineRange | null): boolean => {
    if (!range) return false;

    // Check if analysisType exists in the analysis object
    if (range.analysis?.analysisType) {
      return range.analysis.analysisType === "single";
    }

    // Check if analysisType exists in the raw data
    if (range.rawData?.[0]?.Workout_Analysis?.analysisType) {
      return range.rawData[0].Workout_Analysis.analysisType === "single";
    }

    // Check if it's a qualitative data based on structure (fallback for backward compatibility)
    return (
      "Image Analysis" in (range.analysis?.metricGroups || {}) ||
      range.rawData?.[0]?.Workout_Analysis != null
    );
  };

  useEffect(() => {
    if (
      availableRanges.length > 0 &&
      selectedRange &&
      typeof selectedRange.rawData[0].Date === "string"
    ) {
      const foundStatsOfDateRange = [...availableRanges]
        .filter(
          (item: any) => item.rawData[0].Date === selectedRange.rawData[0].Date,
        )
        ?.map((item: any) => ({ ...item.rawData[0], ...item.metadata }));
      console.log(
        "foundStatsOfDateRange",
        foundStatsOfDateRange,
        availableRanges,
        selectedRange.rawData,
      );
      setData(foundStatsOfDateRange);
    }
  }, [availableRanges, selectedRange]);

  // Update useEffect to fetch training stats and create timeline ranges
  useEffect(() => {
    const fetchTrainingStats = async () => {
      if (!user_id) return;

      try {
        const response = await fetch(`/api/training-stats?userId=${user_id}`);
        if (!response.ok) throw new Error("Failed to fetch training stats");

        const data = await response.json();
        if (data.success && data.stats) {
          console.log("📊 Fetched training stats:", data.stats);
          setTrainingStatsByDate(data.stats);

          // Convert training stats to timeline ranges
          const newRanges = Object.entries(data.stats)
            .map(([date, stats]: [string, any]) => {
              if (date === "undefined") return null;

              return {
                startDate: new Date(date),
                endDate: new Date(date),
                rawData: [
                  {
                    Date: date,
                    ...stats,
                  },
                ],
                analysis: {
                  metricGroups: {
                    Training: {
                      metrics: Object.entries(stats).map(
                        ([type, typeStats]: [string, any]) => ({
                          name: `${type}_training`,
                          unit: "%",
                          description: `${type.charAt(0).toUpperCase() + type.slice(1)} training metrics`,
                        }),
                      ),
                      description: "Training statistics and metrics",
                      category: "Training",
                    },
                  },
                },
              } as TimelineRange;
            })
            .filter((range): range is TimelineRange => range !== null);

          // Update available ranges
          setAvailableRanges((prevRanges) => {
            const existingDates = new Set(
              prevRanges.map((r) => r.startDate.toISOString().split("T")[0]),
            );
            const uniqueNewRanges = newRanges.filter(
              (r) =>
                !existingDates.has(r.startDate.toISOString().split("T")[0]),
            );
            return [...prevRanges, ...uniqueNewRanges];
          });

          // If no range is selected, select the most recent one
          if (!selectedRange && newRanges.length > 0) {
            const mostRecent = newRanges.reduce((latest, current) =>
              latest.startDate > current.startDate ? latest : current,
            );
            setSelectedRange(mostRecent);
          }
        }
      } catch (error) {
        console.error("❌ Error fetching training stats:", error);
        toast.error("Failed to fetch training statistics");
      }
    };

    fetchTrainingStats();
  }, [user_id, selectedRange]);

  useEffect(() => {
    if (selectedRange) {
      handleRangeSelect(selectedRange);
    }
  }, [trainingStatsByDate, selectedRange]);

  // Update handleRangeSelect to include training stats
  const handleRangeSelect = (range: TimelineRange) => {
    setSelectedRange(range);
    console.log(
      "Selected range:",
      range,
      trainingStatsByDate,
      trainingStatsByDate["2025-01-27"],
    );

    if (typeof range.rawData[0].Date === "string") {
      const date = range.rawData[0].Date;
      const trainingStatsForDate = trainingStatsByDate[date] || {};
      console.log(
        "🏋️ Training stats for date:",
        trainingStatsForDate,
        Object.keys(trainingStatsForDate),
      );

      if (Object.keys(trainingStatsForDate).includes("strength")) {
        dispatch(setDailyStrengthStats(trainingStatsForDate["strength"]));
      }
      if (Object.keys(trainingStatsForDate).includes("cycling")) {
        dispatch(setDailyCyclingStats(trainingStatsForDate["cycling"]));
      }
      if (Object.keys(trainingStatsForDate).includes("running")) {
        dispatch(setDailyRunningStats(trainingStatsForDate["running"]));
      }
      if (Object.keys(trainingStatsForDate).includes("nutrition")) {
        dispatch(setDailyNutritionStats(trainingStatsForDate["nutrition"]));
      }
      if (Object.keys(trainingStatsForDate).includes("recovery")) {
        dispatch(setDailyRecoveryStats(trainingStatsForDate["recovery"]));
      }
      // Create a new data object that includes both existing stats and training stats
      const combinedData = {
        ...range.rawData[0],
        ...range.metadata,
        trainingStats: trainingStatsForDate,
        Date: date,
      };

      setData([combinedData]);

      // Process regular stats
      const regularStatsResponse: APIResponse = {
        success: true,
        analysis: {
          dateRange: {
            startDate: range.startDate.toISOString().split("T")[0],
            endDate: range.endDate.toISOString().split("T")[0],
            duration: "auto",
          },
          metricGroups: range.analysis?.metricGroups || {},
          trends: range.analysis?.trends ?? [],
          insights: range.analysis?.insights ?? {
            performance: [],
            patterns: [],
            recommendations: [],
          },
          questions: range.analysis?.questions,
        },
        metadata: {
          fileName: "stats",
          rowCount: 1,
          columnCount: Object.keys(combinedData).length,
          processedChunks: 1,
          image_url: "",
          fileSize: 0,
          fileType: "",
        },
        rawData: [combinedData],
        dateRange: {
          startDate: range.startDate,
          endDate: range.endDate,
          formattedRange: `${range.startDate.toISOString().split("T")[0]} to ${range.endDate.toISOString().split("T")[0]}`,
        },
      };

      // Process both regular stats and training stats
      const regularPanels = processMetricData(
        [combinedData],
        regularStatsResponse,
      );
      const trainingPanels = processTrainingStats(trainingStatsForDate, date);
      console.log(
        "both regularPanels and trainingPanels",
        regularPanels,
        trainingPanels,
        trainingStatsForDate,
        date,
      );

      // Combine both types of panels
      setMetricPanels([...regularPanels, ...trainingPanels]);
      setMetrics(processMetricGroups(regularStatsResponse));
    } else {
      setTrendData(range.rawData);
    }
  };

  // Update groupedPanels to include training stats from the selected date
  const groupedPanels = useMemo<GroupedPanels>(() => {
    const groups: GroupedPanels = {
      "Heart Rate": [],
      Activity: [],
      Sleep: [],
      Recovery: [],
      Training: [],
      Other: [],
    };

    // Process existing metric panels
    metricPanels.forEach((panel) => {
      const title = panel.title.toLowerCase();

      // Check if it's a training panel
      if (panel.customRender) {
        groups["Training"].push(panel);
      }
      // Process regular stats panels
      else if (title.includes("heart") || title.includes("hrv")) {
        groups["Heart Rate"].push(panel);
      } else if (title.includes("sleep")) {
        groups["Sleep"].push(panel);
      } else if (
        title.includes("steps") ||
        title.includes("calories") ||
        title.includes("activity") ||
        title.includes("distance")
      ) {
        groups["Activity"].push(panel);
      } else if (
        title.includes("recovery") ||
        title.includes("stress") ||
        title.includes("vo2") ||
        title.includes("battery")
      ) {
        groups["Recovery"].push(panel);
      } else {
        groups["Other"].push(panel);
      }
    });

    return Object.fromEntries(
      Object.entries(groups).filter(([_, panels]) => panels.length > 0),
    ) as GroupedPanels;
  }, [metricPanels, selectedRange, data]);

  const filteredGroupedPanels = useMemo<GroupedPanels>(() => {
    if (selectedMetrics.size === 0) return groupedPanels;

    const filteredPanels: GroupedPanels = {
      "Heart Rate": [],
      Activity: [],
      Sleep: [],
      Recovery: [],
      Training: [],
      Other: [],
    };

    Object.entries(groupedPanels).forEach(([category, panels]) => {
      const filtered = panels.filter((panel) =>
        selectedMetrics.has(panel.title.replace(/\s/g, "_")),
      );
      if (filtered.length > 0) {
        filteredPanels[category as CategoryType] = filtered;
      }
    });

    return Object.fromEntries(
      Object.entries(filteredPanels).filter(([_, panels]) => panels.length > 0),
    ) as GroupedPanels;
  }, [groupedPanels, selectedMetrics]);
  // event: React.ChangeEvent<HTMLInputElement>
  const handleUpload = async (file: File) => {
    // const file = file
    // event.target.files?.[0];
    console.log("file__Upload", file);
    if (!file) return;

    const formData = new FormData();
    formData.append("file", file);

    setUploading(true);
    // setData([]);
    setMetrics([]);
    setMetricPanels([]);

    const loadingToast = toast.loading("Analyzing your data...");

    try {
      const response = await fetch("/api/analyze", {
        method: "POST",
        body: formData,
      });

      const result = (await response.json()) as APIResponse | { error: string };

      if (response.ok && "success" in result) {
        if (result.analysis.questions?.[0]) {
          setPendingAnalysis(result);
          setCurrentQuestion(result.analysis.questions[0]);
          toast.dismiss(loadingToast);
        } else {
          // Process and save the analysis
          const saveResponse = await fetch(`/api/stats?user_id=${user_id}`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              analysis: result.analysis,
              metadata: result.metadata,
              rawData: result.rawData,
              dateRange: result.dateRange,
            }),
          });

          if (saveResponse.ok) {
            // Create a new TimelineRange object for the new data
            const newRange: TimelineRange = {
              startDate: new Date(result.dateRange.startDate),
              endDate: new Date(result.dateRange.endDate),
              rawData: result.rawData,
              analysis: {
                metricGroups: result.analysis.metricGroups,
              },
              metadata: {
                fileName: result.metadata.fileName,
                fileType: result.metadata?.fileType,
                fileSize: result.metadata?.fileSize,
                image_url: result.metadata?.image_url,
              },
            };

            const foundRange = availableRanges.find(
              (item: TimelineRange) =>
                item.metadata?.fileName === result.metadata.fileName,
            );
            console.log("foundRange", foundRange);

            if (!foundRange) {
              setAvailableRanges((prev) => [...prev, newRange]);
            }
            // Update available ranges and select the new range
            setSelectedRange(newRange);

            // Update the dashboard data
            const foundStats = data.find(
              (item: MetricDataPoint) =>
                item.fileName === result.metadata.fileName,
            );

            if (!foundStats) {
              setData([
                ...data,
                ...[{ ...result.rawData[0], ...result.metadata }],
              ]);
            }

            const processedPanels = processMetricData(result.rawData, result);
            const processedMetrics = processMetricGroups(result);
            setMetricPanels(processedPanels);
            setMetrics(processedMetrics);

            toast.dismiss(loadingToast);
            toast.success("Data analyzed and saved successfully!");
          } else {
            throw new Error("Failed to save analysis");
          }
        }
      } else {
        toast.dismiss(loadingToast);
        const errorMessage =
          "error" in result && result.error ? result.error : "Upload failed";
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.dismiss(loadingToast);
      const errorMessage =
        error instanceof Error ? error.message : "Upload failed";
      toast.error(errorMessage);
    } finally {
      setUploading(false);
    }
  };

  const renderContent = () => {
    if (loading) {
      return (
        <div className="w-full space-y-6">
          <div className="space-y-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="rounded-lg bg-white p-4 shadow">
                <StatsRowSkeleton />
              </div>
            ))}
          </div>
          <div className="rounded-lg bg-white p-4 shadow">
            <CombinedChartsSkeleton />
          </div>
        </div>
      );
    }

    if (data.length > 0 || trendData.length > 0) {
      const analysisItems =
        data
          // selectedRange?.rawData
          .map((item: any) => {
            console.log("Raw Data Item:", item); // Log each item being mapped

            const mappedItem = {
              analysis: {
                dateRange: {
                  startDate:
                    item.Workout_Analysis?.dateRange?.startDate ||
                    selectedRange?.analysis.dateRange?.startDate ||
                    new Date().toISOString().split("T")[0],
                  endDate:
                    item.Workout_Analysis?.dateRange?.endDate ||
                    selectedRange?.analysis.dateRange?.endDate ||
                    new Date().toISOString().split("T")[0],
                  duration:
                    item.Workout_Analysis?.dateRange?.duration ||
                    selectedRange?.analysis.dateRange?.duration ||
                    "N/A",
                },
                trends:
                  item.Workout_Analysis?.trends ||
                  selectedRange?.analysis.trends ||
                  [],
                insights: {
                  performance:
                    item.Workout_Analysis?.insights?.performance ||
                    selectedRange?.analysis.insights?.performance ||
                    [],
                  patterns:
                    item.Workout_Analysis?.insights?.patterns ||
                    selectedRange?.analysis.insights?.patterns ||
                    [],
                  recommendations:
                    item.Workout_Analysis?.insights?.recommendations ||
                    selectedRange?.analysis.insights?.recommendations ||
                    [],
                },
              },
              metadata: {
                fileName: item?.fileName || "Unnamed File",
                fileType: item?.fileType || "Unknown Type",
                fileSize: item?.fileSize || 0,
                image_url: item?.image_url,
              },
              // selectedRange?.metadata || {
              //   fileName: selectedRange?.metadata?.fileName || "Unnamed File",
              //   fileType: selectedRange?.metadata?.fileType || "Unknown Type",
              //   fileSize: selectedRange?.metadata?.fileSize || 0,
              // },
            };

            console.log("Mapped Item:", mappedItem); // Log the final mapped item
            return mappedItem;
          }) || [];

      console.log(
        "Final Analysis Items:",
        analysisItems,
        filteredGroupedPanels,
      );

      if (
        isQualitativeData(selectedRange) &&
        typeof selectedRange?.rawData[0].Date === "string"
      ) {
        console.log("Selected Range:", selectedRange); // Log the full selected range object
        console.log("Selected Range Metadata:", selectedRange?.metadata); // Log just the metadata
        const multipleRanges = data.filter(
          (item: any) => item.Date === selectedRange?.rawData[0]?.Date,
        );
        console.log("multipleRanges", multipleRanges, selectedRange?.rawData);

        // Log the final array
        const analysisItems =
          multipleRanges
            // selectedRange?.rawData
            .map((item: any) => {
              console.log("Raw Data Item:", item); // Log each item being mapped

              const mappedItem = {
                analysis: {
                  dateRange: {
                    startDate:
                      item.Workout_Analysis?.dateRange?.startDate ||
                      selectedRange?.analysis.dateRange?.startDate ||
                      new Date().toISOString().split("T")[0],
                    endDate:
                      item.Workout_Analysis?.dateRange?.endDate ||
                      selectedRange?.analysis.dateRange?.endDate ||
                      new Date().toISOString().split("T")[0],
                    duration:
                      item.Workout_Analysis?.dateRange?.duration ||
                      selectedRange?.analysis.dateRange?.duration ||
                      "N/A",
                  },
                  trends:
                    item.Workout_Analysis?.trends ||
                    selectedRange?.analysis.trends ||
                    [],
                  insights: {
                    performance:
                      item.Workout_Analysis?.insights?.performance ||
                      selectedRange?.analysis.insights?.performance ||
                      [],
                    patterns:
                      item.Workout_Analysis?.insights?.patterns ||
                      selectedRange?.analysis.insights?.patterns ||
                      [],
                    recommendations:
                      item.Workout_Analysis?.insights?.recommendations ||
                      selectedRange?.analysis.insights?.recommendations ||
                      [],
                  },
                },
                metadata: {
                  fileName: item?.fileName || "Unnamed File",
                  fileType: item?.fileType || "Unknown Type",
                  fileSize: item?.fileSize || 0,
                  image_url: item?.image_url,
                },
                // selectedRange?.metadata || {
                //   fileName: selectedRange?.metadata?.fileName || "Unnamed File",
                //   fileType: selectedRange?.metadata?.fileType || "Unknown Type",
                //   fileSize: selectedRange?.metadata?.fileSize || 0,
                // },
              };

              console.log("Mapped Item:", mappedItem); // Log the final mapped item
              return mappedItem;
            }) || [];

        console.log("Final Analysis Items:", analysisItems);
        // return (
        //   <div className="w-full space-y-6">
        //     <div className="rounded-lg bg-white p-4">
        //       <MultiQualitativeDisplay items={analysisItems} />
        //     </div>
        //   </div>
        // );
      }
      return (
        <div className="w-full space-y-6">
          <div className="space-y-6">
            {(
              Object.entries(
                newfilteredPanels ? newfilteredPanels : filteredGroupedPanels,
              ) as [CategoryType, MetricPanel[]][]
            ).map(([category, panels]) => (
              <div
                key={category}
                className="overflow-x-auto rounded-lg bg-white p-4"
              >
                <StatsRow
                  category={category}
                  panels={panels}
                  renderPanel={renderMetricPanel}
                />
              </div>
            ))}
          </div>
          {isQualitativeData(selectedRange) && (
            <div className="rounded-lg bg-white p-4">
              <MultiQualitativeDisplay items={analysisItems} />
            </div>
          )}
          {trendData.length > 0 && (
            <div className="overflow-x-auto rounded-lg bg-white p-4 shadow">
              <div className="mb-4 flex items-center gap-2">
                <LineChart className="text-gray-500 h-6 w-6" />
                <h2 className="text-xl font-medium">Multi-line Graphs</h2>
              </div>
              <div className="mx-auto max-w-4xl">
                {" "}
                {/* Add this wrapper */}
                <CombinedCharts
                  metrics={
                    selectedMetrics.size > 0
                      ? metrics
                        .filter(isDashboardMetricGroup)
                        .filter((m) =>
                          m.datasets.some((d) =>
                            selectedMetrics.has(d.key.replace(/\s/g, "_")),
                          ),
                        )
                      : metrics.filter(isDashboardMetricGroup)
                  }
                  data={trendData}
                  formatDate={formatDate}
                />
              </div>
            </div>
          )}
        </div>
      );
    }

    if (selectedRange) {
      return (
        <div className="text-gray-500 rounded-lg bg-white p-8 text-center shadow">
          No data available for selected date range
        </div>
      );
    }

    if (availableRanges.length > 0) {
      return (
        <div className="text-gray-500 rounded-lg bg-white p-8 text-center">
          Select a date range to view statistics
        </div>
      );
    }

    return null;
  };

  const handleFiltermetric = (metric: CategoryType) => {
    setIsLoading(true);
    try {
      setFilteredmetric(metric);
      const newfilteredGroupedPanels: Partial<GroupedPanels> = {
        [metric]: filteredGroupedPanels[metric],
      };
      console.log("newfilteredGroupedPanels", newfilteredGroupedPanels);
      setNewfilteredPanels(newfilteredGroupedPanels);
      setIsDropdownOpen(false);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const metricFilter = () => {
    return (
      <div className="relative mx-2" ref={dropdownRef}>
        <button
          className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-500 p-2 text-white transition-colors hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          disabled={isLoading}
        >
          {isLoading ? (
            <Loader size={16} className="animate-spin" />
          ) : (
            <SlidersHorizontal size={16} />
          )}
        </button>
        {isDropdownOpen && !isLoading && (
          <div
            className="scrollbar-hide absolute right-0 mt-2 w-48 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5"
            style={{
              zIndex: 1000,
              top: "100%",
              maxHeight: "200px",
              overflowY: "auto",
            }}
          >
            <div
              className="py-1"
              role="menu"
              aria-orientation="vertical"
              aria-labelledby="options-menu"
            >
              {(Object.keys(filteredGroupedPanels) as CategoryType[]).map(
                (metric, idx) => (
                  <div
                    key={idx}
                    className="flex items-center justify-between"
                    style={{
                      backgroundColor:
                        metric === filteredmetric ? "#eeeeee" : undefined,
                    }}
                  >
                    <button
                      className="text-gray-700 hover:bg-gray-700 hover:text-gray-900 block flex w-full items-center justify-between px-4 py-2 text-left text-sm"
                      role="menuitem"
                      onClick={() => handleFiltermetric(metric)}
                    >
                      {metric}
                    </button>
                    {metric === filteredmetric && (
                      <div
                        className="mr-2 flex h-6 w-6 cursor-pointer items-center justify-center rounded-full border"
                        onClick={() => {
                          setFilteredmetric(null);
                          setNewfilteredPanels(null);
                          setIsDropdownOpen(false);
                        }}
                      >
                        <X className="h-4 w-4" />
                      </div>
                    )}
                  </div>
                ),
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Update MetricVisualizer to handle custom components
  const renderMetricPanel = (panel: MetricPanel) => {
    if (panel.customRender && panel.component) {
      return panel.component;
    }
    return <MetricVisualizer metric={panel} />;
  };

  return (
    <div className="flex min-h-screen w-full p-2">
      <div className="min-w-0 flex-1 space-y-6 p-2">
        {/* Header and Timeline Container */}
        <div className="rounded-lg bg-white">
          {/* Header */}
          <div className="mb-6 flex items-center justify-between">
            <h1 className="text-2xl font-bold">Statistics</h1>
            <div className="flex items-center">
              <label className="relative inline-block">
                <input
                  type="file"
                  className="hidden"
                  onChange={(e) => {
                    if (e.target.files && e.target.files[0]) {
                      handleUpload(e.target.files[0]);
                    }
                  }}
                  accept=".csv,.xlsx,.json,.jpg,.jpeg,.png,.webp,.txt"
                  disabled={uploading}
                />
                <span
                  className={`
                    inline-flex items-center gap-2 whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium
                    ${uploading ? "bg-slate-300" : "bg-blue-600 hover:bg-blue-700"} 
                    w-full cursor-pointer text-white transition-colors sm:w-auto
                  `}
                >
                  {uploading ? (
                    <>
                      <Loader className="h-4 w-4 animate-spin" />
                      <span className="whitespace-nowrap">Uploading...</span>
                    </>
                  ) : (
                    <span className="whitespace-nowrap">Upload Stats</span>
                  )}
                </span>
              </label>
              {metricFilter()}
            </div>
          </div>

          {/* Timeline */}
          {loading ? (
            <TimelineSkeleton />
          ) : (
            <StatsTimeline
              ranges={availableRanges}
              selectedRange={selectedRange}
              onRangeSelect={handleRangeSelect}
            />
          )}
        </div>

        {/* Render Content */}
        {renderContent()}
      </div>

      {/* AI Question Modal */}
      <AIQuestionModal
        open={currentQuestion !== null}
        question={currentQuestion}
        onClose={() => {
          setCurrentQuestion(null);
          setPendingAnalysis(null);
          toast.error("Analysis cancelled - question required");
        }}
        onSubmit={handleQuestionResponse}
      />
    </div>
  );
};

export default StatsDashboard;
