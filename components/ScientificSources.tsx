import React from "react";
import { Book } from "lucide-react";

export interface ScientificSource {
  title: string;
  url?: string;
  description: string;
  tags?: string[];
}

interface ScientificSourcesProps {
  sources: ScientificSource[];
  className?: string;
}

const ScientificSources: React.FC<ScientificSourcesProps> = ({ sources, className }) => {
  return (
    <div className={className ? className + " px-4 pb-4" : "px-4 pb-4"}>
      <h3 className="mb-1 flex items-center gap-2 text-base font-semibold text-[#222B45]">
        <Book size={18} className="text-[#6B7280]" />
        Scientific Sources
      </h3>
      <div className="flex flex-col gap-2">
        {sources.map((source, i) => (
          <div
            key={i}
            className="flex flex-col gap-1 rounded-lg border border-[#E6E8EC] bg-white p-3"
          >
            {source.url ? (
              <a
                href={source.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-xs font-semibold text-purple-600 hover:underline"
              >
                {source.title}
              </a>
            ) : (
              <span className="text-xs font-semibold text-purple-600">
                {source.title}
              </span>
            )}
            <span className="text-xs text-[#6B7280]">
              {source.description}
            </span>
            {source.tags && source.tags.length > 0 && (
              <div className="mt-1 flex flex-wrap gap-1">
                {source.tags.map((tag, idx) => (
                  <span
                    key={idx}
                    className="rounded-full bg-[#E6E8EC] px-2 py-0.5 text-[10px] font-medium text-[#6B7280]"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ScientificSources; 