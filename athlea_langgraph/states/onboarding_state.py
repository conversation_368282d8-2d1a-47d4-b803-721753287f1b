import uuid
from datetime import datetime
from typing import Any, Dict, List, Literal, Optional, Union

from langchain_core.messages import BaseMessage, AIMessage, HumanMessage
from pydantic import BaseModel, Field
from typing_extensions import Annotated, TypedDict
from langgraph.graph import add_messages, MessagesState
from .state import AgentState, messages_reducer

# Define the possible stages of the onboarding process
OnboardingStage = Literal[
    "initial",
    "greeting",
    "collecting_goals",
    "summarizing_history",
    "generating_plan",
    "presenting_plan",
    "complete",
    "error",
]

# Define the fields we need to collect
OnboardingField = Literal["goal", "experienceLevel", "timeCommitment", "equipment"]


class UserGoals(BaseModel):
    """Structure for user goals - matches frontend UserGoals interface"""

    exists: bool = Field(
        default=False, description="Flag indicating if goals have been captured"
    )
    list: List[str] = Field(
        default_factory=list, description="List of user's fitness goals"
    )


class SummaryItem(BaseModel):
    """Structure for user history summary items - matches frontend SummaryItem interface"""

    category: str = Field(
        description="Category of information (e.g., 'Training Frequency', 'Dietary Habits')"
    )
    details: str = Field(description="The summarized detail")
    isImportant: bool = Field(
        default=False, description="Flag to highlight important information"
    )


class SportSuggestion(BaseModel):
    """Structure for sport suggestions"""

    label: str = Field(description="Display label for the sport")
    value: str = Field(description="Value identifier for the sport")


class PlanPhase(BaseModel):
    """Structure for a training phase"""

    phase_name: str = Field(description="Name of the training phase")
    duration: str = Field(description="Duration of the phase")
    description: str = Field(
        description="Detailed description of what happens in this phase"
    )


class ExampleSession(BaseModel):
    """Structure for example training sessions"""

    session_name: str = Field(description="Descriptive name for the session")
    session_type: str = Field(
        description="Type of session (e.g., 'Endurance', 'Tempo', 'Intervals')"
    )
    duration: str = Field(description="Estimated duration of the session")
    session_description: str = Field(
        description="Detailed description of session components and goals"
    )


class PlanDetails(BaseModel):
    """Structure for generated fitness plan"""

    plan_id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="Unique identifier for the plan",
    )
    name: str = Field(description="Catchy and descriptive name for the fitness plan")
    description: str = Field(
        description="Detailed description of the plan and its goals"
    )
    duration: str = Field(description="Total duration of the plan")
    level: str = Field(description="Recommended fitness level")
    plan_type: str = Field(description="Primary type of plan")
    disciplines: List[str] = Field(description="List of disciplines involved")
    rationale: str = Field(description="Explanation of why this plan is suitable")
    phases: List[PlanPhase] = Field(description="List of training phases")
    example_sessions: List[ExampleSession] = Field(
        description="List of example training sessions"
    )


class SidebarStateData(BaseModel):
    """Sidebar state for tracking progress - matches frontend SidebarStateData interface"""

    current_stage: str = Field(default="initial")
    goals: UserGoals = Field(default_factory=UserGoals)
    summary_items: Optional[List[SummaryItem]] = Field(default_factory=list)
    generated_plan: Optional[Union[PlanDetails, Dict[str, Any]]] = Field(default=None)
    sport_suggestions: Optional[List[SportSuggestion]] = Field(default=None)
    selected_sport: Optional[str] = Field(default=None)
    selected_sports: List[str] = Field(default_factory=list)
    weekly_plan: Optional[Dict[str, Any]] = Field(
        default=None
    )  # For session generation
    uploaded_documents: Optional[List[Dict[str, Any]]] = Field(
        default_factory=list,
        description="List of uploaded document metadata for display in sidebar",
    )
    key_insights: Optional[Dict[str, str]] = Field(
        default_factory=dict,
        description="Key insights extracted from uploaded files as key-value pairs",
    )

    class Config:
        extra = "allow"  # Allow additional fields to be set dynamically


class OnboardingState(AgentState):
    """
    Main onboarding state interface - extends the base AgentState.

    IMPORTANT: This state extends MessagesState (via AgentState) to enable
    Chat Mode support in LangGraph Studio.
    """

    # Core fields - messages inherited from MessagesState
    # messages: Annotated[List[BaseMessage], add_messages] - inherited from MessagesState

    user_id: str

    # Conversation history from MongoDB for routing decisions
    conversation_history: Optional[List[Dict[str, Any]]] = Field(
        default_factory=list,
        description="Full conversation history loaded from MongoDB for routing decisions",
    )

    # Onboarding specific fields
    current_question_field: Optional[OnboardingField]
    onboarding_stage: OnboardingStage

    # User profile information collected during onboarding
    goal: Optional[str]
    experience_level: Optional[str]
    time_commitment: Optional[str]
    equipment: Optional[str]

    # Tracking for sidebar UI
    sidebar_data: Optional[SidebarStateData]

    # Control flags - match frontend exactly
    has_enough_info: bool
    needs_input: bool
    requires_input: bool
    input_prompt: Optional[str]
    current_task_description: Optional[str]

    # Generated content
    generated_plan: Optional[Union[PlanDetails, Dict[str, Any]]]
    sport_suggestions: Optional[List[SportSuggestion]]

    # User interaction
    user_input: Optional[str]
    resume_value: Optional[str]
    user_name: Optional[str]

    # System fields
    error: Optional[str]
    info_gathered: bool
    pending_goal_clarification: bool
    system_prompt: str

    # LangGraph internal field for interrupt handling
    _langgraph_interrupt: Optional[Any]

    # Tool-related fields specific to onboarding if they use tools
    tool_error: Optional[str]
    last_tool_call: Optional[Dict[str, Any]]


def create_initial_onboarding_state(user_id: str) -> OnboardingState:
    """Helper function to create initial onboarding state - matches frontend logic"""
    return OnboardingState(
        messages=[],
        user_id=user_id,
        conversation_history=[],
        current_question_field=None,
        onboarding_stage="initial",
        goal=None,
        experience_level=None,
        time_commitment=None,
        equipment=None,
        sidebar_data=SidebarStateData(),
        has_enough_info=False,
        needs_input=False,
        requires_input=False,
        input_prompt=None,
        current_task_description=None,
        generated_plan=None,
        sport_suggestions=None,
        user_input=None,
        resume_value=None,
        user_name=None,
        error=None,
        info_gathered=False,
        pending_goal_clarification=False,
        system_prompt="",
        _langgraph_interrupt=None,
    )


# Common sport suggestions - match frontend exactly
COMMON_SPORT_SUGGESTIONS = [
    SportSuggestion(label="🏃 Running", value="Running"),
    SportSuggestion(label="🚲 Cycling", value="Cycling"),
    SportSuggestion(label="🏋️ Strength", value="Strength Training"),
    SportSuggestion(label="🧘 General Fitness", value="General Fitness"),
    SportSuggestion(label="🏊 Swimming", value="Swimming"),
    SportSuggestion(label="🎾 Tennis", value="Tennis"),
]

# Valid sport values for quick lookup
VALID_SPORT_VALUES = {suggestion.value for suggestion in COMMON_SPORT_SUGGESTIONS}


# Utility functions for state manipulation - match frontend logic
def add_human_message(state: OnboardingState, content: str) -> OnboardingState:
    """Add human message to state"""
    new_state = state.copy()
    new_state["messages"] = state["messages"] + [HumanMessage(content=content)]
    new_state["user_input"] = content
    new_state["resume_value"] = None
    return new_state


def add_ai_message(state: OnboardingState, content: str) -> OnboardingState:
    """Add AI message to state"""
    new_state = state.copy()
    new_state["messages"] = state["messages"] + [AIMessage(content=content)]
    return new_state


def set_goals(state: OnboardingState, goals: List[str]) -> OnboardingState:
    """Set goals in sidebar data"""
    new_state = state.copy()
    current_sidebar = state.get("sidebar_data") or SidebarStateData()

    # Update sidebar data
    updated_sidebar = SidebarStateData(
        current_stage=current_sidebar.current_stage,
        goals=UserGoals(exists=len(goals) > 0, list=goals),
        summary_items=current_sidebar.summary_items,
        generated_plan=current_sidebar.generated_plan,
        sport_suggestions=current_sidebar.sport_suggestions,
        selected_sport=current_sidebar.selected_sport,
        selected_sports=current_sidebar.selected_sports,
    )

    new_state["sidebar_data"] = updated_sidebar
    return new_state


def add_summary_item(state: OnboardingState, item: SummaryItem) -> OnboardingState:
    """Add summary item to sidebar data"""
    new_state = state.copy()
    current_sidebar = state.get("sidebar_data") or SidebarStateData()
    current_items = current_sidebar.summary_items or []

    # Update sidebar data
    updated_sidebar = SidebarStateData(
        current_stage=current_sidebar.current_stage,
        goals=current_sidebar.goals,
        summary_items=current_items + [item],
        generated_plan=current_sidebar.generated_plan,
        sport_suggestions=current_sidebar.sport_suggestions,
        selected_sport=current_sidebar.selected_sport,
        selected_sports=current_sidebar.selected_sports,
    )

    new_state["sidebar_data"] = updated_sidebar
    return new_state


def add_selected_sport(state: OnboardingState, sport_value: str) -> OnboardingState:
    """Add selected sport to sidebar data"""
    new_state = state.copy()
    current_sidebar = state.get("sidebar_data") or SidebarStateData()
    current_sports = current_sidebar.selected_sports or []

    # Avoid duplicates
    if sport_value not in current_sports:
        updated_sports = current_sports + [sport_value]
    else:
        updated_sports = current_sports

    # Update sidebar data
    updated_sidebar = SidebarStateData(
        current_stage=current_sidebar.current_stage,
        goals=current_sidebar.goals,
        summary_items=current_sidebar.summary_items,
        generated_plan=current_sidebar.generated_plan,
        sport_suggestions=current_sidebar.sport_suggestions,
        selected_sport=sport_value,  # Set as current selected sport
        selected_sports=updated_sports,
    )

    new_state["sidebar_data"] = updated_sidebar
    return new_state
