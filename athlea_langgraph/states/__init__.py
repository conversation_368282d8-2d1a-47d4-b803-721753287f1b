"""
State management module for the Athlea LangGraph coaching system.

Exports state classes and utility functions for managing conversation state.
"""

# Core state exports
from .state import AgentState

# Onboarding state exports
from .onboarding_state import (
    COMMON_SPORT_SUGGESTIONS,
    VALID_SPORT_VALUES,
    ExampleSession,
    OnboardingField,
    OnboardingStage,
    OnboardingState,
    PlanDetails,
    PlanPhase,
    SidebarStateData,
    SportSuggestion,
    SummaryItem,
    UserGoals,
    create_initial_onboarding_state,
)

# Optimized coaching state exports
from .optimized_state import OptimizedCoachingState, OptimizedState

# Export all state-related components
__all__ = [
    # Core state
    "AgentState",
    # Onboarding state
    "OnboardingState",
    "OnboardingStage",
    "OnboardingField",
    "UserGoals",
    "SummaryItem",
    "SportSuggestion",
    "PlanPhase",
    "ExampleSession",
    "PlanDetails",
    "SidebarStateData",
    "create_initial_onboarding_state",
    "COMMON_SPORT_SUGGESTIONS",
    "VALID_SPORT_VALUES",
    # Optimized coaching state
    "OptimizedCoachingState",
    "OptimizedState",
]
