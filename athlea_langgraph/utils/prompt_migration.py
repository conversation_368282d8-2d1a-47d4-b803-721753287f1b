"""
Migration utility for extracting existing hardcoded prompts and converting them to external JSON files.

This script analyzes the codebase, extracts prompt constants, and creates the structured
JSON files for the externalized prompt management system.
"""

import ast
import json
import logging
import re
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from .prompt_loader import PromptLoader
from .prompt_models import (
    ChangelogEntry,
    PromptConfig,
    PromptContent,
    PromptMetadata,
    PromptType,
    PromptValidation,
    PromptVariables,
)

logger = logging.getLogger(__name__)


class PromptExtractor:
    """Extracts prompts from Python source files."""

    def __init__(self, source_dir: Path):
        self.source_dir = Path(source_dir)
        self.extracted_prompts: Dict[str, Dict[str, Any]] = {}

    def extract_from_file(self, file_path: Path) -> Dict[str, str]:
        """Extract prompt constants from a Python file."""
        prompts = {}

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # Parse the AST to find string assignments
            tree = ast.parse(content)

            for node in ast.walk(tree):
                if isinstance(node, ast.Assign):
                    # Check if this is a prompt assignment
                    if (
                        len(node.targets) == 1
                        and isinstance(node.targets[0], ast.Name)
                        and isinstance(node.value, ast.Constant)
                        and isinstance(node.value.value, str)
                    ):

                        var_name = node.targets[0].id
                        if self._is_prompt_variable(var_name):
                            prompts[var_name] = node.value.value

        except Exception as e:
            logger.error(f"Error extracting prompts from {file_path}: {e}")

        return prompts

    def _is_prompt_variable(self, var_name: str) -> bool:
        """Check if a variable name looks like a prompt."""
        prompt_indicators = [
            "PROMPT",
            "prompt",
            "COACH",
            "coach",
            "TEMPLATE",
            "template",
            "SYSTEM",
            "system",
        ]
        return any(indicator in var_name for indicator in prompt_indicators)

    def extract_all_prompts(self) -> Dict[str, Dict[str, Any]]:
        """Extract all prompts from the source directory."""
        logger.info(f"Extracting prompts from {self.source_dir}")

        python_files = list(self.source_dir.rglob("*.py"))
        logger.info(f"Found {len(python_files)} Python files to analyze")

        all_prompts = {}

        for file_path in python_files:
            file_prompts = self.extract_from_file(file_path)
            if file_prompts:
                relative_path = str(file_path.relative_to(self.source_dir))
                all_prompts[relative_path] = file_prompts
                logger.info(f"Found {len(file_prompts)} prompts in {relative_path}")

        return all_prompts


class PromptMigrator:
    """Migrates extracted prompts to the new externalized format."""

    def __init__(self, prompts_dir: Path):
        self.prompts_dir = Path(prompts_dir)
        self.loader = PromptLoader(str(prompts_dir))

    def create_prompt_config(
        self,
        prompt_name: str,
        prompt_content: str,
        source_file: str,
        prompt_type: PromptType = PromptType.SYSTEM,
    ) -> PromptConfig:
        """Create a PromptConfig from extracted prompt data."""

        # Generate metadata
        now = datetime.now().isoformat()

        # Determine prompt type from name/content
        prompt_type = self._determine_prompt_type(prompt_name, prompt_content)

        # Create clean prompt name for file
        clean_name = self._clean_prompt_name(prompt_name)

        metadata = PromptMetadata(
            name=clean_name,
            version="1.0.0",
            description=f"Migrated {prompt_name} from {source_file}",
            author="Migration Script",
            created_at=now,
            updated_at=now,
            prompt_type=prompt_type,
            tags=self._generate_tags(prompt_name, prompt_content),
            changelog=[
                ChangelogEntry(
                    version="1.0.0",
                    date=now,
                    changes="Initial migration from hardcoded prompt",
                    author="Migration Script",
                )
            ],
        )

        # Process prompt content
        processed_content = self._process_prompt_content(prompt_content)

        prompt = PromptContent(
            system=processed_content,
            context_template=self._extract_context_template(prompt_content),
            instructions=self._extract_instructions(prompt_content),
            constraints=self._extract_constraints(prompt_content),
        )

        # Set appropriate variables based on prompt type
        variables = self._get_default_variables(prompt_type)

        # Set validation rules
        validation = PromptValidation(
            min_length=50,
            max_length=10000,
            required_context=self._extract_required_context(prompt_content),
        )

        return PromptConfig(
            metadata=metadata, prompt=prompt, variables=variables, validation=validation
        )

    def _determine_prompt_type(self, name: str, content: str) -> PromptType:
        """Determine the prompt type from name and content."""
        name_lower = name.lower()
        content_lower = content.lower()

        if "coach" in name_lower:
            return PromptType.COACH
        elif "reasoning" in name_lower or "reason" in content_lower:
            return PromptType.REASONING
        elif "planning" in name_lower or "plan" in content_lower:
            return PromptType.PLANNING
        elif "onboarding" in name_lower or "onboard" in content_lower:
            return PromptType.ONBOARDING
        elif "react" in name_lower or "react" in content_lower:
            return PromptType.REACT
        else:
            return PromptType.SYSTEM

    def _clean_prompt_name(self, name: str) -> str:
        """Clean prompt name for use as filename."""
        # Remove common suffixes and convert to snake_case
        name = name.replace("_PROMPT", "").replace("_prompt", "").replace("PROMPT", "")
        name = re.sub(r"([A-Z]+)([A-Z][a-z])", r"\1_\2", name)
        name = re.sub(r"([a-z\d])([A-Z])", r"\1_\2", name)
        return name.lower().strip("_")

    def _generate_tags(self, name: str, content: str) -> List[str]:
        """Generate appropriate tags for the prompt."""
        tags = []

        name_lower = name.lower()
        content_lower = content.lower()

        # Add type-based tags
        if "coach" in name_lower:
            tags.append("coaching")
        if "strength" in name_lower or "strength" in content_lower:
            tags.append("strength")
        if "cardio" in name_lower or "cardio" in content_lower:
            tags.append("cardio")
        if "nutrition" in name_lower or "nutrition" in content_lower:
            tags.append("nutrition")
        if "mental" in name_lower or "mental" in content_lower:
            tags.append("mental")
        if "recovery" in name_lower or "recovery" in content_lower:
            tags.append("recovery")
        if "cycling" in name_lower or "cycling" in content_lower:
            tags.append("cycling")

        # Add functional tags
        if "reasoning" in name_lower or "analysis" in content_lower:
            tags.append("reasoning")
        if "planning" in name_lower or "plan" in content_lower:
            tags.append("planning")
        if "react" in name_lower:
            tags.append("react")

        return tags or ["general"]

    def _process_prompt_content(self, content: str) -> str:
        """Process and clean prompt content."""
        # Remove excessive whitespace while preserving structure
        lines = content.split("\n")
        processed_lines = []

        for line in lines:
            stripped = line.strip()
            if stripped:
                processed_lines.append(stripped)
            elif processed_lines and processed_lines[-1]:  # Preserve paragraph breaks
                processed_lines.append("")

        return "\n".join(processed_lines).strip()

    def _extract_context_template(self, content: str) -> Optional[str]:
        """Extract context template patterns from prompt content."""
        # Look for template patterns like {variable} or {{variable}}
        template_patterns = re.findall(r"\{[^}]+\}", content)
        if template_patterns:
            # Create a simple context template
            return (
                "User Context: {user_profile}\nGoals: {goals}\nSession: {session_info}"
            )
        return None

    def _extract_instructions(self, content: str) -> Optional[str]:
        """Extract instruction sections from prompt content."""
        # Look for instruction-like patterns
        instruction_indicators = [
            "Instructions:",
            "Please:",
            "You should:",
            "Remember to:",
            "Guidelines:",
            "Rules:",
            "Important:",
        ]

        for indicator in instruction_indicators:
            if indicator in content:
                # Extract the part after the indicator
                parts = content.split(indicator, 1)
                if len(parts) > 1:
                    return parts[1].strip()[:500]  # Limit length

        return None

    def _extract_constraints(self, content: str) -> List[str]:
        """Extract constraint statements from prompt content."""
        constraints = []

        # Look for constraint patterns
        constraint_patterns = [
            r"You must ([^.]+\.).",
            r"Always ([^.]+\.).",
            r"Never ([^.]+\.).",
            r"Don't ([^.]+\.).",
            r"Ensure ([^.]+\.).",
        ]

        for pattern in constraint_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            constraints.extend(matches[:3])  # Limit to 3 constraints

        return constraints

    def _extract_required_context(self, content: str) -> List[str]:
        """Extract required context variables from prompt content."""
        # Look for template variables
        variables = re.findall(r"\{(\w+)\}", content)
        common_context = ["user_profile", "goals", "session_info"]

        # Return intersection of found variables and common context
        return [var for var in common_context if var in variables]

    def _get_default_variables(self, prompt_type: PromptType) -> PromptVariables:
        """Get default model variables based on prompt type."""
        if prompt_type == PromptType.COACH:
            return PromptVariables(temperature=0.7, max_tokens=4000)
        elif prompt_type == PromptType.REASONING:
            return PromptVariables(temperature=0.3, max_tokens=3000)
        elif prompt_type == PromptType.PLANNING:
            return PromptVariables(temperature=0.4, max_tokens=2000)
        else:
            return PromptVariables(temperature=0.7, max_tokens=4000)

    def migrate_prompt(
        self, prompt_name: str, prompt_content: str, source_file: str
    ) -> Path:
        """Migrate a single prompt to external file."""
        prompt_config = self.create_prompt_config(
            prompt_name, prompt_content, source_file
        )
        return self.loader.save_prompt(prompt_config)


def run_migration(source_dir: str = None, prompts_dir: str = None) -> Dict[str, Any]:
    """
    Run the complete prompt migration process.

    Args:
        source_dir: Directory to scan for prompts (defaults to athlea_langgraph)
        prompts_dir: Directory to save prompts (defaults to athlea_langgraph/prompts)

    Returns:
        Migration report with statistics
    """
    if source_dir is None:
        current_dir = Path(__file__).parent.parent
        source_dir = current_dir

    if prompts_dir is None:
        current_dir = Path(__file__).parent.parent
        prompts_dir = current_dir / "prompts"

    logger.info("Starting prompt migration process")

    # Extract prompts
    extractor = PromptExtractor(source_dir)
    extracted_prompts = extractor.extract_all_prompts()

    # Migrate prompts
    migrator = PromptMigrator(prompts_dir)

    migration_report = {
        "start_time": datetime.now().isoformat(),
        "source_files_scanned": len(extracted_prompts),
        "prompts_found": 0,
        "prompts_migrated": 0,
        "migration_errors": [],
        "migrated_files": [],
    }

    for source_file, prompts in extracted_prompts.items():
        migration_report["prompts_found"] += len(prompts)

        for prompt_name, prompt_content in prompts.items():
            try:
                output_path = migrator.migrate_prompt(
                    prompt_name, prompt_content, source_file
                )
                migration_report["prompts_migrated"] += 1
                migration_report["migrated_files"].append(str(output_path))
                logger.info(f"Migrated {prompt_name} to {output_path}")

            except Exception as e:
                error_msg = f"Failed to migrate {prompt_name} from {source_file}: {e}"
                migration_report["migration_errors"].append(error_msg)
                logger.error(error_msg)

    migration_report["end_time"] = datetime.now().isoformat()

    logger.info(
        f"Migration complete: {migration_report['prompts_migrated']}/{migration_report['prompts_found']} prompts migrated"
    )

    return migration_report


def create_sample_prompts() -> None:
    """Create sample prompt files to demonstrate the structure."""
    current_dir = Path(__file__).parent.parent
    prompts_dir = current_dir / "prompts"

    migrator = PromptMigrator(prompts_dir)

    # Create a sample strength coach prompt
    sample_strength_prompt = """You are a Strength Training Coach specializing in resistance training, powerlifting, bodybuilding, and functional strength development.

Your expertise includes:
- Exercise selection and progression
- Program design for different goals (strength, hypertrophy, power)
- Proper form and technique
- Equipment recommendations
- Injury prevention and modification
- Periodization and programming

You have access to specialized tools for exercise databases and program generation.
Always provide evidence-based advice and consider the user's experience level, goals, and any limitations they may have."""

    sample_config = migrator.create_prompt_config(
        "STRENGTH_COACH_PROMPT",
        sample_strength_prompt,
        "coach_manager.py",
        PromptType.COACH,
    )

    migrator.loader.save_prompt(sample_config, "strength_coach")
    logger.info("Created sample strength coach prompt")


if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)

    # Run migration
    report = run_migration()

    # Print report
    print("\n" + "=" * 50)
    print("PROMPT MIGRATION REPORT")
    print("=" * 50)
    print(f"Source files scanned: {report['source_files_scanned']}")
    print(f"Prompts found: {report['prompts_found']}")
    print(f"Prompts migrated: {report['prompts_migrated']}")
    print(f"Errors: {len(report['migration_errors'])}")

    if report["migration_errors"]:
        print("\nErrors:")
        for error in report["migration_errors"]:
            print(f"  - {error}")

    print(f"\nMigrated files:")
    for file_path in report["migrated_files"]:
        print(f"  - {file_path}")

    print("\nMigration complete!")
