"""
Advanced User Data Caching System with Redis

This module provides high-performance caching for user data with multiple
caching strategies, connection pooling, and comprehensive error handling.
Optimized for Azure Redis Cache with SSL support.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set, Tuple, Union
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
from functools import wraps
from dataclasses import dataclass
from enum import Enum

import redis.asyncio as redis
from redis.asyncio import ConnectionPool
from redis.exceptions import ConnectionError, TimeoutError, RedisError
from pymongo import MongoClient
from cachetools import TTLCache, LRUCache
import hashlib

from .user_data_utils import (
    TrainingProfile,
    TrainingPlan,
    UserDataError,
    format_training_profile_for_prompt,
    format_current_plan_for_prompt,
)

logger = logging.getLogger(__name__)


class CacheStrategy(Enum):
    """Caching strategy options."""

    CACHE_ASIDE = "cache_aside"  # Check cache first, load from DB if miss
    WRITE_THROUGH = "write_through"  # Write to cache and DB simultaneously
    WRITE_BEHIND = "write_behind"  # Write to cache immediately, DB asynchronously
    REFRESH_AHEAD = "refresh_ahead"  # Proactively refresh cache before expiry


@dataclass
class CacheConfig:
    """Cache configuration settings."""

    # Redis connection
    host: str = "athlea-redis-standard.redis.cache.windows.net"
    port: int = 6380
    password: str = (
        "IrFtfY8rnuyt80nGrrRdsKbaw2jeueUEUAzCaG123Vo="  # Primary access key for athlea-redis-standard
    )
    ssl: bool = True

    # Cache settings - Reduced timeouts for better streaming responsiveness
    default_ttl: int = 3600  # 1 hour
    max_connections: int = 20
    socket_timeout: float = 2.0  # Reduced from 5.0 to 2.0 seconds
    socket_connect_timeout: float = 2.0  # Reduced from 5.0 to 2.0 seconds
    retry_on_timeout: bool = True
    max_retries: int = 2  # Number of retry attempts
    retry_delay: float = 0.1  # Initial retry delay in seconds

    # Performance settings
    max_memory_cache_size: int = 1000  # In-memory cache size
    compression_threshold: int = 1024  # Compress data larger than this
    batch_size: int = 100  # Batch operations size

    # TTL settings by data type
    profile_ttl: int = 7200  # 2 hours for profiles
    plan_ttl: int = 1800  # 30 minutes for plans
    formatted_ttl: int = 3600  # 1 hour for formatted data

    # Circuit breaker settings
    failure_threshold: int = 5  # Number of failures before opening circuit
    success_threshold: int = 3  # Number of successes to close circuit
    timeout_threshold: float = 30.0  # Seconds before circuit resets


class CacheMetrics:
    """Track cache performance metrics."""

    def __init__(self):
        self.hits = 0
        self.misses = 0
        self.errors = 0
        self.total_requests = 0
        self.response_times: List[float] = []
        self.last_reset = datetime.now()

    def record_hit(self, response_time: float):
        self.hits += 1
        self.total_requests += 1
        self.response_times.append(response_time)

    def record_miss(self, response_time: float):
        self.misses += 1
        self.total_requests += 1
        self.response_times.append(response_time)

    def record_error(self):
        self.errors += 1
        self.total_requests += 1

    @property
    def hit_rate(self) -> float:
        if self.total_requests == 0:
            return 0.0
        return self.hits / self.total_requests

    @property
    def average_response_time(self) -> float:
        if not self.response_times:
            return 0.0
        return sum(self.response_times) / len(self.response_times)

    def reset(self):
        self.hits = 0
        self.misses = 0
        self.errors = 0
        self.total_requests = 0
        self.response_times.clear()
        self.last_reset = datetime.now()


class CircuitBreaker:
    """Circuit breaker for Redis operations to prevent cascade failures."""

    def __init__(self, config: CacheConfig):
        self.config = config
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN

    def can_execute(self) -> bool:
        """Check if operation can be executed based on circuit state."""
        if self.state == "CLOSED":
            return True
        elif self.state == "OPEN":
            if (
                self.last_failure_time
                and time.time() - self.last_failure_time > self.config.timeout_threshold
            ):
                self.state = "HALF_OPEN"
                self.success_count = 0
                return True
            return False
        elif self.state == "HALF_OPEN":
            return True
        return False

    def record_success(self):
        """Record a successful operation."""
        if self.state == "HALF_OPEN":
            self.success_count += 1
            if self.success_count >= self.config.success_threshold:
                self.state = "CLOSED"
                self.failure_count = 0
        elif self.state == "CLOSED":
            self.failure_count = max(0, self.failure_count - 1)

    def record_failure(self):
        """Record a failed operation."""
        self.failure_count += 1
        self.last_failure_time = time.time()

        if self.failure_count >= self.config.failure_threshold:
            self.state = "OPEN"


def cache_performance(func):
    """Decorator to measure cache performance."""

    @wraps(func)
    async def wrapper(self, *args, **kwargs):
        start_time = time.time()
        try:
            result = await func(self, *args, **kwargs)
            response_time = time.time() - start_time

            # Determine if it was a hit or miss based on the result
            if hasattr(self, "metrics"):
                if result is not None:
                    self.metrics.record_hit(response_time)
                else:
                    self.metrics.record_miss(response_time)

            return result
        except Exception as e:
            if hasattr(self, "metrics"):
                self.metrics.record_error()
            raise e

    return wrapper


class AdvancedUserDataCache:
    """
    High-performance user data caching system with Redis.

    Features:
    - Multiple caching strategies
    - Connection pooling and async operations
    - Compression for large data
    - Multi-level caching (Redis + in-memory)
    - Cache warming and refresh-ahead
    - Comprehensive metrics and monitoring
    """

    def __init__(
        self,
        mongodb_uri: str,
        config: Optional[CacheConfig] = None,
        strategy: CacheStrategy = CacheStrategy.CACHE_ASIDE,
    ):
        self.mongodb_uri = mongodb_uri
        self.config = config or CacheConfig()
        self.strategy = strategy

        # Redis connection pool
        self.redis_pool: Optional[ConnectionPool] = None
        self.redis_client: Optional[redis.Redis] = None

        # MongoDB connection
        self.mongo_client: Optional[MongoClient] = None
        self.db = None
        self.collection = None

        # In-memory cache for ultra-fast access
        self.memory_cache = TTLCache(
            maxsize=self.config.max_memory_cache_size,
            ttl=300,  # 5 minutes for memory cache
        )

        # Performance tracking
        self.metrics = CacheMetrics()

        # Circuit breaker for Redis operations
        self.circuit_breaker = CircuitBreaker(self.config)

        # Background tasks
        self.executor = ThreadPoolExecutor(max_workers=5)
        self._background_tasks: Set[asyncio.Task] = set()

        # Cache key prefixes
        self.PROFILE_PREFIX = "user_profile:"
        self.PLAN_PREFIX = "user_plan:"
        self.FORMATTED_PREFIX = "formatted:"
        self.METADATA_PREFIX = "meta:"

        # Initialization flag
        self._initialized = False

    async def initialize(self):
        """Initialize Redis and MongoDB connections."""
        if self._initialized:
            return

        try:
            # Setup Redis connection for Azure Redis Cache - use async Redis
            import redis.asyncio as aioredis

            self.redis_client = aioredis.Redis(
                host=self.config.host,
                port=self.config.port,
                password=self.config.password,
                ssl=self.config.ssl,
                ssl_cert_reqs="none",
                ssl_check_hostname=False,
                decode_responses=True,
                socket_timeout=self.config.socket_timeout,
                socket_connect_timeout=self.config.socket_connect_timeout,
            )

            # Test Redis connection
            await self.redis_client.ping()
            logger.info("✅ Redis connection established successfully")

            # Setup MongoDB connection
            self.mongo_client = MongoClient(self.mongodb_uri)
            self.db = self.mongo_client["AthleaUserData"]
            self.collection = self.db["users"]

            # Test MongoDB connection
            self.collection.find_one({}, limit=1)
            logger.info("✅ MongoDB connection established successfully")

            self._initialized = True

        except Exception as e:
            logger.error(f"❌ Failed to initialize cache system: {e}")
            raise UserDataError(f"Cache initialization failed: {e}")

    def _generate_cache_key(self, prefix: str, user_id: str, suffix: str = "") -> str:
        """Generate standardized cache keys."""
        key = f"{prefix}{user_id}"
        if suffix:
            key += f":{suffix}"
        return key

    def _compress_data(self, data: str) -> Union[str, bytes]:
        """Compress data if it exceeds threshold."""
        if len(data) > self.config.compression_threshold:
            try:
                import zlib

                compressed = zlib.compress(data.encode("utf-8"))
                return f"COMPRESSED:{compressed.hex()}"
            except Exception:
                pass
        return data

    def _decompress_data(self, data: str) -> str:
        """Decompress data if it was compressed."""
        if isinstance(data, str) and data.startswith("COMPRESSED:"):
            try:
                import zlib

                hex_data = data[11:]  # Remove "COMPRESSED:" prefix
                compressed = bytes.fromhex(hex_data)
                return zlib.decompress(compressed).decode("utf-8")
            except Exception:
                pass
        return data

    async def _redis_get(self, key: str) -> Optional[str]:
        """Safe Redis GET with error handling, retries, and circuit breaker."""
        if not self.circuit_breaker.can_execute():
            logger.warning(f"Circuit breaker OPEN - skipping Redis GET for key {key}")
            return None

        for attempt in range(self.config.max_retries + 1):
            try:
                # Use asyncio.wait_for to enforce our own timeout
                result = await asyncio.wait_for(
                    self.redis_client.get(key), timeout=self.config.socket_timeout
                )

                # Record success in circuit breaker
                self.circuit_breaker.record_success()

                return self._decompress_data(result) if result else None

            except asyncio.TimeoutError:
                logger.warning(
                    f"Redis GET timeout for key {key} (attempt {attempt + 1}/{self.config.max_retries + 1})"
                )
                if attempt < self.config.max_retries:
                    await asyncio.sleep(
                        self.config.retry_delay * (2**attempt)
                    )  # Exponential backoff
                    continue
                else:
                    self.circuit_breaker.record_failure()
                    return None

            except (ConnectionError, RedisError) as e:
                logger.warning(
                    f"Redis GET error for key {key}: {e} (attempt {attempt + 1}/{self.config.max_retries + 1})"
                )
                if attempt < self.config.max_retries:
                    await asyncio.sleep(
                        self.config.retry_delay * (2**attempt)
                    )  # Exponential backoff
                    continue
                else:
                    self.circuit_breaker.record_failure()
                    return None
            except Exception as e:
                logger.error(f"Unexpected Redis GET error for key {key}: {e}")
                self.circuit_breaker.record_failure()
                return None

        return None

    async def _redis_set(self, key: str, value: str, ttl: Optional[int] = None) -> bool:
        """Safe Redis SET with error handling."""
        try:
            compressed_value = self._compress_data(value)
            await self.redis_client.set(
                key, compressed_value, ex=ttl or self.config.default_ttl
            )
            return True
        except (ConnectionError, TimeoutError, RedisError) as e:
            logger.warning(f"Redis SET error for key {key}: {e}")
            return False

    async def _redis_mget(self, keys: List[str]) -> Dict[str, Optional[str]]:
        """Batch GET multiple keys from Redis."""
        try:
            values = await self.redis_client.mget(keys)
            result = {}
            for key, value in zip(keys, values):
                result[key] = self._decompress_data(value) if value else None
            return result
        except (ConnectionError, TimeoutError, RedisError) as e:
            logger.warning(f"Redis MGET error: {e}")
            return {key: None for key in keys}

    async def _redis_mset(
        self, mapping: Dict[str, str], ttl: Optional[int] = None
    ) -> bool:
        """Batch SET multiple key-value pairs to Redis."""
        try:
            # Compress values
            compressed_mapping = {k: self._compress_data(v) for k, v in mapping.items()}

            await self.redis_client.mset(compressed_mapping)

            # Set TTL for each key if specified
            if ttl:
                pipeline = self.redis_client.pipeline()
                for key in mapping.keys():
                    pipeline.expire(key, ttl)
                await pipeline.execute()

            return True
        except (ConnectionError, TimeoutError, RedisError) as e:
            logger.warning(f"Redis MSET error: {e}")
            return False

    @cache_performance
    async def get_user_profile_from_cache(
        self, user_id: str
    ) -> Optional[TrainingProfile]:
        """Get user training profile from cache hierarchy."""
        await self.initialize()

        # Check memory cache first (fastest)
        memory_key = f"profile_{user_id}"
        if memory_key in self.memory_cache:
            logger.debug(f"🚀 Memory cache hit for profile: {user_id}")
            return self.memory_cache[memory_key]

        # Check Redis cache
        cache_key = self._generate_cache_key(self.PROFILE_PREFIX, user_id)
        cached_data = await self._redis_get(cache_key)

        if cached_data:
            try:
                profile_data = json.loads(cached_data)
                profile = TrainingProfile(profile_data)

                # Store in memory cache
                self.memory_cache[memory_key] = profile

                logger.debug(f"⚡ Redis cache hit for profile: {user_id}")
                return profile
            except (json.JSONDecodeError, KeyError) as e:
                logger.warning(f"Invalid cached profile data for {user_id}: {e}")

        return None

    @cache_performance
    async def get_user_plan_from_cache(self, user_id: str) -> Optional[TrainingPlan]:
        """Get user training plan from cache hierarchy."""
        await self.initialize()

        # Check memory cache first
        memory_key = f"plan_{user_id}"
        if memory_key in self.memory_cache:
            logger.debug(f"🚀 Memory cache hit for plan: {user_id}")
            return self.memory_cache[memory_key]

        # Check Redis cache
        cache_key = self._generate_cache_key(self.PLAN_PREFIX, user_id)
        cached_data = await self._redis_get(cache_key)

        if cached_data:
            try:
                plan_data = json.loads(cached_data)
                plan = TrainingPlan(plan_data)

                # Store in memory cache
                self.memory_cache[memory_key] = plan

                logger.debug(f"⚡ Redis cache hit for plan: {user_id}")
                return plan
            except (json.JSONDecodeError, KeyError) as e:
                logger.warning(f"Invalid cached plan data for {user_id}: {e}")

        return None

    async def _fetch_profile_from_mongo(
        self, user_id: str
    ) -> Optional[TrainingProfile]:
        """Fetch training profile from MongoDB."""
        try:

            def _fetch():
                return self.collection.find_one(
                    {"user_id": user_id}, {"training_profile": 1, "_id": 0}
                )

            loop = asyncio.get_event_loop()
            user_info = await loop.run_in_executor(self.executor, _fetch)

            if not user_info or not user_info.get("training_profile"):
                return None

            profile_data = user_info["training_profile"]

            # Clean up data (same logic as original)
            if "elevate_profile" in profile_data:
                del profile_data["elevate_profile"]

            # Fix nutrition field if corrupted
            if "nutrition" in profile_data and isinstance(
                profile_data["nutrition"], dict
            ):
                if any(key.isdigit() for key in profile_data["nutrition"].keys()):
                    try:
                        nutrition_text = "".join(
                            str(v) for v in profile_data["nutrition"].values()
                        )
                        profile_data["nutrition"] = nutrition_text
                    except Exception:
                        del profile_data["nutrition"]

            return TrainingProfile(profile_data)

        except Exception as e:
            logger.error(f"Error fetching profile from MongoDB for {user_id}: {e}")
            return None

    async def _fetch_plan_from_mongo(self, user_id: str) -> Optional[TrainingPlan]:
        """Fetch training plan from MongoDB."""
        try:

            def _fetch():
                return self.collection.find_one(
                    {"user_id": user_id}, {"currentPlan": 1, "_id": 0}
                )

            loop = asyncio.get_event_loop()
            user_info = await loop.run_in_executor(self.executor, _fetch)

            if not user_info or not user_info.get("currentPlan"):
                return None

            plan_data = user_info["currentPlan"]
            return TrainingPlan(plan_data)

        except Exception as e:
            logger.error(f"Error fetching plan from MongoDB for {user_id}: {e}")
            return None

    async def cache_user_profile(
        self, user_id: str, profile: TrainingProfile, ttl: Optional[int] = None
    ) -> bool:
        """Cache user profile in Redis and memory."""
        try:
            profile_data = json.dumps(profile.to_dict())
            cache_key = self._generate_cache_key(self.PROFILE_PREFIX, user_id)

            # Cache in Redis
            success = await self._redis_set(
                cache_key, profile_data, ttl or self.config.profile_ttl
            )

            # Cache in memory
            memory_key = f"profile_{user_id}"
            self.memory_cache[memory_key] = profile

            if success:
                logger.debug(f"✅ Cached profile for user: {user_id}")

            return success

        except Exception as e:
            logger.error(f"Error caching profile for {user_id}: {e}")
            return False

    async def cache_user_plan(
        self, user_id: str, plan: TrainingPlan, ttl: Optional[int] = None
    ) -> bool:
        """Cache user plan in Redis and memory."""
        try:
            plan_data = json.dumps(plan.to_dict())
            cache_key = self._generate_cache_key(self.PLAN_PREFIX, user_id)

            # Cache in Redis
            success = await self._redis_set(
                cache_key, plan_data, ttl or self.config.plan_ttl
            )

            # Cache in memory
            memory_key = f"plan_{user_id}"
            self.memory_cache[memory_key] = plan

            if success:
                logger.debug(f"✅ Cached plan for user: {user_id}")

            return success

        except Exception as e:
            logger.error(f"Error caching plan for {user_id}: {e}")
            return False

    async def get_user_data_ultra_fast(
        self, user_id: str
    ) -> Tuple[Optional[TrainingProfile], Optional[TrainingPlan]]:
        """
        Ultra-fast user data retrieval with multi-level caching and robust error handling.

        Strategy:
        1. Check memory cache (sub-millisecond)
        2. Check Redis cache (1-2ms) with circuit breaker protection
        3. Fetch from MongoDB and cache (100-500ms) with fallback
        """
        await self.initialize()

        start_time = time.time()
        profile, plan = None, None

        try:
            # Try to get both from cache simultaneously with timeout protection
            profile_task = asyncio.create_task(
                self.get_user_profile_from_cache(user_id)
            )
            plan_task = asyncio.create_task(self.get_user_plan_from_cache(user_id))

            # Wait for cache results with overall timeout
            try:
                profile, plan = await asyncio.wait_for(
                    asyncio.gather(profile_task, plan_task, return_exceptions=True),
                    timeout=5.0,  # Max 5 seconds for cache operations
                )

                # Handle exceptions in results
                if isinstance(profile, Exception):
                    logger.warning(f"Profile cache error for {user_id}: {profile}")
                    profile = None
                if isinstance(plan, Exception):
                    logger.warning(f"Plan cache error for {user_id}: {plan}")
                    plan = None

            except asyncio.TimeoutError:
                logger.warning(f"Cache operations timed out for user {user_id}")
                profile, plan = None, None

            # Fetch missing data from MongoDB with controlled concurrency
            missing_data_tasks = []
            if profile is None:
                missing_data_tasks.append(self._fetch_and_cache_profile(user_id))
            if plan is None:
                missing_data_tasks.append(self._fetch_and_cache_plan(user_id))

            if missing_data_tasks:
                try:
                    # Use timeout for MongoDB operations too
                    results = await asyncio.wait_for(
                        asyncio.gather(*missing_data_tasks, return_exceptions=True),
                        timeout=10.0,  # Max 10 seconds for MongoDB operations
                    )

                    # Update results safely
                    result_idx = 0
                    if profile is None and result_idx < len(results):
                        result = results[result_idx]
                        if not isinstance(result, Exception):
                            profile = result
                        elif isinstance(result, Exception):
                            logger.warning(
                                f"Profile fetch error for {user_id}: {result}"
                            )
                        result_idx += 1

                    if plan is None and result_idx < len(results):
                        result = results[result_idx]
                        if not isinstance(result, Exception):
                            plan = result
                        elif isinstance(result, Exception):
                            logger.warning(f"Plan fetch error for {user_id}: {result}")

                except asyncio.TimeoutError:
                    logger.warning(f"MongoDB operations timed out for user {user_id}")
                except Exception as e:
                    logger.error(
                        f"Unexpected error in MongoDB operations for {user_id}: {e}"
                    )

        except Exception as e:
            logger.error(f"Critical error in ultra-fast fetch for {user_id}: {e}")

        elapsed_time = time.time() - start_time
        logger.info(f"⚡ User data retrieval for {user_id}: {elapsed_time*1000:.2f}ms")

        return profile, plan

    async def _fetch_and_cache_profile(self, user_id: str) -> Optional[TrainingProfile]:
        """Fetch profile from MongoDB and cache it."""
        profile = await self._fetch_profile_from_mongo(user_id)
        if profile:
            # Cache in background (don't wait)
            asyncio.create_task(self.cache_user_profile(user_id, profile))
        return profile

    async def _fetch_and_cache_plan(self, user_id: str) -> Optional[TrainingPlan]:
        """Fetch plan from MongoDB and cache it."""
        plan = await self._fetch_plan_from_mongo(user_id)
        if plan:
            # Cache in background (don't wait)
            asyncio.create_task(self.cache_user_plan(user_id, plan))
        return plan

    async def warm_cache_for_users(self, user_ids: List[str]) -> Dict[str, bool]:
        """Warm cache for multiple users simultaneously."""
        logger.info(f"🔥 Warming cache for {len(user_ids)} users")

        tasks = [self.get_user_data_ultra_fast(user_id) for user_id in user_ids]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        status = {}
        for user_id, result in zip(user_ids, results):
            if isinstance(result, Exception):
                status[user_id] = False
                logger.error(f"Failed to warm cache for {user_id}: {result}")
            else:
                status[user_id] = True

        successful = sum(status.values())
        logger.info(
            f"✅ Cache warmed successfully for {successful}/{len(user_ids)} users"
        )

        return status

    async def invalidate_user_cache(self, user_id: str) -> bool:
        """Invalidate all cached data for a user."""
        try:
            # Remove from memory cache
            memory_keys = [f"profile_{user_id}", f"plan_{user_id}"]
            for key in memory_keys:
                self.memory_cache.pop(key, None)

            # Remove from Redis
            redis_keys = [
                self._generate_cache_key(self.PROFILE_PREFIX, user_id),
                self._generate_cache_key(self.PLAN_PREFIX, user_id),
                self._generate_cache_key(self.FORMATTED_PREFIX, user_id, "profile"),
                self._generate_cache_key(self.FORMATTED_PREFIX, user_id, "plan"),
            ]

            await self.redis_client.delete(*redis_keys)

            logger.info(f"🗑️ Invalidated cache for user: {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error invalidating cache for {user_id}: {e}")
            return False

    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache performance statistics."""
        try:
            # Redis stats
            redis_info = await self.redis_client.info()
            redis_stats = {
                "connected_clients": redis_info.get("connected_clients", 0),
                "used_memory": redis_info.get("used_memory_human", "unknown"),
                "keyspace_hits": redis_info.get("keyspace_hits", 0),
                "keyspace_misses": redis_info.get("keyspace_misses", 0),
            }

            # Calculate Redis hit rate
            total_redis_requests = (
                redis_stats["keyspace_hits"] + redis_stats["keyspace_misses"]
            )
            redis_hit_rate = (
                redis_stats["keyspace_hits"] / total_redis_requests
                if total_redis_requests > 0
                else 0
            )

            return {
                "application_metrics": {
                    "hit_rate": self.metrics.hit_rate,
                    "total_requests": self.metrics.total_requests,
                    "hits": self.metrics.hits,
                    "misses": self.metrics.misses,
                    "errors": self.metrics.errors,
                    "avg_response_time_ms": self.metrics.average_response_time * 1000,
                    "uptime_seconds": (
                        datetime.now() - self.metrics.last_reset
                    ).total_seconds(),
                },
                "redis_metrics": {
                    **redis_stats,
                    "hit_rate": redis_hit_rate,
                },
                "memory_cache": {
                    "size": len(self.memory_cache),
                    "max_size": self.memory_cache.maxsize,
                    "utilization": len(self.memory_cache) / self.memory_cache.maxsize,
                },
                "configuration": {
                    "strategy": self.strategy.value,
                    "default_ttl": self.config.default_ttl,
                    "profile_ttl": self.config.profile_ttl,
                    "plan_ttl": self.config.plan_ttl,
                },
            }

        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {"error": str(e)}

    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive health check for the caching system."""
        health_status = {
            "redis": {"status": "unknown", "latency_ms": None},
            "mongodb": {"status": "unknown", "latency_ms": None},
            "overall": "unknown",
        }

        # Test Redis
        try:
            start_time = time.time()
            await self.redis_client.ping()
            redis_latency = (time.time() - start_time) * 1000
            health_status["redis"] = {"status": "healthy", "latency_ms": redis_latency}
        except Exception as e:
            health_status["redis"] = {"status": "unhealthy", "error": str(e)}

        # Test MongoDB
        try:
            start_time = time.time()
            self.collection.find_one({}, limit=1)
            mongo_latency = (time.time() - start_time) * 1000
            health_status["mongodb"] = {
                "status": "healthy",
                "latency_ms": mongo_latency,
            }
        except Exception as e:
            health_status["mongodb"] = {"status": "unhealthy", "error": str(e)}

        # Overall health
        if (
            health_status["redis"]["status"] == "healthy"
            and health_status["mongodb"]["status"] == "healthy"
        ):
            health_status["overall"] = "healthy"
        elif health_status["redis"]["status"] == "healthy":
            health_status["overall"] = "degraded"  # Can still serve from cache
        else:
            health_status["overall"] = "unhealthy"

        return health_status

    async def close(self):
        """Clean up connections and resources."""
        try:
            # Cancel background tasks
            for task in self._background_tasks:
                if not task.done():
                    task.cancel()

            # Close Redis connection
            if self.redis_client:
                await self.redis_client.aclose()

            # Close MongoDB connection
            if self.mongo_client:
                self.mongo_client.close()

            # Shutdown executor
            self.executor.shutdown(wait=True)

            logger.info("🔒 Advanced cache system closed successfully")

        except Exception as e:
            logger.error(f"Error closing cache system: {e}")


# Global cache instance
_advanced_cache: Optional[AdvancedUserDataCache] = None


def get_advanced_cache(
    mongodb_uri: str,
    config: Optional[CacheConfig] = None,
    strategy: CacheStrategy = CacheStrategy.CACHE_ASIDE,
) -> AdvancedUserDataCache:
    """Get or create global advanced cache instance."""
    global _advanced_cache
    if _advanced_cache is None:
        _advanced_cache = AdvancedUserDataCache(mongodb_uri, config, strategy)
    return _advanced_cache


async def fetch_user_data_ultra_fast(
    user_id: str, mongodb_uri: str, config: Optional[CacheConfig] = None
) -> Dict[str, Any]:
    """
    Ultra-fast user data fetching with advanced caching and bulletproof error handling.

    This is the main entry point for getting user data with maximum performance and resilience.
    """
    cache = get_advanced_cache(mongodb_uri, config)

    try:
        # Use timeout to prevent hanging the stream
        profile, plan = await asyncio.wait_for(
            cache.get_user_data_ultra_fast(user_id),
            timeout=15.0,  # Maximum 15 seconds total
        )

        return {
            "training_profile": profile.to_dict() if profile else None,
            "current_plan": plan.to_dict() if plan else None,
            "training_profile_formatted": (
                format_training_profile_for_prompt(profile)
                if profile
                else "No training profile data available."
            ),
            "current_plan_formatted": (
                format_current_plan_for_prompt(plan)
                if plan
                else "No current training plan available."
            ),
            "cache_source": "advanced_redis_cache",
            "timestamp": datetime.now().isoformat(),
        }

    except asyncio.TimeoutError:
        logger.error(
            f"Total timeout exceeded for user {user_id} - returning safe defaults"
        )
        return {
            "training_profile": None,
            "current_plan": None,
            "training_profile_formatted": "User data temporarily unavailable due to timeout.",
            "current_plan_formatted": "User plan temporarily unavailable due to timeout.",
            "error": "timeout",
            "cache_source": "timeout_fallback",
            "timestamp": datetime.now().isoformat(),
        }
    except Exception as e:
        logger.error(f"Critical error in ultra-fast fetch for {user_id}: {e}")
        return {
            "training_profile": None,
            "current_plan": None,
            "training_profile_formatted": "User data temporarily unavailable due to system error.",
            "current_plan_formatted": "User plan temporarily unavailable due to system error.",
            "error": str(e),
            "cache_source": "error_fallback",
            "timestamp": datetime.now().isoformat(),
        }
