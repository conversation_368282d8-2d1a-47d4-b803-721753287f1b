"""
Web Search Domain Models

Domain-specific models for web search functionality.
These are NOT state objects - they are data structures used within the web search workflow.
"""

from typing import Dict, List, Optional
from pydantic import BaseModel, Field


class WebSearchResult(BaseModel):
    """Individual web search result."""
    title: str
    url: str
    snippet: str
    position: int
    source_domain: str
    relevance_score: Optional[float] = None


class ScrapedWebContent(BaseModel):
    """Scraped content from a webpage."""
    url: str
    title: str
    content: str
    content_length: int
    extracted_links: Optional[List[str]] = None
    status: str  # "success", "error", "garbled"
    error_message: Optional[str] = None
    scrape_timestamp: Optional[str] = None


class WebSearchPlan(BaseModel):
    """Search plan generated by planner agent."""
    search_terms: List[str]
    search_strategy: str
    expected_sources: List[str]
    content_focus: str
    max_results_per_query: int = 10


class WebSearchContext(BaseModel):
    """
    Complete context for a web search operation.
    This encapsulates all web search data in a single object.
    """
    research_question: str
    research_context: Optional[str] = None
    research_type: str = "general"
    
    # Search planning
    search_plan: Optional[WebSearchPlan] = None
    search_queries: List[str] = Field(default_factory=list)
    
    # Search results
    search_results: List[WebSearchResult] = Field(default_factory=list)
    selected_urls: List[str] = Field(default_factory=list)
    
    # Scraped content
    scraped_contents: List[ScrapedWebContent] = Field(default_factory=list)
    successful_scrapes: List[ScrapedWebContent] = Field(default_factory=list)
    
    # Analysis and synthesis
    content_analysis: Optional[str] = None
    research_summary: Optional[str] = None
    key_findings: List[str] = Field(default_factory=list)
    sources_cited: List[str] = Field(default_factory=list)
    
    # Workflow control
    max_search_results: int = 10
    max_pages_to_scrape: int = 5
    
    # Quality and validation
    quality_score: Optional[float] = None
    validation_status: str = "pending"
    validation_notes: List[str] = Field(default_factory=list)
    
    # Error tracking
    search_errors: List[str] = Field(default_factory=list)
    scraping_errors: List[str] = Field(default_factory=list)
    
    def add_search_result(self, result: WebSearchResult) -> None:
        """Add a search result."""
        self.search_results.append(result)
    
    def add_scraped_content(self, content: ScrapedWebContent) -> None:
        """Add scraped content."""
        self.scraped_contents.append(content)
        if content.status == "success":
            self.successful_scrapes.append(content)
    
    def get_research_metrics(self) -> Dict[str, any]:
        """Get metrics about the research process."""
        return {
            "total_search_results": len(self.search_results),
            "total_scraped_pages": len(self.scraped_contents),
            "successful_scrapes": len(self.successful_scrapes),
            "total_content_length": sum(c.content_length for c in self.successful_scrapes),
            "unique_domains": len(set(r.source_domain for r in self.search_results if r.source_domain)),
            "search_queries_executed": len(self.search_queries),
            "quality_score": self.quality_score,
            "validation_status": self.validation_status,
        } 