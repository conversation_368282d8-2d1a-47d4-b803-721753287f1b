"""
Memory domain models for the hybrid state pattern.

These models encapsulate memory management logic that was previously in StateWithMemory,
providing long-term memory integration, user preferences, and personalization
while storing data in AgentState's specialist_responses field.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, field_validator


class MemoryType(str, Enum):
    """Types of memories stored in the system."""
    USER_PREFERENCE = "user_preference"
    INTERACTION_SUMMARY = "interaction_summary"
    COACHING_INSIGHT = "coaching_insight"
    GOAL_PROGRESS = "goal_progress"
    PERSONAL_CONTEXT = "personal_context"


class MemoryPriority(str, Enum):
    """Priority levels for memory retrieval."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class Memory(BaseModel):
    """Individual memory item with metadata."""
    
    id: str
    type: MemoryType
    content: str
    embedding: Optional[List[float]] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.now)
    last_accessed: datetime = Field(default_factory=datetime.now)
    access_count: int = 0
    relevance_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    priority: MemoryPriority = MemoryPriority.MEDIUM
    
    def update_access(self) -> None:
        """Update access timestamp and count."""
        self.last_accessed = datetime.now()
        self.access_count += 1
    
    def is_recent(self, days: int = 7) -> bool:
        """Check if memory is recent (within specified days)."""
        age = datetime.now() - self.created_at
        return age.days <= days
    
    def decay_relevance(self, decay_rate: float = 0.95) -> None:
        """Apply time-based decay to relevance score."""
        if self.relevance_score is not None:
            days_old = (datetime.now() - self.last_accessed).days
            self.relevance_score *= (decay_rate ** days_old)


class UserPreference(BaseModel):
    """User preference with validation and defaults."""
    
    category: str
    preference: Any
    confidence: float = Field(1.0, ge=0.0, le=1.0)
    source: str = "explicit"  # explicit, inferred, default
    last_updated: datetime = Field(default_factory=datetime.now)
    
    @field_validator(''category'')
    def validate_category(cls, v):
        """Ensure category is not empty."""
        if not v.strip():
            raise ValueError("Category cannot be empty")
        return v.strip().lower()


class InteractionSummary(BaseModel):
    """Summary of a coaching interaction."""
    
    session_id: str
    timestamp: datetime
    user_query: str
    coach_response_summary: str
    coaches_involved: List[str]
    key_topics: List[str]
    outcomes: List[str] = Field(default_factory=list)
    sentiment: Optional[str] = None
    duration_seconds: Optional[float] = None
    
    def to_memory_content(self) -> str:
        """Convert to memory content string."""
        topics = ", ".join(self.key_topics) if self.key_topics else "general discussion"
        coaches = ", ".join(self.coaches_involved) if self.coaches_involved else "head coach"
        return f"Session {self.session_id}: Discussed {topics} with {coaches}. {self.coach_response_summary}"


class PersonalizationData(BaseModel):
    """Data used for personalizing coaching responses."""
    
    communication_style: Optional[str] = None  # formal, casual, motivational, etc.
    learning_preference: Optional[str] = None  # visual, detailed, concise, etc.
    motivation_triggers: List[str] = Field(default_factory=list)
    avoided_topics: List[str] = Field(default_factory=list)
    preferred_examples: List[str] = Field(default_factory=list)
    timezone: Optional[str] = None
    language_preferences: Dict[str, Any] = Field(default_factory=dict)
    
    def get_style_prompt(self) -> str:
        """Generate a style prompt for LLM based on preferences."""
        prompts = []
        if self.communication_style:
            prompts.append(f"Use a {self.communication_style} communication style")
        if self.learning_preference:
            prompts.append(f"Present information in a {self.learning_preference} manner")
        if self.motivation_triggers:
            prompts.append(f"Incorporate motivational elements related to: {'', ''.join(self.motivation_triggers)}")
        return ". ".join(prompts) if prompts else ""


class MemoryContext(BaseModel):
    """Complete memory context for a user interaction."""
    
    # Retrieved memories
    relevant_memories: List[Memory] = Field(default_factory=list)
    memory_query: Optional[str] = None
    retrieval_method: str = "semantic"  # semantic, keyword, hybrid
    
    # User preferences
    user_preferences: Dict[str, UserPreference] = Field(default_factory=dict)
    preference_confidence_threshold: float = 0.7
    
    # Interaction history
    interaction_history: List[InteractionSummary] = Field(default_factory=list)
    max_history_items: int = 10
    
    # Personalization
    personalization_data: PersonalizationData = Field(default_factory=PersonalizationData)
    personalization_enabled: bool = True
    
    def add_memory(self, memory: Memory) -> None:
        """Add a memory to relevant memories."""
        memory.update_access()
        self.relevant_memories.append(memory)
    
    def get_high_priority_memories(self) -> List[Memory]:
        """Get memories with high or critical priority."""
        return [
            m for m in self.relevant_memories 
            if m.priority in [MemoryPriority.HIGH, MemoryPriority.CRITICAL]
        ]
    
    def get_preference(self, category: str, default: Any = None) -> Any:
        """Get user preference by category."""
        pref = self.user_preferences.get(category.lower())
        if pref and pref.confidence >= self.preference_confidence_threshold:
            return pref.preference
        return default
    
    def update_preference(self, category: str, preference: Any, 
                         confidence: float = 1.0, source: str = "explicit") -> None:
        """Update or add a user preference."""
        self.user_preferences[category.lower()] = UserPreference(
            category=category,
            preference=preference,
            confidence=confidence,
            source=source
        )
    
    def add_interaction(self, summary: InteractionSummary) -> None:
        """Add interaction to history, maintaining max items."""
        self.interaction_history.append(summary)
        if len(self.interaction_history) > self.max_history_items:
            self.interaction_history = self.interaction_history[-self.max_history_items:]
    
    def get_recent_topics(self, days: int = 7) -> List[str]:
        """Get topics discussed in recent interactions."""
        cutoff = datetime.now() - timedelta(days=days)
        recent_topics = []
        for interaction in self.interaction_history:
            if interaction.timestamp >= cutoff:
                recent_topics.extend(interaction.key_topics)
        return list(set(recent_topics))
    
    def apply_memory_decay(self, decay_rate: float = 0.95) -> None:
        """Apply decay to all memory relevance scores."""
        for memory in self.relevant_memories:
            memory.decay_relevance(decay_rate)
    
    def to_context_prompt(self) -> str:
        """Generate context prompt for LLM based on memories."""
        prompts = []
        
        # Add high priority memories
        high_priority = self.get_high_priority_memories()
        if high_priority:
            memory_texts = [m.content for m in high_priority[:3]]
            prompts.append(f"Important context: {''; ''.join(memory_texts)}")
        
        # Add personalization
        if self.personalization_enabled:
            style_prompt = self.personalization_data.get_style_prompt()
            if style_prompt:
                prompts.append(style_prompt)
        
        # Add recent topics
        recent_topics = self.get_recent_topics()
        if recent_topics:
            prompts.append(f"Recent discussion topics: {'', ''.join(recent_topics[:5])}")
        
        return "\n".join(prompts) if prompts else ""


# Helper functions for state integration
def memory_to_state(context: MemoryContext) -> Dict[str, Any]:
    """Convert MemoryContext to state-compatible dictionary."""
    return {
        "memory_context": context.model_dump(),
        "relevant_memories": [m.model_dump() for m in context.relevant_memories],
        "user_preferences": {k: v.model_dump() for k, v in context.user_preferences.items()},
        "interaction_history": [i.model_dump() for i in context.interaction_history],
        "personalization_data": context.personalization_data.model_dump(),
    }


def state_to_memory(state_data: Dict[str, Any]) -> MemoryContext:
    """Reconstruct MemoryContext from state data."""
    if "memory_context" in state_data:
        return MemoryContext(**state_data["memory_context"])
    
    # Build from individual fields
    context = MemoryContext()
    
    # Reconstruct memories
    if "relevant_memories" in state_data:
        for memory_data in state_data["relevant_memories"]:
            context.relevant_memories.append(Memory(**memory_data))
    
    # Reconstruct preferences
    if "user_preferences" in state_data:
        for category, pref_data in state_data["user_preferences"].items():
            context.user_preferences[category] = UserPreference(**pref_data)
    
    # Reconstruct interaction history
    if "interaction_history" in state_data:
        for interaction_data in state_data["interaction_history"]:
            context.interaction_history.append(InteractionSummary(**interaction_data))
    
    # Reconstruct personalization
    if "personalization_data" in state_data:
        context.personalization_data = PersonalizationData(**state_data["personalization_data"])
    
    return context


def create_memory_context(
    user_id: str,
    personalization_enabled: bool = True,
    max_history_items: int = 10
) -> MemoryContext:
    """Create a new memory context for a user."""
    return MemoryContext(
        personalization_enabled=personalization_enabled,
        max_history_items=max_history_items,
        memory_query=f"user:{user_id}"
    )


# Import datetime for the model
from datetime import timedelta
