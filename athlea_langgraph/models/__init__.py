"""
Domain models for the Athlea LangGraph coaching system.

These Pydantic models provide business logic, validation, and encapsulation
for complex domain concepts, while state management remains in AgentState.
"""

# Web search models
from .web_search_models import (
    WebSearchResult,
    ScrapedWebContent,
    WebSearchPlan,
    WebSearchContext,
    web_search_to_state,
    state_to_web_search,
    create_web_search_context,
)

# ReWOO models
from .rewoo_models import (
    ReWOOStep,
    ReW<PERSON><PERSON>lan,
    <PERSON><PERSON><PERSON><PERSON>orkerResult,
    ReWOOSynthesis,
    ReWOOContext,
    rewoo_to_state,
    state_to_rewoo,
    create_rewoo_context,
)

# Reflection models
from .reflection_models import (
    ReflectionPriority,
    SafetyValidation,
    ReflectionIteration,
    ReflectionCriteria,
    ReflectionMetadata,
    ReflectionConfig,
    QualityAssessment,
    ReflectionContext,
    reflection_to_state,
    state_to_reflection,
    create_reflection_context,
)

# Specialized coach models
from .specialized_coach_models import (
    ComplexityLevel,
    ComplexityAssessmentMethod,
    ExecutionPath,
    ComplexityAssessment,
    SimpleExecutionResult,
    WorkerResult,
    ComplexExecutionResult,
    ExecutionStep,
    PerformanceMetrics,
    SpecializedCoachContext,
    specialized_coach_to_state,
    state_to_specialized_coach,
    create_specialized_coach_context,
)

# Memory models
from .memory_models import (
    MemoryType,
    MemoryPriority,
    Memory,
    UserPreference,
    InteractionSummary,
    PersonalizationData,
    MemoryContext,
    memory_to_state,
    state_to_memory,
    create_memory_context,
)

__all__ = [
    # Web search
    "WebSearchResult",
    "ScrapedWebContent",
    "WebSearchPlan",
    "WebSearchContext",
    "web_search_to_state",
    "state_to_web_search",
    "create_web_search_context",
    # ReWOO
    "ReWOOStep",
    "ReWOOPlan",
    "ReWOOWorkerResult",
    "ReWOOSynthesis",
    "ReWOOContext",
    "rewoo_to_state",
    "state_to_rewoo",
    "create_rewoo_context",
    # Reflection
    "ReflectionPriority",
    "SafetyValidation",
    "ReflectionIteration",
    "ReflectionCriteria",
    "ReflectionMetadata",
    "ReflectionConfig",
    "QualityAssessment",
    "ReflectionContext",
    "reflection_to_state",
    "state_to_reflection",
    "create_reflection_context",
    # Specialized coach
    "ComplexityLevel",
    "ComplexityAssessmentMethod",
    "ExecutionPath",
    "ComplexityAssessment",
    "SimpleExecutionResult",
    "WorkerResult",
    "ComplexExecutionResult",
    "ExecutionStep",
    "PerformanceMetrics",
    "SpecializedCoachContext",
    "specialized_coach_to_state",
    "state_to_specialized_coach",
    "create_specialized_coach_context",
    # Memory
    "MemoryType",
    "MemoryPriority",
    "Memory",
    "UserPreference",
    "InteractionSummary",
    "PersonalizationData",
    "MemoryContext",
    "memory_to_state",
    "state_to_memory",
    "create_memory_context",
] 