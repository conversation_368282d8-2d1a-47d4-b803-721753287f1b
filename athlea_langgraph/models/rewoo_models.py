"""
ReWOO (Reasoning Without Observation) Domain Models

This module contains Pydantic models for ReWOO functionality,
providing runtime validation and business logic encapsulation.
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime


class ReWOOStep(BaseModel):
    """Individual step in ReWOO execution plan."""
    
    step_id: str
    action: str
    dependencies: List[str] = Field(default_factory=list)
    agent: str
    expected_output: str
    parallel_group: Optional[int] = None
    
    def can_execute(self, completed_steps: List[str]) -> bool:
        """Check if this step can be executed based on dependencies."""
        return all(dep in completed_steps for dep in self.dependencies)


class ReWOOPlan(BaseModel):
    """Complete ReWOO execution plan."""
    
    query: str
    steps: List[ReWOOStep]
    complexity_score: float = Field(ge=0.0, le=1.0)
    created_at: datetime = Field(default_factory=datetime.now)
    domains_involved: List[str] = Field(default_factory=list)
    
    def is_complex(self) -> bool:
        """Determine if this is a complex query."""
        return self.complexity_score > 0.7
    
    def is_simple(self) -> bool:
        """Determine if this is a simple query."""
        return self.complexity_score < 0.3
    
    def get_parallel_groups(self) -> List[List[ReWOOStep]]:
        """Group steps that can be executed in parallel."""
        groups: Dict[int, List[ReWOOStep]] = {}
        
        # First, assign parallel groups if not already assigned
        if not any(step.parallel_group is not None for step in self.steps):
            self._assign_parallel_groups()
        
        # Group by parallel_group
        for step in self.steps:
            group_id = step.parallel_group or 0
            if group_id not in groups:
                groups[group_id] = []
            groups[group_id].append(step)
        
        # Return sorted groups
        return [groups[i] for i in sorted(groups.keys())]
    
    def _assign_parallel_groups(self):
        """Assign parallel groups based on dependencies."""
        completed = set()
        group_id = 0
        
        while len(completed) < len(self.steps):
            current_group = []
            
            for step in self.steps:
                if step.step_id not in completed and step.can_execute(list(completed)):
                    step.parallel_group = group_id
                    current_group.append(step)
            
            completed.update(s.step_id for s in current_group)
            group_id += 1


class ReWOOWorkerResult(BaseModel):
    """Result from a ReWOO worker execution."""
    
    step_id: str
    agent: str
    result: str
    execution_time: float
    success: bool = True
    error: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    def is_successful(self) -> bool:
        """Check if the execution was successful."""
        return self.success and self.error is None


class ReWOOSynthesis(BaseModel):
    """Synthesis information for ReWOO results."""
    
    reasoning: str
    final_response: str
    domains_addressed: List[str]
    confidence_score: float = Field(ge=0.0, le=1.0)
    synthesis_time: float


class ReWOOContext(BaseModel):
    """Complete ReWOO execution context."""
    
    plan: ReWOOPlan
    worker_results: Dict[str, ReWOOWorkerResult] = Field(default_factory=dict)
    synthesis: Optional[ReWOOSynthesis] = None
    total_execution_time: Optional[float] = None
    planning_time: Optional[float] = None
    worker_execution_time: Optional[float] = None
    
    def add_worker_result(self, result: ReWOOWorkerResult):
        """Add a worker result to the context."""
        self.worker_results[result.step_id] = result
    
    def get_successful_results(self) -> List[ReWOOWorkerResult]:
        """Get all successful worker results."""
        return [r for r in self.worker_results.values() if r.is_successful()]
    
    def get_failed_results(self) -> List[ReWOOWorkerResult]:
        """Get all failed worker results."""
        return [r for r in self.worker_results.values() if not r.is_successful()]
    
    def is_complete(self) -> bool:
        """Check if all planned steps have been executed."""
        return len(self.worker_results) == len(self.plan.steps)
    
    def get_completion_percentage(self) -> float:
        """Get the percentage of completed steps."""
        if not self.plan.steps:
            return 100.0
        return (len(self.worker_results) / len(self.plan.steps)) * 100.0


class ReWOOConfig(BaseModel):
    """Configuration for ReWOO execution."""
    
    enable_parallel_execution: bool = True
    max_parallel_workers: int = 5
    timeout_seconds: int = 300
    complexity_threshold_simple: float = 0.3
    complexity_threshold_complex: float = 0.7
    enable_reflection: bool = True
    max_retries: int = 3


# Helper functions for state integration
def store_rewoo_context(state: Dict[str, Any], context: ReWOOContext) -> None:
    """Store ReWOO context in state's specialist_responses."""
    if "specialist_responses" not in state:
        state["specialist_responses"] = {}
    
    if "rewoo" not in state["specialist_responses"]:
        state["specialist_responses"]["rewoo"] = {}
    
    state["specialist_responses"]["rewoo"]["context"] = context.model_dump()


def get_rewoo_context(state: Dict[str, Any]) -> Optional[ReWOOContext]:
    """Retrieve ReWOO context from state."""
    specialist_responses = state.get("specialist_responses", {})
    rewoo_data = specialist_responses.get("rewoo", {})
    context_data = rewoo_data.get("context")
    
    if context_data:
        return ReWOOContext.model_validate(context_data)
    
    return None


def store_rewoo_plan(state: Dict[str, Any], plan: ReWOOPlan) -> None:
    """Store ReWOO plan in state."""
    if "specialist_responses" not in state:
        state["specialist_responses"] = {}
    
    if "rewoo" not in state["specialist_responses"]:
        state["specialist_responses"]["rewoo"] = {}
    
    state["specialist_responses"]["rewoo"]["plan"] = plan.model_dump()


def get_rewoo_plan(state: Dict[str, Any]) -> Optional[ReWOOPlan]:
    """Retrieve ReWOO plan from state."""
    specialist_responses = state.get("specialist_responses", {})
    rewoo_data = specialist_responses.get("rewoo", {})
    plan_data = rewoo_data.get("plan")
    
    if plan_data:
        return ReWOOPlan.model_validate(plan_data)
    
    return None
