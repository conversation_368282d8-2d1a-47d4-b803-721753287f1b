"""
Reflection domain models for the hybrid state pattern.

These models encapsulate reflection logic that was previously in ReflectionState,
providing runtime validation, business logic, and type safety while storing
data in AgentState's specialist_responses field.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, field_validator


class ReflectionPriority(str, Enum):
    """Priority levels for reflection improvements."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class SafetyValidation(BaseModel):
    """Safety validation results for fitness coaching."""
    
    injury_risk_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Risk score from 0-1")
    form_safety_validated: bool = False
    contraindications_checked: bool = False
    progressive_overload_safe: bool = False
    safety_warnings: List[str] = Field(default_factory=list)
    safety_recommendations: List[str] = Field(default_factory=list)
    
    def is_safe(self, threshold: float = 0.8) -> bool:
        """Check if the validation passes safety threshold."""
        if self.injury_risk_score is None:
            return False
        return (
            self.injury_risk_score <= (1 - threshold) and
            self.form_safety_validated and
            self.contraindications_checked and
            self.progressive_overload_safe
        )
    
    def merge_warnings(self, new_warnings: List[str]) -> None:
        """Merge new warnings, avoiding duplicates."""
        self.safety_warnings = list(set(self.safety_warnings + new_warnings))
    
    def merge_recommendations(self, new_recommendations: List[str]) -> None:
        """Merge new recommendations, avoiding duplicates."""
        self.safety_recommendations = list(set(self.safety_recommendations + new_recommendations))


class ReflectionIteration(BaseModel):
    """Single iteration of reflection with feedback and improvements."""
    
    iteration_number: int
    timestamp: datetime = Field(default_factory=datetime.now)
    feedback: str
    improvement_areas: List[str] = Field(default_factory=list)
    quality_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    changes_made: List[str] = Field(default_factory=list)
    
    def has_improvements(self) -> bool:
        """Check if this iteration resulted in improvements."""
        return len(self.changes_made) > 0


class ReflectionCriteria(BaseModel):
    """Criteria for evaluating responses during reflection."""
    
    name: str
    weight: float = Field(1.0, ge=0.0, le=1.0)
    description: Optional[str] = None
    evaluation_prompt: Optional[str] = None
    
    @field_validator('name')
    def validate_name(cls, v):
        """Ensure criteria name is not empty."""
        if not v.strip():
            raise ValueError("Criteria name cannot be empty")
        return v.strip()


class ReflectionMetadata(BaseModel):
    """Metadata for tracking reflection iterations and quality."""
    
    reflection_count: int = 0
    max_reflections: int = 2
    reflection_feedback: Optional[str] = None
    safety_validated: bool = False
    quality_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    improvement_areas: List[str] = Field(default_factory=list)
    reflection_history: List[ReflectionIteration] = Field(default_factory=list)
    
    def can_reflect(self) -> bool:
        """Check if more reflections are allowed."""
        return self.reflection_count < self.max_reflections
    
    def add_iteration(self, feedback: str, improvements: List[str], 
                     quality_score: Optional[float] = None,
                     changes_made: Optional[List[str]] = None) -> None:
        """Add a new reflection iteration."""
        self.reflection_count += 1
        iteration = ReflectionIteration(
            iteration_number=self.reflection_count,
            feedback=feedback,
            improvement_areas=improvements,
            quality_score=quality_score,
            changes_made=changes_made or []
        )
        self.reflection_history.append(iteration)
        self.reflection_feedback = feedback
        self.improvement_areas = improvements
        if quality_score is not None:
            self.quality_score = quality_score


class ReflectionConfig(BaseModel):
    """Configuration for reflection behavior."""
    
    max_reflections: int = Field(2, ge=1, le=5)
    safety_threshold: float = Field(0.8, ge=0.0, le=1.0)
    quality_threshold: float = Field(0.7, ge=0.0, le=1.0)
    enable_safety_validation: bool = True
    reflection_criteria: List[ReflectionCriteria] = Field(default_factory=list)
    timeout_seconds: int = Field(30, ge=1)
    enable_caching: bool = True
    log_reflections: bool = True
    
    def __init__(self, **data):
        super().__init__(**data)
        if not self.reflection_criteria:
            self.reflection_criteria = self._default_criteria()
    
    def _default_criteria(self) -> List[ReflectionCriteria]:
        """Get default reflection criteria."""
        return [
            ReflectionCriteria(name="accuracy", weight=1.0, 
                             description="Technical accuracy and correctness"),
            ReflectionCriteria(name="safety", weight=1.2,
                             description="Safety considerations and injury prevention"),
            ReflectionCriteria(name="completeness", weight=0.9,
                             description="Comprehensive coverage of the topic"),
            ReflectionCriteria(name="clarity", weight=0.8,
                             description="Clear and understandable communication"),
            ReflectionCriteria(name="actionability", weight=1.0,
                             description="Practical and implementable advice")
        ]


class QualityAssessment(BaseModel):
    """Detailed quality assessment of a response."""
    
    overall_score: float = Field(ge=0.0, le=1.0)
    criteria_scores: Dict[str, float] = Field(default_factory=dict)
    strengths: List[str] = Field(default_factory=list)
    weaknesses: List[str] = Field(default_factory=list)
    suggestions: List[str] = Field(default_factory=list)
    
    def meets_threshold(self, threshold: float) -> bool:
        """Check if quality meets the specified threshold."""
        return self.overall_score >= threshold
    
    def get_priority_improvements(self, max_items: int = 3) -> List[str]:
        """Get the most important improvements to make."""
        # Prioritize based on lowest scores
        sorted_criteria = sorted(
            self.criteria_scores.items(), 
            key=lambda x: x[1]
        )
        improvements = []
        for criteria, score in sorted_criteria[:max_items]:
            if score < 0.7:  # Focus on criteria below threshold
                improvements.append(f"Improve {criteria} (current score: {score:.2f})")
        return improvements


class ReflectionContext(BaseModel):
    """Complete reflection context for a coaching response."""
    
    # Original response information
    original_response: str
    current_reflection_agent: Optional[str] = None
    reflection_prompt: Optional[str] = None
    
    # Reflection tracking
    metadata: ReflectionMetadata = Field(default_factory=ReflectionMetadata)
    config: ReflectionConfig = Field(default_factory=ReflectionConfig)
    
    # Safety and quality
    safety_validation: SafetyValidation = Field(default_factory=SafetyValidation)
    quality_before: Optional[QualityAssessment] = None
    quality_after: Optional[QualityAssessment] = None
    
    # Current state
    is_reflecting: bool = False
    reflection_enabled: bool = True
    
    def should_reflect(self) -> bool:
        """Determine if reflection should be performed."""
        if not self.reflection_enabled or not self.metadata.can_reflect():
            return False
        
        # Check if quality is below threshold
        if self.quality_before and not self.quality_before.meets_threshold(
            self.config.quality_threshold
        ):
            return True
        
        # Check if safety validation failed
        if (self.config.enable_safety_validation and 
            not self.safety_validation.is_safe(self.config.safety_threshold)):
            return True
        
        return False
    
    def get_improvement_prompt(self) -> str:
        """Generate a prompt for improving the response."""
        improvements = []
        
        if self.quality_before:
            improvements.extend(self.quality_before.get_priority_improvements())
        
        if self.safety_validation.safety_warnings:
            improvements.append(f"Address safety concerns: {', '.join(self.safety_validation.safety_warnings)}")
        
        if not improvements:
            improvements = self.metadata.improvement_areas
        
        return f"Please improve the response by addressing: {'; '.join(improvements)}"
    
    def calculate_improvement_score(self) -> Optional[float]:
        """Calculate the improvement score between before and after quality."""
        if not self.quality_before or not self.quality_after:
            return None
        return self.quality_after.overall_score - self.quality_before.overall_score


# Helper functions for state integration
def reflection_to_state(context: ReflectionContext) -> Dict[str, Any]:
    """Convert ReflectionContext to state-compatible dictionary."""
    return {
        "reflection_context": context.model_dump(),
        "reflection_enabled": context.reflection_enabled,
        "original_response": context.original_response,
        "current_reflection_agent": context.current_reflection_agent,
        "reflection_prompt": context.reflection_prompt,
        "safety_validated": context.safety_validation.is_safe(),
        "quality_score": context.quality_after.overall_score if context.quality_after else None,
    }


def state_to_reflection(state_data: Dict[str, Any]) -> ReflectionContext:
    """Reconstruct ReflectionContext from state data."""
    if "reflection_context" in state_data:
        return ReflectionContext(**state_data["reflection_context"])
    
    # Build from individual fields if needed
    context = ReflectionContext(
        original_response=state_data.get("original_response", ""),
        current_reflection_agent=state_data.get("current_reflection_agent"),
        reflection_prompt=state_data.get("reflection_prompt"),
    )
    
    if "quality_score" in state_data and state_data["quality_score"] is not None:
        context.quality_after = QualityAssessment(
            overall_score=state_data["quality_score"],
            criteria_scores={},
            strengths=[],
            weaknesses=[],
            suggestions=[]
        )
    
    return context


def create_reflection_context(
    original_response: str,
    coach_domain: Optional[str] = None,
    config: Optional[ReflectionConfig] = None
) -> ReflectionContext:
    """Create a new reflection context for a coaching response."""
    return ReflectionContext(
        original_response=original_response,
        current_reflection_agent=coach_domain,
        config=config or ReflectionConfig(),
        reflection_enabled=True
    )
