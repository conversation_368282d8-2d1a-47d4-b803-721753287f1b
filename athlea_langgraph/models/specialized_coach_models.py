"""
Specialized coach domain models for the hybrid state pattern.

These models encapsulate the logic that was previously in SpecializedCoachState,
providing complexity assessment, execution tracking, and performance monitoring
while storing data in AgentState's specialist_responses field.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, field_validator


class ComplexityLevel(str, Enum):
    """Query complexity levels."""
    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"


class ComplexityAssessmentMethod(str, Enum):
    """Methods used for complexity assessment."""
    LLM_BASED = "llm_based"
    FALLBACK = "fallback"
    RULE_BASED = "rule_based"


class ExecutionPath(str, Enum):
    """Execution paths for query processing."""
    SIMPLE = "simple"
    COMPLEX = "complex"
    COMPLEX_FALLBACK = "complex_fallback"


class ComplexityAssessment(BaseModel):
    """Complexity assessment for a coaching query."""
    
    level: ComplexityLevel
    score: float = Field(ge=0.0, le=1.0)
    reasoning: str
    assessment_time: float = Field(ge=0.0, description="Time in seconds")
    assessment_method: ComplexityAssessmentMethod
    factors: List[str] = Field(default_factory=list, description="Factors contributing to complexity")
    
    def is_simple(self, threshold: float = 0.3) -> bool:
        """Check if query is simple based on threshold."""
        return self.score <= threshold
    
    def is_complex(self, threshold: float = 0.7) -> bool:
        """Check if query is complex based on threshold."""
        return self.score >= threshold
    
    def get_execution_path(self, simple_threshold: float = 0.3, 
                          complex_threshold: float = 0.7) -> ExecutionPath:
        """Determine execution path based on complexity."""
        if self.score <= simple_threshold:
            return ExecutionPath.SIMPLE
        elif self.score >= complex_threshold:
            return ExecutionPath.COMPLEX
        else:
            return ExecutionPath.COMPLEX  # Default to complex for moderate


class SimpleExecutionResult(BaseModel):
    """Results from simple execution path."""
    
    response: str
    execution_time: float = Field(ge=0.0)
    success: bool = True
    tokens_used: Optional[int] = None
    model_used: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for state storage."""
        return self.model_dump(exclude_none=True)


class WorkerResult(BaseModel):
    """Result from a single ReWOO worker execution."""
    
    worker_id: str
    task: str
    result: Any
    success: bool = True
    execution_time: float = Field(ge=0.0)
    error_message: Optional[str] = None
    retry_count: int = 0
    
    def is_critical(self) -> bool:
        """Check if this worker''s failure is critical."""
        # Could be enhanced with actual criticality logic
        return not self.success and self.retry_count >= 2


class ComplexExecutionResult(BaseModel):
    """Results from complex ReWOO execution path."""
    
    rewoo_plan: Dict[str, Any]
    worker_results: List[WorkerResult] = Field(default_factory=list)
    synthesis_result: Optional[str] = None
    execution_time: float = Field(ge=0.0)
    success: bool = False
    workers_executed: int = 0
    successful_workers: int = 0
    parallel_execution: bool = False
    
    def calculate_success_rate(self) -> float:
        """Calculate worker success rate."""
        if self.workers_executed == 0:
            return 0.0
        return self.successful_workers / self.workers_executed
    
    def get_failed_workers(self) -> List[WorkerResult]:
        """Get list of failed worker results."""
        return [w for w in self.worker_results if not w.success]
    
    def add_worker_result(self, result: WorkerResult) -> None:
        """Add a worker result and update counters."""
        self.worker_results.append(result)
        self.workers_executed += 1
        if result.success:
            self.successful_workers += 1


class ExecutionStep(BaseModel):
    """Single step in execution trace."""
    
    step_name: str
    timestamp: datetime = Field(default_factory=datetime.now)
    duration: Optional[float] = None
    data: Dict[str, Any] = Field(default_factory=dict)
    success: bool = True
    error: Optional[str] = None
    
    def to_log_entry(self) -> Dict[str, Any]:
        """Convert to log entry format."""
        return {
            "step": self.step_name,
            "timestamp": self.timestamp.isoformat(),
            "duration": self.duration,
            "success": self.success,
            "error": self.error,
            "data": self.data
        }


class PerformanceMetrics(BaseModel):
    """Performance metrics for coach execution."""
    
    total_execution_time: float = Field(ge=0.0)
    llm_calls_made: int = Field(ge=0)
    tokens_used: Optional[int] = Field(None, ge=0)
    execution_efficiency: Optional[float] = Field(None, ge=0.0, le=1.0)
    complexity_accuracy: Optional[float] = Field(None, ge=0.0, le=1.0)
    worker_success_rate: Optional[float] = Field(None, ge=0.0, le=1.0)
    response_quality: Optional[float] = Field(None, ge=0.0, le=1.0)
    
    def calculate_efficiency(self, complexity_score: float, 
                           execution_path: ExecutionPath) -> None:
        """Calculate execution efficiency based on time vs complexity."""
        if self.total_execution_time == 0:
            self.execution_efficiency = 0.0
            return
            
        # Expected times based on complexity
        if execution_path == ExecutionPath.SIMPLE:
            expected_time = 5.0
        else:
            expected_time = 30.0 * complexity_score  # Scale with complexity
            
        self.execution_efficiency = min(expected_time / self.total_execution_time, 1.0)


class SpecializedCoachContext(BaseModel):
    """Complete context for specialized coach execution."""
    
    # Coach identification
    coach_domain: str  # strength, nutrition, cardio, etc.
    coach_specialization: Optional[str] = None
    
    # Complexity assessment
    complexity_assessment: Optional[ComplexityAssessment] = None
    
    # Execution tracking
    execution_path: Optional[ExecutionPath] = None
    routing_decision: Optional[str] = None
    
    # Results
    simple_result: Optional[SimpleExecutionResult] = None
    complex_result: Optional[ComplexExecutionResult] = None
    
    # Reflection integration
    reflection_applied: bool = False
    reflection_improvements: List[str] = Field(default_factory=list)
    reflection_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    quality_assessment: Optional[Dict[str, Any]] = None
    
    # Performance
    metrics: PerformanceMetrics = Field(default_factory=PerformanceMetrics)
    
    # Debug and monitoring
    execution_trace: List[ExecutionStep] = Field(default_factory=list)
    error_log: List[str] = Field(default_factory=list)
    warning_log: List[str] = Field(default_factory=list)
    
    # Configuration
    rewoo_enabled: bool = True
    reflection_enabled: bool = True
    complexity_threshold_simple: float = 0.3
    complexity_threshold_complex: float = 0.7
    
    def log_step(self, step_name: str, data: Optional[Dict[str, Any]] = None,
                 duration: Optional[float] = None, success: bool = True,
                 error: Optional[str] = None) -> None:
        """Log an execution step."""
        step = ExecutionStep(
            step_name=step_name,
            data=data or {},
            duration=duration,
            success=success,
            error=error
        )
        self.execution_trace.append(step)
        if error:
            self.error_log.append(f"{step_name}: {error}")
    
    def log_warning(self, warning: str) -> None:
        """Log a warning message."""
        self.warning_log.append(warning)
    
    def should_use_rewoo(self) -> bool:
        """Determine if ReWOO should be used."""
        if not self.rewoo_enabled or not self.complexity_assessment:
            return False
        return not self.complexity_assessment.is_simple(self.complexity_threshold_simple)
    
    def get_final_response(self) -> Optional[str]:
        """Get the final response from either simple or complex execution."""
        if self.simple_result and self.simple_result.success:
            return self.simple_result.response
        elif self.complex_result and self.complex_result.synthesis_result:
            return self.complex_result.synthesis_result
        return None
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """Get execution summary for monitoring."""
        return {
            "coach_domain": self.coach_domain,
            "complexity_level": self.complexity_assessment.level.value if self.complexity_assessment else None,
            "complexity_score": self.complexity_assessment.score if self.complexity_assessment else None,
            "execution_path": self.execution_path.value if self.execution_path else None,
            "total_execution_time": self.metrics.total_execution_time,
            "complexity_assessment_time": self.complexity_assessment.assessment_time if self.complexity_assessment else None,
            "simple_execution_time": self.simple_result.execution_time if self.simple_result else None,
            "complex_execution_time": self.complex_result.execution_time if self.complex_result else None,
            "workers_executed": self.complex_result.workers_executed if self.complex_result else 0,
            "successful_workers": self.complex_result.successful_workers if self.complex_result else 0,
            "reflection_applied": self.reflection_applied,
            "llm_calls_made": self.metrics.llm_calls_made,
            "success": bool(self.get_final_response()),
            "errors": len(self.error_log),
            "warnings": len(self.warning_log),
        }


# Helper functions for state integration
def specialized_coach_to_state(context: SpecializedCoachContext) -> Dict[str, Any]:
    """Convert SpecializedCoachContext to state-compatible dictionary."""
    return {
        "specialized_coach_context": context.model_dump(),
        "coach_domain": context.coach_domain,
        "complexity_level": context.complexity_assessment.level.value if context.complexity_assessment else None,
        "complexity_score": context.complexity_assessment.score if context.complexity_assessment else None,
        "execution_path": context.execution_path.value if context.execution_path else None,
        "final_response": context.get_final_response(),
    }


def state_to_specialized_coach(state_data: Dict[str, Any]) -> SpecializedCoachContext:
    """Reconstruct SpecializedCoachContext from state data."""
    if "specialized_coach_context" in state_data:
        return SpecializedCoachContext(**state_data["specialized_coach_context"])
    
    # Build from individual fields if needed
    context = SpecializedCoachContext(
        coach_domain=state_data.get("coach_domain", "unknown"),
        rewoo_enabled=state_data.get("rewoo_enabled", True),
        reflection_enabled=state_data.get("reflection_enabled", True),
    )
    
    # Add complexity assessment if available
    if "complexity_score" in state_data and state_data["complexity_score"] is not None:
        context.complexity_assessment = ComplexityAssessment(
            level=ComplexityLevel(state_data.get("complexity_level", "moderate")),
            score=state_data["complexity_score"],
            reasoning=state_data.get("complexity_reasoning", ""),
            assessment_time=state_data.get("complexity_assessment_time", 0.0),
            assessment_method=ComplexityAssessmentMethod.LLM_BASED
        )
    
    return context


def create_specialized_coach_context(
    coach_domain: str,
    rewoo_enabled: bool = True,
    reflection_enabled: bool = True,
    **kwargs
) -> SpecializedCoachContext:
    """Create a new specialized coach context."""
    return SpecializedCoachContext(
        coach_domain=coach_domain,
        rewoo_enabled=rewoo_enabled,
        reflection_enabled=reflection_enabled,
        **kwargs
    )
