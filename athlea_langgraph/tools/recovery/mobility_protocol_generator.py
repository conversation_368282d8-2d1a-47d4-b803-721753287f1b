"""
Mobility & Stretching Protocol Generator

Generates personalized mobility and stretching routines using the ExerciseDB API,
targeting specific body areas and activities with evidence-based protocols.
"""

import json
import logging
import os
from datetime import datetime
from typing import Any, Dict, List, Optional

import httpx
from langchain_core.tools import BaseTool as LangChainBaseTool, tool
from pydantic import BaseModel, Field as <PERSON>ydanticField

from ...config import config
from ..base_tool import BaseDomainTool
from ..schemas.recovery_schemas import (
    ActivityType,
    MobilityArea,
    MobilityExercise,
    MobilityProtocolInput,
    MobilityProtocolOutput,
    RecoveryGoal,
)

logger = logging.getLogger(__name__)


class MobilityProtocolGenerator(BaseDomainTool):
    """
    Generates personalized mobility and stretching protocols using ExerciseDB API.

    Provides activity-specific mobility routines, targeting areas based on sport/activity
    needs, with progressive difficulty and equipment-specific variations.
    """

    domain: str = "recovery"
    name: str = "mobility_protocol_generator"
    description: str = (
        "Generate personalized mobility and stretching protocols based on activity type, "
        "target areas, and recovery goals using comprehensive exercise database"
    )

    def __init__(self):
        super().__init__()
        self._exercise_cache = {}
        self.api_key = getattr(config, "exercisedb", {}).get("api_key") or os.getenv(
            "EXERCISEDB_API_KEY"
        )
        self.base_url = "https://exercisedb.p.rapidapi.com"
        self.headers = {
            "X-RapidAPI-Key": self.api_key,
            "X-RapidAPI-Host": "exercisedb.p.rapidapi.com",
        }

        # Body part mapping for mobility focus
        self.mobility_body_mapping = {
            MobilityArea.NECK: ["neck"],
            MobilityArea.SHOULDERS: ["shoulders"],
            MobilityArea.THORACIC_SPINE: ["back"],
            MobilityArea.LUMBAR_SPINE: ["waist"],
            MobilityArea.HIPS: ["upper legs"],
            MobilityArea.KNEES: ["lower legs"],
            MobilityArea.ANKLES: ["lower legs"],
            MobilityArea.FULL_BODY: ["back", "shoulders", "upper legs", "lower legs"],
        }

        # Activity-specific mobility priorities
        self.activity_mobility_priorities = {
            ActivityType.RUNNING: {
                "primary": [
                    MobilityArea.HIPS,
                    MobilityArea.ANKLES,
                    MobilityArea.LUMBAR_SPINE,
                ],
                "secondary": [MobilityArea.KNEES, MobilityArea.THORACIC_SPINE],
                "common_tight_muscles": [
                    "hamstrings",
                    "calves",
                    "hip flexors",
                    "glutes",
                ],
            },
            ActivityType.CYCLING: {
                "primary": [
                    MobilityArea.HIPS,
                    MobilityArea.THORACIC_SPINE,
                    MobilityArea.NECK,
                ],
                "secondary": [MobilityArea.SHOULDERS, MobilityArea.ANKLES],
                "common_tight_muscles": ["hip flexors", "upper back", "neck", "quads"],
            },
            ActivityType.STRENGTH_TRAINING: {
                "primary": [
                    MobilityArea.SHOULDERS,
                    MobilityArea.THORACIC_SPINE,
                    MobilityArea.HIPS,
                ],
                "secondary": [MobilityArea.ANKLES, MobilityArea.NECK],
                "common_tight_muscles": ["chest", "lats", "hip flexors", "calves"],
            },
            ActivityType.DESK_WORK: {
                "primary": [
                    MobilityArea.NECK,
                    MobilityArea.THORACIC_SPINE,
                    MobilityArea.HIPS,
                ],
                "secondary": [MobilityArea.SHOULDERS, MobilityArea.LUMBAR_SPINE],
                "common_tight_muscles": ["neck", "upper back", "chest", "hip flexors"],
            },
            ActivityType.SWIMMING: {
                "primary": [
                    MobilityArea.SHOULDERS,
                    MobilityArea.THORACIC_SPINE,
                    MobilityArea.ANKLES,
                ],
                "secondary": [MobilityArea.HIPS, MobilityArea.NECK],
                "common_tight_muscles": ["lats", "chest", "shoulders", "ankle"],
            },
        }

    def get_tool_description(self) -> str:
        """Return a detailed description of the tool for AI agents."""
        return f"""
{self.description}

Domain: {self.domain}
Tool: {self.name}

This tool generates personalized mobility and stretching protocols by:
- Analyzing activity-specific mobility needs
- Targeting key body areas based on sport/activity requirements  
- Providing progressive exercise recommendations
- Including equipment-specific variations
- Offering evidence-based recovery protocols

Supported Activities: Running, Cycling, Strength Training, Swimming, Desk Work
Target Areas: Neck, Shoulders, Spine, Hips, Knees, Ankles, Full Body
Equipment Options: Body weight, resistance bands, stability balls
Session Durations: 5-60 minutes
Experience Levels: Beginner, Intermediate, Advanced

Output includes structured protocols with warm-up, main exercises, and cool-down phases.
"""

    async def _make_api_request(
        self, endpoint: str, params: Optional[Dict] = None
    ) -> List[Dict[str, Any]]:
        """Make HTTP request to ExerciseDB API."""
        if not self.api_key:
            logger.warning("ExerciseDB API key not found. Using fallback data.")
            return self._get_fallback_mobility_exercises()

        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/{endpoint}",
                    headers=self.headers,
                    params=params or {},
                    timeout=10.0,
                )
                response.raise_for_status()
                data = response.json()
                return data if isinstance(data, list) else [data]
        except Exception as e:
            logger.error(f"ExerciseDB API request failed: {e}")
            return self._get_fallback_mobility_exercises()

    async def _get_stretching_exercises_by_body_part(
        self, body_part: str
    ) -> List[Dict[str, Any]]:
        """Get stretching exercises for specific body part."""
        # First try to get stretching exercises
        stretching_exercises = []

        # Get exercises by body part and filter for stretching or body weight
        endpoint = f"exercises/bodyPart/{body_part}"
        all_exercises = await self._make_api_request(endpoint)

        # Filter for mobility-friendly exercises
        for exercise in all_exercises:
            equipment = exercise.get("equipment", "").lower()
            name = exercise.get("name", "").lower()

            # Include body weight, stretching, and mobility-friendly equipment
            if any(
                keyword in equipment
                for keyword in ["body weight", "band", "stability ball"]
            ) or any(
                keyword in name
                for keyword in ["stretch", "mobility", "twist", "rotation", "bend"]
            ):
                stretching_exercises.append(exercise)

        return stretching_exercises[:10]  # Limit to 10 exercises per body part

    def _normalize_exercise_for_mobility(
        self, exercise_data: Dict[str, Any], duration: str = "30 seconds"
    ) -> MobilityExercise:
        """Convert ExerciseDB exercise to mobility exercise format."""
        return MobilityExercise(
            name=exercise_data.get("name", "").title(),
            target_muscle=exercise_data.get("target", ""),
            equipment=exercise_data.get("equipment", "body weight"),
            duration=duration,
            instructions=exercise_data.get(
                "instructions", ["Hold position and breathe deeply"]
            ),
            benefits=[
                f"Improves {exercise_data.get('target', '')} flexibility",
                "Enhances range of motion",
                "Reduces muscle tension",
            ],
            difficulty="beginner",  # Most mobility work starts at beginner level
            gif_url=exercise_data.get("gifUrl"),
        )

    def _get_session_structure(self, duration: int) -> Dict[str, int]:
        """Calculate session structure based on total duration."""
        if duration <= 10:
            return {"warm_up": 2, "main": 6, "cool_down": 2}
        elif duration <= 20:
            return {"warm_up": 3, "main": 14, "cool_down": 3}
        elif duration <= 30:
            return {"warm_up": 5, "main": 20, "cool_down": 5}
        else:
            return {"warm_up": 7, "main": duration - 14, "cool_down": 7}

    def _generate_protocol_name(
        self, activity_type: ActivityType, target_areas: List[MobilityArea]
    ) -> str:
        """Generate descriptive protocol name."""
        area_names = [area.value.replace("_", " ").title() for area in target_areas[:2]]
        activity_name = activity_type.value.replace("_", " ").title()

        if len(area_names) == 1:
            return f"{activity_name} {area_names[0]} Mobility Protocol"
        else:
            return f"{activity_name} Multi-Area Mobility Protocol"

    def _get_progression_tips(
        self, recovery_goals: List[RecoveryGoal], activity_type: ActivityType
    ) -> List[str]:
        """Generate progression tips based on goals and activity."""
        base_tips = [
            "Start with 2-3 sessions per week and gradually increase frequency",
            "Hold each stretch for at least 30 seconds for optimal benefits",
            "Focus on controlled breathing throughout each movement",
            "Listen to your body and avoid forcing movements",
        ]

        goal_specific_tips = {
            RecoveryGoal.FLEXIBILITY: [
                "Increase hold time progressively (30s → 45s → 60s)",
                "Add gentle end-range movements for dynamic flexibility",
            ],
            RecoveryGoal.INJURY_PREVENTION: [
                "Perform these exercises before and after training sessions",
                "Pay extra attention to historically tight or injured areas",
            ],
            RecoveryGoal.PERFORMANCE_ENHANCEMENT: [
                "Incorporate sport-specific movement patterns",
                "Progress to dynamic movements as flexibility improves",
            ],
        }

        for goal in recovery_goals:
            if goal in goal_specific_tips:
                base_tips.extend(goal_specific_tips[goal])

        return base_tips[:6]  # Limit to 6 tips

    def _get_frequency_recommendation(
        self, recovery_goals: List[RecoveryGoal], activity_type: ActivityType
    ) -> str:
        """Get frequency recommendation based on goals and activity."""
        if RecoveryGoal.INJURY_PREVENTION in recovery_goals:
            return "Daily, with focus sessions 3-4x per week after training"
        elif RecoveryGoal.PAIN_RELIEF in recovery_goals:
            return "2x daily until pain subsides, then maintain with daily sessions"
        elif activity_type in [ActivityType.DESK_WORK]:
            return "2-3 short sessions daily (5-10 minutes each)"
        else:
            return "4-6 sessions per week, ideally post-workout or on rest days"

    def _get_fallback_mobility_exercises(self) -> List[Dict[str, Any]]:
        """Fallback mobility exercises when API is unavailable."""
        return [
            {
                "name": "Cat-Cow Stretch",
                "target": "spine",
                "equipment": "body weight",
                "instructions": [
                    "Start on hands and knees in tabletop position",
                    "Arch back and look up (cow pose)",
                    "Round back and tuck chin (cat pose)",
                    "Repeat slowly and smoothly",
                ],
                "gifUrl": None,
            },
            {
                "name": "Hip Flexor Stretch",
                "target": "hip flexors",
                "equipment": "body weight",
                "instructions": [
                    "Step into a lunge position",
                    "Lower back knee to ground",
                    "Push hips forward gently",
                    "Hold and breathe deeply",
                ],
                "gifUrl": None,
            },
            {
                "name": "Shoulder Cross-Body Stretch",
                "target": "shoulders",
                "equipment": "body weight",
                "instructions": [
                    "Bring arm across chest",
                    "Use opposite hand to gently pull arm closer",
                    "Keep shoulders relaxed",
                    "Hold for 30 seconds each side",
                ],
                "gifUrl": None,
            },
        ]

    async def generate_mobility_protocol(
        self, protocol_input: MobilityProtocolInput
    ) -> MobilityProtocolOutput:
        """Generate personalized mobility protocol."""
        try:
            # Get session structure
            structure = self._get_session_structure(protocol_input.session_duration)

            # Collect exercises for target areas
            all_exercises = []
            for area in protocol_input.target_areas:
                body_parts = self.mobility_body_mapping.get(area, [area.value])
                for body_part in body_parts:
                    exercises = await self._get_stretching_exercises_by_body_part(
                        body_part
                    )
                    all_exercises.extend(exercises)

            # Remove duplicates and limit
            unique_exercises = []
            seen_names = set()
            for exercise in all_exercises:
                name = exercise.get("name", "")
                if name not in seen_names:
                    unique_exercises.append(exercise)
                    seen_names.add(name)

            # Organize exercises by phase
            total_exercises_needed = (
                structure["warm_up"] + structure["main"] + structure["cool_down"]
            ) // 3  # Rough estimate

            if len(unique_exercises) < total_exercises_needed:
                # Add fallback exercises if needed
                fallback_exercises = self._get_fallback_mobility_exercises()
                unique_exercises.extend(fallback_exercises)

            # Create exercise phases
            warm_up_exercises = []
            main_exercises = []
            cool_down_exercises = []

            # Distribute exercises across phases
            exercises_per_phase = max(1, len(unique_exercises) // 3)

            for i, exercise in enumerate(unique_exercises[: exercises_per_phase * 3]):
                if i < exercises_per_phase:
                    duration = "15-20 seconds"
                    warm_up_exercises.append(
                        self._normalize_exercise_for_mobility(exercise, duration)
                    )
                elif i < exercises_per_phase * 2:
                    duration = "30-45 seconds"
                    main_exercises.append(
                        self._normalize_exercise_for_mobility(exercise, duration)
                    )
                else:
                    duration = "45-60 seconds"
                    cool_down_exercises.append(
                        self._normalize_exercise_for_mobility(exercise, duration)
                    )

            # Ensure minimum exercises per phase
            if not warm_up_exercises:
                warm_up_exercises = [
                    self._normalize_exercise_for_mobility(
                        self._get_fallback_mobility_exercises()[0], "15-20 seconds"
                    )
                ]
            if not main_exercises:
                main_exercises = [
                    self._normalize_exercise_for_mobility(
                        self._get_fallback_mobility_exercises()[1], "30-45 seconds"
                    )
                ]
            if not cool_down_exercises:
                cool_down_exercises = [
                    self._normalize_exercise_for_mobility(
                        self._get_fallback_mobility_exercises()[2], "45-60 seconds"
                    )
                ]

            return MobilityProtocolOutput(
                protocol_name=self._generate_protocol_name(
                    protocol_input.activity_type, protocol_input.target_areas
                ),
                total_duration=protocol_input.session_duration,
                target_areas=[area.value for area in protocol_input.target_areas],
                warm_up_exercises=warm_up_exercises,
                main_exercises=main_exercises,
                cool_down_exercises=cool_down_exercises,
                progression_tips=self._get_progression_tips(
                    protocol_input.recovery_goals, protocol_input.activity_type
                ),
                frequency_recommendation=self._get_frequency_recommendation(
                    protocol_input.recovery_goals, protocol_input.activity_type
                ),
            )

        except Exception as e:
            logger.error(f"Error generating mobility protocol: {e}")
            raise


# Create a standalone instance of the generator
mobility_protocol_generator = MobilityProtocolGenerator()


@tool(args_schema=MobilityProtocolInput)
async def generate_mobility_protocol(
    target_areas: List[MobilityArea],
    activity_type: ActivityType,
    session_duration: int = 15,
    experience_level: str = "intermediate",
    recovery_goals: List[RecoveryGoal] = [RecoveryGoal.FLEXIBILITY],
    available_equipment: List[str] = ["body weight"],
    time_of_day: Optional[str] = "morning",
) -> str:
    """
    Generates a personalized mobility protocol based on user needs. Use this tool when a user mentions tightness, mobility issues, or asks for stretching or mobility routines for specific body parts.
    """
    try:
        input_data = MobilityProtocolInput(
            target_areas=target_areas,
            activity_type=activity_type,
            session_duration=session_duration,
            experience_level=experience_level,
            recovery_goals=recovery_goals,
            available_equipment=available_equipment,
            time_of_day=time_of_day,
        )
        result = await mobility_protocol_generator.generate_mobility_protocol(
            input_data
        )
        return result.model_dump_json(indent=2)
    except Exception as e:
        logger.error(f"Error in generate_mobility_protocol tool: {e}")
        return f'{{"error": "Failed to generate mobility protocol: {str(e)}"}}'
