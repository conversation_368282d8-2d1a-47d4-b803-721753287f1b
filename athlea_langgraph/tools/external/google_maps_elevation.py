"""
Google Maps Elevation API Tool

Production-ready tool for getting elevation data with:
- Circuit breaker protection
- Input/output validation
- Exponential backoff retries
- Batch processing for multiple points
- Comprehensive error handling
"""

import asyncio
import logging
import os
from typing import Any, Dict, List, Optional
from urllib.parse import urlencode

import aiohttp
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from ..base_tool import HardenedTool, ToolError, ToolErrorType
from ..circuit_breaker import CircuitBreaker
from ..schemas.elevation_schemas import (
    ElevationInput,
    ElevationOutput,
    ElevationPoint,
    ElevationResult,
)

logger = logging.getLogger(__name__)


class GoogleMapsElevationToolInput(BaseModel):
    """Input schema for Google Maps Elevation tool."""

    mode: str = Field(
        description="Mode: 'point' for single location, 'path' for route elevation profile, 'locations' for multiple points"
    )
    lat: Optional[float] = Field(
        default=None, description="Latitude for single point mode"
    )
    lon: Optional[float] = Field(
        default=None, description="Longitude for single point mode"
    )
    points: Optional[List[Dict[str, float]]] = Field(
        default=None, description="List of lat/lon points for path or locations mode"
    )
    samples: Optional[int] = Field(
        default=None, description="Number of samples for path mode (optional)"
    )


class GoogleMapsElevationLangChainTool(BaseTool):
    """LangChain-compatible wrapper for GoogleMapsElevationTool."""

    name: str = "google_maps_elevation"
    description: str = """Get elevation data for locations and routes using Google Maps Elevation API.
    
    Modes available:
    - point: Get elevation for a single lat/lon coordinate
    - path: Get elevation profile for a route with multiple points
    - locations: Get elevation for multiple individual points
    
    Use this tool for route planning, analyzing terrain difficulty, and calculating elevation gain/loss for cycling and running routes."""

    args_schema: type[BaseModel] = GoogleMapsElevationToolInput
    elevation_tool: "GoogleMapsElevationTool" = Field(exclude=True)

    def __init__(self, elevation_tool: "GoogleMapsElevationTool"):
        super().__init__(elevation_tool=elevation_tool)

    async def _arun(self, **kwargs) -> str:
        """Async implementation of the tool."""
        try:
            # Convert points format if needed
            if kwargs.get("points"):
                points = [
                    ElevationPoint(lat=p["lat"], lon=p["lon"]) for p in kwargs["points"]
                ]
                kwargs["points"] = points

            result = await self.elevation_tool.execute(ElevationInput(**kwargs))
            return f"Elevation data retrieved: {result.model_dump_json()}"
        except Exception as e:
            return f"Error getting elevation data: {str(e)}"

    def _run(self, **kwargs) -> str:
        """Sync implementation of the tool."""
        import asyncio

        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                return f"Elevation lookup requires async execution. Error: Cannot run sync in async context."
            else:
                # Convert points format if needed
                if kwargs.get("points"):
                    points = [
                        ElevationPoint(lat=p["lat"], lon=p["lon"])
                        for p in kwargs["points"]
                    ]
                    kwargs["points"] = points

                result = loop.run_until_complete(
                    self.elevation_tool.execute(ElevationInput(**kwargs))
                )
                return f"Elevation data retrieved: {result.model_dump_json()}"
        except Exception as e:
            return f"Error getting elevation data: {str(e)}"


class GoogleMapsElevationTool(HardenedTool[ElevationInput, ElevationOutput]):
    """
    Google Maps Elevation API tool with comprehensive hardening.

    Features:
    - Single point, path, and batch location elevation lookup
    - Request batching for efficiency (up to 500 points)
    - Circuit breaker for API failure protection
    - Exponential backoff for rate limiting
    - Elevation gain/loss calculation for paths
    """

    # Declare these as Pydantic fields to avoid validation errors
    api_key: Optional[str] = Field(None)
    base_url: str = Field(default="https://maps.googleapis.com/maps/api/elevation/json")
    max_batch_size: int = Field(default=500)

    def __init__(
        self,
        api_key: Optional[str] = None,
        timeout_seconds: float = 10.0,
        max_retries: int = 3,
    ):
        """
        Initialize Google Maps Elevation tool.

        Args:
            api_key: Google Maps API key (defaults to GOOGLE_MAPS_API_KEY env var)
            timeout_seconds: Request timeout
            max_retries: Maximum retry attempts
        """
        # Get API key from centralized configuration if not provided
        if not api_key:
            try:
                from ...config import config

                api_key = getattr(config, "google_maps", {}).get(
                    "api_key"
                ) or os.getenv("GOOGLE_MAPS_API_KEY")
            except (ImportError, AttributeError):
                # Fallback to environment variable if config not available
                api_key = os.getenv("GOOGLE_MAPS_API_KEY")

        if not api_key:
            raise ValueError(
                "Google Maps API key required. Set GOOGLE_MAPS_API_KEY environment variable "
                "or pass api_key parameter."
            )

        # Initialize circuit breaker for Google Maps API
        circuit_breaker = CircuitBreaker(
            name="google_maps_elevation",
            failure_threshold=5,
            recovery_timeout=60,
            expected_exception=(ToolError, aiohttp.ClientError, asyncio.TimeoutError),
        )

        super().__init__(
            name="google_maps_elevation",
            input_schema=ElevationInput,
            output_schema=ElevationOutput,
            timeout_seconds=timeout_seconds,
            max_retries=max_retries,
            circuit_breaker=circuit_breaker,
            api_key=api_key,
        )

        logger.info("Google Maps Elevation tool initialized")

    def to_langchain_tool(self) -> GoogleMapsElevationLangChainTool:
        """Convert to LangChain-compatible tool."""
        return GoogleMapsElevationLangChainTool(elevation_tool=self)

    async def _execute_tool(self, validated_input: ElevationInput) -> Dict[str, Any]:
        """
        Execute the elevation lookup with the Google Maps API.

        Args:
            validated_input: Validated input parameters

        Returns:
            Elevation data dictionary
        """
        try:
            if validated_input.mode == "point":
                return await self._get_single_point_elevation(
                    validated_input.lat, validated_input.lon
                )

            elif validated_input.mode == "path":
                return await self._get_path_elevation(
                    validated_input.points, validated_input.samples
                )

            elif validated_input.mode == "locations":
                return await self._get_locations_elevation(validated_input.points)

            else:
                raise ToolError(
                    f"Unsupported mode: {validated_input.mode}",
                    ToolErrorType.VALIDATION_ERROR,
                )

        except aiohttp.ClientError as e:
            raise ToolError(
                f"Google Maps API request failed: {e}",
                ToolErrorType.EXTERNAL_SERVICE_ERROR,
            )
        except asyncio.TimeoutError:
            raise ToolError(
                "Google Maps API request timed out", ToolErrorType.TIMEOUT_ERROR
            )

    async def _get_single_point_elevation(
        self, lat: float, lon: float
    ) -> Dict[str, Any]:
        """Get elevation for a single point."""

        params = {"locations": f"{lat},{lon}", "key": self.api_key}

        response_data = await self._make_api_request(params)

        if not response_data.get("results"):
            raise ToolError(
                "No elevation data returned for point",
                ToolErrorType.EXTERNAL_SERVICE_ERROR,
            )

        result = response_data["results"][0]
        elevation = result["elevation"]

        return {
            "elevation": elevation,
            "points": [
                ElevationResult(
                    lat=lat,
                    lon=lon,
                    elevation=elevation,
                    resolution=result.get("resolution"),
                ).model_dump()
            ],
            "api_status": response_data["status"],
            "total_points": 1,
        }

    async def _get_path_elevation(
        self, points: List[ElevationPoint], samples: Optional[int] = None
    ) -> Dict[str, Any]:
        """Get elevation profile for a path with optional sampling."""

        # Build path string
        path_str = "|".join([f"{p.lat},{p.lon}" for p in points])

        params = {"path": path_str, "key": self.api_key}

        if samples:
            params["samples"] = samples

        response_data = await self._make_api_request(params)

        if not response_data.get("results"):
            raise ToolError(
                "No elevation data returned for path",
                ToolErrorType.EXTERNAL_SERVICE_ERROR,
            )

        # Process results and calculate elevation metrics
        results = response_data["results"]
        elevations = [r["elevation"] for r in results]

        elevation_points = [
            ElevationResult(
                lat=r["location"]["lat"],
                lon=r["location"]["lng"],
                elevation=r["elevation"],
                resolution=r.get("resolution"),
            ).model_dump()
            for r in results
        ]

        # Calculate elevation gain/loss
        elevation_gain = 0.0
        elevation_loss = 0.0

        for i in range(1, len(elevations)):
            diff = elevations[i] - elevations[i - 1]
            if diff > 0:
                elevation_gain += diff
            else:
                elevation_loss += abs(diff)

        return {
            "min_elevation": min(elevations),
            "max_elevation": max(elevations),
            "elevation_gain": elevation_gain,
            "elevation_loss": elevation_loss,
            "points": elevation_points,
            "api_status": response_data["status"],
            "total_points": len(results),
        }

    async def _get_locations_elevation(
        self, points: List[ElevationPoint]
    ) -> Dict[str, Any]:
        """Get elevation for multiple discrete locations."""

        # Handle batching if more than max batch size
        if len(points) > self.max_batch_size:
            return await self._get_batched_elevations(points)

        # Build locations string
        locations_str = "|".join([f"{p.lat},{p.lon}" for p in points])

        params = {"locations": locations_str, "key": self.api_key}

        response_data = await self._make_api_request(params)

        if not response_data.get("results"):
            raise ToolError(
                "No elevation data returned for locations",
                ToolErrorType.EXTERNAL_SERVICE_ERROR,
            )

        results = response_data["results"]
        elevation_points = [
            ElevationResult(
                lat=r["location"]["lat"],
                lon=r["location"]["lng"],
                elevation=r["elevation"],
                resolution=r.get("resolution"),
            ).model_dump()
            for r in results
        ]

        return {
            "points": elevation_points,
            "api_status": response_data["status"],
            "total_points": len(results),
        }

    async def _get_batched_elevations(
        self, points: List[ElevationPoint]
    ) -> Dict[str, Any]:
        """Handle elevation lookup for large point sets using batching."""

        all_points = []
        batch_count = 0

        # Process in batches
        for i in range(0, len(points), self.max_batch_size):
            batch = points[i : i + self.max_batch_size]
            batch_result = await self._get_locations_elevation(batch)
            all_points.extend(batch_result["points"])
            batch_count += 1

            # Small delay between batches to be respectful to API
            if i + self.max_batch_size < len(points):
                await asyncio.sleep(0.1)

        logger.info(f"Processed {len(points)} points in {batch_count} batches")

        return {
            "points": all_points,
            "api_status": "OK",
            "total_points": len(all_points),
        }

    async def _make_api_request(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Make authenticated request to Google Maps API."""

        url = f"{self.base_url}?{urlencode(params)}"

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=self.timeout_seconds) as response:

                    if response.status == 429:
                        # Rate limited - extract retry-after if available
                        retry_after = int(response.headers.get("Retry-After", 60))
                        raise ToolError(
                            "Google Maps API rate limit exceeded",
                            ToolErrorType.RATE_LIMIT_ERROR,
                            retry_after=retry_after,
                        )

                    if response.status == 403:
                        raise ToolError(
                            "Google Maps API authentication failed - check API key",
                            ToolErrorType.AUTHENTICATION_ERROR,
                        )

                    if response.status != 200:
                        raise ToolError(
                            f"Google Maps API returned status {response.status}",
                            ToolErrorType.EXTERNAL_SERVICE_ERROR,
                        )

                    data = await response.json()

                    # Check API-level status
                    if data.get("status") != "OK":
                        status = data.get("status", "UNKNOWN")
                        error_message = data.get(
                            "error_message", f"API returned status: {status}"
                        )

                        if status == "OVER_QUERY_LIMIT":
                            raise ToolError(
                                "Google Maps API quota exceeded",
                                ToolErrorType.RATE_LIMIT_ERROR,
                                retry_after=3600,  # Retry after 1 hour
                            )
                        elif status == "REQUEST_DENIED":
                            raise ToolError(
                                f"Google Maps API request denied: {error_message}",
                                ToolErrorType.AUTHENTICATION_ERROR,
                            )
                        else:
                            raise ToolError(
                                f"Google Maps API error: {error_message}",
                                ToolErrorType.EXTERNAL_SERVICE_ERROR,
                            )

                    return data

        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            # Let these exceptions propagate to the circuit breaker
            # The base tool will catch them and convert to ToolError
            raise ToolError(
                f"Google Maps API request failed: {str(e)}",
                ToolErrorType.EXTERNAL_SERVICE_ERROR,
            ) from e

    async def _generate_fallback(self, error: ToolError) -> Optional[Dict[str, Any]]:
        """
        Generate fallback response for elevation failures.

        In production, this could use terrain datasets or cached data.
        For now, returns a safe fallback indicating service unavailable.
        """
        if error.error_type == ToolErrorType.RATE_LIMIT_ERROR:
            return {
                "elevation": None,
                "points": [],
                "api_status": "RATE_LIMITED",
                "total_points": 0,
                "fallback_message": f"Rate limited, retry after {error.retry_after} seconds",
            }

        return {
            "elevation": None,
            "points": [],
            "api_status": "SERVICE_UNAVAILABLE",
            "total_points": 0,
            "fallback_message": "Elevation service temporarily unavailable",
        }
