"""
Azure Search Retriever Tool

Hardened tool for retrieving documents from Azure Cognitive Search,
supporting keyword, vector, and hybrid search.
"""

import asyncio
import logging
import os
from ...config import config
import time
import uuid
from typing import Any, Dict, List, Optional

from azure.core.credentials import AzureKeyCredential
from azure.search.documents.aio import SearchClient
from azure.search.documents.models import VectorizedQuery
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from ...schemas.azure_search_retriever_schemas import (
    AzureSearchRetrieverInput,
    AzureSearchRetrieverOutput,
    SearchResultItem,
)
from ..circuit_breaker import (  # Assuming circuit breaker is in tools module
    CircuitBreaker,
)
from .utils.embedding_utils import get_azure_openai_embedding

logger = logging.getLogger(__name__)

DEFAULT_AZURE_SEARCH_INDEX = "docs-chunks-index-v2"  # From TS code


class AzureSearchRetrieverToolInput(BaseModel):
    """Input schema for Azure Search Retriever tool."""

    query: str = Field(description="Search query text")
    index_name: Optional[str] = Field(
        default=None,
        description="Azure Search index name (optional, uses default if not provided)",
    )
    top_k: Optional[int] = Field(
        default=5, description="Number of results to return (default: 5)"
    )
    vector_search: Optional[bool] = Field(
        default=False, description="Enable vector search using embeddings"
    )
    hybrid_search: Optional[bool] = Field(
        default=False, description="Enable hybrid search (keyword + vector)"
    )
    filter_expression: Optional[str] = Field(
        default=None, description="OData filter expression"
    )


class AzureSearchRetrieverLangChainTool(BaseTool):
    """LangChain-compatible wrapper for AzureSearchRetrieverTool."""

    name: str = "azure_search_retriever"
    description: str = """Search and retrieve documents from Azure Cognitive Search.
    
    Search modes available:
    - Keyword search: Traditional text-based search
    - Vector search: Semantic search using embeddings
    - Hybrid search: Combines keyword and vector search for best results
    
    Use this tool to find relevant fitness research, exercise information, nutrition data, and training guidance from the knowledge base."""

    args_schema: type[BaseModel] = AzureSearchRetrieverToolInput
    search_tool: "AzureSearchRetrieverTool" = Field(exclude=True)

    def __init__(self, search_tool: "AzureSearchRetrieverTool"):
        super().__init__(search_tool=search_tool)

    async def _arun(self, query: str, **kwargs) -> str:
        """Async implementation of the tool."""
        try:
            # Combine query with any additional kwargs
            input_data = {"query": query, **kwargs}
            result = await self.search_tool.invoke(input_data)
            return f"Search completed: {result.model_dump_json()}"
        except Exception as e:
            return f"Error searching documents: {str(e)}"

    def _run(self, query: str, **kwargs) -> str:
        """Sync implementation of the tool."""
        import asyncio

        try:
            # Combine query with any additional kwargs
            input_data = {"query": query, **kwargs}
            loop = asyncio.get_event_loop()
            if loop.is_running():
                return f"Azure Search requires async execution. Error: Cannot run sync in async context."
            else:
                result = loop.run_until_complete(self.search_tool.invoke(input_data))
                return f"Search completed: {result.model_dump_json()}"
        except Exception as e:
            return f"Error searching documents: {str(e)}"


class AzureSearchRetrieverTool:
    """
    Tool to interact with Azure Cognitive Search for document retrieval.
    Supports keyword, vector, and hybrid search modes.
    """

    def __init__(
        self,
        azure_search_service_name: Optional[str] = None,
        azure_search_api_key: Optional[str] = None,
        default_index_name: Optional[str] = None,
        azure_openai_api_key: Optional[str] = None,  # For embeddings
        azure_openai_endpoint: Optional[str] = None,
        azure_openai_embedding_deployment_id: Optional[str] = None,
        timeout: int = 30,  # Longer timeout for potential embedding + search
        max_retries: int = 2,
        base_delay: float = 1.0,
    ):
        if not azure_search_service_name or not azure_search_api_key:
            from ...config import config

            self.service_name = (
                azure_search_service_name or config.azure_search.service_name
            )
            self.search_api_key = azure_search_api_key or config.azure_search.api_key
            self.default_index = default_index_name or config.azure_search.index_name
        else:
            self.service_name = azure_search_service_name
            self.search_api_key = azure_search_api_key
            self.default_index = default_index_name or DEFAULT_AZURE_SEARCH_INDEX

        self.openai_api_key = azure_openai_api_key  # Will be passed to embedding util, which checks env vars too
        self.openai_endpoint = azure_openai_endpoint
        self.openai_embedding_deployment_id = azure_openai_embedding_deployment_id

        self.timeout = timeout
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.circuit_breaker = CircuitBreaker("azure_search_retriever")
        self._search_clients: Dict[str, SearchClient] = {}

        if not self.service_name or not self.search_api_key:
            logger.error(
                "Azure Search service name or API key is missing. Tool will not function."
            )
            self.is_initialized = False
        else:
            self.is_initialized = True
            logger.info(
                f"AzureSearchRetrieverTool initialized for service: {self.service_name}, default index: {self.default_index}"
            )

    def to_langchain_tool(self) -> AzureSearchRetrieverLangChainTool:
        """Convert to LangChain-compatible tool."""
        return AzureSearchRetrieverLangChainTool(search_tool=self)

    async def _get_search_client(self, index_name: str) -> SearchClient:
        if index_name not in self._search_clients:
            if not self.is_initialized:
                raise ConnectionError(
                    "Azure Search client cannot be created: tool not initialized."
                )
            endpoint = f"https://{self.service_name}.search.windows.net/"
            credential = AzureKeyCredential(self.search_api_key)

            # For development: bypass SSL verification if environment variable is set
            transport_options = {}
            if (
                os.getenv("AZURE_SEARCH_DISABLE_SSL_VERIFICATION", "false").lower()
                == "true"
            ):
                logger.warning(
                    "⚠️ SSL verification disabled for Azure Search (development only)"
                )
                import ssl

                ssl_context = ssl.create_default_context()
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE
                transport_options["connection_verify"] = False

            self._search_clients[index_name] = SearchClient(
                endpoint=endpoint,
                index_name=index_name,
                credential=credential,
                **transport_options,
            )
            logger.info(f"Created Azure Search client for index: {index_name}")
        return self._search_clients[index_name]

    async def invoke(self, input_data: Dict[str, Any]) -> AzureSearchRetrieverOutput:
        start_time = time.time()
        request_id = str(uuid.uuid4())[:8]
        query_for_output = input_data.get("query", "")

        logger.info(
            f"Azure Search request {request_id} started for query: '{query_for_output[:50]}...'"
        )

        if not self.is_initialized:
            exec_time = int((time.time() - start_time) * 1000)
            return AzureSearchRetrieverOutput(
                success=False,
                query=query_for_output,
                result_count=0,
                error_type="configuration_error",
                message="Azure Search Tool is not properly initialized.",
                request_id=request_id,
                execution_time_ms=exec_time,
            )

        if self.circuit_breaker.is_open():
            logger.warning("Circuit breaker is open for azure_search_retriever. Skipping execution.")
            return AzureSearchRetrieverOutput(
                success=False,
                query=query_for_output,
                result_count=0,
                error_type="circuit_breaker_open",
                message="Circuit breaker is open.",
                request_id=request_id,
                execution_time_ms=int((time.time() - start_time) * 1000),
            )

        try:
            validated_input = AzureSearchRetrieverInput(**input_data)
            query_for_output = validated_input.query  # Update with validated query

            index_name_to_use = validated_input.index_name or self.default_index
            search_results: List[SearchResultItem] = []

            for attempt in range(self.max_retries + 1):
                try:
                    client = await self._get_search_client(index_name_to_use)
                    query_vector: Optional[List[float]] = None

                    if validated_input.vector_search:
                        try:
                            logger.info(
                                f"Generating embedding for query: '{validated_input.query[:50]}...'"
                            )
                            query_vector = await asyncio.wait_for(
                                get_azure_openai_embedding(
                                    text=validated_input.query,
                                    azure_openai_api_key=self.openai_api_key,  # Pass through, util checks env if None
                                    azure_openai_endpoint=self.openai_endpoint,
                                    azure_openai_embedding_deployment_id=self.openai_embedding_deployment_id,
                                ),
                                timeout=self.timeout
                                / 2,  # Allocate portion of timeout to embedding
                            )
                        except Exception as emb_e:
                            logger.error(
                                f"Embedding generation failed for request {request_id}: {emb_e}"
                            )
                            # Don't fail entire search yet, can fall back to keyword if hybrid or keyword was intended
                            if (
                                not validated_input.hybrid_search
                            ):  # If pure vector, then it's a hard fail for this attempt
                                raise ValueError(
                                    f"Embedding failed for pure vector search: {emb_e}"
                                ) from emb_e
                            query_vector = (
                                None  # Ensure it's None so pure vector path isn't taken
                            )

                    search_text = (
                        "*"
                        if validated_input.vector_search
                        and not validated_input.hybrid_search
                        else validated_input.query
                    )

                    vector_query_obj: Optional[VectorizedQuery] = None
                    if query_vector:
                        vector_query_obj = VectorizedQuery(
                            vector=query_vector,
                            k_nearest_neighbors=validated_input.top_k,
                            fields="content_vector",  # Assuming 'content_vector' from TS
                        )

                    search_options: Dict[str, Any] = {
                        "top": validated_input.top_k,
                        "include_total_count": True,
                        "filter": validated_input.filter_expression,
                        "query_type": validated_input.query_type,
                        "highlight_fields": (
                            ",".join(validated_input.highlight_fields)
                            if validated_input.highlight_fields
                            and not (
                                validated_input.vector_search
                                and not validated_input.hybrid_search
                            )
                            else None
                        ),  # Disable highlights for pure vector searches
                        "select": validated_input.select_fields
                        or [
                            "id",
                            "content",
                            "title",
                            "source",
                            "url",
                            "created_at",
                            "chunk_id",
                            "metadata",
                        ],  # Common fields
                        "semantic_configuration_name": validated_input.semantic_configuration_name,
                    }
                    if vector_query_obj:
                        search_options["vector_queries"] = [vector_query_obj]

                    # Remove None keys from search_options
                    search_options = {
                        k: v for k, v in search_options.items() if v is not None
                    }

                    logger.info(
                        f"Executing search on index '{index_name_to_use}' with options: {search_options} for text: '{search_text[:50]}...'"
                    )

                    response = await asyncio.wait_for(
                        client.search(search_text=search_text, **search_options),
                        timeout=self.timeout,
                    )

                    async for page in response.by_page():
                        async for doc in page:
                            search_results.append(
                                SearchResultItem(
                                    content=doc.get("content", ""),
                                    title=doc.get("title"),
                                    source=doc.get("source"),
                                    url=doc.get("url"),
                                    score=doc.get("@search.score"),
                                    highlights=doc.get("@search.highlights"),
                                    chunk_id=doc.get("chunk_id"),
                                    metadata={
                                        k: v
                                        for k, v in doc.items()
                                        if k
                                        not in [
                                            "content",
                                            "title",
                                            "source",
                                            "url",
                                            "@search.score",
                                            "@search.highlights",
                                            "chunk_id",
                                        ]
                                    },
                                )
                            )
                        if len(search_results) >= validated_input.top_k:
                            break  # Respect top_k across pages
                    search_results = search_results[
                        : validated_input.top_k
                    ]  # Ensure exact top_k

                    self.circuit_breaker.record_success()
                    exec_time = int((time.time() - start_time) * 1000)
                    logger.info(
                        f"Azure Search request {request_id} successful, {len(search_results)} results in {exec_time}ms."
                    )
                    return AzureSearchRetrieverOutput(
                        success=True,
                        query=validated_input.query,
                        results=search_results,
                        result_count=len(search_results),
                        message="Search completed successfully.",
                        request_id=request_id,
                        execution_time_ms=exec_time,
                    )

                except asyncio.TimeoutError:
                    logger.warning(
                        f"Attempt {attempt + 1} timed out for Azure Search request {request_id}."
                    )
                    if attempt >= self.max_retries:
                        self.circuit_breaker.record_failure()
                        exec_time = int((time.time() - start_time) * 1000)
                        return AzureSearchRetrieverOutput(
                            success=False,
                            query=validated_input.query,
                            result_count=0,
                            error_type="timeout_error",
                            message=f"Search timed out after {self.max_retries + 1} attempts.",
                            request_id=request_id,
                            execution_time_ms=exec_time,
                        )
                    await asyncio.sleep(self.base_delay * (2**attempt))
                except Exception as e:
                    logger.error(
                        f"Attempt {attempt + 1} failed for Azure Search request {request_id}: {e}"
                    )
                    if attempt >= self.max_retries:
                        self.circuit_breaker.record_failure()
                        exec_time = int((time.time() - start_time) * 1000)
                        return AzureSearchRetrieverOutput(
                            success=False,
                            query=validated_input.query,
                            result_count=0,
                            error_type="search_api_error",
                            message=f"Search failed after {self.max_retries + 1} attempts: {str(e)}",
                            request_id=request_id,
                            execution_time_ms=exec_time,
                        )
                    await asyncio.sleep(self.base_delay * (2**attempt))

            # Should not be reached if logic is correct, but as a safeguard:
            exec_time = int((time.time() - start_time) * 1000)
            return AzureSearchRetrieverOutput(
                success=False,
                query=validated_input.query,
                result_count=0,
                error_type="unknown_error",
                message="Search failed due to an unexpected error after retries.",
                request_id=request_id,
                execution_time_ms=exec_time,
            )

        except Exception as e:
            logger.error(f"Azure Search Retriever failed for query '{query_for_output}': {e}", exc_info=True)
            self.circuit_breaker.record_failure()
            exec_time = int((time.time() - start_time) * 1000)
            return AzureSearchRetrieverOutput(
                success=False,
                query=query_for_output,
                result_count=0,
                error_type="unknown_error",
                message=f"Search failed due to an unexpected error after retries: {str(e)}",
                request_id=request_id,
                execution_time_ms=exec_time,
            )

    async def close(self):
        """Close all underlying Azure Search clients."""
        for client_key in list(
            self._search_clients.keys()
        ):  # Iterate over keys to allow deletion
            client = self._search_clients.pop(client_key)
            if client:
                await client.close()
                logger.info(f"Closed Azure Search client for index: {client_key}")
        logger.info("All Azure Search clients closed.")


# Example Usage (for testing purposes)
async def main():
    # Ensure you have AZURE_SEARCH_SERVICE_NAME, AZURE_SEARCH_API_KEY, etc. in your .env file
    # and that it's loaded (e.g., by python-dotenv)
    from dotenv import load_dotenv

    load_dotenv(".env.local")  # Make sure .env.local has the Azure creds

    logger.basicConfig(level=logging.INFO)

    tool = AzureSearchRetrieverTool()
    if not tool.is_initialized:
        print("Tool could not be initialized. Check logs and .env variables.")
        return

    print(f"Default index: {tool.default_index}")

    # Test Keyword Search
    keyword_input = AzureSearchRetrieverInput(query="benefits of strength training")
    print(f"\n--- Testing Keyword Search: '{keyword_input.query}' ---")
    response = await tool.invoke(keyword_input.model_dump())
    print(
        f"Success: {response.success}, Found: {response.result_count} results, Time: {response.execution_time_ms}ms"
    )
    if response.success and response.results:
        for i, res in enumerate(response.results[:2]):  # Print first 2
            print(f"  Result {i+1}: {res.title} (Score: {res.score})")
            print(f"    Content: {res.content[:150]}...")
            if res.highlights:
                print(f"    Highlights: {res.highlights}")
    elif not response.success:
        print(f"  Error: {response.error_type} - {response.message}")

    # Test Vector Search (assuming embedding model is configured and index has vectors)
    # This requires AZURE_OPENAI_API_KEY, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_EMBEDDING_DEPLOYMENT_ID
    vector_input = AzureSearchRetrieverInput(
        query="cardiovascular health and running",
        vector_search=True,
        hybrid_search=False,  # Pure vector
        top_k=3,
    )
    print(f"\n--- Testing Pure Vector Search: '{vector_input.query}' ---")
    response = await tool.invoke(vector_input.model_dump())
    print(
        f"Success: {response.success}, Found: {response.result_count} results, Time: {response.execution_time_ms}ms"
    )
    if response.success and response.results:
        for i, res in enumerate(response.results):
            print(f"  Result {i+1}: {res.title} (Score: {res.score})")
            print(f"    Content: {res.content[:150]}...")
    elif not response.success:
        print(f"  Error: {response.error_type} - {response.message}")

    # Test Hybrid Search
    hybrid_input = AzureSearchRetrieverInput(
        query="HIIT for fat loss", vector_search=True, hybrid_search=True, top_k=3
    )
    print(f"\n--- Testing Hybrid Search: '{hybrid_input.query}' ---")
    response = await tool.invoke(hybrid_input.model_dump())
    print(
        f"Success: {response.success}, Found: {response.result_count} results, Time: {response.execution_time_ms}ms"
    )
    if response.success and response.results:
        for i, res in enumerate(response.results):
            print(f"  Result {i+1}: {res.title} (Score: {res.score})")
            print(f"    Content: {res.content[:150]}...")
    elif not response.success:
        print(f"  Error: {response.error_type} - {response.message}")

    await tool.close()  # Important to close clients


if __name__ == "__main__":
    # This is for local testing. Ensure .env.local is populated with necessary Azure credentials.
    # Example .env.local content:
    # AZURE_SEARCH_SERVICE_NAME="your-search-service-name"
    # AZURE_SEARCH_API_KEY="your-search-service-admin-key"
    # AZURE_SEARCH_INDEX_NAME="your-default-index-name" (optional, defaults to docs-chunks-index-v2)
    # AZURE_OPENAI_API_KEY="your-azure-openai-key"
    # AZURE_OPENAI_ENDPOINT="https://your-azure-openai-resource.openai.azure.com/"
    # AZURE_OPENAI_EMBEDDING_DEPLOYMENT_ID="your-embedding-deployment-name"

    # Check if .env.local exists before trying to load
    if os.path.exists(".env.local"):
        asyncio.run(main())
    else:
        print("Skipping AzureSearchRetrieverTool example: .env.local not found.")
        print("Please create .env.local with Azure credentials to run this example.")
