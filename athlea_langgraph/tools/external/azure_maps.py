"""
Azure Maps API Tool

Production-ready tool for Azure Maps services with:
- Circuit breaker protection
- Input/output validation
- Exponential backoff retries
- Comprehensive error handling
- Support for geocoding, weather, nearby search, and route finding
"""

import asyncio
import logging
import os
from ...config import config
from typing import Any, Dict, List, Optional
from urllib.parse import urlencode

import aiohttp
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from ..base_tool import HardenedTool, ToolError, ToolErrorType
from ..circuit_breaker import CircuitBreaker
from ..schemas.azure_maps_schemas import (
    Address,
    AzureMapsInput,
    AzureMapsOutput,
    GeocodeResult,
    LocationResult,
    POIInfo,
    Position,
    WeatherMetric,
    WeatherResult,
)

logger = logging.getLogger(__name__)


class AzureMapsToolInput(BaseModel):
    """Input schema for Azure Maps tool."""

    command: str = Field(
        description="Command: 'geocode', 'weather', 'nearby', or 'routes'"
    )
    address: Optional[str] = Field(
        default=None, description="Address to geocode (required for geocode command)"
    )
    lat: Optional[float] = Field(
        default=None,
        description="Latitude (required for weather, nearby, routes commands)",
    )
    lon: Optional[float] = Field(
        default=None,
        description="Longitude (required for weather, nearby, routes commands)",
    )
    query: Optional[str] = Field(
        default=None, description="Search query for nearby places"
    )
    radius: Optional[int] = Field(
        default=5000, description="Search radius in meters (default: 5000)"
    )


class AzureMapsLangChainTool(BaseTool):
    """LangChain-compatible wrapper for AzureMapsTool."""

    name: str = "azure_maps"
    description: str = """Access Azure Maps services for location and mapping data.
    
    Commands available:
    - geocode: Convert address to coordinates
    - weather: Get current weather conditions for a location
    - nearby: Search for nearby places (restaurants, gyms, etc.)
    - routes: Find running/cycling routes and trails near a location
    
    Use this tool for location-based services, route planning, and environmental data for fitness activities."""

    args_schema: type[BaseModel] = AzureMapsToolInput
    azure_maps_tool: "AzureMapsTool" = Field(exclude=True)

    def __init__(self, azure_maps_tool: "AzureMapsTool"):
        super().__init__(azure_maps_tool=azure_maps_tool)

    async def _arun(self, **kwargs) -> str:
        """Async implementation of the tool."""
        try:
            result = await self.azure_maps_tool.execute(AzureMapsInput(**kwargs))
            return f"Azure Maps data retrieved: {result.model_dump_json()}"
        except Exception as e:
            return f"Error accessing Azure Maps: {str(e)}"

    def _run(self, **kwargs) -> str:
        """Sync implementation of the tool."""
        import asyncio

        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                return f"Azure Maps lookup requires async execution. Error: Cannot run sync in async context."
            else:
                result = loop.run_until_complete(
                    self.azure_maps_tool.execute(AzureMapsInput(**kwargs))
                )
                return f"Azure Maps data retrieved: {result.model_dump_json()}"
        except Exception as e:
            return f"Error accessing Azure Maps: {str(e)}"


class AzureMapsTool(HardenedTool[AzureMapsInput, AzureMapsOutput]):
    """
    Azure Maps API tool with comprehensive hardening.

    Features:
    - Geocoding addresses to coordinates
    - Current weather conditions lookup
    - Nearby places search
    - Running routes and trails discovery
    - Circuit breaker for API failure protection
    - Exponential backoff for rate limiting
    - Request batching and deduplication
    """

    # Declare these as Pydantic fields to avoid validation errors
    subscription_key: Optional[str] = Field(None)
    base_url: str = Field(default="https://atlas.microsoft.com/")
    api_version: str = Field(default="1.0")

    def __init__(self, **data: Any):
        # Get subscription key from centralized configuration if not provided
        if not data.get("subscription_key"):
            from ...config import config

            data["subscription_key"] = config.azure_maps.subscription_key

        if not data.get("subscription_key"):
            raise ValueError(
                "Azure Maps subscription key required. Set AZURE_MAPS_SUBSCRIPTION_KEY environment variable "
                "or pass subscription_key parameter."
            )

        # Set default name and schemas if not provided
        data.setdefault("name", "azure_maps")
        data.setdefault("input_schema", AzureMapsInput)
        data.setdefault("output_schema", AzureMapsOutput)

        super().__init__(**data)

        # Initialize circuit breaker for Azure Maps API
        self.circuit_breaker = CircuitBreaker(
            name="azure_maps",
            failure_threshold=5,
            recovery_timeout=60,
            expected_exception=(aiohttp.ClientError, asyncio.TimeoutError),
        )

        logger.info("Azure Maps tool initialized")

    def to_langchain_tool(self) -> AzureMapsLangChainTool:
        """Convert to LangChain-compatible tool."""
        return AzureMapsLangChainTool(azure_maps_tool=self)

    async def _execute_tool(self, validated_input: AzureMapsInput) -> Dict[str, Any]:
        """
        Execute the Azure Maps API command.

        Args:
            validated_input: Validated input parameters

        Returns:
            Azure Maps data dictionary
        """
        command = validated_input.command

        try:
            if command == "geocode":
                return await self._geocode_address(validated_input.address)

            elif command == "weather":
                return await self._get_weather(validated_input.lat, validated_input.lon)

            elif command == "nearby":
                return await self._search_nearby(
                    validated_input.lat,
                    validated_input.lon,
                    validated_input.query,
                    validated_input.radius,
                )

            elif command == "routes":
                return await self._find_routes(
                    validated_input.lat, validated_input.lon, validated_input.radius
                )

            else:
                raise ToolError(
                    f"Unsupported command: {command}", ToolErrorType.VALIDATION_ERROR
                )

        except aiohttp.ClientError as e:
            raise ToolError(
                f"Azure Maps API request failed: {e}",
                ToolErrorType.EXTERNAL_SERVICE_ERROR,
            )
        except asyncio.TimeoutError:
            raise ToolError(
                "Azure Maps API request timed out", ToolErrorType.TIMEOUT_ERROR
            )

    async def _geocode_address(self, address: str) -> Dict[str, Any]:
        """Geocode an address to coordinates."""

        params = {
            "subscription-key": self.subscription_key,
            "api-version": self.api_version,
            "query": address,
            "limit": "1",
        }

        response_data = await self._make_api_request("search/address/json", params)

        if not response_data.get("results"):
            raise ToolError(
                f"No geocoding results found for address: {address}",
                ToolErrorType.EXTERNAL_SERVICE_ERROR,
            )

        result = response_data["results"][0]

        # Transform to our schema format
        geocode_result = GeocodeResult(
            position=Position(
                lat=result["position"]["lat"], lon=result["position"]["lon"]
            ),
            address=Address(
                freeform_address=result.get("address", {}).get("freeformAddress"),
                street_name=result.get("address", {}).get("streetName"),
                municipality=result.get("address", {}).get("municipality"),
                municipality_subdivision=result.get("address", {}).get(
                    "municipalitySubdivision"
                ),
                country_subdivision=result.get("address", {}).get("countrySubdivision"),
                country_code=result.get("address", {}).get("countryCode"),
                country=result.get("address", {}).get("country"),
                local_name=result.get("address", {}).get("localName"),
            ),
            confidence=result.get("confidence"),
        )

        return {
            "command": "geocode",
            "success": True,
            "geocode_result": geocode_result.model_dump(),
            "api_status": "OK",
        }

    async def _get_weather(self, lat: float, lon: float) -> Dict[str, Any]:
        """Get current weather conditions."""

        params = {
            "subscription-key": self.subscription_key,
            "api-version": self.api_version,
            "query": f"{lat},{lon}",
        }

        response_data = await self._make_api_request(
            "weather/currentConditions/json", params
        )

        if not response_data.get("results"):
            raise ToolError(
                f"No weather data found for coordinates: {lat}, {lon}",
                ToolErrorType.EXTERNAL_SERVICE_ERROR,
            )

        result = response_data["results"][0]

        # Helper function for safe data extraction
        def get_metric(data: Dict[str, Any], default_unit: str = "") -> WeatherMetric:
            if isinstance(data, dict) and "value" in data:
                return WeatherMetric(
                    value=data.get("value", 0), unit=data.get("unit", default_unit)
                )
            return WeatherMetric(value=0, unit=default_unit)

        # Transform to our schema format
        weather_result = WeatherResult(
            temperature=get_metric(result.get("temperature", {}), "C"),
            humidity=WeatherMetric(value=result.get("relativeHumidity", 0), unit="%"),
            wind_speed=get_metric(result.get("wind", {}).get("speed", {}), "km/h"),
            precipitation=get_metric(result.get("precipitation", {}), "mm"),
            condition=result.get("phrase") or result.get("iconPhrase", "Unknown"),
            feels_like=get_metric(result.get("realFeelTemperature", {}), "C"),
        )

        return {
            "command": "weather",
            "success": True,
            "weather_result": weather_result.model_dump(),
            "api_status": "OK",
        }

    async def _search_nearby(
        self, lat: float, lon: float, query: str, radius: int
    ) -> Dict[str, Any]:
        """Search for nearby places."""

        params = {
            "subscription-key": self.subscription_key,
            "api-version": self.api_version,
            "lat": str(lat),
            "lon": str(lon),
            "radius": str(radius),
            "query": query,
            "limit": "25",
        }

        response_data = await self._make_api_request("search/nearby/json", params)

        results = response_data.get("results", [])
        location_results = []

        for result in results:
            # Transform to our schema format
            location_result = LocationResult(
                id=result.get("id", ""),
                type=result.get("type", ""),
                score=result.get("score", 0),
                distance=result.get("dist"),  # Already in km from Azure Maps
                position=Position(
                    lat=result["position"]["lat"], lon=result["position"]["lon"]
                ),
                address=Address(
                    freeform_address=result.get("address", {}).get("freeformAddress"),
                    street_name=result.get("address", {}).get("streetName"),
                    municipality=result.get("address", {}).get("municipality"),
                    municipality_subdivision=result.get("address", {}).get(
                        "municipalitySubdivision"
                    ),
                    country_subdivision=result.get("address", {}).get(
                        "countrySubdivision"
                    ),
                    country_code=result.get("address", {}).get("countryCode"),
                ),
                poi=(
                    POIInfo(
                        name=result.get("poi", {}).get("name", ""),
                        categories=result.get("poi", {}).get("categories", []),
                        category_set=result.get("poi", {}).get("categorySet", []),
                    )
                    if result.get("poi")
                    else None
                ),
            )

            location_results.append(location_result.model_dump())

        return {
            "command": "nearby",
            "success": True,
            "locations": location_results,
            "location_count": len(location_results),
            "search_radius": radius,
            "search_query": query,
            "api_status": "OK",
        }

    async def _find_routes(self, lat: float, lon: float, radius: int) -> Dict[str, Any]:
        """Find running routes and trails near the location."""

        # Search for multiple route-related queries
        route_queries = ["parks", "trails", "running paths", "bike paths"]
        all_results = []

        for query in route_queries:
            try:
                params = {
                    "subscription-key": self.subscription_key,
                    "api-version": self.api_version,
                    "lat": str(lat),
                    "lon": str(lon),
                    "radius": str(radius),
                    "query": query,
                    "limit": "10",  # Smaller limit per query
                }

                response_data = await self._make_api_request(
                    "search/nearby/json", params
                )
                results = response_data.get("results", [])

                for result in results:
                    # Add route type context
                    result["route_type"] = query
                    all_results.append(result)

                # Small delay between requests to be respectful
                await asyncio.sleep(0.1)

            except Exception as e:
                logger.warning(f"Route search failed for '{query}': {e}")
                continue

        # Deduplicate results by position (some places might appear in multiple searches)
        seen_positions = set()
        unique_results = []

        for result in all_results:
            pos_key = f"{result['position']['lat']:.6f},{result['position']['lon']:.6f}"
            if pos_key not in seen_positions:
                seen_positions.add(pos_key)
                unique_results.append(result)

        # Transform to our schema format
        location_results = []
        for result in unique_results:
            location_result = LocationResult(
                id=result.get("id", ""),
                type=result.get("type", ""),
                score=result.get("score", 0),
                distance=result.get("dist"),
                position=Position(
                    lat=result["position"]["lat"], lon=result["position"]["lon"]
                ),
                address=Address(
                    freeform_address=result.get("address", {}).get("freeformAddress"),
                    street_name=result.get("address", {}).get("streetName"),
                    municipality=result.get("address", {}).get("municipality"),
                    municipality_subdivision=result.get("address", {}).get(
                        "municipalitySubdivision"
                    ),
                    country_subdivision=result.get("address", {}).get(
                        "countrySubdivision"
                    ),
                    country_code=result.get("address", {}).get("countryCode"),
                ),
                poi=(
                    POIInfo(
                        name=result.get("poi", {}).get("name", ""),
                        categories=result.get("poi", {}).get("categories", [])
                        + [result.get("route_type", "")],
                        category_set=result.get("poi", {}).get("categorySet", []),
                    )
                    if result.get("poi")
                    else None
                ),
            )

            location_results.append(location_result.model_dump())

        # Sort by distance if available
        location_results.sort(key=lambda x: x.get("distance", float("inf")))

        return {
            "command": "routes",
            "success": True,
            "locations": location_results,
            "location_count": len(location_results),
            "search_radius": radius,
            "search_query": "running routes and trails",
            "api_status": "OK",
        }

    async def _make_api_request(
        self, endpoint: str, params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Make authenticated request to Azure Maps API."""

        url = f"{self.base_url}{endpoint}"

        async with aiohttp.ClientSession() as session:
            async with session.get(
                url, params=params, timeout=self.timeout_seconds
            ) as response:

                if response.status == 429:
                    # Rate limited - extract retry-after if available
                    retry_after = int(response.headers.get("Retry-After", 60))
                    raise ToolError(
                        "Azure Maps API rate limit exceeded",
                        ToolErrorType.RATE_LIMIT_ERROR,
                        retry_after=retry_after,
                    )

                if response.status == 401:
                    raise ToolError(
                        "Azure Maps API authentication failed - check subscription key",
                        ToolErrorType.AUTHENTICATION_ERROR,
                    )

                if response.status == 403:
                    raise ToolError(
                        "Azure Maps API access forbidden - check subscription and permissions",
                        ToolErrorType.AUTHENTICATION_ERROR,
                    )

                if response.status != 200:
                    raise ToolError(
                        f"Azure Maps API returned status {response.status}",
                        ToolErrorType.EXTERNAL_SERVICE_ERROR,
                    )

                data = await response.json()
                return data

    async def _generate_fallback(self, error: ToolError) -> Optional[Dict[str, Any]]:
        """
        Generate fallback response for Azure Maps failures.

        In production, this could use cached data or alternative services.
        """
        if error.error_type == ToolErrorType.RATE_LIMIT_ERROR:
            return {
                "command": "unknown",
                "success": False,
                "api_status": "RATE_LIMITED",
                "fallback_message": f"Rate limited, retry after {error.retry_after} seconds",
            }

        return {
            "command": "unknown",
            "success": False,
            "api_status": "SERVICE_UNAVAILABLE",
            "fallback_message": "Azure Maps service temporarily unavailable",
        }
