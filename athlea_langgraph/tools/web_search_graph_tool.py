"""
WebSearchGraph LangChain Tool Wrapper

Wraps the sophisticated WebSearchGraph multi-agent workflow as a LangChain tool
for use by domain coaches, following LangGraph tutorial patterns.
"""

import asyncio
import json
import logging
from typing import Any, Dict, Optional

from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class WebSearchGraphInput(BaseModel):
    """Input schema for web search graph tool."""

    research_question: str = Field(
        description="The research question or topic to investigate thoroughly"
    )
    research_context: Optional[str] = Field(
        default="",
        description="Additional context for the search (user profile, goals, etc.)",
    )
    max_sources: Optional[int] = Field(
        default=5, description="Maximum number of sources to search"
    )


class WebSearchGraphTool(BaseTool):
    """
    LangChain tool wrapper for WebSearchGraph multi-agent workflow.

    This provides sophisticated multi-agent web research capabilities
    compared to the basic WebSearchTool.
    """

    name: str = "web_search_graph"
    description: str = """
    Advanced multi-agent web research tool powered by WebSearchGraph.
    
    Use this for comprehensive research that requires:
    - Strategic research planning
    - Multiple coordinated web searches  
    - Content quality analysis
    - Evidence synthesis across sources
    - Domain-specific research insights
    
    Input should be a JSON string with research_question and optional context.
    
    Example: {"research_question": "best strength training for runners", "research_context": "beginner athlete, injury prevention focus"}
    """
    args_schema: type = WebSearchGraphInput
    web_search_graph: Any = None

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._initialize_graph()

    def _initialize_graph(self):
        """Initialize the WebSearchGraph lazily."""
        try:
            # Try to import and initialize the web search graph (use sync version)
            from ..graphs.tools.web_search_graph import create_web_search_graph_sync

            # Create the graph instance using sync version
            self.web_search_graph = create_web_search_graph_sync()
            logger.info("✅ WebSearchGraph initialized successfully")
        except ImportError as e:
            logger.warning(f"⚠️ WebSearchGraph not available: {e}")
            self.web_search_graph = None
        except Exception as e:
            logger.error(f"❌ Failed to initialize WebSearchGraph: {e}")
            self.web_search_graph = None

    def _run(
        self, research_question: str, research_context: str = "", max_sources: int = 5
    ) -> str:
        """Synchronous wrapper for the async implementation."""
        try:
            # Run the async version in a new event loop
            return asyncio.run(
                self._arun(research_question, research_context, max_sources)
            )
        except Exception as e:
            logger.error(f"❌ WebSearchGraph sync execution failed: {e}")
            return self._fallback_response(research_question, str(e))

    async def _arun(
        self, research_question: str, research_context: str = "", max_sources: int = 5
    ) -> str:
        """Execute the web search graph for comprehensive research."""
        if not self.web_search_graph:
            logger.warning("⚠️ WebSearchGraph not available, using fallback")
            return self._fallback_response(
                research_question, "WebSearchGraph not initialized"
            )

        try:
            logger.info(
                f"🔍 WEBSEARCH_GRAPH: Starting research for: {research_question}"
            )

            # Prepare the research request in the format expected by the web search graph
            research_input = {
                "research_question": research_question,
                "research_type": "fitness_and_health",  # Default for our coaches
                "max_search_results": max_sources,
                "max_pages_to_scrape": min(max_sources, 5),
            }

            # Execute the web search graph using standard LangGraph interface
            result = await self.web_search_graph.ainvoke(research_input)

            # Extract the comprehensive research results from the graph output
            final_research = result.get("final_research", {})
            research_summary = result.get("research_summary", "")
            actionable_insights = result.get("actionable_insights", [])
            successful_scrapes = result.get("successful_scrapes", [])
            quality_metrics = final_research.get("quality_metrics", {})

            # Format the comprehensive research results
            formatted_result = {
                "research_question": research_question,
                "research_strategy": "Multi-agent web search workflow",
                "sources_analyzed": len(successful_scrapes),
                "key_findings": actionable_insights[:5],  # Top 5 insights
                "evidence_summary": (
                    research_summary[:1000]
                    if research_summary
                    else "Research completed successfully"
                ),
                "quality_score": quality_metrics.get("overall_score", 75),
                "recommendations": actionable_insights,
                "source_urls": [scrape.get("url", "") for scrape in successful_scrapes],
                "search_method": "multi_agent_websearch_graph",
                "credibility": quality_metrics.get("credibility", "Medium"),
                "coverage": quality_metrics.get("coverage", "Partial"),
            }

            logger.info(
                f"✅ WEBSEARCH_GRAPH: Research completed with {len(successful_scrapes)} sources"
            )
            return json.dumps(formatted_result, indent=2)

        except Exception as e:
            logger.error(f"❌ WEBSEARCH_GRAPH: Research failed: {e}")
            return self._fallback_response(research_question, str(e))

    def _fallback_response(self, research_question: str, error_msg: str) -> str:
        """Provide a fallback response when WebSearchGraph fails."""
        fallback_result = {
            "research_question": research_question,
            "research_strategy": "Fallback mode - WebSearchGraph unavailable",
            "sources_analyzed": 0,
            "key_findings": [
                "WebSearchGraph service temporarily unavailable",
                "Please try basic web search or consult reliable fitness resources",
                "Consider peer-reviewed research from PubMed or similar sources",
            ],
            "evidence_summary": f"Unable to complete comprehensive research due to: {error_msg}",
            "quality_score": "N/A",
            "recommendations": [
                "Use alternative research methods",
                "Consult established fitness authorities",
                "Try the basic web search tool as backup",
            ],
            "source_urls": [],
            "search_method": "fallback_mode",
            "error": error_msg,
        }

        return json.dumps(fallback_result, indent=2)


def create_web_search_graph_tool() -> WebSearchGraphTool:
    """
    Factory function to create a WebSearchGraph tool instance.

    This is the function that gets imported by the domain coaches.
    """
    try:
        tool = WebSearchGraphTool()
        logger.info("✅ WebSearchGraph tool created successfully")
        return tool
    except Exception as e:
        logger.error(f"❌ Failed to create WebSearchGraph tool: {e}")
        # Return a basic fallback tool
        return _create_fallback_tool()


def _create_fallback_tool() -> BaseTool:
    """Create a basic fallback tool when WebSearchGraph fails."""
    from langchain_core.tools import tool

    @tool
    def fallback_web_research(research_question: str) -> str:
        """Fallback web research tool when WebSearchGraph is unavailable."""
        return json.dumps(
            {
                "research_question": research_question,
                "research_strategy": "Fallback mode",
                "key_findings": ["WebSearchGraph temporarily unavailable"],
                "evidence_summary": "Please use alternative research methods",
                "recommendations": [
                    "Try basic web search",
                    "Consult fitness authorities",
                ],
                "search_method": "fallback",
                "error": "WebSearchGraph initialization failed",
            },
            indent=2,
        )

    return fallback_web_research


# Export the main factory function
__all__ = ["create_web_search_graph_tool", "WebSearchGraphTool", "WebSearchGraphInput"]
