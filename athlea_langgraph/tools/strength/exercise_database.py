"""
Strength Exercise Database Tool

Provides comprehensive exercise lookup, progression tracking, and movement pattern
analysis for strength training programs.
"""

import json
import logging
import os
from typing import Any, Dict, List, Optional

from ...config import config

import httpx
from langchain_core.tools import BaseTool, tool
from pydantic import BaseModel, Field, field_validator

from ..base_tool import BaseDomainTool
from ..schemas.strength_schemas import (
    ExerciseProgressionInput,
    ExerciseProgressionOutput,
    ExerciseSearchInput,
    ExerciseSearchOutput,
)

logger = logging.getLogger(__name__)


class StrengthExerciseDatabase(BaseDomainTool):
    """
    Exercise database tool for strength training domain.

    Provides exercise lookup, movement pattern analysis, progression tracking,
    and equipment-specific recommendations using the ExerciseDB API.
    """

    domain: str = "strength_training"
    name: str = "strength_exercise_database"
    description: str = (
        "Search and analyze strength training exercises with progression recommendations using ExerciseDB API"
    )

    def __init__(self):
        super().__init__()
        self._exercise_cache = {}
        self.api_key = getattr(config, "exercisedb", {}).get("api_key") or os.getenv(
            "EXERCISEDB_API_KEY"
        )
        self.base_url = "https://exercisedb.p.rapidapi.com"
        self.headers = {
            "X-RapidAPI-Key": self.api_key,
            "X-RapidAPI-Host": "exercisedb.p.rapidapi.com",
        }

        # Body part mapping from ExerciseDB format to our internal format
        self.body_part_mapping = {
            "back": ["lats", "rhomboids", "traps", "erector_spinae"],
            "cardio": ["cardiovascular_system"],
            "chest": ["pectorals", "chest"],
            "lower arms": ["forearms"],
            "lower legs": ["calves", "shins"],
            "neck": ["neck"],
            "shoulders": ["deltoids", "delts"],
            "upper arms": ["biceps", "triceps"],
            "upper legs": ["quadriceps", "hamstrings", "glutes"],
            "waist": ["abs", "obliques", "core"],
        }

        # Equipment mapping
        self.equipment_mapping = {
            "barbell": ["barbell"],
            "body weight": ["bodyweight"],
            "cable": ["cable"],
            "dumbbell": ["dumbbells", "dumbbell"],
            "ez barbell": ["ez_bar"],
            "assisted": ["assisted"],
            "leverage machine": ["machine"],
            "olympic barbell": ["olympic_barbell"],
            "resistance band": ["bands", "band"],
            "skierg machine": ["skierg"],
            "smith machine": ["smith_machine"],
            "stability ball": ["stability_ball"],
            "stationary bike": ["bike"],
            "kettlebell": ["kettlebell"],
            "medicine ball": ["medicine_ball"],
            "rope": ["rope"],
            "sled machine": ["sled"],
            "tire": ["tire"],
            "trap bar": ["trap_bar"],
            "upper body ergometer": ["ergometer"],
            "weighted": ["weighted"],
            "wheel roller": ["ab_wheel"],
            "bosu ball": ["bosu"],
        }

        self._movement_patterns = {
            "squat": ["back_squat", "front_squat", "goblet_squat", "overhead_squat"],
            "deadlift": [
                "conventional_deadlift",
                "sumo_deadlift",
                "romanian_deadlift",
                "stiff_leg_deadlift",
            ],
            "press": [
                "bench_press",
                "overhead_press",
                "incline_press",
                "dumbbell_press",
            ],
            "pull": ["pull_ups", "chin_ups", "rows", "lat_pulldowns"],
            "lunge": [
                "forward_lunge",
                "reverse_lunge",
                "lateral_lunge",
                "walking_lunge",
            ],
            "carry": [
                "farmers_walk",
                "suitcase_carry",
                "overhead_carry",
                "front_carry",
            ],
        }

    async def _make_api_request(
        self, endpoint: str, params: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Make HTTP request to ExerciseDB API."""
        if not self.api_key:
            logger.warning("ExerciseDB API key not found. Using fallback data.")
            return {}

        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/{endpoint}",
                    headers=self.headers,
                    params=params or {},
                    timeout=10.0,
                )
                response.raise_for_status()
                return response.json()
        except Exception as e:
            logger.error(f"ExerciseDB API request failed: {e}")
            return {}

    async def _get_exercises_by_body_part(self, body_part: str) -> List[Dict[str, Any]]:
        """Get exercises filtered by body part from ExerciseDB API."""
        endpoint = f"exercises/bodyPart/{body_part}"
        return await self._make_api_request(endpoint)

    async def _get_exercises_by_target_muscle(
        self, muscle: str
    ) -> List[Dict[str, Any]]:
        """Get exercises filtered by target muscle from ExerciseDB API."""
        endpoint = f"exercises/target/{muscle}"
        return await self._make_api_request(endpoint)

    async def _get_exercises_by_equipment(self, equipment: str) -> List[Dict[str, Any]]:
        """Get exercises filtered by equipment from ExerciseDB API."""
        endpoint = f"exercises/equipment/{equipment}"
        return await self._make_api_request(endpoint)

    async def _get_all_body_parts(self) -> List[str]:
        """Get list of all available body parts."""
        endpoint = "exercises/bodyPartList"
        return await self._make_api_request(endpoint)

    async def _get_all_target_muscles(self) -> List[str]:
        """Get list of all available target muscles."""
        endpoint = "exercises/targetList"
        return await self._make_api_request(endpoint)

    async def _get_all_equipment(self) -> List[str]:
        """Get list of all available equipment."""
        endpoint = "exercises/equipmentList"
        return await self._make_api_request(endpoint)

    def _normalize_exercise_data(self, exercise_data: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize ExerciseDB exercise data to our internal format."""
        if not exercise_data:
            return {}

        return {
            "id": exercise_data.get("id"),
            "name": exercise_data.get("name", "").title(),
            "muscle_groups": [exercise_data.get("target", "")],
            "body_part": exercise_data.get("bodyPart", ""),
            "equipment": [exercise_data.get("equipment", "")],
            "secondary_muscles": exercise_data.get("secondaryMuscles", []),
            "instructions": exercise_data.get("instructions", []),
            "gif_url": exercise_data.get("gifUrl", ""),
            "difficulty": "intermediate",  # ExerciseDB doesn't provide difficulty, default to intermediate
            "benefits": [
                "Strength development",
                f"{exercise_data.get('target', '')} strengthening",
                "Functional movement",
            ],
            "variations": [],
            "movement_pattern": self._determine_movement_pattern(
                exercise_data.get("name", "")
            ),
        }

    def _determine_movement_pattern(self, exercise_name: str) -> str:
        """Determine movement pattern based on exercise name."""
        name_lower = exercise_name.lower()

        if any(pattern in name_lower for pattern in ["squat", "lunge"]):
            return "squat"
        elif any(pattern in name_lower for pattern in ["deadlift", "rdl", "romanian"]):
            return "deadlift"
        elif any(pattern in name_lower for pattern in ["press", "bench", "overhead"]):
            return "press"
        elif any(pattern in name_lower for pattern in ["pull", "row", "chin", "lat"]):
            return "pull"
        elif any(pattern in name_lower for pattern in ["lunge", "step"]):
            return "lunge"
        elif any(pattern in name_lower for pattern in ["carry", "walk", "farmer"]):
            return "carry"
        else:
            return "other"

    async def search_exercises(
        self, search_input: ExerciseSearchInput
    ) -> ExerciseSearchOutput:
        """Search for exercises based on criteria using ExerciseDB API."""
        try:
            # Extract search parameters
            muscle_groups = search_input.muscle_groups or []
            equipment = search_input.equipment or []
            movement_pattern = search_input.movement_pattern
            difficulty_level = search_input.difficulty_level or "intermediate"

            exercises = await self._search_exercise_database(
                muscle_groups=muscle_groups,
                equipment=equipment,
                movement_pattern=movement_pattern,
                difficulty=difficulty_level,
            )

            return ExerciseSearchOutput(
                exercises=exercises,
                total_count=len(exercises),
                search_criteria=search_input.dict(),
            )

        except Exception as e:
            logger.error(f"Exercise search failed: {e}")
            return ExerciseSearchOutput(
                exercises=[],
                total_count=0,
                search_criteria=search_input.dict(),
                error=str(e),
            )

    async def get_exercise_progression(
        self, progression_input: ExerciseProgressionInput
    ) -> ExerciseProgressionOutput:
        """Get progression recommendations for an exercise."""
        try:
            exercise_name = progression_input.exercise_name
            current_level = progression_input.current_level
            user_goals = progression_input.user_goals or []

            progression_plan = self._generate_progression_plan(
                exercise=exercise_name, current_level=current_level, goals=user_goals
            )

            return ExerciseProgressionOutput(
                exercise_name=exercise_name,
                current_level=current_level,
                progression_steps=progression_plan["steps"],
                estimated_timeline=progression_plan["timeline"],
                key_milestones=progression_plan["milestones"],
            )

        except Exception as e:
            logger.error(f"Exercise progression failed: {e}")
            return ExerciseProgressionOutput(
                exercise_name=progression_input.exercise_name,
                current_level=progression_input.current_level,
                progression_steps=[],
                estimated_timeline="Unable to determine",
                key_milestones=[],
                error=str(e),
            )

    async def _search_exercise_database(
        self,
        muscle_groups: List[str],
        equipment: List[str],
        movement_pattern: Optional[str],
        difficulty: str,
    ) -> List[Dict[str, Any]]:
        """Internal exercise database search logic using ExerciseDB API."""

        all_exercises = []

        try:
            # Search by muscle groups
            if muscle_groups:
                for muscle_group in muscle_groups:
                    # Try to find exercises by target muscle
                    muscle_exercises = await self._get_exercises_by_target_muscle(
                        muscle_group.lower()
                    )
                    if muscle_exercises:
                        all_exercises.extend(muscle_exercises)

                    # Also try body part search
                    for body_part, muscles in self.body_part_mapping.items():
                        if muscle_group.lower() in [m.lower() for m in muscles]:
                            body_part_exercises = (
                                await self._get_exercises_by_body_part(body_part)
                            )
                            if body_part_exercises:
                                all_exercises.extend(body_part_exercises)

            # Search by equipment
            if equipment:
                for equip in equipment:
                    # Map our equipment terms to ExerciseDB format
                    exercisedb_equipment = None
                    for db_equip, our_terms in self.equipment_mapping.items():
                        if equip.lower() in [term.lower() for term in our_terms]:
                            exercisedb_equipment = db_equip
                            break

                    if exercisedb_equipment:
                        equipment_exercises = await self._get_exercises_by_equipment(
                            exercisedb_equipment
                        )
                        if equipment_exercises:
                            all_exercises.extend(equipment_exercises)

            # If no specific criteria, get some general exercises
            if not muscle_groups and not equipment:
                # Get exercises from common body parts
                for body_part in ["chest", "back", "upper legs"]:
                    body_part_exercises = await self._get_exercises_by_body_part(
                        body_part
                    )
                    if body_part_exercises:
                        all_exercises.extend(
                            body_part_exercises[:5]
                        )  # Limit to 5 per body part

            # Remove duplicates based on exercise ID
            unique_exercises = {}
            for exercise in all_exercises:
                if exercise.get("id") not in unique_exercises:
                    unique_exercises[exercise.get("id")] = exercise

            # Normalize and filter exercises
            normalized_exercises = []
            for exercise in unique_exercises.values():
                normalized = self._normalize_exercise_data(exercise)
                if normalized:
                    # Apply movement pattern filter
                    if (
                        movement_pattern
                        and normalized.get("movement_pattern") != movement_pattern
                    ):
                        continue
                    normalized_exercises.append(normalized)

            # Limit results to avoid overwhelming response
            return normalized_exercises[:20]

        except Exception as e:
            logger.error(f"ExerciseDB search failed: {e}")
            # Fallback to mock data if API fails
            return self._get_fallback_exercises(
                muscle_groups, equipment, movement_pattern, difficulty
            )

    def _get_fallback_exercises(
        self,
        muscle_groups: List[str],
        equipment: List[str],
        movement_pattern: Optional[str],
        difficulty: str,
    ) -> List[Dict[str, Any]]:
        """Fallback exercise data when API is unavailable."""
        exercises_db = [
            {
                "id": "fallback_1",
                "name": "Barbell Back Squat",
                "muscle_groups": ["quadriceps", "glutes", "hamstrings", "core"],
                "equipment": ["barbell", "squat_rack"],
                "movement_pattern": "squat",
                "difficulty": "intermediate",
                "benefits": [
                    "leg strength",
                    "power development",
                    "athletic performance",
                ],
                "variations": ["high_bar", "low_bar", "box_squat"],
                "instructions": [
                    "Set up barbell on squat rack",
                    "Position bar on upper back",
                    "Squat down keeping chest up",
                ],
                "gif_url": "",
            },
            {
                "id": "fallback_2",
                "name": "Conventional Deadlift",
                "muscle_groups": [
                    "hamstrings",
                    "glutes",
                    "erector_spinae",
                    "traps",
                    "lats",
                ],
                "equipment": ["barbell"],
                "movement_pattern": "deadlift",
                "difficulty": "intermediate",
                "benefits": [
                    "total body strength",
                    "posterior chain",
                    "functional strength",
                ],
                "variations": ["sumo", "deficit", "pause"],
                "instructions": [
                    "Set up bar over mid-foot",
                    "Grip bar with hands outside legs",
                    "Drive through heels to stand",
                ],
                "gif_url": "",
            },
            {
                "id": "fallback_3",
                "name": "Push-ups",
                "muscle_groups": ["chest", "shoulders", "triceps", "core"],
                "equipment": ["bodyweight"],
                "movement_pattern": "press",
                "difficulty": "beginner",
                "benefits": ["upper body strength", "core stability", "endurance"],
                "variations": ["incline", "decline", "diamond", "single_arm"],
                "instructions": [
                    "Start in plank position",
                    "Lower chest to ground",
                    "Push back to starting position",
                ],
                "gif_url": "",
            },
            {
                "id": "fallback_4",
                "name": "Pull-ups",
                "muscle_groups": ["lats", "rhomboids", "biceps", "rear_delts"],
                "equipment": ["pull_up_bar"],
                "movement_pattern": "pull",
                "difficulty": "intermediate",
                "benefits": ["back strength", "grip strength", "relative strength"],
                "variations": ["chin_ups", "neutral_grip", "wide_grip", "weighted"],
                "instructions": [
                    "Hang from pull-up bar",
                    "Pull body up until chin over bar",
                    "Lower with control",
                ],
                "gif_url": "",
            },
        ]

        # Filter exercises based on criteria
        filtered_exercises = []
        for exercise in exercises_db:
            # Check muscle group match
            muscle_match = not muscle_groups or any(
                mg.lower() in [m.lower() for m in exercise["muscle_groups"]]
                for mg in muscle_groups
            )

            # Check equipment match
            equipment_match = not equipment or any(
                eq.lower() in [e.lower() for e in exercise["equipment"]]
                for eq in equipment
            )

            # Check movement pattern match
            pattern_match = (
                not movement_pattern or exercise["movement_pattern"] == movement_pattern
            )

            if muscle_match and equipment_match and pattern_match:
                filtered_exercises.append(exercise)

        return filtered_exercises

    def _generate_progression_plan(
        self, exercise: str, current_level: str, goals: List[str]
    ) -> Dict[str, Any]:
        """Generate progression plan for specific exercise."""

        # Sample progression logic (would be more sophisticated in production)
        progression_templates = {
            "push-ups": {
                "beginner": {
                    "steps": [
                        "Wall push-ups (3x10)",
                        "Incline push-ups on bench (3x8)",
                        "Knee push-ups (3x5)",
                        "Full push-ups (3x3)",
                        "Full push-ups (3x8)",
                    ],
                    "timeline": "6-8 weeks",
                    "milestones": ["First full push-up", "10 consecutive push-ups"],
                },
                "intermediate": {
                    "steps": [
                        "Standard push-ups (3x15)",
                        "Diamond push-ups (3x8)",
                        "Decline push-ups (3x10)",
                        "Single-arm progression prep",
                        "Archer push-ups (3x5 each side)",
                    ],
                    "timeline": "8-12 weeks",
                    "milestones": ["20 consecutive push-ups", "First diamond push-up"],
                },
            },
            "pull-ups": {
                "beginner": {
                    "steps": [
                        "Dead hangs (3x20s)",
                        "Assisted pull-ups with band (3x5)",
                        "Negative pull-ups (3x3)",
                        "First full pull-up",
                        "Full pull-ups (3x3)",
                    ],
                    "timeline": "8-12 weeks",
                    "milestones": ["60-second dead hang", "First unassisted pull-up"],
                }
            },
        }

        exercise_key = exercise.lower().replace(" ", "_").replace("-", "_")
        if (
            exercise_key in progression_templates
            and current_level in progression_templates[exercise_key]
        ):
            return progression_templates[exercise_key][current_level]

        # Default progression template
        return {
            "steps": [
                f"Master current {exercise} form",
                f"Increase {exercise} volume gradually",
                f"Add {exercise} variations",
                f"Progress to advanced {exercise} techniques",
            ],
            "timeline": "6-10 weeks",
            "milestones": [
                f"Consistent {exercise} performance",
                f"Advanced {exercise} variation",
            ],
        }


# Note: LangChain tool wrappers have been moved to __init__.py for consistency
# This avoids conflicts between different tool definitions
