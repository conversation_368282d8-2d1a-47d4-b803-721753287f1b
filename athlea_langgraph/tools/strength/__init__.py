"""
Strength Training Tools

This module provides comprehensive tools for strength training assessment,
exercise database management, and program design.
"""

import json
import logging

# LangChain tool integrations
from langchain_core.tools import tool
from pydantic import BaseModel, Field
from typing import List, Optional

from ..base_tool import BaseDomainTool
from .exercise_database import StrengthExerciseDatabase
from .strength_assessment import (
    EquipmentInput,
    ExperienceInput,
    GoalsInput,
    MovementScreenInput,
    StrengthAssessmentInput,
    StrengthAssessmentOutput,
    StrengthAssessmentTool,
    StrengthTestingInput,
)
from ..schemas.strength_schemas import (
    SearchStrengthExercisesSchema,
    ExerciseSearchInput,
    ExerciseProgressionInput,
)

# Import web search fallback - use WebSearchGraph workflow instead of basic tool
try:
    from ..web_search_graph_tool import WebSearchGraphTool

    WEB_SEARCH_AVAILABLE = True
except ImportError:
    WEB_SEARCH_AVAILABLE = False

logger = logging.getLogger(__name__)

# Mapping dictionaries to convert natural language to enum values
MUSCLE_GROUP_MAPPING = {
    # Natural terms to enum values
    "legs": ["quadriceps", "hamstrings", "glutes", "calves"],
    "upper_body": [
        "chest",
        "shoulders",
        "triceps",
        "biceps",
        "lats",
        "rhomboids",
        "traps",
    ],
    "arms": ["triceps", "biceps", "forearms"],
    "back": ["lats", "rhomboids", "traps", "erector_spinae"],
    "abs": ["core"],
    "leg": ["quadriceps", "hamstrings", "glutes", "calves"],
    "arm": ["triceps", "biceps", "forearms"],
    # Direct mappings
    "quadriceps": ["quadriceps"],
    "hamstrings": ["hamstrings"],
    "glutes": ["glutes"],
    "calves": ["calves"],
    "chest": ["chest"],
    "shoulders": ["shoulders"],
    "triceps": ["triceps"],
    "biceps": ["biceps"],
    "lats": ["lats"],
    "rhomboids": ["rhomboids"],
    "traps": ["traps"],
    "erector_spinae": ["erector_spinae"],
    "core": ["core"],
    "forearms": ["forearms"],
}

MOVEMENT_PATTERN_MAPPING = {
    # Natural terms to enum values
    "balance": "combination",
    "stability": "combination",
    "functional": "combination",
    "compound": "combination",
    "isolation": "combination",
    # Direct mappings
    "squat": "squat",
    "deadlift": "deadlift",
    "press": "press",
    "pull": "pull",
    "lunge": "lunge",
    "carry": "carry",
    "rotation": "rotation",
    "combination": "combination",
}

EQUIPMENT_MAPPING = {
    # Natural terms to enum values
    "dumbbell": "dumbbells",
    "barbell": "barbell",
    "machine": "machines",
    "band": "resistance_bands",
    "bands": "resistance_bands",
    "kettlebell": "kettlebells",
    "cable": "cables",
    "cables": "cables",  # Handle both singular and plural
    "pullup_bar": "pull_up_bar",
    "pull_up": "pull_up_bar",
    "bodyweight": "bodyweight",
    "no_equipment": "bodyweight",
    "free_weights": "dumbbells",
    "weights": "dumbbells",
    # Direct mappings
    "dumbbells": "dumbbells",
    "kettlebells": "kettlebells",
    "resistance_bands": "resistance_bands",
    "pull_up_bar": "pull_up_bar",
    "squat_rack": "squat_rack",
    "bench": "bench",
    "machines": "machines",
    "suspension_trainer": "suspension_trainer",
    "medicine_ball": "medicine_ball",
}


def parse_and_map_input(input_str: str) -> dict:
    """
    Parse JSON input and intelligently map natural language terms to enum values.
    """
    try:
        # Parse the JSON input
        input_data = json.loads(input_str) if isinstance(input_str, str) else input_str

        # Map muscle groups
        if "muscle_groups" in input_data and input_data["muscle_groups"]:
            mapped_muscle_groups = []
            for muscle_group in input_data["muscle_groups"]:
                muscle_group_lower = muscle_group.lower()
                if muscle_group_lower in MUSCLE_GROUP_MAPPING:
                    mapped_muscle_groups.extend(
                        MUSCLE_GROUP_MAPPING[muscle_group_lower]
                    )
                else:
                    # Try partial matching
                    for key, values in MUSCLE_GROUP_MAPPING.items():
                        if muscle_group_lower in key or key in muscle_group_lower:
                            mapped_muscle_groups.extend(values)
                            break

            # Remove duplicates and limit to 5 as per schema validation
            input_data["muscle_groups"] = list(set(mapped_muscle_groups))[:5]

        # Map movement pattern
        if "movement_pattern" in input_data and input_data["movement_pattern"]:
            pattern = input_data["movement_pattern"].lower()
            if pattern in MOVEMENT_PATTERN_MAPPING:
                input_data["movement_pattern"] = MOVEMENT_PATTERN_MAPPING[pattern]
            else:
                # Default to combination for unrecognized patterns
                input_data["movement_pattern"] = "combination"

        # Map equipment
        if "equipment" in input_data and input_data["equipment"]:
            mapped_equipment = []
            for equip in input_data["equipment"]:
                equip_lower = equip.lower()
                if equip_lower in EQUIPMENT_MAPPING:
                    mapped_equipment.append(EQUIPMENT_MAPPING[equip_lower])
                else:
                    # Try partial matching
                    for key, value in EQUIPMENT_MAPPING.items():
                        if equip_lower in key or key in equip_lower:
                            mapped_equipment.append(value)
                            break

            # Remove duplicates and limit to 8 as per schema validation
            input_data["equipment"] = list(set(mapped_equipment))[:8]

        return input_data

    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON input: {e}")
    except Exception as e:
        raise ValueError(f"Error processing input: {e}")


# Initialize WebSearchGraph tool if available
_web_search_graph_tool = None
if WEB_SEARCH_AVAILABLE:
    try:
        _web_search_graph_tool = WebSearchGraphTool()
        logger.info("✅ WebSearchGraph workflow initialized for strength tools")
    except Exception as e:
        logger.warning(f"⚠️ WebSearchGraph workflow failed to initialize: {e}")
        _web_search_graph_tool = None


async def web_search_fallback(query: str, context: str = "") -> str:
    """
    Fallback to WebSearchGraph workflow when structured outputs fail.

    Args:
        query: The search query
        context: Additional context for the search

    Returns:
        JSON string with web search results formatted for strength coaching
    """
    if not _web_search_graph_tool:
        return json.dumps(
            {
                "exercises": [],
                "total_count": 0,
                "search_criteria": {},
                "error": "WebSearchGraph workflow not available",
                "fallback_used": True,
            }
        )

    try:
        # Create a comprehensive search query for strength training
        search_query = f"strength training exercises {query}"
        if context:
            search_query += f" {context}"

        logger.info(
            f"🔍 FALLBACK: Using WebSearchGraph workflow for strength query: {search_query}"
        )

        # Use the WebSearchGraph workflow tool
        search_result = await _web_search_graph_tool.ainvoke({"query": search_query})

        # Parse the web search results and format for strength coaching
        fallback_result = {
            "exercises": [
                {
                    "name": "Web Search Results",
                    "description": "Fallback search results from WebSearchGraph workflow",
                    "instructions": search_result[:500],  # Truncate for readability
                    "muscle_groups": ["general"],
                    "equipment": ["various"],
                    "difficulty": "intermediate",
                }
            ],
            "total_count": 1,
            "search_criteria": {"query": query, "fallback": True},
            "search_source": "web_search_graph_workflow",
            "fallback_used": True,
            "web_search_results": search_result,
        }

        logger.info(f"✅ FALLBACK: WebSearchGraph workflow completed successfully")
        return json.dumps(fallback_result, indent=2)

    except Exception as e:
        logger.error(f"❌ FALLBACK: WebSearchGraph workflow failed: {e}")
        return json.dumps(
            {
                "exercises": [],
                "total_count": 0,
                "search_criteria": {"query": query},
                "error": f"Both structured search and WebSearchGraph workflow failed: {str(e)}",
                "fallback_used": True,
            }
        )


@tool
async def search_strength_exercises(input_json: str) -> str:
    """Search for strength training exercises based on specific criteria.

    Input should be a JSON string with the following structure:
    {
        "muscle_groups": ["chest", "shoulders"],  // ALLOWED VALUES ONLY: "quadriceps", "hamstrings", "glutes", "calves", "chest", "shoulders", "triceps", "biceps", "lats", "rhomboids", "traps", "erector_spinae", "core", "forearms"
        "equipment": ["dumbbells", "barbell"],    // ALLOWED VALUES ONLY: "bodyweight", "barbell", "dumbbells", "kettlebells", "resistance_bands", "pull_up_bar", "squat_rack", "bench", "cables", "machines", "suspension_trainer", "medicine_ball"
        "movement_pattern": "press",              // ALLOWED VALUES ONLY: "squat", "deadlift", "press", "pull", "lunge", "carry", "rotation", "combination"
        "difficulty_level": "intermediate",       // ALLOWED VALUES: "beginner", "intermediate", "advanced"
        "max_results": 10
    }

    SMART MAPPING: This tool intelligently maps common terms:
    - "legs" -> ["quadriceps", "hamstrings", "glutes", "calves"]
    - "arms" -> ["triceps", "biceps", "forearms"]
    - "balance" -> "combination"
    - "dumbbell" -> "dumbbells"

    WEB SEARCH FALLBACK: If structured search fails, automatically falls back to web search.

    Returns a JSON string with exercises, search criteria, and any errors.
    """
    try:
        # Parse and intelligently map input
        mapped_input = parse_and_map_input(input_json)

        try:
            # Create the validated input
            search_input = ExerciseSearchInput(**mapped_input)
        except Exception as validation_error:
            # If validation fails even after mapping, trigger fallback
            logger.warning(f"⚠️ Validation failed after mapping: {validation_error}")
            raise Exception(f"Validation error: {validation_error}")

        # Create database instance and search
        database = StrengthExerciseDatabase()
        result = await database.search_exercises(search_input)

        return json.dumps(result.model_dump(), indent=2)

    except Exception as e:
        logger.warning(f"⚠️ Structured search failed: {e}")

        # Extract search context for fallback
        try:
            parsed_input = (
                json.loads(input_json) if isinstance(input_json, str) else input_json
            )
            muscle_groups = parsed_input.get("muscle_groups", [])
            equipment = parsed_input.get("equipment", [])
            movement_pattern = parsed_input.get("movement_pattern", "")

            search_context = f"muscle groups: {', '.join(muscle_groups)} equipment: {', '.join(equipment)} movement: {movement_pattern}".strip()
            search_query = f"exercises {search_context}".replace("  ", " ")

        except Exception:
            search_query = "strength training exercises"
            search_context = ""

        # Try WebSearchGraph workflow fallback
        try:
            fallback_result = await web_search_fallback(search_query, search_context)
            return fallback_result

        except Exception as fallback_error:
            logger.error(
                f"❌ Both structured search and fallback failed: {fallback_error}"
            )

            # Final fallback - basic error response
            error_result = {
                "exercises": [],
                "total_count": 0,
                "search_criteria": {},
                "error": f"Exercise search failed: {str(e)}. Fallback also failed: {str(fallback_error)}",
                "fallback_attempted": True,
            }
            return json.dumps(error_result, indent=2)


@tool
def get_exercise_progression(input_json: str) -> str:
    """Get progression steps for a specific strength exercise.

    Input should be a JSON string with the following structure:
    {
        "exercise_name": "push-ups",
        "current_level": "beginner",        // ALLOWED VALUES: "beginner", "intermediate", "advanced"
        "user_goals": ["increase reps", "build strength"],
        "current_performance": {
            "max_reps": 10,
            "sets": 3,
            "frequency_per_week": 3
        },
        "time_availability": "3 sessions per week, 30 minutes each"
    }

    WEB SEARCH FALLBACK: If structured progression fails, automatically searches web for progression guides.

    Returns a JSON string with progression steps, timeline, and milestones.
    """
    try:
        # Parse input
        input_data = (
            json.loads(input_json) if isinstance(input_json, str) else input_json
        )

        # Create the validated input
        progression_input = ExerciseProgressionInput(**input_data)

        # Create database instance
        database = StrengthExerciseDatabase()

        # Get progression (this would call the actual progression method)
        # For now, return a structured response
        result = {
            "exercise_name": progression_input.exercise_name,
            "current_level": progression_input.current_level,
            "progression_steps": [
                f"Master current form with {progression_input.current_performance.get('max_reps', 'current')} reps",
                "Increase reps by 2-3 per week",
                "Add extra set when reaching target reps",
                "Progress to more challenging variation",
            ],
            "estimated_timeline": "4-8 weeks depending on consistency",
            "key_milestones": [
                "Perfect form achievement",
                "Target rep range completion",
                "Consistent performance across sets",
            ],
            "prerequisite_skills": ["basic movement pattern", "core stability"],
            "next_progression": f"Advanced {progression_input.exercise_name} variation",
        }

        return json.dumps(result, indent=2)

    except Exception as e:
        logger.warning(f"⚠️ Structured progression failed: {e}")

        # Extract exercise name for fallback search
        try:
            parsed_input = (
                json.loads(input_json) if isinstance(input_json, str) else input_json
            )
            exercise_name = parsed_input.get("exercise_name", "exercise")
            current_level = parsed_input.get("current_level", "beginner")

            search_query = f"{exercise_name} progression {current_level} training plan"
            search_context = f"progression guide for {exercise_name}"

        except Exception:
            search_query = "exercise progression training plan"
            search_context = "progression guidance"

        # Try web search fallback
        import asyncio

        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                import concurrent.futures

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(
                        asyncio.run, web_search_fallback(search_query, search_context)
                    )
                    fallback_result = future.result(timeout=10)
            else:
                fallback_result = asyncio.run(
                    web_search_fallback(search_query, search_context)
                )

            # Parse fallback result and reformat for progression
            try:
                fallback_data = json.loads(fallback_result)
                progression_result = {
                    "exercise_name": (
                        exercise_name if "exercise_name" in locals() else "unknown"
                    ),
                    "current_level": (
                        current_level if "current_level" in locals() else "unknown"
                    ),
                    "progression_steps": [
                        "See web search results for progression guidance",
                        "Follow evidence-based progression principles",
                        "Gradually increase difficulty over time",
                    ],
                    "estimated_timeline": "4-12 weeks depending on consistency",
                    "key_milestones": [
                        "Form mastery",
                        "Strength gains",
                        "Progression to next level",
                    ],
                    "web_search_results": fallback_data.get("web_search_results", ""),
                    "fallback_used": True,
                }
                return json.dumps(progression_result, indent=2)
            except Exception:
                return fallback_result

        except Exception as fallback_error:
            logger.error(
                f"❌ Both structured progression and fallback failed: {fallback_error}"
            )

            error_result = {
                "exercise_name": "unknown",
                "current_level": "unknown",
                "progression_steps": [],
                "estimated_timeline": "unknown",
                "key_milestones": [],
                "error": f"Progression planning failed: {str(e)}. Fallback also failed: {str(fallback_error)}",
                "fallback_attempted": True,
            }
            return json.dumps(error_result, indent=2)


@tool
def comprehensive_strength_assessment(assessment_input: str) -> str:
    """Perform comprehensive strength assessment including movement screening and recommendations.

    Input should be a JSON string with assessment parameters:
    {
        "age": 30,
        "gender": "male",              // ALLOWED VALUES: "male", "female", "other"
        "weight_kg": 75,
        "experience_years": 2,
        "goals": ["build muscle", "get stronger"],
        "training_frequency": 3
    }

    WEB SEARCH FALLBACK: If structured assessment fails, searches for assessment guidelines.
    """
    try:
        # Parse input
        input_data = json.loads(assessment_input)

        # Create assessment input
        strength_input = StrengthAssessmentInput(**input_data)

        # Create and run assessment
        assessment_tool = StrengthAssessmentTool()
        result = assessment_tool.assess_strength_level(strength_input)

        return json.dumps(result.model_dump(), indent=2)

    except Exception as e:
        logger.warning(f"⚠️ Structured assessment failed: {e}")

        # Extract user info for fallback search
        try:
            parsed_input = json.loads(assessment_input)
            age = parsed_input.get("age", "adult")
            experience = parsed_input.get("experience_years", 0)
            goals = ", ".join(parsed_input.get("goals", ["general fitness"]))

            search_query = f"strength assessment {age} years old {experience} years experience {goals}"
            search_context = f"fitness assessment guidelines"

        except Exception:
            search_query = "strength fitness assessment guidelines"
            search_context = "assessment methodology"

        # Try web search fallback
        import asyncio

        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                import concurrent.futures

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(
                        asyncio.run, web_search_fallback(search_query, search_context)
                    )
                    fallback_result = future.result(timeout=10)
            else:
                fallback_result = asyncio.run(
                    web_search_fallback(search_query, search_context)
                )

            # Parse and reformat for assessment
            try:
                fallback_data = json.loads(fallback_result)
                assessment_result = {
                    "assessment_summary": "Assessment based on web search results",
                    "strength_level": "intermediate",  # Safe default
                    "recommendations": [
                        "Consult web search results for detailed guidance",
                        "Follow progressive overload principles",
                        "Focus on compound movements",
                    ],
                    "web_search_results": fallback_data.get("web_search_results", ""),
                    "fallback_used": True,
                }
                return json.dumps(assessment_result, indent=2)
            except Exception:
                return fallback_result

        except Exception as fallback_error:
            logger.error(
                f"❌ Both structured assessment and fallback failed: {fallback_error}"
            )

            error_result = {
                "assessment_summary": "Assessment failed",
                "strength_level": "unknown",
                "recommendations": [],
                "error": f"Assessment failed: {str(e)}. Fallback also failed: {str(fallback_error)}",
                "fallback_attempted": True,
            }
            return json.dumps(error_result, indent=2)


# Export the tools
__all__ = [
    "StrengthExerciseDatabase",
    "StrengthAssessmentTool",
    "StrengthAssessmentInput",
    "StrengthAssessmentOutput",
    "MovementScreenInput",
    "StrengthTestingInput",
    "ExperienceInput",
    "EquipmentInput",
    "GoalsInput",
    "search_strength_exercises",
    "get_exercise_progression",
    "comprehensive_strength_assessment",
    "StrengthExerciseSearchParams",
    "ExerciseProgressionParams",
    "web_search_fallback",
]
