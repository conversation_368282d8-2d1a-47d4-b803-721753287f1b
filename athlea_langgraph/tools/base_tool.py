"""
Base Hardened Tool Implementation

Provides a robust foundation for all external tools with:
- Schema validation using Pydantic
- Circuit breaker pattern
- Timeout protection and exponential backoff retries
- Structured error handling and logging
- Performance metrics tracking

Updated to include BaseDomainTool for Phase 2: Tool Layer Organization
"""

import asyncio
import logging
import time
import uuid
import json
from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from functools import wraps
from typing import Any, Dict, Generic, List, Optional, TypeVar, Union, Type

from prometheus_client import Counter, Gauge, Histogram
from pydantic import BaseModel, Field, ValidationError, ConfigDict

# Metrics for tool performance
tool_calls_total = Counter(
    "tool_calls_total", "Total tool calls", ["tool_name", "status", "error_type"]
)

tool_duration_seconds = Histogram(
    "tool_duration_seconds", "Tool execution time", ["tool_name", "status"]
)

active_tool_calls = Gauge(
    "active_tool_calls", "Number of active tool calls", ["tool_name"]
)

logger = logging.getLogger(__name__)

# Type variable for input/output schemas
InputSchema = TypeVar("InputSchema", bound=BaseModel)
OutputSchema = TypeVar("OutputSchema", bound=BaseModel)


class ToolErrorType(Enum):
    """Types of tool errors for classification and handling."""

    VALIDATION_ERROR = "validation_error"
    TIMEOUT_ERROR = "timeout_error"
    CIRCUIT_BREAKER_OPEN = "circuit_breaker_open"
    EXTERNAL_SERVICE_ERROR = "external_service_error"
    NETWORK_ERROR = "network_error"
    AUTHENTICATION_ERROR = "authentication_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    UNKNOWN_ERROR = "unknown_error"


class ToolError(Exception):
    """Structured error for tool failures."""

    def __init__(
        self,
        message: str,
        error_type: ToolErrorType = ToolErrorType.UNKNOWN_ERROR,
        retry_after: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(message)
        self.message = message
        self.error_type = error_type
        self.retry_after = retry_after
        self.metadata = metadata or {}
        self.timestamp = datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary for logging/serialization."""
        return {
            "message": self.message,
            "error_type": self.error_type.value,
            "retry_after": self.retry_after,
            "metadata": self.metadata,
            "timestamp": self.timestamp.isoformat(),
        }


class ToolResponse(BaseModel, Generic[OutputSchema]):
    """Standardized tool response with metadata."""

    data: OutputSchema
    success: bool = True
    execution_time_ms: float
    tool_name: str
    request_id: str
    metadata: Dict[str, Any] = Field(default_factory=dict)

    model_config = ConfigDict(arbitrary_types_allowed=True)


class ToolFallbackResponse(BaseModel):
    """Fallback response when tool fails gracefully."""

    message: str
    success: bool = False
    error_type: str
    retry_after: Optional[int] = None
    fallback_data: Optional[Dict[str, Any]] = None


class AIValidationError(Exception):
    """Enhanced validation error with AI-friendly guidance."""

    def __init__(
        self,
        message: str,
        required_fields: Dict[str, Any] = None,
        example_input: Dict[str, Any] = None,
        suggestions: List[str] = None,
    ):
        self.message = message
        self.required_fields = required_fields or {}
        self.example_input = example_input or {}
        self.suggestions = suggestions or []
        super().__init__(self.message)

    def to_ai_response(self) -> Dict[str, Any]:
        """Convert to AI-friendly error response."""
        response = {
            "error": self.message,
            "status": "validation_failed",
            "guidance": {
                "required_fields": self.required_fields,
                "example_input": self.example_input,
                "suggestions": self.suggestions,
            },
        }
        return response


def ai_friendly_tool(
    schema_class: Type[BaseModel] = None,
    example_input: Dict[str, Any] = None,
    field_mappings: Dict[str, str] = None,
):
    """
    Decorator to make tools more AI-friendly with better validation and error handling.

    Args:
        schema_class: Pydantic model class for input validation
        example_input: Example of valid input for AI reference
        field_mappings: Map alternative field names to standard ones
    """

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract input data from args
            input_data = None
            if args and isinstance(args[0], str):
                try:
                    input_data = json.loads(args[0])
                except json.JSONDecodeError as e:
                    return json.dumps(
                        {
                            "error": f"Invalid JSON input: {str(e)}",
                            "status": "json_parse_failed",
                            "guidance": {
                                "suggestion": "Ensure input is valid JSON format",
                                "example": example_input or {"key": "value"},
                            },
                        },
                        indent=2,
                    )

            # Apply field mappings if provided
            if input_data and field_mappings:
                mapped_data = {}
                for key, value in input_data.items():
                    mapped_key = field_mappings.get(key, key)
                    mapped_data[mapped_key] = value
                input_data = mapped_data

            # Validate with schema if provided
            if schema_class and input_data:
                try:
                    validated_data = schema_class(**input_data)
                    # Replace the first argument with validated data
                    new_args = (json.dumps(validated_data.model_dump()),) + args[1:]
                    return await func(*new_args, **kwargs)
                except ValidationError as e:
                    return _handle_validation_error(e, schema_class, example_input)

            return await func(*args, **kwargs)

        # Enhance the function's docstring with AI-friendly information
        if schema_class and example_input:
            original_doc = func.__doc__ or ""
            enhanced_doc = f"""{original_doc}

AI Usage Guide:
=============
Required Input Schema: {schema_class.__name__}
Example Input:
{json.dumps(example_input, indent=2)}

Required Fields:
{_generate_field_docs(schema_class)}
"""
            wrapper.__doc__ = enhanced_doc

        return wrapper

    return decorator


def _handle_validation_error(
    error: ValidationError,
    schema_class: Type[BaseModel],
    example_input: Dict[str, Any] = None,
) -> str:
    """Convert Pydantic validation error to AI-friendly response."""
    missing_fields = {}
    suggestions = []

    for err in error.errors():
        field_name = err["loc"][0] if err["loc"] else "unknown"
        error_type = err["type"]

        if error_type == "missing":
            field_info = schema_class.model_fields.get(field_name)
            if field_info:
                missing_fields[field_name] = {
                    "description": field_info.description or "Required field",
                    "type": (
                        str(field_info.annotation) if field_info.annotation else "any"
                    ),
                }
                suggestions.append(
                    f"Add required field '{field_name}': {field_info.description or 'This field is required'}"
                )

    ai_error = AIValidationError(
        message=f"Input validation failed for {schema_class.__name__}",
        required_fields=missing_fields,
        example_input=example_input,
        suggestions=suggestions,
    )

    return json.dumps(ai_error.to_ai_response(), indent=2)


def _generate_field_docs(schema_class: Type[BaseModel]) -> str:
    """Generate field documentation from Pydantic model."""
    docs = []
    for field_name, field_info in schema_class.model_fields.items():
        field_type = str(field_info.annotation) if field_info.annotation else "any"
        description = field_info.description or "No description available"
        required = "Required" if field_info.is_required() else "Optional"
        docs.append(f"  - {field_name} ({field_type}): {description} [{required}]")
    return "\n".join(docs)


class BaseDomainTool(ABC):
    """
    Enhanced base class for domain-specific tools with AI-friendly validation.
    """

    def __init__(self):
        self.domain: str = ""
        self.name: str = ""
        self.description: str = ""
        self.created_at = datetime.now()

    def get_tool_description(self) -> str:
        """Return a detailed description of the tool for AI agents."""
        # Provide a default implementation to avoid abstract method errors
        return f"""
{self.description or 'Domain-specific tool'}

Domain: {self.domain or 'unknown'}
Tool: {self.name or 'unnamed_tool'}

This tool provides domain-specific functionality for athletic training and coaching.
For detailed information about this tool's capabilities, please refer to the tool's documentation.
"""

    def validate_input(
        self, data: Dict[str, Any], schema_class: Type[BaseModel]
    ) -> BaseModel:
        """Validate input data against a Pydantic schema with AI-friendly errors."""
        try:
            return schema_class(**data)
        except ValidationError as e:
            logger.error(f"Validation failed for {self.name}: {e}")
            raise AIValidationError(
                message=f"Input validation failed for {self.name}",
                required_fields=self._extract_required_fields(e, schema_class),
            )

    def _extract_required_fields(
        self, error: ValidationError, schema_class: Type[BaseModel]
    ) -> Dict[str, Any]:
        """Extract required fields information from validation error."""
        required_fields = {}
        for err in error.errors():
            if err["type"] == "missing":
                field_name = err["loc"][0] if err["loc"] else "unknown"
                field_info = schema_class.model_fields.get(field_name)
                if field_info:
                    required_fields[field_name] = {
                        "description": field_info.description or "Required field",
                        "type": (
                            str(field_info.annotation)
                            if field_info.annotation
                            else "any"
                        ),
                    }
        return required_fields

    def create_success_response(self, data: Any, message: str = "Success") -> str:
        """Create a standardized success response."""
        return json.dumps(
            {
                "status": "success",
                "message": message,
                "data": data,
                "timestamp": datetime.now().isoformat(),
            },
            indent=2,
            default=str,
        )

    def create_error_response(self, error: str, suggestions: List[str] = None) -> str:
        """Create a standardized error response with AI guidance."""
        return json.dumps(
            {
                "status": "error",
                "error": error,
                "suggestions": suggestions or [],
                "timestamp": datetime.now().isoformat(),
            },
            indent=2,
        )

    def log_tool_usage(self, input_data: Dict[str, Any], success: bool = True):
        """Log tool usage for monitoring and debugging."""
        logger.info(
            f"Tool {self.name} used - Success: {success} - Input keys: {list(input_data.keys())}"
        )


def create_ai_friendly_docstring(
    tool_name: str,
    description: str,
    schema_class: Type[BaseModel],
    example_input: Dict[str, Any],
) -> str:
    """Create enhanced docstring with AI guidance."""
    field_docs = _generate_field_docs(schema_class)

    return f"""{description}

Tool: {tool_name}
Schema: {schema_class.__name__}

Example Usage:
{json.dumps(example_input, indent=2)}

Input Fields:
{field_docs}

Note: All fields marked as [Required] must be provided. The tool will guide you
if any required fields are missing and suggest corrections.
"""


class HardenedTool(BaseModel, Generic[InputSchema, OutputSchema]):
    """
    Base class for hardened tools with comprehensive error handling.

    Features:
    - Input/output schema validation
    - Circuit breaker pattern
    - Timeout protection with exponential backoff
    - Structured logging and metrics
    - Graceful degradation
    """

    model_config = ConfigDict(arbitrary_types_allowed=True)

    name: str
    input_schema: type[InputSchema]
    output_schema: type[OutputSchema]
    timeout_seconds: float = 10.0
    max_retries: int = 3
    circuit_breaker: Optional[Any] = None
    enable_metrics: bool = True

    # Internal state - made public to comply with Pydantic v2
    last_failure_time: Optional[float] = Field(None, exclude=True)
    consecutive_failures: int = Field(0, exclude=True)

    def __init__(self, **data: Any):
        super().__init__(**data)
        logger.info(f"Initialized hardened tool: {self.name}")

    async def invoke(
        self, input_data: Dict[str, Any]
    ) -> Union[ToolResponse[OutputSchema], ToolFallbackResponse]:
        """
        Main entry point for tool execution with full hardening.

        Args:
            input_data: Raw input data to validate and process

        Returns:
            ToolResponse with validated output or ToolFallbackResponse on failure
        """
        request_id = str(uuid.uuid4())
        start_time = time.time()

        # Track active calls
        if self.enable_metrics:
            active_tool_calls.labels(tool_name=self.name).inc()

        try:
            # Log tool invocation
            logger.info(f"[{request_id}] Invoking tool: {self.name}")

            # 1. Input validation
            try:
                validated_input = self.input_schema(**input_data)
            except ValidationError as e:
                error = ToolError(
                    f"Input validation failed: {e}",
                    ToolErrorType.VALIDATION_ERROR,
                    metadata={"validation_errors": e.errors()},
                )
                return await self._handle_error(error, request_id, start_time)

            # 2. Circuit breaker check
            if self.circuit_breaker and self.circuit_breaker.is_open:
                retry_after = (
                    int(self.circuit_breaker.next_attempt_time - time.time())
                    if self.circuit_breaker.next_attempt_time
                    else self.circuit_breaker.recovery_timeout
                )
                error = ToolError(
                    "Circuit breaker is open",
                    ToolErrorType.CIRCUIT_BREAKER_OPEN,
                    retry_after=max(retry_after, 1),
                )
                return await self._handle_error(error, request_id, start_time)

            # 3. Execute with timeout and retries
            result = await self._execute_with_retries(validated_input, request_id)

            # 4. Output validation
            try:
                validated_output = self.output_schema(**result)
            except ValidationError as e:
                error = ToolError(
                    f"Output validation failed: {e}",
                    ToolErrorType.VALIDATION_ERROR,
                    metadata={"validation_errors": e.errors(), "raw_output": result},
                )
                return await self._handle_error(error, request_id, start_time)

            # 5. Success response
            execution_time = (time.time() - start_time) * 1000

            if self.enable_metrics:
                tool_calls_total.labels(
                    tool_name=self.name, status="success", error_type="none"
                ).inc()
                tool_duration_seconds.labels(
                    tool_name=self.name, status="success"
                ).observe(execution_time / 1000)

            logger.info(
                f"[{request_id}] Tool {self.name} completed successfully in {execution_time:.2f}ms"
            )

            return ToolResponse[OutputSchema](
                data=validated_output,
                execution_time_ms=execution_time,
                tool_name=self.name,
                request_id=request_id,
            )

        except Exception as e:
            # Handle unexpected errors
            if isinstance(e, ToolError):
                # This is already a ToolError, handle it directly
                return await self._handle_error(e, request_id, start_time)
            else:
                # Wrap unexpected exceptions
                error = ToolError(
                    f"Unexpected error in {self.name}: {str(e)}",
                    ToolErrorType.UNKNOWN_ERROR,
                    metadata={"exception_type": type(e).__name__},
                )
                return await self._handle_error(error, request_id, start_time)

        finally:
            # Cleanup
            if self.enable_metrics:
                active_tool_calls.labels(tool_name=self.name).dec()

    async def _execute_with_retries(
        self, validated_input: InputSchema, request_id: str
    ) -> Dict[str, Any]:
        """Execute tool with exponential backoff retries."""

        for attempt in range(self.max_retries + 1):
            try:
                # Execute with timeout and circuit breaker protection
                if self.circuit_breaker:
                    # Use circuit breaker to wrap the execution
                    result = await self.circuit_breaker.call(
                        self._execute_tool_with_timeout, validated_input
                    )
                else:
                    # Execute with timeout only
                    result = await asyncio.wait_for(
                        self._execute_tool(validated_input),
                        timeout=self.timeout_seconds,
                    )

                # Reset failure tracking on success
                self.consecutive_failures = 0
                self.last_failure_time = None

                return result

            except asyncio.TimeoutError:
                logger.warning(
                    f"[{request_id}] Tool {self.name} timeout on attempt {attempt + 1}"
                )
                if attempt == self.max_retries:
                    raise ToolError(
                        f"Tool {self.name} timed out after {self.max_retries + 1} attempts",
                        ToolErrorType.TIMEOUT_ERROR,
                    )

            except Exception as e:
                logger.warning(
                    f"[{request_id}] Tool {self.name} error on attempt {attempt + 1}: {e}"
                )
                if attempt == self.max_retries:
                    # Classify error type based on the original exception
                    if hasattr(e, "error_type"):
                        # This is already a ToolError
                        raise e
                    else:
                        # Classify the exception
                        error_type = self._classify_error(e)
                        raise ToolError(
                            f"Tool {self.name} failed after {self.max_retries + 1} attempts: {str(e)}",
                            error_type,
                            metadata={"last_exception": str(e)},
                        )

            # Exponential backoff
            if attempt < self.max_retries:
                delay = 2**attempt + (time.time() % 1)  # Add jitter
                logger.info(f"[{request_id}] Retrying {self.name} in {delay:.2f}s...")
                await asyncio.sleep(delay)

    async def _execute_tool_with_timeout(
        self, validated_input: InputSchema
    ) -> Dict[str, Any]:
        """Execute tool with timeout wrapper for circuit breaker."""
        return await asyncio.wait_for(
            self._execute_tool(validated_input), timeout=self.timeout_seconds
        )

    def _classify_error(self, error: Exception) -> ToolErrorType:
        """Classify errors for better handling."""
        # If it's already a ToolError, preserve its error type
        if isinstance(error, ToolError):
            return error.error_type

        error_str = str(error).lower()

        if "timeout" in error_str:
            return ToolErrorType.TIMEOUT_ERROR
        elif "network" in error_str or "connection" in error_str:
            return ToolErrorType.NETWORK_ERROR
        elif "auth" in error_str or "permission" in error_str:
            return ToolErrorType.AUTHENTICATION_ERROR
        elif "rate limit" in error_str or "throttle" in error_str:
            return ToolErrorType.RATE_LIMIT_ERROR
        else:
            return ToolErrorType.EXTERNAL_SERVICE_ERROR

    async def _handle_error(
        self, error: ToolError, request_id: str, start_time: float
    ) -> ToolFallbackResponse:
        """Handle tool errors with structured logging and metrics."""

        execution_time = (time.time() - start_time) * 1000

        # Update failure tracking
        self.consecutive_failures += 1
        self.last_failure_time = time.time()

        # Log structured error
        logger.error(f"[{request_id}] Tool {self.name} failed: {error.to_dict()}")

        # Track metrics
        if self.enable_metrics:
            tool_calls_total.labels(
                tool_name=self.name, status="error", error_type=error.error_type.value
            ).inc()
            tool_duration_seconds.labels(tool_name=self.name, status="error").observe(
                execution_time / 1000
            )

        # Generate fallback response
        fallback_data = await self._generate_fallback(error)

        return ToolFallbackResponse(
            message=error.message,
            error_type=error.error_type.value,
            retry_after=error.retry_after,
            fallback_data=fallback_data,
        )

    @abstractmethod
    async def _execute_tool(self, validated_input: InputSchema) -> Dict[str, Any]:
        """
        Implement the actual tool logic here.

        Args:
            validated_input: Input data that has passed schema validation

        Returns:
            Raw output data (will be validated against output schema)

        Raises:
            Any exception (will be caught and handled by the framework)
        """
        pass

    async def _generate_fallback(self, error: ToolError) -> Optional[Dict[str, Any]]:
        """
        Generate fallback data when tool fails.
        Override this method to provide domain-specific fallbacks.

        Args:
            error: The error that caused the failure

        Returns:
            Optional fallback data
        """
        return {
            "fallback_message": f"The {self.name} service is temporarily unavailable. Please try again later.",
            "error_type": error.error_type.value,
            "timestamp": error.timestamp.isoformat(),
        }
