"""
Mental Agent - Modular ReAct Implementation

Specialized agent for mental training, sports psychology, motivation, and mindset coaching.
Follows multi-agent best practices with single responsibility and ReAct pattern.
"""

import logging
from typing import Any, Dict, List

from langchain_core.tools import BaseTool, Tool

from athlea_langgraph.states import AgentState

# Direct tool imports for self-contained agent
from ..tools.mental import (
    mental_state_assessment,
    stress_level_tracker,
    mood_pattern_analyzer,
    goal_tracker,
)
from ..utils.prompt_loader import get_prompt_loader
from .base_agent import BaseReActAgent

logger = logging.getLogger(__name__)


class MentalAgent(BaseReActAgent):
    """Specialized agent for mental training coaching using ReAct pattern."""

    def __init__(self):
        """Initialize the mental agent with domain-specific tools."""
        # Fallback prompt for general conversation and greetings
        fallback_prompt = """You are a world-class Mental Performance Coach and sports psychologist. You specialize in helping athletes and fitness enthusiasts develop mental resilience, focus, motivation, and peak performance mindset.

**YOUR ROLE:**
- Provide supportive, evidence-based guidance on mental wellness and sports psychology
- Help users develop mental skills for athletic performance and life success
- Engage with empathy and professionalism in all interactions
- Use your specialized tools when appropriate for assessments and data-driven insights

**CONVERSATION APPROACH:**
- For greetings: Respond warmly and ask how you can help with their mental training goals
- For specific questions: Provide expert advice based on sports psychology principles
- For assessments: Use your tools to provide structured evaluations when the user wants to assess their mental state, track progress, or analyze patterns
- For research: Use your knowledge base and web search tools for evidence-based information

**AVAILABLE TOOLS (use when appropriate):**
- Mental state assessment for comprehensive psychological evaluations
- Stress tracking and mood analysis for ongoing monitoring
- Goal setting and motivation coaching tools
- Research tools for evidence-based strategies

**COMMUNICATION STYLE:**
- Professional yet approachable
- Empathetic and supportive
- Focus on actionable advice and mental performance improvement
- Ask clarifying questions when needed to provide the most helpful guidance

Remember: Not every conversation requires tool usage. Simple greetings, general questions, and casual interactions should be handled conversationally. Use tools when the user specifically wants assessments, tracking, or detailed analysis."""

        # Tools will be loaded asynchronously in get_domain_tools()
        super().__init__(
            name="mental_agent",
            domain="mental_training",
            system_prompt=fallback_prompt,  # Will be updated lazily
            tools=[],  # Will be populated by get_domain_tools()
            permissions=["mental_assessment", "goal_setting", "motivation_coaching"],
            max_iterations=10,
            temperature=0.2,
        )

        self._prompt_loaded = False
        self.fallback_prompt = fallback_prompt
        self._tools_loaded = False

    async def _load_system_prompt(self, config: Dict[str, Any] = None) -> str:
        """Load the system prompt lazily, checking for Studio overrides first"""
        if not self._prompt_loaded:
            # Check for Studio prompt override first
            studio_prompt = None
            if config and isinstance(config, dict):
                # Check for the prompt parameter from Studio configuration
                studio_prompt = config.get("mental_coach_prompt")
                if studio_prompt and studio_prompt.strip():
                    logger.info("Using mental coach prompt from Studio configuration")
                    self.system_prompt = studio_prompt
                    self._prompt_loaded = True
                    return self.system_prompt

            # Fall back to JSON file prompt
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt("coaches/mental_coach")
                self.system_prompt = prompt_config.get_rendered_prompt()
                logger.info("Successfully loaded mental coach prompt from JSON file")
            except Exception as e:
                logger.error(f"Failed to load mental coach prompt: {e}")
                self.system_prompt = self.fallback_prompt
            self._prompt_loaded = True

        return self.system_prompt

    async def get_domain_tools(self) -> List[BaseTool]:
        """
        STANDARDIZED TOOL ACCESS for all domain coaches.

        Every coach gets access to:
        1. Domain-specific tools (mental tools)
        2. GraphRAG - Advanced research capabilities
        3. Azure Search - Evidence-based research
        4. WebSearchGraph - Multi-agent web research
        5. Maps Workflow - Location-based services
        6. Session Generation - Domain-specific session creation
        """
        if not self._tools_loaded:
            # 1. Domain-specific mental tools
            mental_tools = [
                mental_state_assessment,
                stress_level_tracker,
                mood_pattern_analyzer,
                goal_tracker,
            ]

            # STANDARDIZED UNIVERSAL TOOLS

            # 2. GraphRAG tool for advanced research
            try:
                from ..tools.graphrag_tool import create_graphrag_tool

                graphrag_tool = create_graphrag_tool()
                mental_tools.append(graphrag_tool)
                logger.info("✅ Added GraphRAG tool to mental coach")
            except Exception as e:
                logger.warning(f"Failed to load GraphRAG tool: {e}")

            # 3. Azure Search for evidence-based research
            try:
                from ..tools.external.azure_search_retriever import (
                    AzureSearchRetrieverTool,
                )

                azure_search_tool = AzureSearchRetrieverTool()
                azure_search_langchain = Tool(
                    name="azure_search_retriever",
                    description="""Search Azure Cognitive Search for research, documentation, and evidence-based mental training information.
                    
                    Use this for:
                    - Sports psychology research and techniques
                    - Mindfulness and meditation studies
                    - Cognitive behavioral therapy (CBT) for athletes
                    - Goal setting and motivation strategies
                    
                    Input should be a search query string.""",
                    func=lambda query: "Azure Search requires async execution",
                    coroutine=azure_search_tool.invoke,
                )
                mental_tools.append(azure_search_langchain)
                logger.info("✅ Added Azure Search tool to mental coach")
            except Exception as e:
                logger.warning(f"Failed to load Azure Search tool: {e}")

            # 4. WebSearchGraph (multi-agent research)
            try:
                from ..tools.web_search_graph_tool import create_web_search_graph_tool

                web_research_tool = create_web_search_graph_tool()
                mental_tools.append(web_research_tool)
                logger.info("✅ Added WebSearchGraph tool to mental coach")
            except Exception as e:
                logger.warning(f"Failed to load WebSearchGraph tool: {e}")
                # Fallback to basic web search tool
                try:
                    from ..tools.web_search_tool import WebSearchTool

                    web_search_tool = WebSearchTool()
                    mental_tools.append(web_search_tool)
                    logger.info(
                        "⚠️ Fallback: Added basic WebSearch tool to mental coach"
                    )
                except Exception as fallback_e:
                    logger.warning(
                        f"Failed to load fallback web search tool: {fallback_e}"
                    )

            # 5. Maps workflow tool for location-based services
            try:
                from ..tools.external.maps_workflow import create_maps_workflow_tool

                maps_tool = await create_maps_workflow_tool()
                mental_tools.append(maps_tool)
                logger.info("✅ Added Maps workflow tool to mental coach")
            except Exception as e:
                logger.warning(f"Failed to load Maps workflow tool: {e}")

            # 6. Domain-specific session generation
            try:
                from ..tools.external.session_generation import SessionGenerationTool

                session_gen_tool = SessionGenerationTool()
                session_gen_langchain = Tool(
                    name="generate_mental_training_session",
                    description="""Generate structured mental training sessions and mindfulness protocols.
                    
                    Use this for:
                    - Guided meditation and mindfulness exercises
                    - Visualization and mental rehearsal scripts
                    - Goal-setting workshops
                    - Pre-competition mental preparation routines
                    
                    Input should be session parameters as JSON string.""",
                    func=session_gen_tool.generate_session,
                )
                mental_tools.append(session_gen_langchain)
                logger.info(
                    "✅ Added domain-specific session generation to mental coach"
                )
            except Exception as e:
                logger.warning(f"Failed to load session generation tool: {e}")

            # Update the agent's tools
            self.tools = mental_tools

            logger.info(
                f"✅ STANDARDIZED TOOLS: Loaded {len(self.tools)} tools for mental coach"
            )
            logger.info(f"🔧 Tool names: {[t.name for t in self.tools]}")
            self._tools_loaded = True

        return self.tools

    def get_domain_prompt(self) -> str:
        """Get the domain-specific prompt for mental coaching."""
        return self.system_prompt

    async def process(
        self, state: AgentState, config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Process mental training requests with tool loading."""
        # Ensure tools are loaded before processing
        if not self.tools:
            await self.get_domain_tools()

        # Ensure prompt is loaded before processing (pass config for Studio overrides)
        await self._load_system_prompt(config)

        return await super().process(state, config)


# Create the mental agent instance
mental_coach = MentalAgent()


async def mental_coach_node(
    state: AgentState, config: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    LangGraph node function for mental training coaching.

    Args:
        state: Current agent state
        config: Optional configuration

    Returns:
        Updated state with mental agent response
    """
    logger.info("--- Running Mental Agent Node ---")

    try:
        # Ensure agent has tools loaded
        if not mental_coach.tools:
            await mental_coach.get_domain_tools()

        # Process the request
        result = await mental_coach.process(state, config)

        # Update state with results
        updated_state = {
            "messages": state.get("messages", [])
            + [{"role": "assistant", "content": result.get("response", "")}],
            "specialist_completed": result.get("specialist_completed", True),
            "current_agent": "mental_agent",
            "agent_metadata": result.get("metadata", {}),
        }

        if result.get("error"):
            updated_state["error"] = result["error"]

        logger.info(
            f"Mental coach completed with {len(mental_coach.tools)} tools available"
        )
        return updated_state

    except Exception as e:
        logger.error(f"Error in mental agent node: {e}")
        return {
            "messages": state.get("messages", [])
            + [
                {
                    "role": "assistant",
                    "content": f"I encountered an error while processing your mental training request: {str(e)}",
                }
            ],
            "specialist_completed": False,
            "current_agent": "mental_agent",
            "error": str(e),
        }
