"""
Strength Agent - Modular ReAct Implementation

Specialized agent for strength training, resistance training, and muscle building.
Follows multi-agent best practices with single responsibility and ReAct pattern.
"""

import logging
from typing import Any, Dict, List, Optional

from langchain_core.tools import BaseTool

from athlea_langgraph.states import AgentState

# Direct tool imports for self-contained agent
from ..tools.strength import (
    search_strength_exercises,
    get_exercise_progression,
    comprehensive_strength_assessment,
)

# Additional tool access capabilities
from ..tools.external.azure_search_retriever import AzureSearchRetrieverTool
from ..tools.external.session_generation import SessionGenerationTool
from langchain_core.tools import Tool

from ..utils.prompt_loader import get_prompt_loader
from .base_agent import BaseReActAgent

logger = logging.getLogger(__name__)


class StrengthAgent(BaseReActAgent):
    """Specialized agent for strength training coaching using ReAct pattern."""

    def __init__(self):
        """Initialize the strength agent with domain-specific tools."""
        # Fallback prompt
        fallback_prompt = """You are a Strength Training Coach specializing in resistance training, powerlifting, bodybuilding, and functional strength development.

Your expertise includes:
- Exercise selection and progression
- Program design for different goals (strength, hypertrophy, power)
- Proper form and technique
- Equipment recommendations
- Injury prevention and modification
- Periodization and programming

You have access to specialized tools for exercise databases, strength assessment, and program generation.
Based on the user's query, you MUST decide whether to use one of your tools or answer directly.

Your available tools are:
- `search_strength_exercises`: Use this tool when a user wants to find exercises based on muscle groups, equipment, exercise type, or difficulty level.
- `get_exercise_progression`: Use this tool when a user wants progression suggestions for a specific exercise or wants to advance their training.
- `comprehensive_strength_assessment`: Use this tool when a user wants a complete strength assessment including movement screening, strength testing, and personalized recommendations.

Always provide evidence-based advice and consider the user's experience level, goals, and any limitations they may have."""

        # Tools will be loaded asynchronously in get_domain_tools()
        super().__init__(
            name="strength_agent",
            domain="strength_training",
            system_prompt=fallback_prompt,  # Will be updated lazily
            tools=[],  # Will be populated by get_domain_tools()
            permissions=["strength_assessment", "program_design", "exercise_selection"],
            max_iterations=10,
            temperature=0.7,
        )

        self._prompt_loaded = False
        self.fallback_prompt = fallback_prompt
        self._tools_loaded = False

    async def _load_system_prompt(self, config: Optional[Dict[str, Any]] = None) -> str:
        """Load the system prompt lazily, checking for Studio overrides first"""
        if not self._prompt_loaded:
            # Check for Studio prompt override first
            studio_prompt = None
            if config and isinstance(config, dict):
                # Check for the prompt parameter from Studio configuration
                studio_prompt = config.get("strength_coach_prompt")
                if studio_prompt and studio_prompt.strip():
                    logger.info("Using strength coach prompt from Studio configuration")
                    self.system_prompt = studio_prompt
                    self._prompt_loaded = True
                    return self.system_prompt

            # Fall back to JSON file prompt
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt(
                    "coaches/strength_coach"
                )
                self.system_prompt = prompt_config.get_rendered_prompt()
                logger.info("Successfully loaded strength coach prompt from JSON file")
            except Exception as e:
                logger.error(f"Failed to load strength coach prompt: {e}")
                self.system_prompt = self.fallback_prompt
            self._prompt_loaded = True

        return self.system_prompt

    async def get_domain_tools(self) -> List[BaseTool]:
        """
        Standardized tool access for strength agent following new requirements:
        - Domain-specific tools
        - GraphRAG for advanced research
        - Maps for location-based services
        - WebSearchGraph for comprehensive research
        - Azure Search for evidence-based information
        - Domain-specific session generation
        """
        if not self._tools_loaded:
            # Domain-specific strength tools
            strength_tools = [
                search_strength_exercises,
                get_exercise_progression,
                comprehensive_strength_assessment,
            ]

            # STANDARDIZED UNIVERSAL TOOLS (all domain coaches should have these)

            # 1. GraphRAG tool for advanced research
            try:
                from ..tools.graphrag_tool import create_graphrag_tool

                graphrag_tool = create_graphrag_tool()
                strength_tools.append(graphrag_tool)
                logger.info("✅ Added GraphRAG tool to strength agent")
            except Exception as e:
                logger.warning(f"Failed to load GraphRAG tool: {e}")

            # 2. Maps workflow tool for location-based services
            try:
                from ..tools.external.maps_workflow import create_maps_workflow_tool

                maps_tool = await create_maps_workflow_tool()
                strength_tools.append(maps_tool)
                logger.info("✅ Added Maps workflow tool to strength agent")
            except Exception as e:
                logger.warning(f"Failed to load Maps workflow tool: {e}")

            # 3. WebSearchGraph (multi-agent research) instead of basic web search tool
            try:
                from ..tools.web_search_graph_tool import create_web_search_graph_tool

                web_research_tool = create_web_search_graph_tool()
                strength_tools.append(web_research_tool)
                logger.info("✅ Added WebSearchGraph tool to strength agent")
            except Exception as e:
                logger.warning(f"Failed to load WebSearchGraph tool: {e}")
                # Fallback to basic web search tool if graph fails
                try:
                    from ..tools.web_search_tool import WebSearchTool

                    web_search_tool = WebSearchTool()
                    strength_tools.append(web_search_tool)
                    logger.info(
                        "⚠️ Fallback: Added basic WebSearch tool to strength agent"
                    )
                except Exception as fallback_e:
                    logger.warning(
                        f"Failed to load fallback web search tool: {fallback_e}"
                    )

            # 4. Azure Search for evidence-based research
            try:
                from ..tools.external.azure_search_retriever import (
                    AzureSearchRetrieverTool,
                )

                azure_search_tool = AzureSearchRetrieverTool()
                azure_search_langchain = Tool(
                    name="azure_search_retriever",
                    description="""Search Azure Cognitive Search for research, documentation, and evidence-based strength training information.
                    
                    Use this tool for:
                    - Strength training research and methodologies
                    - Exercise biomechanics and technique studies
                    - Program design and periodization research
                    - Injury prevention and rehabilitation studies
                    - Equipment reviews and recommendations
                    
                    Input should be a search query string.""",
                    func=lambda query: "Azure Search requires async execution",
                    coroutine=azure_search_tool.invoke,
                )
                strength_tools.append(azure_search_langchain)
                logger.info("✅ Added Azure Search tool to strength agent")
            except Exception as e:
                logger.warning(f"Failed to load Azure Search tool: {e}")

            # 5. Domain-specific session generation
            try:
                from ..tools.external.session_generation import SessionGenerationTool

                session_gen_tool = SessionGenerationTool()
                session_gen_langchain = Tool(
                    name="generate_strength_session",
                    description="""Generate structured strength training sessions and workout plans.
                    
                    Use this tool to create:
                    - Strength training workouts
                    - Powerlifting sessions
                    - Bodybuilding routines
                    - Functional strength programs
                    - Rehabilitation protocols
                    
                    Input should be session parameters as JSON string.""",
                    func=session_gen_tool.generate_session,
                )
                strength_tools.append(session_gen_langchain)
                logger.info(
                    "✅ Added domain-specific session generation to strength agent"
                )
            except Exception as e:
                logger.warning(f"Failed to load session generation tool: {e}")

            # Update the agent's tools
            self.tools = strength_tools

            logger.info(
                f"✅ STANDARDIZED TOOLS: Loaded {len(strength_tools)} tools for strength agent"
            )
            logger.info(f"🔧 Tool names: {[t.name for t in strength_tools]}")
            self._tools_loaded = True

        return self.tools

    def get_domain_prompt(self) -> str:
        """Get the domain-specific prompt for strength coaching."""
        return self.system_prompt

    async def process(
        self, state: AgentState, config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Process strength training requests with tool loading."""
        # Ensure tools are loaded before processing
        if not self.tools:
            await self.get_domain_tools()

        # Ensure prompt is loaded before processing (pass config for Studio overrides)
        await self._load_system_prompt(config or {})

        return await super().process(state, config or {})


# Create the strength agent instance
strength_coach = StrengthAgent()


async def strength_coach_node(
    state: AgentState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    LangGraph node function for strength training coaching.

    Args:
        state: Current agent state
        config: Optional configuration

    Returns:
        Updated state with strength agent response
    """
    logger.info("--- Running Strength Agent Node ---")

    try:
        # Ensure agent has tools loaded
        if not strength_coach.tools:
            await strength_coach.get_domain_tools()

        # Process the request
        result = await strength_coach.process(state, config or {})

        # Update state with results
        updated_state = {
            "messages": state.get("messages", [])
            + [{"role": "assistant", "content": result.get("response", "")}],
            "specialist_completed": result.get("specialist_completed", True),
            "current_agent": "strength_agent",
            "agent_metadata": result.get("metadata", {}),
        }

        if result.get("error"):
            updated_state["error"] = result["error"]

        logger.info(
            f"Strength coach completed with {len(strength_coach.tools)} tools available"
        )
        return updated_state

    except Exception as e:
        logger.error(f"Error in strength agent node: {e}")
        return {
            "messages": state.get("messages", [])
            + [
                {
                    "role": "assistant",
                    "content": f"I encountered an error while processing your strength training request: {str(e)}",
                }
            ],
            "specialist_completed": False,
            "current_agent": "strength_agent",
            "error": str(e),
        }
