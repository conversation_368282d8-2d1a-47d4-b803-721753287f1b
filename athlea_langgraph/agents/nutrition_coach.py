"""
Nutrition Agent - Modular ReAct Implementation

Specialized agent for nutritional coaching, dietary planning, and meal optimization.
Follows multi-agent best practices with single responsibility and ReAct pattern.
"""

import logging
from typing import Any, Dict, List

from langchain_core.tools import BaseTool, Tool

from athlea_langgraph.states import AgentState

# Direct tool imports for self-contained agent
from ..tools.nutrition import (
    calculate_daily_calories,
    calculate_macro_targets,
    generate_meal_plan,
    search_recipes,
    get_recipe_recommendations,
)
from ..utils.prompt_loader import get_prompt_loader
from .base_agent import BaseReActAgent
from ..tools.graphrag_tool import create_graphrag_tool

logger = logging.getLogger(__name__)


class NutritionAgent(BaseReActAgent):
    """Specialized agent for nutrition coaching using ReAct pattern."""

    def __init__(self):
        """Initialize the nutrition agent with domain-specific tools."""
        # Fallback prompt with strong tool calling instructions
        fallback_prompt = """You are a world-class Nutrition Coach. Your primary function is to provide precise, data-driven nutritional advice by leveraging your specialized tools.

**CORE DIRECTIVE:** Your first step is always to use your tools to gather the necessary data for any user request related to calculations, meal plans, or recipes. Once you have the tool's output, you will then synthesize that information into a helpful, conversational coaching response.

**MANDATORY TOOL USAGE:**
1.  For any request involving calculations (calories, macros), meal plans, or recipe searches, you MUST use a tool to get the data before you answer.
2.  **Do not perform calculations from memory.** Use `calculate_daily_calories` and `calculate_macro_targets` for all calculations.
3.  **Map goals accurately** for tool inputs. Use these exact values: 'maintenance', 'weight_loss', 'aggressive_weight_loss', 'weight_gain', 'muscle_gain', 'body_recomposition', 'performance', 'cutting', 'bulking'.
    - "maintain my weight" -> "maintenance"
    - "lose weight" -> "weight_loss"
    - "gain muscle" -> "muscle_gain"

**YOUR WORKFLOW:**
1.  **User Request:** "How many calories should I eat to lose weight?"
2.  **Your Action (Tool Call):** You will first call the `calculate_daily_calories` tool with the required parameters (age, weight, etc.).
3.  **Your Final Response (Synthesis):** After the tool returns the data (e.g., 2200 calories), you will formulate a response like: "Based on your goals, a good starting point would be around 2200 calories per day. We can create a meal plan around this number to help you achieve your weight loss goals effectively."

**If you don't have enough information to use a tool, you MUST ask the user for the missing details.** Your goal is to be a data-driven expert who provides clear, actionable advice based on precise tool outputs."""

        # Tools will be loaded asynchronously in get_domain_tools()
        super().__init__(
            name="nutrition_agent",
            domain="nutrition",
            system_prompt=fallback_prompt,  # Will be updated lazily
            tools=[],  # Will be populated by get_domain_tools()
            permissions=["nutrition_assessment", "meal_planning", "macro_calculation"],
            max_iterations=10,
            temperature=0.2,  # Lowered for more deterministic tool calling
        )

        self._prompt_loaded = False
        self.fallback_prompt = fallback_prompt
        self._tools_loaded = False

    async def _load_system_prompt(self, config: Dict[str, Any] = None) -> str:
        """Load the system prompt lazily, checking for Studio overrides first"""
        if not self._prompt_loaded:
            # Check for Studio prompt override first
            studio_prompt = None
            if config and isinstance(config, dict):
                # Check for the prompt parameter from Studio configuration
                studio_prompt = config.get("nutrition_coach_prompt")
                if studio_prompt and studio_prompt.strip():
                    logger.info(
                        "Using nutrition coach prompt from Studio configuration"
                    )
                    self.system_prompt = studio_prompt
                    self._prompt_loaded = True
                    return self.system_prompt

            # Fall back to JSON file prompt
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt(
                    "coaches/nutrition_coach"
                )
                self.system_prompt = prompt_config.get_rendered_prompt()

                # System prompt loaded successfully for direct tool calling

                logger.info("Successfully loaded nutrition coach prompt from JSON file")
            except Exception as e:
                logger.error(f"Failed to load nutrition coach prompt: {e}")
                self.system_prompt = self.fallback_prompt
            self._prompt_loaded = True

        return self.system_prompt

    async def get_domain_tools(self) -> List[BaseTool]:
        """
        STANDARDIZED TOOL ACCESS for all domain coaches.

        Every coach gets access to:
        1. Domain-specific tools (nutrition tools)
        2. GraphRAG - Advanced research capabilities
        3. Azure Search - Evidence-based research
        4. WebSearchGraph - Multi-agent web research
        5. Maps Workflow - Location-based services
        6. Session Generation - Domain-specific session creation
        """
        if not self._tools_loaded:
            # 1. Domain-specific nutrition tools
            nutrition_tools = [
                calculate_daily_calories,
                calculate_macro_targets,
                generate_meal_plan,
                search_recipes,
                get_recipe_recommendations,
            ]

            # STANDARDIZED UNIVERSAL TOOLS

            # 2. GraphRAG tool for advanced research
            try:
                from ..tools.graphrag_tool import create_graphrag_tool

                graphrag_tool = create_graphrag_tool()
                nutrition_tools.append(graphrag_tool)
                logger.info("✅ Added GraphRAG tool to nutrition coach")
            except Exception as e:
                logger.warning(f"Failed to load GraphRAG tool: {e}")

            # 3. Azure Search for evidence-based research
            try:
                from ..tools.external.azure_search_retriever import (
                    AzureSearchRetrieverTool,
                )

                azure_search_tool = AzureSearchRetrieverTool()
                azure_search_langchain = Tool(
                    name="azure_search_retriever",
                    description="""Search Azure Cognitive Search for research, documentation, and evidence-based nutrition information.
                    
                    Use this for:
                    - Nutritional science and dietary guidelines
                    - Food composition and nutrient data
                    - Sports nutrition and performance research
                    - Supplement research and recommendations
                    
                    Input should be a search query string.""",
                    func=lambda query: "Azure Search requires async execution",
                    coroutine=azure_search_tool.invoke,
                )
                nutrition_tools.append(azure_search_langchain)
                logger.info("✅ Added Azure Search tool to nutrition coach")
            except Exception as e:
                logger.warning(f"Failed to load Azure Search tool: {e}")

            # 4. WebSearchGraph (multi-agent research)
            try:
                from ..tools.web_search_graph_tool import create_web_search_graph_tool

                web_research_tool = create_web_search_graph_tool()
                nutrition_tools.append(web_research_tool)
                logger.info("✅ Added WebSearchGraph tool to nutrition coach")
            except Exception as e:
                logger.warning(f"Failed to load WebSearchGraph tool: {e}")
                # Fallback to basic web search tool
                try:
                    from ..tools.web_search_tool import WebSearchTool

                    web_search_tool = WebSearchTool()
                    nutrition_tools.append(web_search_tool)
                    logger.info(
                        "⚠️ Fallback: Added basic WebSearch tool to nutrition coach"
                    )
                except Exception as fallback_e:
                    logger.warning(
                        f"Failed to load fallback web search tool: {fallback_e}"
                    )

            # 5. Maps workflow tool for location-based services
            try:
                from ..tools.external.maps_workflow import create_maps_workflow_tool

                maps_tool = await create_maps_workflow_tool()
                nutrition_tools.append(maps_tool)
                logger.info("✅ Added Maps workflow tool to nutrition coach")
            except Exception as e:
                logger.warning(f"Failed to load Maps workflow tool: {e}")

            # 6. Domain-specific session generation
            try:
                from ..tools.external.session_generation import SessionGenerationTool

                session_gen_tool = SessionGenerationTool()
                session_gen_langchain = Tool(
                    name="generate_nutrition_plan",
                    description="""Generate structured nutrition plans and meal schedules.
                    
                    Use this for:
                    - Daily meal plans based on calorie/macro targets
                    - Weekly nutrition schedules
                    - Pre/post workout nutrition timing
                    - Hydration and supplement plans
                    
                    Input should be session parameters as JSON string.""",
                    func=session_gen_tool.generate_session,
                )
                nutrition_tools.append(session_gen_langchain)
                logger.info(
                    "✅ Added domain-specific session generation to nutrition coach"
                )
            except Exception as e:
                logger.warning(f"Failed to load session generation tool: {e}")

            # Update the agent's tools
            self.tools = nutrition_tools

            logger.info(
                f"✅ STANDARDIZED TOOLS: Loaded {len(self.tools)} tools for nutrition coach"
            )
            logger.info(f"🔧 Tool names: {[t.name for t in self.tools]}")
            self._tools_loaded = True

        return self.tools

    def get_domain_prompt(self) -> str:
        """Get the domain-specific prompt for nutrition coaching."""
        return self.system_prompt

    async def process(
        self, state: AgentState, config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Process nutrition requests with tool loading."""
        # Ensure tools are loaded before processing
        if not self.tools:
            await self.get_domain_tools()

        # Ensure prompt is loaded before processing (pass config for Studio overrides)
        await self._load_system_prompt(config)

        # Fix common goal mapping issues before processing
        user_query = state.get("user_query", "")
        messages = state.get("messages", [])

        # Map common goal variations to valid enum values
        goal_mappings = {
            "maintain_weight": "maintenance",
            "maintain": "maintenance",
            "weight_maintenance": "maintenance",
            "stay_same": "maintenance",
            "lose_weight": "weight_loss",
            "gain_weight": "weight_gain",
            "build_muscle": "muscle_gain",
            "cut": "cutting",
            "bulk": "bulking",
        }

        # Update messages to fix goal terminology
        processed_messages = []
        for msg in messages:
            if hasattr(msg, "content"):
                content = msg.content
                for old_goal, new_goal in goal_mappings.items():
                    if old_goal in content.lower():
                        content = content.replace(old_goal, new_goal)

                # Create a new message object with updated content
                if hasattr(msg, "type"):
                    from langchain_core.messages import HumanMessage, AIMessage

                    if msg.type == "human":
                        processed_messages.append(HumanMessage(content=content))
                    elif msg.type == "ai":
                        processed_messages.append(AIMessage(content=content))
                    else:
                        processed_messages.append(msg)
                else:
                    processed_messages.append(msg)
            else:
                processed_messages.append(msg)

        # Also fix the user_query
        processed_query = user_query
        for old_goal, new_goal in goal_mappings.items():
            if old_goal in processed_query.lower():
                processed_query = processed_query.replace(old_goal, new_goal)

        # Create processed state
        processed_state = {**state}
        processed_state["messages"] = processed_messages
        processed_state["user_query"] = processed_query

        return await super().process(processed_state, config)


# Create the nutrition agent instance
nutrition_coach = NutritionAgent()


async def nutrition_coach_node(
    state: AgentState, config: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    LangGraph node function for nutrition coaching.

    Args:
        state: Current agent state
        config: Optional configuration

    Returns:
        Updated state with nutrition agent response
    """
    logger.info("--- Running Nutrition Agent Node ---")

    try:
        # Ensure agent has tools loaded
        if not nutrition_coach.tools:
            await nutrition_coach.get_domain_tools()

        # Process the request
        result = await nutrition_coach.process(state, config)

        # Update state with results
        updated_state = {
            "messages": state.get("messages", [])
            + [{"role": "assistant", "content": result.get("response", "")}],
            "specialist_completed": result.get("specialist_completed", True),
            "current_agent": "nutrition_agent",
            "agent_metadata": result.get("metadata", {}),
        }

        if result.get("error"):
            updated_state["error"] = result["error"]

        logger.info(
            f"Nutrition coach completed with {len(nutrition_coach.tools)} tools available"
        )
        return updated_state

    except Exception as e:
        logger.error(f"Error in nutrition agent node: {e}")
        return {
            "messages": state.get("messages", [])
            + [
                {
                    "role": "assistant",
                    "content": f"I encountered an error while processing your nutrition request: {str(e)}",
                }
            ],
            "specialist_completed": False,
            "current_agent": "nutrition_agent",
            "error": str(e),
        }
