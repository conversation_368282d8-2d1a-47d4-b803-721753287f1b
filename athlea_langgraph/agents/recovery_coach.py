"""
Recovery Agent - Modular ReAct Implementation

Specialized agent for recovery, regeneration, sleep optimization, and injury prevention.
Follows multi-agent best practices with single responsibility and ReAct pattern.
"""

import logging
from typing import Any, Dict, List

from langchain_core.tools import BaseTool, Tool

from athlea_langgraph.states import AgentState

# Direct tool imports for self-contained agent
from ..tools.recovery import (
    generate_mobility_protocol,
    optimize_sleep,
    assess_wellness,
    create_graphrag_tool,
)
from ..utils.prompt_loader import get_prompt_loader
from .base_agent import BaseReActAgent

logger = logging.getLogger(__name__)


class RecoveryAgent(BaseReActAgent):
    """Specialized agent for recovery coaching using ReAct pattern."""

    def __init__(self):
        """Initialize the recovery agent with domain-specific tools."""
        # Fallback prompt with strong tool calling instructions
        fallback_prompt = """You are a world-class Recovery Coach. Your primary function is to provide precise, evidence-based advice on physical and mental recovery by leveraging your specialized tools.

**CRITICAL INSTRUCTIONS:**
1.  **Assess the User's Query:** Analyze the user's question to determine if one of your tools can provide a precise, data-driven answer.
2.  **Prioritize Tool Usage:** For any request related to generating mobility protocols, optimizing sleep, assessing wellness, or retrieving scientific research, you MUST use the appropriate tool. Do not answer from general knowledge if a tool is available.
3.  **Use Tools Correctly:**
    - To generate a mobility routine, use `generate_mobility_protocol`.
    - To get sleep optimization advice, use `optimize_sleep`.
    - To assess overall wellness, use `assess_wellness`.
    - For research questions, use `azure_search_retriever`.
    - For product reviews or current news, use `web_search`.
4.  **Engage in Conversation:** If a tool is not required, respond naturally and conversationally. If you need more information to use a tool effectively, ask the user clarifying questions.

Your goal is to be a helpful and accurate recovery coach, using your tools to provide the best possible guidance."""

        # Tools will be loaded asynchronously in get_domain_tools()
        super().__init__(
            name="recovery_agent",
            domain="recovery",
            system_prompt=fallback_prompt,
            tools=[],
            permissions=[
                "mobility_protocols",
                "sleep_optimization",
                "wellness_assessment",
            ],
            max_iterations=10,
            temperature=0.2,
        )

        self._prompt_loaded = False
        self.fallback_prompt = fallback_prompt
        self._tools_loaded = False

    async def _load_system_prompt(self, config: Dict[str, Any] = None) -> str:
        """Load the system prompt lazily, checking for Studio overrides first"""
        if not self._prompt_loaded:
            # Check for Studio prompt override first
            studio_prompt = None
            if config and isinstance(config, dict):
                # Check for the prompt parameter from Studio configuration
                studio_prompt = config.get("recovery_coach_prompt")
                if studio_prompt and studio_prompt.strip():
                    logger.info("Using recovery coach prompt from Studio configuration")
                    self.system_prompt = studio_prompt
                    self._prompt_loaded = True
                    return self.system_prompt

            # Fall back to JSON file prompt
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt(
                    "coaches/recovery_coach"
                )
                self.system_prompt = prompt_config.get_rendered_prompt()
                logger.info("Successfully loaded recovery coach prompt from JSON file")
            except Exception as e:
                logger.error(f"Failed to load recovery coach prompt: {e}")
                self.system_prompt = self.fallback_prompt
            self._prompt_loaded = True

        return self.system_prompt

    async def get_domain_tools(self) -> List[BaseTool]:
        """
        STANDARDIZED TOOL ACCESS for all domain coaches.

        Every coach gets access to:
        1. Domain-specific tools (recovery tools)
        2. GraphRAG - Advanced research capabilities
        3. Azure Search - Evidence-based research
        4. WebSearchGraph - Multi-agent web research
        5. Maps Workflow - Location-based services
        6. Session Generation - Domain-specific session creation
        """
        if not self._tools_loaded:
            # 1. Domain-specific recovery tools
            recovery_tools = [
                generate_mobility_protocol,
                optimize_sleep,
                assess_wellness,
            ]

            # STANDARDIZED UNIVERSAL TOOLS

            # 2. GraphRAG tool for advanced research
            try:
                from ..tools.graphrag_tool import create_graphrag_tool

                graphrag_tool = create_graphrag_tool()
                recovery_tools.append(graphrag_tool)
                logger.info("✅ Added GraphRAG tool to recovery coach")
            except Exception as e:
                logger.warning(f"Failed to load GraphRAG tool: {e}")

            # 3. Azure Search for evidence-based research
            try:
                from ..tools.external.azure_search_retriever import (
                    AzureSearchRetrieverTool,
                )

                azure_search_tool = AzureSearchRetrieverTool()
                azure_search_langchain = Tool(
                    name="azure_search_retriever",
                    description="""Search Azure Cognitive Search for research, documentation, and evidence-based recovery information.
                    
                    Use this for:
                    - Recovery modalities and techniques
                    - Sleep science and optimization research
                    - Mobility and flexibility protocols
                    - Injury prevention and management studies
                    
                    Input should be a search query string.""",
                    func=lambda query: "Azure Search requires async execution",
                    coroutine=azure_search_tool.invoke,
                )
                recovery_tools.append(azure_search_langchain)
                logger.info("✅ Added Azure Search tool to recovery coach")
            except Exception as e:
                logger.warning(f"Failed to load Azure Search tool: {e}")

            # 4. WebSearchGraph (multi-agent research)
            try:
                from ..tools.web_search_graph_tool import create_web_search_graph_tool

                web_research_tool = create_web_search_graph_tool()
                recovery_tools.append(web_research_tool)
                logger.info("✅ Added WebSearchGraph tool to recovery coach")
            except Exception as e:
                logger.warning(f"Failed to load WebSearchGraph tool: {e}")
                # Fallback to basic web search tool
                try:
                    from ..tools.web_search_tool import WebSearchTool

                    web_search_tool = WebSearchTool()
                    recovery_tools.append(web_search_tool)
                    logger.info(
                        "⚠️ Fallback: Added basic WebSearch tool to recovery coach"
                    )
                except Exception as fallback_e:
                    logger.warning(
                        f"Failed to load fallback web search tool: {fallback_e}"
                    )

            # 5. Maps workflow tool for location-based services
            try:
                from ..tools.external.maps_workflow import create_maps_workflow_tool

                maps_tool = await create_maps_workflow_tool()
                recovery_tools.append(maps_tool)
                logger.info("✅ Added Maps workflow tool to recovery coach")
            except Exception as e:
                logger.warning(f"Failed to load Maps workflow tool: {e}")

            # 6. Domain-specific session generation
            try:
                from ..tools.external.session_generation import SessionGenerationTool

                session_gen_tool = SessionGenerationTool()
                session_gen_langchain = Tool(
                    name="generate_recovery_protocol",
                    description="""Generate structured recovery protocols and wellness sessions.
                    
                    Use this for:
                    - Post-workout recovery routines
                    - Mobility and flexibility sessions
                    - Sleep hygiene protocols
                    - Active recovery and regeneration plans
                    
                    Input should be session parameters as JSON string.""",
                    func=session_gen_tool.generate_session,
                )
                recovery_tools.append(session_gen_langchain)
                logger.info(
                    "✅ Added domain-specific session generation to recovery coach"
                )
            except Exception as e:
                logger.warning(f"Failed to load session generation tool: {e}")

            # Update the agent's tools
            self.tools = recovery_tools

            logger.info(
                f"✅ STANDARDIZED TOOLS: Loaded {len(self.tools)} tools for recovery coach"
            )
            logger.info(f"🔧 Tool names: {[t.name for t in self.tools]}")
            self._tools_loaded = True

        return self.tools

    def get_domain_prompt(self) -> str:
        """Get the domain-specific prompt for recovery coaching."""
        return self.system_prompt

    async def process(
        self, state: AgentState, config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Process recovery requests with tool loading."""
        # Ensure tools are loaded before processing
        if not self.tools:
            await self.get_domain_tools()

        # Ensure prompt is loaded before processing (pass config for Studio overrides)
        await self._load_system_prompt(config)

        return await super().process(state, config)


# Create the recovery agent instance
recovery_coach = RecoveryAgent()


async def recovery_coach_node(
    state: AgentState, config: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    LangGraph node function for recovery coaching.

    Args:
        state: Current agent state
        config: Optional configuration

    Returns:
        Updated state with recovery agent response
    """
    logger.info("--- Running Recovery Agent Node ---")

    try:
        # Ensure agent has tools loaded
        if not recovery_coach.tools:
            await recovery_coach.get_domain_tools()

        # Process the request
        result = await recovery_coach.process(state, config)

        # Update state with results
        updated_state = {
            "messages": state.get("messages", [])
            + [{"role": "assistant", "content": result.get("response", "")}],
            "specialist_completed": result.get("specialist_completed", True),
            "current_agent": "recovery_agent",
            "agent_metadata": result.get("metadata", {}),
        }

        if result.get("error"):
            updated_state["error"] = result["error"]

        logger.info(
            f"Recovery coach completed with {len(recovery_coach.tools)} tools available"
        )
        return updated_state

    except Exception as e:
        logger.error(f"Error in recovery agent node: {e}")
        return {
            "messages": state.get("messages", [])
            + [
                {
                    "role": "assistant",
                    "content": f"I encountered an error while processing your recovery request: {str(e)}",
                }
            ],
            "specialist_completed": False,
            "current_agent": "recovery_agent",
            "error": str(e),
        }
