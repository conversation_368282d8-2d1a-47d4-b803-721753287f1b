import logging
from typing import Any, Dict

from langchain_core.messages import AIMessage, SystemMessage, HumanMessage

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.states.onboarding_state import (
    OnboardingState,
    SidebarStateData,
)
from athlea_langgraph.utils.prompt_loader import get_prompt_loader

logger = logging.getLogger(__name__)


class GeneratePlanSummaryNode:
    """Node for generating a plan summary preview before full plan generation"""

    def __init__(self):
        self.llm = create_azure_chat_openai(temperature=0.3)
        self.plan_summary_prompt_template = None  # Will be loaded lazily

        # Fallback prompt template
        self.fallback_prompt_template = """You are an expert fitness coach. Based on the user's goals and the summarized information, create a plan summary preview that shows what their personalized training plan will include.

User Goals: {user_goals}

User Information Summary:
{summary_string}

Selected Sports/Activities: {selected_sports}

Create a conversational summary that explains:
1. What type of training plan you'll create for them
2. The main focus areas based on their goals
3. How their experience level and time commitment will shape the plan
4. What equipment/setup considerations you'll include
5. Ask them to confirm if this direction looks good or if they'd like to adjust anything

Be encouraging and show that you understand their specific needs. Keep it conversational and not too technical. End by asking for their confirmation to proceed with generating the full detailed plan."""

    async def _get_plan_summary_prompt_template(self) -> str:
        """Load the plan summary prompt template lazily"""
        if self.plan_summary_prompt_template is None:
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt(
                    "onboarding/plan_summary_generation"
                )
                self.plan_summary_prompt_template = prompt_config.get_rendered_prompt()
                logger.info(
                    "Successfully loaded onboarding plan summary generation template prompt from file"
                )
            except Exception as e:
                logger.error(
                    f"Failed to load onboarding plan summary generation template prompt: {e}"
                )
                self.plan_summary_prompt_template = self.fallback_prompt_template

        return self.plan_summary_prompt_template

    async def __call__(self, state: OnboardingState) -> Dict[str, Any]:
        """Generate a plan summary based on collected information"""
        logger.info("[Node: generatePlanSummary] Entering plan summary generation node")

        # Get both current turn messages and full conversation history
        current_messages = state.get("messages", [])
        conversation_history = state.get("conversation_history", [])

        # Convert conversation history to LangChain message format if needed
        history_messages = []
        if conversation_history:
            logger.info(
                f"[Node: generatePlanSummary] Found {len(conversation_history)} messages in conversation history"
            )
            for msg in conversation_history:
                if isinstance(msg, dict):
                    role = msg.get("role", "")
                    content = msg.get("content", "")
                    if role == "human" and content:
                        history_messages.append(HumanMessage(content=content))
                    elif role == "assistant" and content:
                        history_messages.append(AIMessage(content=content))
                elif hasattr(msg, "content"):
                    # Already a LangChain message
                    history_messages.append(msg)

        # Combine conversation history with current turn messages for complete context
        messages = history_messages + current_messages

        logger.info(
            f"[Node: generatePlanSummary] Using {len(history_messages)} history messages + {len(current_messages)} current messages = {len(messages)} total messages"
        )

        sidebar_data = state.get("sidebar_data") or SidebarStateData()
        user_id = state.get("user_id")

        # Format summary items for the prompt
        summary_string = ""
        if sidebar_data.summary_items:
            summary_string = "\n - ".join(
                [
                    f"{item.category}: {item.details}"
                    for item in sidebar_data.summary_items
                ]
            )
        else:
            summary_string = "No summary available."

        # Format user goals
        user_goals = ""
        if sidebar_data.goals and sidebar_data.goals.list:
            user_goals = ", ".join(sidebar_data.goals.list)
        else:
            user_goals = "Not specified"

        # Format selected sports
        selected_sports = ""
        if sidebar_data.selected_sports:
            selected_sports = ", ".join(sidebar_data.selected_sports)
        else:
            selected_sports = "Not specified"

        # Get the plan summary prompt template (loads lazily if needed)
        summary_template = await self._get_plan_summary_prompt_template()

        # Prepare the system prompt using the template
        system_prompt = summary_template.format(
            user_goals=user_goals,
            summary_string=summary_string,
            selected_sports=selected_sports,
        )

        logger.info(
            "[Node: generatePlanSummary] Calling LLM for plan summary generation."
        )

        # Configure LLM with tags for streaming detection
        streaming_llm = self.llm.with_config(
            {"tags": ["final_response", "generatePlanSummary"]}
        )

        try:
            # Create conversation with system prompt and full history
            conversation_with_prompt = [SystemMessage(content=system_prompt)] + messages

            # Stream the response and collect content
            summary_response_content = ""
            async for chunk in streaming_llm.astream(conversation_with_prompt):
                if hasattr(chunk, "content") and chunk.content:
                    summary_response_content += chunk.content

            logger.info(
                f"[Node: generatePlanSummary] Successfully generated plan summary"
            )

            # Create AI message with the summary content
            summary_message = AIMessage(content=summary_response_content)

            # Update sidebar data to indicate plan summary is ready
            updated_sidebar_data = SidebarStateData(
                current_stage="plan_summary_ready",  # New stage for plan summary
                goals=sidebar_data.goals,
                summary_items=sidebar_data.summary_items,
                generated_plan=None,  # No full plan yet
                sport_suggestions=sidebar_data.sport_suggestions,
                selected_sport=sidebar_data.selected_sport,
                selected_sports=sidebar_data.selected_sports,
                uploaded_documents=sidebar_data.uploaded_documents,
                key_insights=sidebar_data.key_insights,
            )

            return {
                "messages": [summary_message],
                "sidebar_data": updated_sidebar_data,
                "onboarding_stage": "plan_summary_ready",
            }

        except Exception as error:
            logger.error(
                f"[Node: generatePlanSummary] Error generating plan summary: {error}"
            )

            error_sidebar_data = SidebarStateData(
                current_stage="error",
                goals=sidebar_data.goals,
                summary_items=sidebar_data.summary_items,
                generated_plan=sidebar_data.generated_plan,
                sport_suggestions=sidebar_data.sport_suggestions,
                selected_sport=sidebar_data.selected_sport,
                selected_sports=sidebar_data.selected_sports,
                uploaded_documents=sidebar_data.uploaded_documents,
                key_insights=sidebar_data.key_insights,
            )

            # Stream error message
            error_message_content = ""
            try:
                error_streaming_llm = self.llm.with_config(
                    {"tags": ["final_response", "generatePlanSummary"]}
                )
                async for chunk in error_streaming_llm.astream(
                    [
                        SystemMessage(
                            content="Respond with exactly this message: I apologize, but I encountered an error while creating your plan summary. Please try again."
                        )
                    ]
                ):
                    if hasattr(chunk, "content") and chunk.content:
                        error_message_content += chunk.content
            except:
                error_message_content = "Error generating plan summary."

            error_message = AIMessage(content=error_message_content)

            return {
                "messages": [error_message],
                "sidebar_data": error_sidebar_data,
                "onboarding_stage": "error",
            }


# Create the node instance
generate_plan_summary_node = GeneratePlanSummaryNode()
