import logging
from typing import Any, Dict

from langchain_core.messages import BaseMessage, SystemMessage, HumanMessage, AIMessage

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.states.onboarding_state import OnboardingState
from athlea_langgraph.utils.prompt_loader import get_prompt_loader

logger = logging.getLogger(__name__)


class CheckCompletionNode:
    """Node for checking if enough information has been gathered"""

    def __init__(self):
        # Low temperature for deterministic check
        self.llm = create_azure_chat_openai(temperature=0.0)
        self.completion_check_prompt = None  # Will be loaded lazily

        # Fallback prompt in case loading fails
        self.fallback_prompt = """Analyze the provided conversation history, focusing ONLY on information explicitly stated by the USER.

**Your Task:** Verify if the USER has provided information covering ALL of the following categories:
1.  **Specific Fitness Goal(s):** At least one clear goal mentioned for their primary sport(s).
2.  **Experience Level:** Some statement about their experience (overall or per sport).
3.  **Time Commitment:** Details on days/week, duration, or time of day.
4.  **Equipment Access:** Mention of available equipment or workout location relevant to their goals.
5.  **Priorities/Connections/Seasonality:** Statement on how goals relate, primary focus, or relevant time constraints.

**Additional Check:** Examine the VERY LAST user message. Does it clearly indicate readiness to proceed or explicitly ask for the plan (e.g., "Okay", "Yes", "Let's do it", "Generate the plan", "Sounds good", confirming the last piece of info)?

**Response Rules:**
- Respond ONLY with the word "true" IF AND ONLY IF:
    - There is clear evidence from the USER for **ALL 5 required categories** listed above.
    - AND the **last user message** indicates readiness.
- Respond ONLY with the word "false" otherwise (if any category is missing OR the user isn't ready).

Do not provide any explanation or other text."""

    async def _get_completion_check_prompt(self) -> str:
        """Load the completion check prompt lazily"""
        if self.completion_check_prompt is None:
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt(
                    "onboarding/completion_check"
                )
                self.completion_check_prompt = prompt_config.get_rendered_prompt()
                logger.info(
                    "Successfully loaded onboarding completion check prompt from file"
                )
            except Exception as e:
                logger.error(f"Failed to load onboarding completion check prompt: {e}")
                self.completion_check_prompt = self.fallback_prompt

        return self.completion_check_prompt

    def _analyze_conversation_categories(self, human_messages: list) -> Dict[str, Any]:
        """
        Analyze the conversation to determine which categories might be present.
        This is a heuristic analysis to help with debugging.
        """
        all_text = " ".join([msg.content.lower() for msg in human_messages])
        
        analysis = {
            "fitness_goals": {
                "keywords": ["goal", "want to", "improve", "build", "lose", "gain", "run", "lift", "train"],
                "found": False,
                "evidence": []
            },
            "experience_level": {
                "keywords": ["beginner", "intermediate", "advanced", "experience", "years", "new to", "familiar with"],
                "found": False,
                "evidence": []
            },
            "time_commitment": {
                "keywords": ["days", "week", "hours", "minutes", "time", "schedule", "available", "dedicate"],
                "found": False,
                "evidence": []
            },
            "equipment_access": {
                "keywords": ["gym", "home", "equipment", "weights", "treadmill", "bike", "outdoors", "access"],
                "found": False,
                "evidence": []
            },
            "priorities_seasonality": {
                "keywords": ["priority", "focus", "season", "winter", "summer", "important", "primary"],
                "found": False,
                "evidence": []
            }
        }
        
        for category, info in analysis.items():
            for keyword in info["keywords"]:
                if keyword in all_text:
                    info["found"] = True
                    # Find the message containing this keyword
                    for msg in human_messages:
                        if keyword in msg.content.lower():
                            info["evidence"].append(f"'{keyword}' in: {msg.content[:100]}...")
                            break
        
        return analysis

    async def __call__(self, state: OnboardingState) -> Dict[str, Any]:
        """
        Check if enough information has been gathered to proceed with plan generation.

        This node analyzes the complete conversation history to determine if we have
        collected sufficient information across the 5 essential categories.
        """
        logger.info("[Node: checkCompletion] Entering completion check node")

        # Get both current turn messages and full conversation history
        current_messages = state.get("messages", [])
        conversation_history = state.get("conversation_history", [])

        # Convert conversation history to LangChain message format if needed
        history_messages = []
        if conversation_history:
            logger.info(
                f"[Node: checkCompletion] Found {len(conversation_history)} messages in conversation history"
            )
            for msg in conversation_history:
                if isinstance(msg, dict):
                    role = msg.get("role", "")
                    content = msg.get("content", "")
                    if role == "human" and content:
                        history_messages.append(HumanMessage(content=content))
                    elif role == "assistant" and content:
                        history_messages.append(AIMessage(content=content))
                elif hasattr(msg, "content"):
                    # Already a LangChain message
                    history_messages.append(msg)

        # Combine conversation history with current turn messages for complete context
        messages = history_messages + current_messages

        logger.info(
            f"[Node: checkCompletion] Using {len(history_messages)} history messages + {len(current_messages)} current messages = {len(messages)} total messages"
        )

        # Filter out system messages for analysis
        user_messages = [msg for msg in messages if not isinstance(msg, SystemMessage)]

        if not user_messages:
            logger.info("[Node: checkCompletion] No messages, cannot be complete.")
            return {"has_enough_info": False}

        # Enhanced logging: Log all user messages for analysis
        logger.info("[Node: checkCompletion] === USER MESSAGES ANALYSIS ===")
        human_messages = [msg for msg in user_messages if isinstance(msg, HumanMessage)]
        for i, msg in enumerate(human_messages, 1):
            content_preview = msg.content[:200] + "..." if len(msg.content) > 200 else msg.content
            logger.info(f"[Node: checkCompletion] User Message {i}: {content_preview}")
        
        # Log the last user message specifically for readiness check
        if human_messages:
            last_user_message = human_messages[-1].content
            logger.info(f"[Node: checkCompletion] === LAST USER MESSAGE (READINESS CHECK) ===")
            logger.info(f"[Node: checkCompletion] Last message: '{last_user_message}'")
            
            # Basic analysis of readiness indicators
            readiness_indicators = ["okay", "yes", "let's do it", "generate the plan", "sounds good", "let's go", "perfect", "ready", "create the plan"]
            has_readiness_indicator = any(indicator in last_user_message.lower() for indicator in readiness_indicators)
            logger.info(f"[Node: checkCompletion] Contains readiness indicator: {has_readiness_indicator}")
        else:
            logger.info("[Node: checkCompletion] No user messages found for readiness check")

        # Enhanced logging: Analyze conversation for each category
        if human_messages:
            logger.info("[Node: checkCompletion] === CATEGORY ANALYSIS ===")
            category_analysis = self._analyze_conversation_categories(human_messages)
            for category, info in category_analysis.items():
                status = "✅ LIKELY PRESENT" if info["found"] else "❌ LIKELY MISSING"
                logger.info(f"[Node: checkCompletion] {category.upper()}: {status}")
                if info["evidence"]:
                    for evidence in info["evidence"][:2]:  # Show max 2 pieces of evidence
                        logger.info(f"[Node: checkCompletion]   Evidence: {evidence}")

        # Get the completion check prompt (loads lazily if needed)
        completion_prompt = await self._get_completion_check_prompt()

        # Prepare history for the check LLM
        history_for_check = [
            SystemMessage(content=completion_prompt),
            *user_messages,  # Exclude other system prompts
        ]

        logger.info(
            f"[Node: checkCompletion] Calling LLM for completion check with history length: {len(history_for_check) - 1}"
        )

        # Enhanced logging: Log the categories we're checking for
        logger.info("[Node: checkCompletion] === REQUIRED CATEGORIES CHECK ===")
        logger.info("[Node: checkCompletion] Checking for these 5 required categories:")
        logger.info("[Node: checkCompletion] 1. Specific Fitness Goal(s): Clear goals for primary sport(s)")
        logger.info("[Node: checkCompletion] 2. Experience Level: Statement about experience (overall/per sport)")
        logger.info("[Node: checkCompletion] 3. Time Commitment: Days/week, duration, or time of day")
        logger.info("[Node: checkCompletion] 4. Equipment Access: Available equipment or workout location")
        logger.info("[Node: checkCompletion] 5. Priorities/Connections/Seasonality: How goals relate, focus, constraints")

        try:
            response = await self.llm.ainvoke(history_for_check)
            content = (
                response.content.strip().lower()
                if isinstance(response.content, str)
                else ""
            )
            logger.info(f"[Node: checkCompletion] Raw LLM Response: {content}")

            is_complete = content == "true"
            logger.info(f"[Node: checkCompletion] Determined completion: {is_complete}")

            # Enhanced logging: If completion is false, explain what might be missing
            if not is_complete:
                logger.info("[Node: checkCompletion] === COMPLETION ANALYSIS ===")
                logger.info("[Node: checkCompletion] Completion returned FALSE. Possible reasons:")
                logger.info("[Node: checkCompletion] - One or more of the 5 required categories is missing")
                logger.info("[Node: checkCompletion] - Last user message doesn't indicate readiness to proceed")
                logger.info("[Node: checkCompletion] - Information provided is too vague or incomplete")
                logger.info("[Node: checkCompletion] The LLM determined that more information is needed before generating a plan")
                
                # Log current sidebar data for context
                sidebar_data = state.get("sidebar_data", {})
                if sidebar_data:
                    goals = sidebar_data.get("goals", {})
                    summary_items = sidebar_data.get("summary_items", [])
                    selected_sports = sidebar_data.get("selected_sports", [])
                    
                    logger.info(f"[Node: checkCompletion] Current extracted data - Goals exist: {goals.get('exists', False)}")
                    logger.info(f"[Node: checkCompletion] Current extracted data - Goals list: {goals.get('list', [])}")
                    logger.info(f"[Node: checkCompletion] Current extracted data - Selected sports: {selected_sports}")
                    logger.info(f"[Node: checkCompletion] Current extracted data - Summary items count: {len(summary_items)}")
                    
                    for i, item in enumerate(summary_items, 1):
                        logger.info(f"[Node: checkCompletion] Summary item {i}: {item}")
            else:
                logger.info("[Node: checkCompletion] === COMPLETION SUCCESS ===")
                logger.info("[Node: checkCompletion] All required information categories are present and user is ready!")

            return {"has_enough_info": is_complete}

        except Exception as error:
            logger.error(f"[Node: checkCompletion] Error calling LLM: {error}")
            return {"has_enough_info": False}  # Default to false on error


# Create the node instance
check_completion_node = CheckCompletionNode()
