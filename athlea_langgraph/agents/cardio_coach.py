"""
Cardio Agent - Modular ReAct Implementation

Specialized agent for cardiovascular training, running, cycling, and endurance sports.
Follows multi-agent best practices with single responsibility and ReAct pattern.
"""

import logging
from typing import Any, Dict, List, Optional

from langchain_core.tools import BaseTool, Tool

from athlea_langgraph.states import AgentState

# Direct tool imports for self-contained agent
from ..tools.cardio import (
    comprehensive_cardio_assessment,
    calculate_training_zones,
    calculate_heart_rate_zones,
)

# Additional tool access capabilities
from ..tools.external.azure_search_retriever import AzureSearchRetrieverTool
from ..tools.external.session_generation import SessionGenerationTool
from ..utils.prompt_loader import get_prompt_loader
from .base_agent import BaseReActAgent

logger = logging.getLogger(__name__)


class CardioAgent(BaseReActAgent):
    """Specialized agent for cardio coaching using ReAct pattern."""

    def __init__(self):
        """Initialize the cardio agent with domain-specific tools."""
        # Fallback prompt with strong tool calling instructions
        fallback_prompt = """You are a world-class Cardio Coach. Your primary function is to provide precise, evidence-based cardiovascular training advice by leveraging your specialized tools.

**CRITICAL INSTRUCTIONS:**
1.  **Assess the User's Query:** Analyze the user's question to determine if one of your tools can provide a precise, data-driven answer.
2.  **Prioritize Tool Usage:** For any request related to calculating training zones, assessing cardio fitness, retrieving scientific research, or finding recent race results, you MUST use the appropriate tool. Do not answer from general knowledge if a tool is available.
3.  **Use Tools Correctly:**
    - To calculate training zones, use `calculate_training_zones`.
    - To calculate heart rate zones, use `calculate_heart_rate_zones`.
    - To perform a comprehensive assessment, use `comprehensive_cardio_assessment`.
    - For research questions, use `azure_search_retriever`.
    - For current news or event results, use `web_search`.
4.  **Engage in Conversation:** If a tool is not required, respond naturally and conversationally. If you need more information to use a tool effectively, ask the user clarifying questions.

Your goal is to be a helpful and accurate cardio coach, using your tools to provide the best possible guidance."""

        # Tools will be loaded asynchronously in get_domain_tools()
        super().__init__(
            name="cardio_agent",
            domain="cardio",
            system_prompt=fallback_prompt,
            tools=[],
            permissions=[
                "training_zone_calculation",
                "heart_rate_monitoring",
                "cardio_assessment",
            ],
            max_iterations=10,
            temperature=0.2,
        )

        self._prompt_loaded = False
        self.fallback_prompt = fallback_prompt
        self._tools_loaded = False

    async def _load_system_prompt(self, config: Optional[Dict[str, Any]] = None) -> str:
        """Load the system prompt lazily, checking for Studio overrides first"""
        if not self._prompt_loaded:
            # Check for Studio prompt override first
            studio_prompt = None
            if config and isinstance(config, dict):
                # Check for the prompt parameter from Studio configuration
                studio_prompt = config.get("cardio_coach_prompt")
                if studio_prompt and studio_prompt.strip():
                    logger.info("Using cardio coach prompt from Studio configuration")
                    self.system_prompt = studio_prompt
                    self._prompt_loaded = True
                    return self.system_prompt

            # Fall back to JSON file prompt
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt("coaches/cardio_coach")
                self.system_prompt = prompt_config.get_rendered_prompt()
                logger.info("Successfully loaded cardio coach prompt from JSON file")
            except Exception as e:
                logger.error(f"Failed to load cardio coach prompt: {e}")
                self.system_prompt = self.fallback_prompt
            self._prompt_loaded = True

        return self.system_prompt

    async def get_domain_tools(self) -> List[BaseTool]:
        """
        STANDARDIZED TOOL ACCESS for all domain coaches.

        Every coach gets access to:
        1. Domain-specific tools (cardio tools)
        2. GraphRAG - Advanced research capabilities
        3. Azure Search - Evidence-based research
        4. WebSearchGraph - Multi-agent web research
        5. Maps Workflow - Location-based services
        6. Session Generation - Domain-specific session creation
        """
        if not self._tools_loaded:
            # 1. Domain-specific cardio tools
            cardio_tools = [
                comprehensive_cardio_assessment,
                calculate_training_zones,
                calculate_heart_rate_zones,
            ]

            # STANDARDIZED UNIVERSAL TOOLS

            # 2. GraphRAG tool for advanced research
            try:
                from ..tools.graphrag_tool import create_graphrag_tool

                graphrag_tool = create_graphrag_tool()
                cardio_tools.append(graphrag_tool)
                logger.info("✅ Added GraphRAG tool to cardio coach")
            except Exception as e:
                logger.warning(f"Failed to load GraphRAG tool: {e}")

            # 3. Azure Search for evidence-based research
            try:
                from ..tools.external.azure_search_retriever import (
                    AzureSearchRetrieverTool,
                )

                azure_search_tool = AzureSearchRetrieverTool()
                azure_search_langchain = Tool(
                    name="azure_search_retriever",
                    description="""Search Azure Cognitive Search for research, documentation, and evidence-based cardio/endurance information.
                    
                    Use this for:
                    - Research findings on cardiovascular training
                    - Exercise science and training methodology
                    - Equipment reviews and recommendations
                    - Injury prevention strategies
                    
                    Input should be a search query string.""",
                    func=lambda query: "Azure Search requires async execution",
                    coroutine=azure_search_tool.invoke,
                )
                cardio_tools.append(azure_search_langchain)
                logger.info("✅ Added Azure Search tool to cardio coach")
            except Exception as e:
                logger.warning(f"Failed to load Azure Search tool: {e}")

            # 4. WebSearchGraph (multi-agent research)
            try:
                from ..tools.web_search_graph_tool import create_web_search_graph_tool

                web_research_tool = create_web_search_graph_tool()
                cardio_tools.append(web_research_tool)
                logger.info("✅ Added WebSearchGraph tool to cardio coach")
            except Exception as e:
                logger.warning(f"Failed to load WebSearchGraph tool: {e}")
                # Fallback to basic web search tool
                try:
                    from ..tools.web_search_tool import WebSearchTool

                    web_search_tool = WebSearchTool()
                    cardio_tools.append(web_search_tool)
                    logger.info(
                        "⚠️ Fallback: Added basic WebSearch tool to cardio coach"
                    )
                except Exception as fallback_e:
                    logger.warning(
                        f"Failed to load fallback web search tool: {fallback_e}"
                    )

            # 5. Maps workflow tool for location-based services
            try:
                from ..tools.external.maps_workflow import create_maps_workflow_tool

                maps_tool = await create_maps_workflow_tool()
                cardio_tools.append(maps_tool)
                logger.info("✅ Added Maps workflow tool to cardio coach")
            except Exception as e:
                logger.warning(f"Failed to load Maps workflow tool: {e}")

            # 6. Domain-specific session generation
            try:
                from ..tools.external.session_generation import SessionGenerationTool

                session_gen_tool = SessionGenerationTool()
                session_gen_langchain = Tool(
                    name="generate_cardio_session",
                    description="""Generate structured cardio training sessions and workout plans.
                    
                    Use this for:
                    - Running workouts (intervals, tempo, long runs)
                    - Cycling sessions (endurance, power, recovery)
                    - Swimming workouts or cross-training sessions
                    
                    Input should be session parameters as JSON string.""",
                    func=session_gen_tool.generate_session,
                )
                cardio_tools.append(session_gen_langchain)
                logger.info(
                    "✅ Added domain-specific session generation to cardio coach"
                )
            except Exception as e:
                logger.warning(f"Failed to load session generation tool: {e}")

            self.tools = cardio_tools

            logger.info(
                f"✅ STANDARDIZED TOOLS: Loaded {len(self.tools)} tools for cardio coach"
            )
            logger.info(f"🔧 Tool names: {[t.name for t in self.tools]}")
            self._tools_loaded = True

        return self.tools

    def get_domain_prompt(self) -> str:
        """Get the domain-specific prompt for cardio coaching."""
        return self.system_prompt

    async def process(
        self, state: AgentState, config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Process cardio training requests with tool loading."""
        # Ensure tools are loaded before processing
        if not self.tools:
            await self.get_domain_tools()

        # Ensure prompt is loaded before processing (pass config for Studio overrides)
        await self._load_system_prompt(config or {})

        return await super().process(state, config or {})


# Create the cardio agent instance
cardio_coach = CardioAgent()


async def cardio_coach_node(
    state: AgentState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    LangGraph node function for cardio/endurance coaching.

    Args:
        state: Current agent state
        config: Optional configuration

    Returns:
        Updated state with cardio agent response
    """
    logger.info("--- Running Cardio Agent Node ---")

    try:
        # Ensure agent has tools loaded
        if not cardio_coach.tools:
            await cardio_coach.get_domain_tools()

        # Process the request
        result = await cardio_coach.process(state, config or {})

        # Update state with results
        updated_state = {
            "messages": state.get("messages", [])
            + [{"role": "assistant", "content": result.get("response", "")}],
            "specialist_completed": result.get("specialist_completed", True),
            "current_agent": "cardio_agent",
            "agent_metadata": result.get("metadata", {}),
        }

        if result.get("error"):
            updated_state["error"] = result["error"]

        logger.info(
            f"Cardio coach completed with {len(cardio_coach.tools)} tools available"
        )
        return updated_state

    except Exception as e:
        logger.error(f"Error in cardio agent node: {e}")
        return {
            "messages": state.get("messages", [])
            + [
                {
                    "role": "assistant",
                    "content": f"I encountered an error while processing your cardio training request: {str(e)}",
                }
            ],
            "specialist_completed": False,
            "current_agent": "cardio_agent",
            "error": str(e),
        }
