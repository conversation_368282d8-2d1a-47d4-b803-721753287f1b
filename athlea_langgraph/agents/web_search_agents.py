"""
Web Search Agents

Multi-agent system for comprehensive web research based on LangGraph patterns.
Includes planning, searching, scraping, analysis, and synthesis agents.
"""

import json
import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from langchain_core.language_models import BaseChatModel
from pydantic import BaseModel, Field

# NOTE: WebSearchState and related classes need to be properly defined
# The web_search_state module was removed during state management refactoring
# TODO: Define these classes in athlea_langgraph/graphs/tools/web_search_graph.py
# or create a new module for web search data models

# Temporary imports from the actual location where WebSearchState is defined
from ..graphs.tools.web_search_graph import WebSearchState

# These classes need to be defined somewhere - they don't exist currently
# from somewhere import WebSearchResult, ScrapedWebContent, WebSearchPlan
from ..tools.web_search_tool import (
    WebSearchTool,
    WebScrapingTool,
    WebResearchTool,
    WebSearchInput,
    WebScrapingInput,
)
from ..prompt_system import PromptRegistry

logger = logging.getLogger(__name__)


class WebSearchPlannerAgent:
    """
    Agent responsible for creating search strategies and plans.

    Analyzes research questions and generates comprehensive search plans
    including search terms, strategies, and expected sources.
    """

    def __init__(
        self,
        llm: BaseChatModel,
        prompt_system: Optional[PromptRegistry] = None,
        max_search_terms: int = 5,
    ):
        self.llm = llm
        self.prompt_system = prompt_system or PromptRegistry()
        self.max_search_terms = max_search_terms

    async def plan_research(self, state: WebSearchState) -> WebSearchState:
        """Generate a comprehensive research plan."""

        try:
            # Create planning prompt
            planning_prompt = self._create_planning_prompt(
                state.research_question, state.research_context, state.research_type
            )

            # Get LLM response
            messages = [SystemMessage(content=planning_prompt)]
            response = await self.llm.ainvoke(messages)

            # Parse the response to extract search plan
            search_plan = self._parse_planning_response(response.content)

            # Update state
            state.search_plan = search_plan
            state.search_queries = search_plan.search_terms[: self.max_search_terms]
            state.current_step = "searching"

            # Add to message history
            state.planner_messages.append(AIMessage(content=response.content))

            logger.info(
                f"Generated search plan with {len(search_plan.search_terms)} search terms"
            )
            return state

        except Exception as e:
            logger.error(f"Planning failed: {str(e)}")
            state.search_errors.append(f"Planning error: {str(e)}")
            # Create fallback plan
            state.search_plan = WebSearchPlan(
                search_terms=[state.research_question],
                search_strategy="basic",
                expected_sources=["general web"],
                content_focus="comprehensive",
            )
            state.search_queries = [state.research_question]
            state.current_step = "searching"
            return state

    def _create_planning_prompt(
        self, question: str, context: Optional[str], research_type: str
    ) -> str:
        """Create the research planning prompt."""

        base_prompt = f"""You are a research planning expert. Create a comprehensive search strategy for the following research question:

RESEARCH QUESTION: {question}

RESEARCH TYPE: {research_type}
"""

        if context:
            base_prompt += f"\nADDITIONAL CONTEXT: {context}"

        base_prompt += """

Create a search plan that includes:

1. SEARCH_TERMS: 3-5 specific search queries that will find the most relevant information
2. SEARCH_STRATEGY: The overall approach (comprehensive, targeted, recent, authoritative, etc.)
3. EXPECTED_SOURCES: Types of sources likely to have the best information
4. CONTENT_FOCUS: What specific aspects to focus on

Format your response as JSON:
{
    "search_terms": ["query1", "query2", "query3"],
    "search_strategy": "description of strategy",
    "expected_sources": ["source_type1", "source_type2"],
    "content_focus": "focus description",
    "max_results_per_query": 10
}

Be strategic and specific. Consider synonyms, related terms, and different angles to approach the research question."""

        return base_prompt

    def _parse_planning_response(self, response: str) -> WebSearchPlan:
        """Parse the LLM response to extract search plan."""

        try:
            # Try to extract JSON from response
            start_idx = response.find("{")
            end_idx = response.rfind("}") + 1

            if start_idx >= 0 and end_idx > start_idx:
                json_str = response[start_idx:end_idx]
                plan_data = json.loads(json_str)

                return WebSearchPlan(
                    search_terms=plan_data.get("search_terms", []),
                    search_strategy=plan_data.get("search_strategy", "comprehensive"),
                    expected_sources=plan_data.get("expected_sources", ["general web"]),
                    content_focus=plan_data.get("content_focus", "comprehensive"),
                    max_results_per_query=plan_data.get("max_results_per_query", 10),
                )
            else:
                raise ValueError("No valid JSON found in response")

        except Exception as e:
            logger.warning(f"Failed to parse planning response: {str(e)}")
            # Fallback: extract search terms from text
            lines = response.split("\n")
            search_terms = []
            for line in lines:
                if "query" in line.lower() or "search" in line.lower():
                    # Extract quoted text as potential search term
                    if '"' in line:
                        parts = line.split('"')
                        for i in range(1, len(parts), 2):
                            if parts[i].strip():
                                search_terms.append(parts[i].strip())

            if not search_terms:
                search_terms = [
                    response.split("\n")[0][:100]
                ]  # Use first line as fallback

            return WebSearchPlan(
                search_terms=search_terms[:5],
                search_strategy="basic",
                expected_sources=["general web"],
                content_focus="comprehensive",
            )


class WebSearchExecutorAgent:
    """
    Agent responsible for executing web searches.

    Takes search queries and executes them using the web search tools,
    collecting and organizing search results.
    """

    def __init__(self, search_tool: WebSearchTool, max_results_per_query: int = 10):
        self.search_tool = search_tool
        self.max_results_per_query = max_results_per_query

    async def execute_searches(self, state: WebSearchState) -> WebSearchState:
        """Execute all planned search queries."""

        try:
            all_results = []

            for i, query in enumerate(state.search_queries):
                try:
                    # Execute search
                    search_input = WebSearchInput(
                        query=query,
                        max_results=min(
                            state.max_search_results, self.max_results_per_query
                        ),
                        search_type="web",
                    )

                    response = await self.search_tool.invoke(search_input.dict())

                    if hasattr(response, "data") and response.data:
                        search_data = response.data

                        # Convert to WebSearchResult objects
                        for result_data in search_data.search_results:
                            result = WebSearchResult(
                                title=result_data["title"],
                                url=result_data["url"],
                                snippet=result_data["snippet"],
                                position=result_data["position"],
                                source_domain=result_data["source_domain"],
                            )
                            all_results.append(result)

                    logger.info(
                        f"Search query '{query}' returned {len(search_data.search_results if hasattr(response, 'data') and response.data else [])} results"
                    )

                except Exception as e:
                    logger.error(f"Search query '{query}' failed: {str(e)}")
                    state.search_errors.append(f"Query '{query}' failed: {str(e)}")
                    continue

            # Add results to state
            state.search_results.extend(all_results)

            # Select top URLs for scraping
            state.selected_urls = self._select_urls_for_scraping(
                all_results, state.max_pages_to_scrape
            )
            state.current_step = "scraping"

            # Add message
            summary_msg = f"Executed {len(state.search_queries)} search queries, found {len(all_results)} total results"
            state.searcher_messages.append(AIMessage(content=summary_msg))

            logger.info(f"Search execution complete: {len(all_results)} total results")
            return state

        except Exception as e:
            logger.error(f"Search execution failed: {str(e)}")
            state.search_errors.append(f"Search execution failed: {str(e)}")
            state.current_step = "error"
            return state

    def _select_urls_for_scraping(
        self, results: List[WebSearchResult], max_urls: int
    ) -> List[str]:
        """Select the best URLs for content scraping."""

        # Simple selection: take top results, avoiding duplicates
        selected_urls = []
        seen_domains = set()

        for result in results:
            if len(selected_urls) >= max_urls:
                break

            # Avoid too many results from the same domain
            if (
                result.source_domain not in seen_domains
                or len(selected_urls) < max_urls // 2
            ):
                selected_urls.append(result.url)
                seen_domains.add(result.source_domain)

        return selected_urls


class WebContentScrapingAgent:
    """
    Agent responsible for scraping content from selected URLs.

    Scrapes webpage content and validates quality for research purposes.
    """

    def __init__(self, scraping_tool: WebScrapingTool):
        self.scraping_tool = scraping_tool

    async def scrape_content(self, state: WebSearchState) -> WebSearchState:
        """Scrape content from selected URLs."""

        try:
            scraped_contents = []

            for url in state.selected_urls:
                try:
                    # Scrape content
                    scraping_input = WebScrapingInput(
                        url=url, max_content_length=4000, extract_links=False
                    )

                    response = await self.scraping_tool.invoke(scraping_input.dict())

                    if hasattr(response, "data") and response.data:
                        content_data = response.data.scraped_content

                        scraped_content = ScrapedWebContent(
                            url=content_data["url"],
                            title=content_data["title"],
                            content=content_data["content"],
                            content_length=content_data["content_length"],
                            extracted_links=content_data.get("extracted_links"),
                            status=content_data["status"],
                            error_message=content_data.get("error_message"),
                            scrape_timestamp=datetime.now().isoformat(),
                        )

                        scraped_contents.append(scraped_content)

                        logger.info(
                            f"Scraped {content_data['content_length']} chars from {url}"
                        )

                except Exception as e:
                    logger.error(f"Failed to scrape {url}: {str(e)}")
                    state.scraping_errors.append(f"Failed to scrape {url}: {str(e)}")

                    # Add error content
                    error_content = ScrapedWebContent(
                        url=url,
                        title="Scraping Failed",
                        content="",
                        content_length=0,
                        status="error",
                        error_message=str(e),
                        scrape_timestamp=datetime.now().isoformat(),
                    )
                    scraped_contents.append(error_content)
                    continue

            # Add scraped content to state
            for content in scraped_contents:
                state.add_scraped_content(content)

            state.current_step = "analysis"

            # Add message
            successful_scrapes = len(
                [c for c in scraped_contents if c.status == "success"]
            )
            summary_msg = f"Scraped {len(scraped_contents)} pages, {successful_scrapes} successful"
            state.scraper_messages.append(AIMessage(content=summary_msg))

            logger.info(
                f"Content scraping complete: {successful_scrapes}/{len(scraped_contents)} successful"
            )
            return state

        except Exception as e:
            logger.error(f"Content scraping failed: {str(e)}")
            state.scraping_errors.append(f"Content scraping failed: {str(e)}")
            state.current_step = "error"
            return state


class WebContentAnalyzerAgent:
    """
    Agent responsible for analyzing scraped content.

    Analyzes the quality, relevance, and key information from scraped content.
    """

    def __init__(self, llm: BaseChatModel):
        self.llm = llm

    async def analyze_content(self, state: WebSearchState) -> WebSearchState:
        """Analyze all scraped content for relevance and quality."""

        try:
            successful_content = state.get_successful_content()

            if not successful_content:
                state.content_analysis = "No successful content to analyze"
                state.quality_score = 0.0
                state.current_step = "synthesis"
                return state

            # Create analysis prompt
            analysis_prompt = self._create_analysis_prompt(
                state.research_question, successful_content
            )

            # Get LLM analysis
            messages = [SystemMessage(content=analysis_prompt)]
            response = await self.llm.ainvoke(messages)

            # Parse analysis response
            analysis_result = self._parse_analysis_response(response.content)

            # Update state
            state.content_analysis = analysis_result["analysis"]
            state.key_findings = analysis_result.get("key_findings", [])
            state.quality_score = analysis_result.get("quality_score", 0.5)
            state.information_gaps = analysis_result.get("information_gaps", [])
            state.current_step = "synthesis"

            # Add message
            state.analyzer_messages.append(AIMessage(content=response.content))

            logger.info(
                f"Content analysis complete. Quality score: {state.quality_score}"
            )
            return state

        except Exception as e:
            logger.error(f"Content analysis failed: {str(e)}")
            state.content_analysis = f"Analysis failed: {str(e)}"
            state.quality_score = 0.0
            state.current_step = "synthesis"
            return state

    def _create_analysis_prompt(
        self, question: str, content_list: List[ScrapedWebContent]
    ) -> str:
        """Create the content analysis prompt."""

        content_summary = "\n\n".join(
            [
                f"SOURCE: {content.title} ({content.url})\nCONTENT: {content.content[:500]}..."
                for content in content_list[:5]  # Limit to top 5 sources
            ]
        )

        prompt = f"""You are a research content analyst. Analyze the following web content for relevance to the research question.

RESEARCH QUESTION: {question}

SCRAPED CONTENT:
{content_summary}

Provide analysis in JSON format:
{{
    "analysis": "Overall analysis of content quality and relevance",
    "key_findings": ["finding1", "finding2", "finding3"],
    "quality_score": 0.85,
    "information_gaps": ["gap1", "gap2"],
    "bias_indicators": ["bias1", "bias2"],
    "source_reliability": "assessment of source reliability"
}}

Focus on:
1. How well the content answers the research question
2. Quality and credibility of the sources
3. Key insights and findings
4. What information is missing
5. Any potential bias in the sources"""

        return prompt

    def _parse_analysis_response(self, response: str) -> Dict[str, Any]:
        """Parse the LLM analysis response."""

        try:
            # Extract JSON from response
            start_idx = response.find("{")
            end_idx = response.rfind("}") + 1

            if start_idx >= 0 and end_idx > start_idx:
                json_str = response[start_idx:end_idx]
                return json.loads(json_str)
            else:
                raise ValueError("No valid JSON found")

        except Exception as e:
            logger.warning(f"Failed to parse analysis response: {str(e)}")
            return {
                "analysis": response[:500] + "..." if len(response) > 500 else response,
                "key_findings": [],
                "quality_score": 0.5,
                "information_gaps": [],
                "bias_indicators": [],
                "source_reliability": "unknown",
            }


class WebResearchSynthesizerAgent:
    """
    Agent responsible for synthesizing research findings.

    Creates comprehensive research summaries from analyzed content.
    """

    def __init__(self, llm: BaseChatModel):
        self.llm = llm

    async def synthesize_research(self, state: WebSearchState) -> WebSearchState:
        """Create final research synthesis."""

        try:
            successful_content = state.get_successful_content()

            if not successful_content:
                state.research_summary = "No content available for synthesis"
                state.current_step = "complete"
                return state

            # Create synthesis prompt
            synthesis_prompt = self._create_synthesis_prompt(
                state.research_question,
                successful_content,
                state.key_findings,
                state.content_analysis,
            )

            # Get LLM synthesis
            messages = [SystemMessage(content=synthesis_prompt)]
            response = await self.llm.ainvoke(messages)

            # Update state
            state.research_summary = response.content
            state.sources_cited = [content.url for content in successful_content]
            state.current_step = "complete"
            state.validation_status = "completed"

            # Add message
            state.synthesizer_messages.append(AIMessage(content=response.content))

            logger.info("Research synthesis complete")
            return state

        except Exception as e:
            logger.error(f"Research synthesis failed: {str(e)}")
            state.research_summary = f"Synthesis failed: {str(e)}"
            state.current_step = "error"
            return state

    def _create_synthesis_prompt(
        self,
        question: str,
        content_list: List[ScrapedWebContent],
        key_findings: List[str],
        analysis: Optional[str],
    ) -> str:
        """Create the research synthesis prompt."""

        sources_info = "\n".join(
            [
                f"- {content.title} ({content.url}): {content.content[:200]}..."
                for content in content_list[:5]
            ]
        )

        findings_text = (
            "\n".join([f"- {finding}" for finding in key_findings])
            if key_findings
            else "None identified"
        )

        prompt = f"""You are a research synthesis expert. Create a comprehensive research summary based on the analyzed web content.

RESEARCH QUESTION: {question}

KEY FINDINGS:
{findings_text}

CONTENT ANALYSIS:
{analysis or "No detailed analysis available"}

SOURCES ANALYZED:
{sources_info}

Create a comprehensive research summary that:
1. Directly addresses the research question
2. Synthesizes information from multiple sources
3. Highlights key findings and insights
4. Notes any limitations or gaps in the research
5. Provides a clear, well-structured response

Format as a professional research summary suitable for decision-making."""

        return prompt


class WebSearchWorkflowController:
    """
    Controller for orchestrating the complete web search workflow.

    Manages the flow between agents and handles error recovery.
    """

    def __init__(
        self,
        llm: BaseChatModel,
        search_tool: WebSearchTool,
        scraping_tool: WebScrapingTool,
        prompt_system: Optional[PromptRegistry] = None,
    ):
        self.planner = WebSearchPlannerAgent(llm, prompt_system)
        self.searcher = WebSearchExecutorAgent(search_tool)
        self.scraper = WebContentScrapingAgent(scraping_tool)
        self.analyzer = WebContentAnalyzerAgent(llm)
        self.synthesizer = WebResearchSynthesizerAgent(llm)

    async def execute_workflow(self, state: WebSearchState) -> WebSearchState:
        """Execute the complete web search workflow."""

        try:
            logger.info(f"Starting web search workflow for: {state.research_question}")

            # Step 1: Planning
            if state.current_step == "planning":
                state = await self.planner.plan_research(state)

            # Step 2: Searching
            if state.current_step == "searching":
                state = await self.searcher.execute_searches(state)

            # Step 3: Scraping
            if state.current_step == "scraping":
                state = await self.scraper.scrape_content(state)

            # Step 4: Analysis
            if state.current_step == "analysis":
                state = await self.analyzer.analyze_content(state)

            # Step 5: Synthesis
            if state.current_step == "synthesis":
                state = await self.synthesizer.synthesize_research(state)

            logger.info(f"Workflow completed with status: {state.current_step}")
            return state

        except Exception as e:
            logger.error(f"Workflow execution failed: {str(e)}")
            state.current_step = "error"
            state.research_summary = f"Workflow failed: {str(e)}"
            return state
