"""
Reasoning Node

Python translation of app/api/coaching/lib/agents/reasoning-node.ts
Analyzes the user request and provides structured thought process.
"""

import json
import logging
from typing import Any, Dict, List, Optional

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.states import AgentState

logger = logging.getLogger(__name__)


def _get_str_content(content: Any) -> str:
    """Safely convert message content to a string, handling multimodal cases."""
    if isinstance(content, str):
        return content
    if isinstance(content, list):
        text_parts = []
        for part in content:
            if isinstance(part, str):
                text_parts.append(part)
            elif isinstance(part, dict) and part.get("type") == "text":
                text_parts.append(part.get("text", ""))
        return "\n".join(text_parts)
    return ""


async def reasoning_node(
    state: AgentState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Reasoning node that analyzes the user request and provides structured thought process.

    Args:
        state: The current state of the graph
        config: Optional configuration including user profile and current plan

    Returns:
        Partial graph state with the reasoning message
    """

    node_name = "ReasoningAgent"
    print(f"--- Running {node_name} Node ---")

    # Extract configuration
    config = config or {}
    user_profile = config.get("userProfile") or state.get("user_profile")
    current_plan_info = config.get("currentPlan")
    messages = state.get("messages", [])

    # Handle onboarding state that might not have user_profile in expected format
    if not user_profile and hasattr(state, "user_profile"):
        user_profile = getattr(state, "user_profile", None)

    # For onboarding, user_profile might be None - provide safe fallback
    if user_profile is None:
        user_profile = {
            "note": "User profile not yet available (onboarding in progress)"
        }

    # Better user input extraction - handle both dict and Pydantic model state
    user_query = ""
    latest_user_query = ""

    if isinstance(state, dict):
        # State is a dictionary - use dict access
        user_query = state.get("user_query", "")
        if state.get("messages"):
            # ALWAYS extract the latest human message to ensure we analyze the most recent input
            for msg in reversed(state["messages"]):
                if isinstance(msg, HumanMessage):
                    latest_user_query = _get_str_content(msg.content)
                    break
    else:
        # State is a Pydantic model - use attribute access
        user_query = (
            getattr(state, "user_query", "") if hasattr(state, "user_query") else ""
        )
        if hasattr(state, "messages") and state.messages:
            # ALWAYS extract the latest human message to ensure we analyze the most recent input
            for msg in reversed(state.messages):
                if isinstance(msg, HumanMessage):
                    latest_user_query = _get_str_content(msg.content)
                    break

    # Use the latest user query if found, otherwise fall back to state user_query
    final_user_query = latest_user_query if latest_user_query else user_query

    # Log the query being analyzed for debugging
    logger.info(f"🧠 REASONING_NODE: Analyzing query: '{final_user_query}'")
    logger.info(f"📊 REASONING_NODE: Original user_query from state: '{user_query}'")
    logger.info(
        f"🔍 REASONING_NODE: Latest message from history: '{latest_user_query}'"
    )

    # Fallback to "Hi" if still empty (common Studio default)
    if not final_user_query:
        final_user_query = "Hi"

    # Create the reasoning LLM with final_response tag for streaming
    reasoning_llm = create_azure_chat_openai(
        temperature=0.7, max_tokens=4000, streaming=True
    ).with_config({"tags": ["final_response", "reasoning", "analysis"]})

    # Improved system prompt with better structure
    reasoning_prompt_template = """You are an analytical assistant for a fitness coaching application. Your task is to analyze the user's current request, provide structured reasoning about the situation, and identify any physiological or mental aspects mentioned.

User Profile:
{user_profile_info}

Current Plan Context:
{current_plan_info}

Conversation History:
{message_history}

CURRENT USER INPUT TO ANALYZE:
"{latest_user_message_content}"

Analyze the current user input above within the context of the history and profile/plan. Generate a well-structured analysis using proper markdown formatting.

Include the following sections:

## Request Summary
- Summarize what the user is asking for in their current message
- If the input is unclear or minimal (like "Hi" or "N/A"), note this explicitly

## Clarity Assessment
- Is the request clear or ambiguous?
- What specific information is provided vs. what might be missing?

## Domain Analysis
- What fitness or wellness domains does this request relate to? (strength, cardio, nutrition, recovery, mental, etc.)
- *If applicable*: What specific physiological body parts or mental aspects are referenced? (e.g., knees, back, motivation, stress)

## Context Relevance
- How does this request relate to the user's profile or current plan?
- Any relevant historical context from previous messages?

## Next Steps Analysis
- What would be the most helpful response approach?
- Is clarification needed before proceeding?

Structure your response using proper markdown formatting with clear sections and bullet points. Be concise but thorough."""

    # Prepare template variables
    user_profile_info = (
        json.dumps(user_profile, indent=2) if user_profile else "Not Available"
    )
    current_plan_info_str = (
        json.dumps(current_plan_info, indent=2)
        if current_plan_info
        else "Not Available"
    )

    # Prepare history string with proper formatting
    history_string = ""
    if messages:
        history_parts = []
        for msg in messages:
            msg_type = (
                "User"
                if msg.__class__.__name__ == "HumanMessage"
                else (getattr(msg, "name", None) or "Assistant")
            )
            content = (
                str(msg.content) if hasattr(msg, "content") else "[non-string content]"
            )
            history_parts.append(f"{msg_type}: {content}")
        history_string = "\n".join(history_parts)
    else:
        history_string = "No history yet."

    # Use the latest user query if found, otherwise fall back to state user_query
    latest_user_content = final_user_query if final_user_query else "N/A"

    # Fill in the template
    prompt_input = reasoning_prompt_template.format(
        user_profile_info=user_profile_info,
        current_plan_info=current_plan_info_str,
        message_history=history_string,
        latest_user_message_content=latest_user_content,
    )

    llm_messages = [SystemMessage(content=prompt_input)]
    reasoning_text = "Analyzing request..."  # Default fallback

    try:
        print(f"  > Calling {node_name} LLM with streaming...")
        reasoning_text = ""
        async for chunk in reasoning_llm.astream(llm_messages):
            if hasattr(chunk, "content") and isinstance(chunk.content, str):
                reasoning_text += chunk.content
                # Individual chunks will be automatically handled by the graph streaming

        # Safely handle the response content
        if reasoning_text.strip():
            print(f"  > {node_name} LLM Response Summary: {reasoning_text[:150]}...")
        else:
            print(f"  > {node_name} LLM returned empty content.")
            reasoning_text = "Analysis completed."

    except Exception as error:
        print(f"Error during {node_name} LLM call: {error}")
        reasoning_text = "Error during analysis."

    # Create the reasoning message with domain metadata
    reasoning_message = AIMessage(content=reasoning_text, name=node_name)

    print(f"  > {node_name} completed analysis")

    # Return updated state with the reasoning message
    messages = state.get("messages", [])
    messages.append(reasoning_message)
    state["messages"] = messages

    def _detect_safety_concerns(query: str) -> Optional[Dict]:
        """Detect pain, injury, or safety-related keywords."""
        query_lower = query.lower()
        safety_keywords = {
            "pain": ["hurt", "pain", "ache", "sore", "discomfort", "tweak"],
            "injury": ["injury", "injured", "strain", "sprain", "tear", "rehab"],
            "medical": [
                "surgery",
                "doctor",
                "diagnosis",
                "condition",
                "physical therapist",
            ],
        }

        concerns = {}
        for category, keywords in safety_keywords.items():
            found_keywords = [kw for kw in keywords if kw in query_lower]
            if found_keywords:
                concerns[category] = found_keywords

        return concerns if concerns else None

    safety_concerns = _detect_safety_concerns(final_user_query)

    if safety_concerns:
        logger.warning(f"Safety concern detected: {safety_concerns}")
        state["safety_alert"] = True
        state["safety_details"] = safety_concerns
        state["routing_priority"] = "safety_first"
    else:
        state["safety_alert"] = False

    return {
        "messages": state["messages"],
        "safety_alert": state.get("safety_alert"),
        "safety_details": state.get("safety_details"),
        "routing_priority": state.get("routing_priority"),
    }


def extract_markdown_sections(markdown_text: str) -> Dict[str, str]:
    """
    Extract markdown sections from the reasoning text for telemetry.

    Args:
        markdown_text: The markdown formatted text

    Returns:
        Dictionary mapping section names to their content
    """
    sections = {}
    lines = markdown_text.split("\n")
    current_section = None
    current_content = []

    for line in lines:
        if line.startswith("## "):
            # Save previous section if exists
            if current_section and current_content:
                sections[current_section] = "\n".join(current_content).strip()

            # Start new section
            current_section = line[3:].strip()
            current_content = []
        elif current_section:
            current_content.append(line)

    # Save last section
    if current_section and current_content:
        sections[current_section] = "\n".join(current_content).strip()

    return sections


def _should_trigger_research(self, query_analysis: Dict[str, Any]) -> bool:
    """Determine if external research should be triggered for this query."""

    # Research triggers
    research_indicators = [
        # Evidence-based requests
        "research",
        "study",
        "studies",
        "evidence",
        "proven",
        "scientific",
        # Best practices
        "best",
        "optimal",
        "most effective",
        "recommended",
        "guidelines",
        # Current/latest information
        "latest",
        "current",
        "new",
        "recent",
        "2024",
        "2023",
        # Recovery specific
        "recovery tips",
        "how to recover",
        "recovery methods",
        "post-workout",
    ]

    query_text = query_analysis.get("original_query", "").lower()

    # Check for research indicators
    has_research_indicators = any(
        indicator in query_text for indicator in research_indicators
    )

    # Check for knowledge-seeking vs action-seeking
    knowledge_patterns = ["what are", "how to", "why", "tips", "benefits", "effects"]
    is_knowledge_seeking = any(pattern in query_text for pattern in knowledge_patterns)

    # Don't research for immediate action requests
    immediate_action = ["create", "generate", "plan my", "schedule", "book"]
    is_immediate_action = any(action in query_text for action in immediate_action)

    return (has_research_indicators or is_knowledge_seeking) and not is_immediate_action


def _create_research_plan(
    self, domain: str, query_analysis: Dict[str, Any]
) -> Dict[str, Any]:
    """Create a research plan for multi-source data gathering."""

    query = query_analysis.get("original_query", "")

    return {
        "sources": ["graphrag", "azure_search", "web_search"],
        "graphrag_query": f"{domain} protocols and evidence for: {query}",
        "azure_search_query": f"{domain} research {query}",
        "web_search_query": f"latest {domain} research {query} 2024",
        "research_priority": "high" if "evidence" in query.lower() else "medium",
    }
