"""
Planning Node

This node creates execution plans for user requests, determining which specialists to consult.
"""

import json
import logging
from typing import Any, Dict, List, Optional

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.tools import tool
from pydantic import BaseModel, Field

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.states import AgentState

logger = logging.getLogger(__name__)


class PlanSchema(BaseModel):
    """Schema for the execution plan."""

    steps: List[str] = Field(
        description="REQUIRED: An array with exactly ONE coach name. Must be one of: 'strength_coach', 'running_coach', 'cardio_coach', 'cycling_coach', 'nutrition_coach', 'recovery_coach', 'mental_coach', or 'clarification'. Example: ['cycling_coach'] for cycling questions."
    )
    confidence: float = Field(
        default=0.5,
        description="Confidence level (0.0-1.0) in the routing decision. High confidence (>0.8) enables fast path routing.",
    )
    direct_route: bool = Field(
        default=False,
        description="Whether this plan should use direct routing (bypass intermediate coordination steps)",
    )


# List of valid agent node names - REMOVED head_coach to prevent loops
AGENT_NODE_NAMES = [
    "strength_coach",
    "running_coach",
    "cardio_coach",
    "cycling_coach",
    "nutrition_coach",
    "recovery_coach",
    "mental_coach",
    "planning",
    "reasoning",
    "clarification",
]


async def planning_node(
    state: AgentState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Planning node for the coaching graph.
    Analyzes user request and determines which specialists should handle it.

    Args:
        state: The current state of the graph
        config: Optional configuration including user profile and current plan

    Returns:
        Partial graph state with plan and current_step
    """

    print("--- Running Planning Node --- (Plan Only Version)")

    # Extract configuration
    config = config or {}
    user_profile = config.get("userProfile") or state.get("user_profile")
    current_plan_info = config.get("currentPlan")
    messages = state.get("messages", [])

    # Handle onboarding state that might not have user_profile in expected format
    if not user_profile and hasattr(state, "user_profile"):
        user_profile = getattr(state, "user_profile", None)

    # For onboarding, user_profile might be None - provide safe fallback
    if user_profile is None:
        user_profile = {
            "note": "User profile not yet available (onboarding in progress)"
        }

    latest_message = messages[-1] if messages else None

    # Create the planning LLM
    planning_llm = create_azure_chat_openai(
        temperature=0.7, max_tokens=4000, streaming=True
    )

    # Define the tool for setting execution plan
    @tool("set_execution_plan", args_schema=PlanSchema)
    def set_execution_plan(
        steps: List[str], confidence: float = 0.5, direct_route: bool = False
    ) -> Dict[str, Any]:
        """Sets the execution plan based on the user request analysis."""
        return {"steps": steps, "confidence": confidence, "direct_route": direct_route}

    # Bind the tool to the LLM
    planning_llm_with_tool = planning_llm.bind_tools([set_execution_plan])

    # Revised prompt - More decisive routing with explicit cycling keywords and MANDATORY tool usage
    planning_prompt_template = """You are the Planning Agent for a fitness coaching system. Your role is to analyze the user's request and determine which specialist coach should handle it.

MANDATORY: You MUST use the set_execution_plan tool to provide your answer. Do not provide a text response.

User Profile:
{user_profile_info}

Current Plan Context:
{current_plan_info}

Conversation History (excluding last message):
{message_history}

User's Latest Request: {latest_user_message}

Available Specialist Coaches:
- strength_coach: Strength training, weightlifting, muscle building, powerlifting, bodybuilding, exercise form, resistance training
- running_coach: Running, jogging, marathons, 5K, 10K, running form, pace training
- cardio_coach: General cardio, HIIT, interval training, cardiovascular fitness
- cycling_coach: CYCLING, BIKE training, bicycle workouts, cycling routes, bike setup, cycling performance, bike fitness
- nutrition_coach: Diet, nutrition, meal planning, weight loss/gain, supplements
- recovery_coach: Sleep, rest, injury prevention, stretching, mobility
- mental_coach: Motivation, goal setting, mental performance, confidence

ROUTING RULES (FOLLOW EXACTLY):
1. **CYCLING/BIKE/BICYCLE keywords** → cycling_coach
2. **STRENGTH/MUSCLE/WEIGHTS/LIFTING** → strength_coach
3. **RUNNING/JOGGING/RACES** → running_coach  
4. **CARDIO/HIIT/HEART RATE** → cardio_coach
5. **NUTRITION/DIET/FOOD** → nutrition_coach
6. **RECOVERY/SLEEP/INJURY** → recovery_coach
7. **MOTIVATION/GOALS/MINDSET** → mental_coach
8. **ONLY use clarification** for: greetings, non-fitness topics, or truly unclear requests

COMPLEXITY DETECTION (use LOW confidence=0.5, direct_route=False):
- Words like "complex", "comprehensive", "multi-domain", "complete program", "full plan", "everything", "overall"
- Requests that span multiple coaches or domains
- Vague requests without specific domain focus

CRITICAL: Check for complexity keywords FIRST:
- "comprehensive" = complex → confidence=0.5, direct_route=False
- "complete program" = complex → confidence=0.5, direct_route=False  
- "design a plan" = complex → confidence=0.5, direct_route=False
- "everything" = complex → confidence=0.5, direct_route=False

CONFIDENCE AND FAST PATH RULES:
- Set confidence=0.9 and direct_route=True for clear, single-domain requests
- Set confidence=0.7 and direct_route=False for moderately clear requests  
- Set confidence=0.5 and direct_route=False for unclear or complex requests

CYCLING EXAMPLES (MUST route to cycling_coach with HIGH confidence):
- "How can I improve my cycling training?" → set_execution_plan(["cycling_coach"], confidence=0.9, direct_route=True)
- "I want to start cycling" → set_execution_plan(["cycling_coach"], confidence=0.9, direct_route=True)
- "Help me with bike training" → set_execution_plan(["cycling_coach"], confidence=0.9, direct_route=True)
- "What's the best cycling workout?" → set_execution_plan(["cycling_coach"], confidence=0.9, direct_route=True)
- "How do I cycle better?" → set_execution_plan(["cycling_coach"], confidence=0.9, direct_route=True)
- "Bike setup advice" → set_execution_plan(["cycling_coach"], confidence=0.9, direct_route=True)
- "Cycling routes" → set_execution_plan(["cycling_coach"], confidence=0.9, direct_route=True)

STRENGTH EXAMPLES (route to strength_coach with HIGH confidence):
- "I want to build muscle" → set_execution_plan(["strength_coach"], confidence=0.9, direct_route=True)
- "Help with strength training" → set_execution_plan(["strength_coach"], confidence=0.9, direct_route=True)
- "Create a workout program" → set_execution_plan(["strength_coach"], confidence=0.8, direct_route=True)
- "Weightlifting advice" → set_execution_plan(["strength_coach"], confidence=0.9, direct_route=True)

COMPLEX/MULTI-DOMAIN EXAMPLES (use LOW confidence, NO direct route):
- "Create a complex multi-domain program" → set_execution_plan(["strength_coach"], confidence=0.5, direct_route=False)
- "Design a comprehensive fitness plan" → set_execution_plan(["strength_coach"], confidence=0.5, direct_route=False)
- "I need help with everything" → set_execution_plan(["clarification"], confidence=0.5, direct_route=False)
- "Create a complete training program" → set_execution_plan(["strength_coach"], confidence=0.6, direct_route=False)

Other Examples:
- "I need running advice" → set_execution_plan(["running_coach"], confidence=0.9, direct_route=True)
- "Help with my diet" → set_execution_plan(["nutrition_coach"], confidence=0.9, direct_route=True)
- "Hello" → set_execution_plan(["clarification"], confidence=0.8, direct_route=True)


REQUIRED ACTION: You MUST call the set_execution_plan tool with the appropriate coach name, confidence level, and direct_route flag. Do not respond with text only."""

    # Prepare template variables
    user_profile_info = (
        json.dumps(user_profile, indent=2) if user_profile else "Not Available"
    )
    current_plan_info_str = (
        json.dumps(current_plan_info, indent=2)
        if current_plan_info
        else "Not Available"
    )

    # Prepare message history (excluding last message)
    message_history = ""
    if len(messages) > 1:
        history_parts = []
        for msg in messages[:-1]:  # Exclude last message
            msg_type = (
                "User"
                if msg.__class__.__name__ == "HumanMessage"
                else (getattr(msg, "name", None) or "Assistant")
            )
            content = (
                str(msg.content)[:150] + "..."
                if hasattr(msg, "content")
                else "[non-string content]"
            )
            history_parts.append(f"{msg_type}: {content}")
        message_history = "\n".join(history_parts)
    else:
        message_history = "No previous history"

    latest_user_message = (
        str(latest_message.content)
        if latest_message and hasattr(latest_message, "content")
        else "No latest message"
    )

    # Fill in the template
    prompt_input = planning_prompt_template.format(
        user_profile_info=user_profile_info,
        current_plan_info=current_plan_info_str,
        message_history=message_history,
        latest_user_message=latest_user_message,
    )

    llm_messages = [SystemMessage(content=prompt_input)]
    parsed_plan = ["head_coach"]  # Default fallback

    try:
        print("  > Calling Planning LLM with tool and streaming...")

        # Use ainvoke instead of astream for reliable tool call handling
        # Streaming with tool calls can be problematic
        response = await planning_llm_with_tool.ainvoke(llm_messages)

        # Initialize defaults
        parsed_plan = ["head_coach"]  # Default fallback
        confidence = 0.5
        direct_route = False

        # Check if the response has tool calls
        if hasattr(response, "tool_calls") and response.tool_calls:
            tool_call = response.tool_calls[0]
            if tool_call.get("name") == "set_execution_plan":
                args = tool_call.get("args", {})
                steps = args.get("steps", [])
                confidence = args.get("confidence", 0.5)
                direct_route = args.get("direct_route", False)

                # Validate that all steps are valid node names
                valid_steps = [
                    step
                    for step in steps
                    if step in AGENT_NODE_NAMES or step == "clarification"
                ]

                if valid_steps:
                    parsed_plan = valid_steps
                    print(f"  > Planning LLM returned valid plan: {parsed_plan}")
                    print(f"  > Confidence: {confidence}, Direct route: {direct_route}")
                else:
                    print(
                        f"  > Planning LLM returned invalid steps: {steps}. Using fallback."
                    )
                    confidence = 0.3  # Low confidence for fallback
                    direct_route = False
            else:
                print(
                    f"  > Planning LLM called wrong tool: {tool_call.get('name')}. Using fallback."
                )
        else:
            print("  > Planning LLM did not call the tool. Using fallback.")
            # Debug: show what we got instead
            if hasattr(response, "content") and response.content:
                print(f"  > Response content: {response.content[:200]}...")

    except Exception as error:
        print(f"Error during Planning LLM call: {error}")
        parsed_plan = ["head_coach"]  # Fallback on error
        confidence = 0.3
        direct_route = False

    print(f"  > Final execution plan: {parsed_plan}")
    print(f"  > Confidence: {confidence}, Direct route: {direct_route}")

    # Return the plan with confidence and routing metadata
    return {
        "plan": parsed_plan,
        "current_step": 0,
        "plan_confidence": confidence,
        "use_direct_route": direct_route,
    }
