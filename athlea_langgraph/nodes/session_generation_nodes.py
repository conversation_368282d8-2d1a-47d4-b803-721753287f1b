"""
Session Generation Nodes

Graph nodes that integrate the intelligent session generation system
with the existing onboarding flow.
"""

import logging
from datetime import datetime
from typing import Any, Dict

from ..states.onboarding_state import OnboardingState
from ..engines.session_generation_engine import SessionGenerationEngine
from ..engines.session_state_models import (
    create_fitness_profile_from_onboarding,
    update_sidebar_with_session_data,
)

logger = logging.getLogger(__name__)


async def session_generation_node(state: OnboardingState) -> OnboardingState:
    """Generate first week of sessions after plan creation"""

    logger.info("[SessionGenNode] Starting session generation")

    try:
        # Check if we have a generated plan
        plan_details = state.get("generated_plan")
        if not plan_details:
            logger.error("[SessionGenNode] No generated plan found in state")
            state["error"] = (
                "No training plan found. Please complete plan generation first."
            )
            return state

        user_id = state["user_id"]

        # Create initial fitness profile from onboarding data
        if not state.get("fitness_profile"):
            fitness_profile = create_fitness_profile_from_onboarding(state)
            state["fitness_profile"] = fitness_profile
            logger.info("[SessionGenNode] Created fitness profile from onboarding data")
        else:
            fitness_profile = state["fitness_profile"]

        # Initialize session generation engine
        session_engine = SessionGenerationEngine()

        # Generate full intelligent weekly plan using the complete system
        try:
            weekly_plan = await session_engine.generate_weekly_sessions(
                user_profile=fitness_profile,
                plan_details=plan_details,
                week_number=1,
                previous_performance=[],  # No previous performance for first week
            )

            logger.info("[SessionGenNode] Generated full intelligent weekly plan")

            # Convert WeeklyPlan object to dict for state storage
            weekly_plan_dict = {
                "week_number": weekly_plan.week_number,
                "start_date": weekly_plan.start_date.isoformat(),
                "end_date": weekly_plan.end_date.isoformat(),
                "weekly_focus": weekly_plan.weekly_focus,
                "sessions": {
                    day_key: [
                        {
                            "session_id": session.session_id,
                            "domain": session.domain,
                            "session_type": session.session_type,
                            "duration": session.duration_minutes,
                            "intensity": session.intensity_level,
                            "difficulty": session.difficulty_level,
                            "coach": session.generating_coach,
                            "rationale": session.coach_rationale,
                            "benefits": session.expected_benefits,
                            "equipment": session.equipment_needed,
                            "adaptation_focus": session.adaptation_focus,
                        }
                        for session in sessions
                    ]
                    for day_key, sessions in weekly_plan.sessions.items()
                },
                "total_sessions": sum(
                    len(sessions) for sessions in weekly_plan.sessions.values()
                ),
                "weekly_volume": weekly_plan.weekly_volume,
                "rationale": weekly_plan.rationale,
                "coach_coordination_notes": weekly_plan.coach_coordination_notes,
                "adaptation_notes": weekly_plan.adaptation_notes,
                "generated_at": session_engine._get_current_timestamp(),
                "generation_method": "intelligent_coordination",
            }

        except Exception as e:
            logger.warning(
                f"[SessionGenNode] Full system failed, falling back to basic generation: {e}"
            )
            # Fallback to basic week generation
            weekly_plan_dict = await session_engine.generate_basic_week(
                user_id, week_number=1
            )
            weekly_plan_dict["generation_method"] = "basic_fallback"

        # Store the generated weekly plan
        if "generated_weekly_plans" not in state:
            state["generated_weekly_plans"] = {}

        state["generated_weekly_plans"][1] = weekly_plan_dict
        state["current_week"] = 1
        state["current_day"] = 1
        state["onboarding_stage"] = "sessions_generated"

        # Update sidebar data with session information
        if state.get("sidebar_data"):
            # Convert our weekly plan to match sidebar format
            sidebar_update = {
                "weekly_plan": {
                    "week_number": weekly_plan_dict["week_number"],
                    "total_sessions": weekly_plan_dict["total_sessions"],
                    "weekly_focus": weekly_plan_dict["weekly_focus"],
                    "total_volume_minutes": sum(
                        weekly_plan_dict["weekly_volume"].values()
                    ),
                    "sessions_by_day": weekly_plan_dict["sessions"],
                    "generation_method": weekly_plan_dict["generation_method"],
                    "coach_coordination": weekly_plan_dict.get(
                        "coach_coordination_notes", ""
                    ),
                }
            }

            # Add to existing sidebar data
            current_sidebar = state["sidebar_data"]
            setattr(current_sidebar, "weekly_plan", sidebar_update["weekly_plan"])
            current_sidebar.current_stage = "sessions_ready"

        # Create informative message based on generation method
        if weekly_plan_dict["generation_method"] == "intelligent_coordination":
            session_details = (
                f"Your week includes {weekly_plan_dict['total_sessions']} sessions "
                f"coordinated across {len(weekly_plan_dict['weekly_volume'])} training domains, "
                f"focusing on {weekly_plan_dict['weekly_focus'].lower()}."
            )
        else:
            session_details = (
                f"Your week includes {weekly_plan_dict['total_sessions']} sessions "
                f"focusing on {weekly_plan_dict['weekly_focus'].lower()}."
            )

        # Add informative message
        state["messages"] = state.get("messages", []) + [
            {
                "type": "ai",
                "content": f"Great! I've generated your first week of training sessions. "
                f"{session_details} You can view your detailed schedule "
                f"and start training whenever you're ready!",
            }
        ]

        # Set completion flags
        state["has_enough_info"] = True
        state["needs_input"] = False
        state["requires_input"] = False

        logger.info(
            f"[SessionGenNode] Generated weekly plan with {weekly_plan_dict['total_sessions']} sessions using {weekly_plan_dict['generation_method']}"
        )

        return state

    except Exception as e:
        logger.error(f"[SessionGenNode] Error generating sessions: {e}")
        state["error"] = f"Session generation failed: {str(e)}"
        state["onboarding_stage"] = "error"
        return state


async def coach_coordination_node(state: OnboardingState) -> OnboardingState:
    """Coordinate between different domain coaches (placeholder for now)"""

    logger.info("[CoachCoordNode] Starting coach coordination")

    try:
        # For now, this is a simple coordination check
        weekly_plans = state.get("generated_weekly_plans", {})

        if not weekly_plans:
            logger.warning("[CoachCoordNode] No weekly plans found for coordination")
            return state

        current_week = state.get("current_week", 1)
        current_plan = weekly_plans.get(current_week)

        if current_plan:
            # Add coordination notes
            coordination_notes = {
                "strength_coach": f"Generated {current_plan['weekly_volume'].get('strength', 0)} minutes of strength training",
                "running_coach": f"Generated {current_plan['weekly_volume'].get('running', 0)} minutes of running",
                "coordination_summary": "Coaches coordinated to avoid conflicts and optimize training distribution",
            }

            # Store coordination information
            if "coach_coordination" not in state:
                state["coach_coordination"] = {}

            state["coach_coordination"][current_week] = coordination_notes

            logger.info("[CoachCoordNode] Coach coordination completed")

        return state

    except Exception as e:
        logger.error(f"[CoachCoordNode] Error in coach coordination: {e}")
        return state


async def user_control_node(state: OnboardingState) -> OnboardingState:
    """Handle user control commands (pause, adjust, feedback)"""

    logger.info("[UserControlNode] Processing user control input")

    try:
        user_input = state.get("user_input", "").lower()

        # Initialize user control state if not exists
        if "user_control_state" not in state:
            state["user_control_state"] = {
                "is_paused": False,
                "pause_reason": None,
                "user_adjustments": [],
            }

        user_control = state["user_control_state"]

        # Handle pause commands
        if "pause" in user_input:
            user_control["is_paused"] = True
            user_control["pause_reason"] = user_input
            user_control["paused_at"] = datetime.now().isoformat()

            state["needs_input"] = True
            state["input_prompt"] = (
                "Your training has been paused. Type 'resume' when you're ready to continue, or let me know if you'd like to make any adjustments."
            )

            # Add response message
            state["messages"] = state.get("messages", []) + [
                {
                    "type": "ai",
                    "content": "I've paused your training plan. You can resume anytime by saying 'resume' or let me know if you'd like to make any changes to your sessions.",
                }
            ]

            logger.info("[UserControlNode] Training paused by user")

        # Handle resume commands
        elif "resume" in user_input and user_control.get("is_paused"):
            user_control["is_paused"] = False
            user_control["pause_reason"] = None

            state["needs_input"] = False
            state["input_prompt"] = None

            # Add response message
            state["messages"] = state.get("messages", []) + [
                {
                    "type": "ai",
                    "content": "Welcome back! Your training plan has been resumed. You can continue with your scheduled sessions.",
                }
            ]

            logger.info("[UserControlNode] Training resumed by user")

        # Handle adjustment requests
        elif any(
            word in user_input
            for word in ["adjust", "change", "modify", "easier", "harder"]
        ):
            adjustment = {
                "request": user_input,
                "timestamp": datetime.now().isoformat(),
                "status": "pending",
            }

            if "user_adjustments" not in user_control:
                user_control["user_adjustments"] = []

            user_control["user_adjustments"].append(adjustment)

            # Add response message
            state["messages"] = state.get("messages", []) + [
                {
                    "type": "ai",
                    "content": "I've noted your adjustment request. I'll modify your upcoming sessions accordingly. The changes will be reflected in your next training sessions.",
                }
            ]

            logger.info(f"[UserControlNode] User adjustment requested: {user_input}")

        return state

    except Exception as e:
        logger.error(f"[UserControlNode] Error processing user control: {e}")
        return state


async def session_adaptation_node(state: OnboardingState) -> OnboardingState:
    """Adapt sessions based on user feedback and performance (placeholder)"""

    logger.info("[SessionAdaptNode] Processing session adaptations")

    try:
        # Check for feedback history
        feedback_history = state.get("feedback_history", [])

        if not feedback_history:
            logger.info(
                "[SessionAdaptNode] No feedback history found, skipping adaptation"
            )
            return state

        # For now, simple adaptation logic
        recent_feedback = feedback_history[-3:]  # Last 3 sessions

        if recent_feedback:
            # Analyze feedback patterns
            avg_difficulty = sum(
                f.get("difficulty_rating", 5) for f in recent_feedback
            ) / len(recent_feedback)
            completion_rate = sum(
                1 for f in recent_feedback if f.get("completed", False)
            ) / len(recent_feedback)

            adaptations_made = []

            # Adapt based on difficulty ratings
            if avg_difficulty > 8:  # Too hard
                adaptations_made.append(
                    "Reduced intensity due to high difficulty ratings"
                )

                # Update user preferences
                if "adaptation_data" not in state:
                    state["adaptation_data"] = {}

                state["adaptation_data"][
                    "intensity_adjustment"
                ] = 0.9  # Reduce intensity by 10%

            elif avg_difficulty < 3:  # Too easy
                adaptations_made.append(
                    "Increased intensity due to low difficulty ratings"
                )

                if "adaptation_data" not in state:
                    state["adaptation_data"] = {}

                state["adaptation_data"][
                    "intensity_adjustment"
                ] = 1.1  # Increase intensity by 10%

            # Adapt based on completion rate
            if completion_rate < 0.7:  # Less than 70% completion
                adaptations_made.append(
                    "Adjusted session frequency due to low completion rate"
                )

            # Log adaptations
            if adaptations_made:
                state["adaptation_history"] = state.get("adaptation_history", []) + [
                    {
                        "timestamp": datetime.now().isoformat(),
                        "adaptations": adaptations_made,
                        "trigger": "feedback_analysis",
                    }
                ]

                logger.info(
                    f"[SessionAdaptNode] Applied {len(adaptations_made)} adaptations"
                )

        return state

    except Exception as e:
        logger.error(f"[SessionAdaptNode] Error in session adaptation: {e}")
        return state


# Routing function for session flow
def session_flow_router(state: OnboardingState) -> str:
    """Route the session flow based on current state"""

    # Check if user control is needed
    user_control = state.get("user_control_state", {})
    if user_control.get("is_paused"):
        return "user_control"

    # Check if adaptations are needed
    feedback_history = state.get("feedback_history", [])
    if len(feedback_history) > 0 and len(feedback_history) % 3 == 0:  # Every 3 sessions
        return "session_adaptation"

    # Normal completion
    return "complete"
