"""
Tools graphs module.

This module contains the combined multi-node graphs for tool workflows:
- Session Generation Graph (3 nodes): structure → content → knowledge
- Maps Workflow Graph (4 nodes): location → route → area → weather
- Web Search Graph (5 nodes): planner → searcher → scraper → analyzer → synthesizer
"""

# Session Generation Graph - 3 nodes
from .session_generation_graph import (
    create_session_generation_graph_sync,
    create_session_generation_graph,
)

# Maps Workflow Graph - 4 nodes
from .maps_workflow_graph import (
    create_maps_workflow_graph_sync,
    create_maps_workflow_graph,
)

# Web Search Graph - 5 nodes
from .web_search_graph import (
    create_web_search_graph_sync,
    create_web_search_graph,
)

__all__ = [
    # Session Generation (3 nodes)
    "create_session_generation_graph_sync",
    "create_session_generation_graph",
    # Maps Workflow (4 nodes)
    "create_maps_workflow_graph_sync",
    "create_maps_workflow_graph",
    # Web Search (5 nodes)
    "create_web_search_graph_sync",
    "create_web_search_graph",
]
