"""
Comprehensive Coaching Graph with ReWOO+Reflection Integration

This graph combines all the advanced features with the new ReWOO+Reflection pattern:
- Complexity Assessment after Planning
- Simple vs Complex Path Routing
- ReWOO-style Planner → Executor → Router for Complex Path
- Centralized Coach Invocations per branch
- Aggregation and Reflection with Reflexion loops
- All specialized coaches with domain-specific tools
- Comprehensive tool integration (Airtable, Google Maps, Azure Maps, Session Generation, Azure Search)
- Fixed tool input validation
- Memory integration
- Human-in-the-loop capabilities
- LangGraph Studio compatibility
"""

import asyncio
import json
import logging
import os
from typing import Any, Dict, List, Optional, Union

# CRITICAL: Load .env file explicitly to ensure LANGGRAPH_DEFAULT_POOL_SIZE is set
from dotenv import load_dotenv

load_dotenv()

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.graph.state import CompiledStateGraph
from langgraph.types import Command, interrupt
from pydantic import BaseModel, Field

from ...nodes.aggregation_node import aggregation_node
from ...nodes.planning_node import planning_node
from ...agents.react_coach_node import ReActCoachExecutor
from ...services.azure_openai_service import create_azure_chat_openai

# Import GraphRAG nodes
from ...agents.graphrag_nodes import (
    knowledge_assessment_node,
    graphrag_retrieval_node,
)

# Import traditional nodes for fallback
from ...agents.reasoning_node import reasoning_node
from ...agents.specialized_coaches import (
    CARDIO_COACH_PROMPT,
    CYCLING_COACH_PROMPT,
    MENTAL_COACH_PROMPT,
    NUTRITION_COACH_PROMPT,
    RECOVERY_COACH_PROMPT,
    STRENGTH_COACH_PROMPT,
    get_tools_manager,
)

# Import state and core components
from ...states.state import AgentState

# Import prompt loader for versioned prompts
from ...utils.prompt_loader import PromptConfig, get_prompt_loader

# Import existing nodes from other modules
from ...agents.head_coach import clarification_node as head_coach_clarification_node
from ...agents.react_coach_executor import create_react_coach_node

# Import the Mem0 memory service
from ...services.mem0_memory_service import get_mem0_service

logger = logging.getLogger(__name__)

# Global caches to prevent loops and repeated loading
_PROMPT_CACHE: Dict[str, Optional[PromptConfig]] = {}
_LLM_CACHE = {}

prompt_loader = None
tools_manager = None
react_coaches: Dict[str, ReActCoachExecutor] = {}
_resources_initialized = False
_initialization_lock = asyncio.Lock()


async def initialize_global_resources(config: "ComprehensiveCoachingConfig"):
    """
    Initializes global resources like prompt loader, tools manager, and ReAct coaches.
    This function is designed to run only once to prevent performance bottlenecks.
    """
    global prompt_loader, tools_manager, react_coaches, _resources_initialized

    async with _initialization_lock:
        if _resources_initialized:
            logger.info("✅ GRAPH: [GLOBAL] Resources already initialized.")
            return

        logger.info(
            "🚀 GRAPH: [GLOBAL] Initializing global resources for the first time..."
        )
        try:
            prompt_loader = get_prompt_loader()
            logger.info("  - Prompt loader created.")

            tools_manager = await get_tools_manager()
            logger.info("  - Tools manager created.")

            # Create the ReAct coach executors which pre-loads prompts and tools
            react_coaches = await create_react_coaches(tools_manager, config)
            logger.info(f"  - {len(react_coaches)} ReAct coaches created and cached.")

            _resources_initialized = True
            logger.info("✅ GRAPH: [GLOBAL] Global resources initialized successfully.")
        except Exception as e:
            logger.error(
                f"❌ GRAPH: [GLOBAL] Failed to initialize global resources: {e}",
                exc_info=True,
            )
            # Reset flag so next request can retry initialization
            _resources_initialized = False
            raise


async def get_cached_prompt_config(
    prompt_name: str, prompt_loader
) -> Optional[PromptConfig]:
    """Get cached prompt config to prevent repeated loading that causes loops."""
    if prompt_name not in _PROMPT_CACHE:
        try:
            prompt_config = await prompt_loader.load_prompt(prompt_name)
            _PROMPT_CACHE[prompt_name] = prompt_config
            logger.info(f"🔄 Cached prompt config: {prompt_name}")
        except Exception as e:
            logger.warning(f"⚠️ Failed to load {prompt_name}, caching None: {e}")
            _PROMPT_CACHE[prompt_name] = None
    return _PROMPT_CACHE[prompt_name]


def get_cached_llm(node_type: str):
    """Get cached LLM to prevent repeated creation."""
    if node_type not in _LLM_CACHE:
        _LLM_CACHE[node_type] = create_azure_chat_openai(node_type=node_type)
        logger.info(f"🔄 Cached LLM: {node_type}")
    return _LLM_CACHE[node_type]


async def generate_clarifying_question(state: "ComprehensiveState") -> str:
    """Dynamically generate a clarifying question based on the conversation."""
    # This is a simplified example. You can make this more sophisticated.
    return "It seems like the query is a bit unclear. Could you please provide more details on what you're looking for?"


class ComprehensiveCoachingConfig(BaseModel):
    """Configuration for the comprehensive coaching graph."""

    user_id: str = Field(default="user", description="User identifier")
    mongodb_uri: str = Field(
        default="mongodb://host.docker.internal:27017",
        description="MongoDB URI (use host.docker.internal for Docker)",
    )
    thread_id: str = Field(default="thread", description="Thread identifier")
    enable_memory: bool = Field(default=True, description="Enable memory integration")
    enable_mem0: bool = Field(
        default=True, description="Enable Mem0 intelligent memory"
    )
    mem0_use_api: bool = Field(
        default=False, description="Use Mem0 API instead of local"
    )
    use_react_agents: bool = Field(
        default=True, description="Use ReAct agents instead of basic coaches"
    )
    max_iterations: int = Field(
        default=5, description="Max iterations for ReAct agents"
    )
    enable_human_feedback: bool = Field(
        default=False, description="Enable human-in-the-loop"
    )
    enable_reflexion: bool = Field(
        default=False,
        description="Enable reflexion loops for quality improvement (DISABLED by default to prevent infinite loops)",
    )
    max_reflexion_iterations: int = Field(
        default=2, description="Maximum reflexion iterations"
    )
    complexity_threshold: float = Field(
        default=0.6, description="Threshold for simple vs complex routing"
    )

    # Editable Prompt Fields for LangGraph Studio
    strength_coach_prompt: str = Field(
        default="DEPRECATED: This prompt is now loaded from versioned JSON files",
        description="System prompt for the Strength Training Coach. Defines expertise in resistance training, powerlifting, bodybuilding, and functional strength development.",
        json_schema_extra={
            "langgraph_nodes": ["strength_coach"],
            "langgraph_type": "prompt",
        },
    )

    cardio_coach_prompt: str = Field(
        default="DEPRECATED: This prompt is now loaded from versioned JSON files",
        description="System prompt for the Cardiovascular Training Coach. Defines expertise in aerobic fitness, endurance training, and heart health.",
        json_schema_extra={
            "langgraph_nodes": ["cardio_coach"],
            "langgraph_type": "prompt",
        },
    )

    cycling_coach_prompt: str = Field(
        default="DEPRECATED: This prompt is now loaded from versioned JSON files",
        description="System prompt for the Cycling Coach. Defines expertise in road cycling, mountain biking, and indoor cycling training.",
        json_schema_extra={
            "langgraph_nodes": ["cycling_coach"],
            "langgraph_type": "prompt",
        },
    )

    nutrition_coach_prompt: str = Field(
        default="DEPRECATED: This prompt is now loaded from versioned JSON files",
        description="System prompt for the Nutrition Coach. Defines expertise in sports nutrition, meal planning, and dietary optimization.",
        json_schema_extra={
            "langgraph_nodes": ["nutrition_coach"],
            "langgraph_type": "prompt",
        },
    )

    recovery_coach_prompt: str = Field(
        default="DEPRECATED: This prompt is now loaded from versioned JSON files",
        description="System prompt for the Recovery Coach. Defines expertise in rest, regeneration, and injury prevention.",
        json_schema_extra={
            "langgraph_nodes": ["recovery_coach"],
            "langgraph_type": "prompt",
        },
    )

    mental_coach_prompt: str = Field(
        default="DEPRECATED: This prompt is now loaded from versioned JSON files",
        description="System prompt for the Mental Training Coach. Defines expertise in sports psychology and mindset development.",
        json_schema_extra={
            "langgraph_nodes": ["mental_coach"],
            "langgraph_type": "prompt",
        },
    )


class ComprehensiveState(AgentState):
    """Extended state for comprehensive coaching - simplified for ASGI compatibility."""

    # Core fields only - to prevent ASGI serialization errors
    current_node: Optional[str] = None
    routing_decision: Optional[str] = None
    final_response: Optional[str] = None

    # Essential execution tracking
    execution_steps: List[str] = Field(default_factory=list)

    # Essential coach data
    coach_responses: Dict[str, str] = Field(default_factory=dict)

    # Simple debug info - avoid complex nested objects
    debug_info: Dict[str, str] = Field(default_factory=dict)  # Changed from Any to str

    # Keep only essential fields for basic functionality
    reasoning_output: Optional[str] = None
    complexity_score: Optional[float] = None
    execution_path: Optional[str] = None  # 'simple' or 'complex'

    # Essential human feedback
    awaiting_human_input: bool = False
    user_feedback: Optional[str] = None
    force_clarification: bool = False

    # Removed complex fields that cause ASGI serialization errors:
    # - tool_calls_made, rewoo_step_results, vector_results, graph_results
    # - graphrag_results, coach_focus_areas, and other complex Dict[str, Any] fields


async def create_comprehensive_coaching_graph(
    config: Optional[Union[Dict[str, Any], ComprehensiveCoachingConfig]] = None,
) -> CompiledStateGraph:
    """
    Create the comprehensive coaching graph with all advanced features.

    This function now orchestrates the one-time initialization of global resources
    and then builds the graph using these cached components.
    """
    logger.info("🏗️ GRAPH: [CREATE] Starting comprehensive coaching graph creation")

    if config is None:
        config = {}

    # Parse configuration - handle both dict and ComprehensiveCoachingConfig instances
    if isinstance(config, ComprehensiveCoachingConfig):
        coaching_config = config
    elif isinstance(config, dict):
        coaching_config = ComprehensiveCoachingConfig(**config)
    else:
        # Fallback for other types
        coaching_config = ComprehensiveCoachingConfig()

    logger.info("⚙️ GRAPH: [CREATE] Configuration parsed.")

    # Initialize global resources if this is the first run.
    # This is the critical step to prevent re-loading on every request.
    await initialize_global_resources(coaching_config)

    # Now, build the graph using the globally initialized components.
    # This part remains fast as it only defines the graph structure.
    uncompiled_graph = await build_comprehensive_graph(
        coaching_config, tools_manager, react_coaches
    )

    # Compile the graph with a memory saver if enabled
    if coaching_config.enable_memory:
        logger.info("🧠 GRAPH: [CREATE] Memory is enabled. Using MemorySaver.")
        memory = MemorySaver()
        graph = uncompiled_graph.compile(checkpointer=memory)
    else:
        logger.info("🧠 GRAPH: [CREATE] Memory is disabled.")
        graph = uncompiled_graph.compile()

    logger.info(
        "✅ GRAPH: [CREATE] Comprehensive coaching graph compiled successfully."
    )
    return graph


async def create_basic_coaches(config: ComprehensiveCoachingConfig) -> Dict[str, Any]:
    """Create basic coach stubs when ReAct agents are disabled."""

    # Create simple coach function stubs
    class BasicCoach:
        def __init__(self, coach_name: str):
            self.coach_name = coach_name

        async def execute(
            self, user_message: str, conversation_history=None, user_profile=None
        ):
            return {
                "final_answer": f"Basic {self.coach_name} response to: {user_message}",
                "success": True,
                "iterations": 1,
            }

        def get_tool_call_history(self):
            return []

    coaches = {
        "strength_coach": BasicCoach("strength_coach"),
        "cardio_coach": BasicCoach("cardio_coach"),
        "cycling_coach": BasicCoach("cycling_coach"),
        "nutrition_coach": BasicCoach("nutrition_coach"),
        "recovery_coach": BasicCoach("recovery_coach"),
        "mental_coach": BasicCoach("mental_coach"),
    }

    logger.info(f"Created {len(coaches)} basic coach stubs")
    return coaches


async def create_domain_agents(
    tools_manager, config: ComprehensiveCoachingConfig
) -> Dict[str, Any]:
    """Create domain agent nodes with full MCP integration."""

    logger.info("🤖 Creating domain agents with MCP integration...")

    # Import the actual agent instances
    from ...agents.mental_agent import mental_agent
    from ...agents.nutrition_agent import nutrition_agent
    from ...agents.cardio_agent import cardio_agent
    from ...agents.strength_agent import strength_agent
    from ...agents.recovery_agent import recovery_agent

    # Ensure all agents have their tools loaded
    agents = {
        "strength_agent": strength_agent,
        "cardio_agent": cardio_agent,
        "nutrition_agent": nutrition_agent,
        "recovery_agent": recovery_agent,
        "mental_agent": mental_agent,
    }

    # Initialize tools for each agent
    for agent_name, agent in agents.items():
        try:
            if not agent.tools:
                await agent.get_domain_tools()
            logger.info(f"✅ {agent_name} initialized with {len(agent.tools)} tools")
        except Exception as e:
            logger.warning(f"⚠️ Failed to initialize tools for {agent_name}: {e}")

    # Create node functions that wrap the agent execution
    async def create_agent_node(agent, agent_name):
        """Create a node function for an agent."""

        async def agent_node(state):
            logger.info(f"🤖 {agent_name.upper()}: Starting execution")

            # Extract user query
            if isinstance(state, dict):
                user_query = state.get("user_query", "")
                if not user_query and state.get("messages"):
                    for msg in reversed(state["messages"]):
                        if hasattr(msg, "content"):
                            user_query = msg.content
                            break
            else:
                user_query = getattr(state, "user_query", "")
                if not user_query and hasattr(state, "messages") and state.messages:
                    for msg in reversed(state.messages):
                        if hasattr(msg, "content"):
                            user_query = msg.content
                            break

            try:
                # Execute the agent
                response = await agent.execute(user_query)

                logger.info(f"✅ {agent_name.upper()}: Generated response")

                return {
                    "current_node": agent_name,
                    "final_response": response,
                    "debug_info": {
                        "node": agent_name,
                        "action": "agent_executed",
                        "response_length": len(response) if response else 0,
                    },
                }

            except Exception as e:
                logger.error(f"❌ {agent_name.upper()}: Error during execution: {e}")
                fallback_response = f"I encountered an issue while processing your {agent_name.replace('_', ' ')} request. Please try again."

                return {
                    "current_node": agent_name,
                    "final_response": fallback_response,
                    "debug_info": {
                        "node": agent_name,
                        "action": "error_fallback",
                        "error": str(e),
                    },
                }

        return agent_node

    coaches = {
        "strength_coach": await create_agent_node(strength_agent, "strength_coach"),
        "cardio_coach": await create_agent_node(cardio_agent, "cardio_coach"),
        "nutrition_coach": await create_agent_node(nutrition_agent, "nutrition_coach"),
        "recovery_coach": await create_agent_node(recovery_agent, "recovery_coach"),
        "mental_coach": await create_agent_node(mental_agent, "mental_coach"),
        # Add cycling_coach as alias to cardio for compatibility
        "cycling_coach": await create_agent_node(cardio_agent, "cycling_coach"),
    }

    logger.info(f"✅ Created {len(coaches)} domain agent nodes with MCP integration")
    return coaches


async def create_domain_integrated_react_coaches(
    tools_manager, config: ComprehensiveCoachingConfig
) -> Dict[str, ReActCoachExecutor]:
    """Create ReAct coach executors using domain agents' tools instead of generic tools."""

    # Import prompt loader for versioned prompts
    import asyncio
    from ...utils.prompt_loader import get_prompt_loader

    # Import domain agents
    from ...agents.strength_agent import strength_agent
    from ...agents.cardio_agent import cardio_agent
    from ...agents.nutrition_agent import nutrition_agent
    from ...agents.recovery_agent import recovery_agent
    from ...agents.mental_agent import mental_agent

    coaches = {}
    prompt_loader = await get_prompt_loader("athlea_langgraph/prompts")

    # Map coach names to domain agents
    agent_mapping = {
        "strength_coach": strength_agent,
        "cardio_coach": cardio_agent,
        "nutrition_coach": nutrition_agent,
        "recovery_coach": recovery_agent,
        "mental_coach": mental_agent,
    }

    # Add cycling coach using cardio agent tools for now
    # (we can create a dedicated cycling agent later if needed)
    for coach_name in [
        "strength_coach",
        "cardio_coach",
        "cycling_coach",
        "nutrition_coach",
        "recovery_coach",
        "mental_coach",
    ]:
        try:
            # Load versioned prompt for this coach
            prompt_config = await prompt_loader.load_prompt(f"coaches/{coach_name}")
            coach_prompt = prompt_config.prompt.system
            version = prompt_config.metadata.version

            logger.info(f"✅ Loaded versioned prompt for {coach_name} v{version}")

            # Get tools from domain agent or fallback to generic tools
            if coach_name in agent_mapping:
                # Use domain agent's tools
                domain_agent = agent_mapping[coach_name]
                tools = await domain_agent.get_domain_tools()
                logger.info(
                    f"🔧 {coach_name} using domain agent tools: {[t.name for t in tools]}"
                )
            else:
                # Fallback to tools manager for unsupported coaches (like cycling_coach)
                tools = getattr(
                    tools_manager, f"get_{coach_name.replace('_coach', '')}_coach_tools"
                )()
                logger.info(
                    f"🔧 {coach_name} using generic tools: {[t.name for t in tools]}"
                )

            # Create ReAct executor with domain-specific tools
            executor = ReActCoachExecutor(
                coach_name=coach_name,
                coach_prompt=coach_prompt,
                tools=tools,
                max_iterations=config.max_iterations,
                temperature=0.7,
            )

            # Override the LLM with our optimized version
            executor.llm = create_azure_chat_openai(
                node_type="coaching", streaming=True
            )

            coaches[coach_name] = executor

        except Exception as e:
            logger.warning(f"⚠️ Failed to load domain agent tools for {coach_name}: {e}")
            # Fallback to original tools manager approach
            executor = ReActCoachExecutor(
                coach_name=coach_name,
                coach_prompt=getattr(
                    config, f"{coach_name}_prompt", "You are a helpful fitness coach."
                ),
                tools=getattr(
                    tools_manager, f"get_{coach_name.replace('_coach', '')}_coach_tools"
                )(),
                max_iterations=config.max_iterations,
                temperature=0.7,
            )

            # Override the LLM with our optimized version
            executor.llm = create_azure_chat_openai(
                node_type="coaching", streaming=True
            )

            coaches[coach_name] = executor

    logger.info(f"Created {len(coaches)} ReAct coach executors with domain agent tools")
    return coaches


async def create_react_coaches(
    tools_manager, config: ComprehensiveCoachingConfig
) -> Dict[str, ReActCoachExecutor]:
    """Create ReAct coach executors for each specialized domain."""

    # Import prompt loader for versioned prompts
    import asyncio

    from ...utils.prompt_loader import get_prompt_loader

    coaches = {}
    prompt_loader = await get_prompt_loader("athlea_langgraph/prompts")

    # Initialize each ReAct coach executor with versioned prompts
    for coach_name in [
        "strength_coach",
        "cardio_coach",
        "cycling_coach",
        "nutrition_coach",
        "recovery_coach",
        "mental_coach",
    ]:
        try:
            # Load versioned prompt for this coach
            prompt_config = await prompt_loader.load_prompt(f"coaches/{coach_name}")
            coach_prompt = prompt_config.prompt.system
            version = prompt_config.metadata.version

            logger.info(f"✅ Loaded versioned prompt for {coach_name} v{version}")

            # Create ReAct executor with optimized model
            executor = ReActCoachExecutor(
                coach_name=coach_name,
                coach_prompt=coach_prompt,
                tools=getattr(
                    tools_manager, f"get_{coach_name.replace('_coach', '')}_coach_tools"
                )(),
                max_iterations=config.max_iterations,
                temperature=0.7,  # Will be overridden by the LLM node_type config
            )

            # Override the LLM with our optimized version
            executor.llm = create_azure_chat_openai(
                node_type="coaching", streaming=True  # Use optimized coaching model
            )

            coaches[coach_name] = executor

        except Exception as e:
            logger.warning(f"⚠️ Failed to load versioned prompt for {coach_name}: {e}")
            # Fallback to basic coach with default tools
            executor = ReActCoachExecutor(
                coach_name=coach_name,
                coach_prompt=getattr(
                    config, f"{coach_name}_prompt", "You are a helpful fitness coach."
                ),
                tools=getattr(
                    tools_manager, f"get_{coach_name.replace('_coach', '')}_coach_tools"
                )(),
                max_iterations=config.max_iterations,
                temperature=0.7,
            )

            # Override the LLM with our optimized version
            executor.llm = create_azure_chat_openai(
                node_type="coaching", streaming=True
            )

            coaches[coach_name] = executor

    logger.info(f"Created {len(coaches)} ReAct coach executors with versioned prompts")
    return coaches


async def build_comprehensive_graph(
    config: ComprehensiveCoachingConfig,
    tools_manager,
    react_coaches: Dict[str, ReActCoachExecutor],
) -> StateGraph:
    """Build the comprehensive graph with ReWOO+Reflection pattern."""

    # Initialize prompt loader
    prompt_loader = await get_prompt_loader("athlea_langgraph/prompts")

    # Create enhanced node functions
    async def enhanced_reasoning_node(state: ComprehensiveState) -> Dict[str, Any]:
        """Enhanced reasoning node with ReWOO-style observation and Mem0 memory integration."""
        if isinstance(state, dict):
            state["current_node"] = "reasoning"
            state["execution_steps"] = state.get("execution_steps", []) + ["reasoning"]
            user_query = state.get("user_query", "")
            if not user_query and state.get("messages"):
                for msg in reversed(state["messages"]):
                    if isinstance(msg, HumanMessage):
                        user_query = msg.content
                        break
            conversation_history = list(state.get("messages", []))

            # Ensure user message is in the messages array if we have a user_query
            if user_query and not any(
                isinstance(msg, HumanMessage) and msg.content == user_query
                for msg in conversation_history
            ):
                conversation_history.append(HumanMessage(content=user_query))
                state["messages"] = conversation_history
        else:
            state.current_node = "reasoning"
            state.execution_steps = state.execution_steps + ["reasoning"]
            user_query = state.user_query
            if not user_query and state.messages:
                for msg in reversed(state.messages):
                    if isinstance(msg, HumanMessage):
                        user_query = msg.content
                        break
            conversation_history = list(state.messages)

            # Ensure user message is in the messages array if we have a user_query
            if user_query and not any(
                isinstance(msg, HumanMessage) and msg.content == user_query
                for msg in conversation_history
            ):
                conversation_history.append(HumanMessage(content=user_query))
                state.messages = conversation_history

        logger.info(
            f"🧠 REASONING: Starting enhanced reasoning for: {user_query[:50]}..."
        )

        # Mem0 Memory Integration - Retrieve coaching context
        memory_context = ""
        if config.enable_mem0 and user_query:
            try:
                memory_service = await get_mem0_service(use_api=config.mem0_use_api)
                memory_context = await memory_service.get_user_coaching_context(
                    user_id=config.user_id, query=user_query
                )
                logger.info(
                    f"🧠 MEMORY: Retrieved context: {len(memory_context)} chars"
                )
            except Exception as e:
                logger.warning(f"⚠️ MEMORY: Failed to retrieve context: {e}")
                memory_context = ""

        try:
            # Use cached LLM for complex reasoning
            reasoning_llm = get_cached_llm("reasoning")
            reasoning_prompt_config = await get_cached_prompt_config(
                "reasoning/enhanced_reasoning", prompt_loader
            )

            if reasoning_prompt_config:
                system_prompt = reasoning_prompt_config.prompt.system
                # Inject memory context into system prompt if available
                if (
                    memory_context
                    and memory_context != "No previous coaching context available."
                ):
                    enhanced_system_prompt = f"{system_prompt}\n\n{memory_context}"
                else:
                    enhanced_system_prompt = system_prompt

                user_prompt = reasoning_prompt_config.prompt.user_template.format(
                    user_query=user_query
                )

                messages = [
                    SystemMessage(content=enhanced_system_prompt),
                    HumanMessage(content=user_prompt),
                ]
            else:
                logger.warning(
                    "Failed to load enhanced reasoning prompt, using fallback."
                )
                # Load fallback prompt from prompts folder
                try:
                    fallback_prompt_config = await prompt_loader.load_prompt(
                        "reasoning/enhanced_reasoning_fallback"
                    )
                    fallback_system_prompt = fallback_prompt_config.prompt.system
                    fallback_user_prompt = (
                        fallback_prompt_config.prompt.user_template.format(
                            user_query=user_query
                        )
                    )

                    # Inject memory context into fallback prompt if available
                    if (
                        memory_context
                        and memory_context != "No previous coaching context available."
                    ):
                        fallback_system_prompt += f"\n\n{memory_context}"

                    messages = [
                        SystemMessage(content=fallback_system_prompt),
                        HumanMessage(content=fallback_user_prompt),
                    ]
                    logger.info("Using versioned fallback reasoning prompt")
                except Exception as fallback_error:
                    logger.error(f"Failed to load fallback prompt: {fallback_error}")
                    # Ultimate fallback with minimal hardcoded prompt
                    messages = [
                        SystemMessage(
                            content="You are an AI fitness reasoning agent. Analyze user queries."
                        ),
                        HumanMessage(content=user_query),
                    ]

            # Use LLM to generate reasoning
            response = await reasoning_llm.ainvoke(messages)
            reasoning_output = response.content

            logger.info(
                f"✅ REASONING: Generated reasoning analysis with memory context"
            )

            return {
                "reasoning_output": reasoning_output,
                "memory_context": memory_context,
                "current_node": "reasoning",
                "messages": conversation_history,  # Include updated messages
                "debug_info": {
                    "node": "reasoning",
                    "user_query": (
                        user_query[:50] + "..." if len(user_query) > 50 else user_query
                    ),
                    "reasoning_length": str(len(reasoning_output)),
                    "memory_retrieved": str(bool(memory_context)),
                    "memory_length": str(len(memory_context) if memory_context else 0),
                },
            }

        except Exception as e:
            logger.error(f"❌ REASONING: Error in reasoning node: {e}")
            fallback_reasoning = (
                f"User query: {user_query}. Need to proceed with analysis."
            )

            return {
                "reasoning_output": fallback_reasoning,
                "current_node": "reasoning",
                "messages": conversation_history,  # Include updated messages
                "debug_info": {
                    "node": "reasoning",
                    "error": str(e),
                    "used_fallback": True,
                },
            }

    # Placeholder node implementations (minimal for testing)
    async def multi_coach_executor_node(state: ComprehensiveState) -> Dict[str, Any]:
        """Execute multiple coaches based on the required_coaches list."""
        if isinstance(state, dict):
            state["current_node"] = "multi_coach_executor"
            state["execution_steps"] = state.get("execution_steps", []) + [
                "multi_coach_executor"
            ]
            user_query = state.get("user_query", "")
            messages = state.get("messages", [])
            required_coaches = state.get("required_coaches", ["strength_coach"])
            user_profile = state.get("user_profile")
        else:
            state.current_node = "multi_coach_executor"
            state.execution_steps.append("multi_coach_executor")
            user_query = getattr(state, "user_query", "")
            messages = getattr(state, "messages", [])
            required_coaches = getattr(state, "required_coaches", ["strength_coach"])
            user_profile = getattr(state, "user_profile", None)

        # Get the latest user message if user_query is empty
        if not user_query and messages:
            for msg in reversed(messages):
                if isinstance(msg, HumanMessage):
                    user_query = msg.content
                    break

        logger.info(
            f"📋 MULTI_COACH_EXECUTOR: Executing {len(required_coaches)} coaches for: {user_query[:50]}..."
        )

        coach_responses = {}

        # Execute each required coach
        for coach_name in required_coaches:
            if coach_name in react_coaches:
                try:
                    logger.info(f"🏃‍♂️ Executing {coach_name}...")
                    executor = react_coaches[coach_name]

                    # Load coach prompt
                    prompt_config = await get_cached_prompt_config(
                        coach_name, prompt_loader
                    )
                    if prompt_config:
                        executor.coach_prompt = prompt_config.prompt.system

                    # Execute the coach
                    result = await executor.execute(
                        user_message=user_query,
                        conversation_history=messages,
                        user_profile=user_profile,
                    )

                    final_answer = result.get("final_answer", "I'd be happy to help!")
                    if not isinstance(final_answer, str):
                        final_answer = json.dumps(final_answer)

                    coach_responses[coach_name] = final_answer
                    logger.info(f"✅ {coach_name} completed: {len(final_answer)} chars")

                except Exception as e:
                    logger.error(f"❌ Error executing {coach_name}: {e}")
                    coach_responses[coach_name] = (
                        f"I apologize, but I encountered an error. Please try again."
                    )
            else:
                logger.warning(f"⚠️  Coach {coach_name} not found in react_coaches")

        logger.info(
            f"📋 MULTI_COACH_EXECUTOR: Completed {len(coach_responses)} coaches"
        )

        return {
            "current_node": "multi_coach_executor",
            "coach_responses": coach_responses,
            "routing_decision": "aggregation",
            "debug_info": {
                "node": "multi_coach_executor",
                "coaches_executed": list(coach_responses.keys()),
                "total_responses": str(len(coach_responses)),
            },
        }

    # Note: Using updated aggregation_node with prompts from prompts folder for better consistency

    async def automated_greeting_node(state: ComprehensiveState) -> Dict[str, Any]:
        """Generates a dynamic, context-aware greeting using a lightweight model."""
        if isinstance(state, dict):
            user_query = state.get("user_query", "")
            messages = state.get("messages", [])
        else:
            user_query = getattr(state, "user_query", "")
            messages = getattr(state, "messages", [])

        # Create a copy of messages to preserve the original
        preserved_messages = list(messages) if messages else []

        # Get the latest user message if user_query is empty
        if not user_query and messages:
            for msg in reversed(messages):
                if hasattr(msg, "type") and msg.type == "human":
                    user_query = msg.content
                    break

        # Ensure user message is in preserved_messages if we have a user_query
        if user_query and not any(
            isinstance(msg, HumanMessage) and msg.content == user_query
            for msg in preserved_messages
        ):
            preserved_messages.append(HumanMessage(content=user_query))

        # Check if this is the first interaction (no previous AI messages)
        has_previous_ai_messages = False
        if messages:
            for msg in messages:
                if hasattr(msg, "type") and msg.type == "ai":
                    has_previous_ai_messages = True
                    break

        logger.info(
            f"👋 AUTOMATED_GREETING: Generating {'follow-up' if has_previous_ai_messages else 'initial'} response for: {user_query}"
        )

        try:
            # Use cached LLM for greeting generation
            greeting_llm = get_cached_llm("greeting_detection")

            # Load the new "receptionist" prompt
            greeting_prompt_config = await prompt_loader.load_prompt(
                "system/receptionist"
            )

            if greeting_prompt_config:
                system_prompt = greeting_prompt_config.prompt.system

                # Select appropriate instruction based on context
                if has_previous_ai_messages:
                    instruction_prompt = greeting_prompt_config.prompt.instructions[
                        "follow_up"
                    ].format(user_query=user_query)
                else:
                    instruction_prompt = greeting_prompt_config.prompt.instructions[
                        "first_interaction"
                    ].format(user_query=user_query)

                messages_for_llm = [
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=instruction_prompt),
                ]
            else:
                logger.warning("Failed to load receptionist prompt, using fallback.")
                # Load fallback prompt from prompts folder
                try:
                    fallback_receptionist_config = await prompt_loader.load_prompt(
                        "system/receptionist_fallback"
                    )
                    fallback_system = fallback_receptionist_config.prompt.system

                    # Select appropriate instruction based on context
                    if has_previous_ai_messages:
                        greeting_prompt = (
                            fallback_receptionist_config.prompt.instructions[
                                "follow_up"
                            ].format(user_query=user_query)
                        )
                    else:
                        greeting_prompt = (
                            fallback_receptionist_config.prompt.instructions[
                                "first_interaction"
                            ].format(user_query=user_query)
                        )

                    messages_for_llm = [
                        SystemMessage(content=fallback_system),
                        HumanMessage(content=greeting_prompt),
                    ]
                    logger.info("Using versioned fallback receptionist prompt")
                except Exception as fallback_error:
                    logger.error(
                        f"Failed to load fallback receptionist prompt: {fallback_error}"
                    )
                    # Ultimate fallback
                    simple_greeting = (
                        "Welcome to Athlea! How can our coaching team help you today?"
                    )
                    # Ultimate hardcoded fallback - keep minimal for emergencies only
                    fallback_response = (
                        "Welcome to Athlea! How can our coaching team help you today?"
                    )
                    messages_for_llm = None  # Skip LLM call and use direct response

            # Generate dynamic response
            if messages_for_llm:
                response = await greeting_llm.ainvoke(messages_for_llm)
                greeting_response = response.content.strip()
            else:
                # Use the fallback response directly
                greeting_response = fallback_response

            logger.info(
                f"✅ AUTOMATED_GREETING: Generated {'follow-up' if has_previous_ai_messages else 'initial'} response: {greeting_response[:100]}..."
            )

            # Create AIMessage for the response and add to preserved messages
            from langchain_core.messages import AIMessage

            ai_message = AIMessage(content=greeting_response, name="athlea_coach")
            preserved_messages.append(ai_message)

            return {
                "current_node": "automated_greeting",
                "final_response": greeting_response,
                "messages": preserved_messages,
                "debug_info": {
                    "node": "automated_greeting",
                    "action": "context_aware_greeting_generated",
                    "user_query": (
                        user_query[:50] + "..." if len(user_query) > 50 else user_query
                    ),
                    "response_length": str(len(greeting_response)),
                    "is_follow_up": str(has_previous_ai_messages),
                },
            }

        except Exception as e:
            logger.error(
                f"❌ AUTOMATED_GREETING: Error generating dynamic response: {e}"
            )
            # Fallback to context-aware simple response with Receptionist persona
            if has_previous_ai_messages:
                fallback_response = f"Thanks for reaching out again! How can our coaching team help you today?"
            else:
                fallback_response = f"Welcome to Athlea! I'm the AI Receptionist. I see you said '{user_query}' - how can our expert coaching team assist you today?"

            # Create AIMessage for the fallback response and add to preserved messages
            from langchain_core.messages import AIMessage

            ai_message = AIMessage(content=fallback_response, name="athlea_coach")
            preserved_messages.append(ai_message)

            return {
                "current_node": "automated_greeting",
                "final_response": fallback_response,
                "messages": preserved_messages,
                "debug_info": {
                    "node": "automated_greeting",
                    "action": "fallback_context_aware_greeting",
                    "user_query": user_query,
                    "error": str(e),
                    "is_follow_up": has_previous_ai_messages,
                },
            }

    async def early_intent_classifier(state: ComprehensiveState) -> Dict[str, Any]:
        """Early intent classifier using LLM to determine user intent instead of hardcoded patterns."""
        if isinstance(state, dict):
            state["current_node"] = "early_intent_classifier"
            state["execution_steps"] = state.get("execution_steps", []) + [
                "early_intent_classifier"
            ]
            user_query = state.get("user_query", "")
            if not user_query and state.get("messages"):
                for msg in reversed(state["messages"]):
                    if isinstance(msg, HumanMessage):
                        user_query = msg.content
                        break
        else:
            state.current_node = "early_intent_classifier"
            state.execution_steps.append("early_intent_classifier")
            user_query = state.user_query if hasattr(state, "user_query") else ""
            if not user_query and hasattr(state, "messages") and state.messages:
                for msg in reversed(state.messages):
                    if isinstance(msg, HumanMessage):
                        user_query = msg.content
                        break

        logger.info(f"🎯 EARLY_INTENT: Classifying intent for: {user_query[:50]}...")

        try:
            # Use fast, cheap model for simple intent classification
            intent_llm = create_azure_chat_openai(node_type="intent_classification")

            # Load intent classification prompt from JSON file
            try:
                intent_prompt_config = await prompt_loader.load_prompt(
                    "system/intent_classification"
                )
                system_prompt = intent_prompt_config.prompt.system
                user_prompt = intent_prompt_config.prompt.user_template.format(
                    user_query=user_query
                )

                messages = [
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=user_prompt),
                ]
            except Exception as prompt_error:
                logger.warning(
                    f"Failed to load intent classification prompt, using fallback: {prompt_error}"
                )
                # Load fallback prompt from prompts folder
                try:
                    fallback_intent_config = await prompt_loader.load_prompt(
                        "system/intent_classification_fallback"
                    )
                    fallback_system = fallback_intent_config.prompt.system
                    fallback_user = fallback_intent_config.prompt.user_template.format(
                        user_query=user_query
                    )
                    messages = [
                        SystemMessage(content=fallback_system),
                        HumanMessage(content=fallback_user),
                    ]
                    logger.info("Using versioned fallback intent classification prompt")
                except Exception as fallback_error:
                    logger.error(
                        f"Failed to load fallback intent prompt: {fallback_error}"
                    )
                    # Ultimate fallback
                    messages = [
                        SystemMessage(
                            content="Classify as SIMPLE_GREETING, UNCLEAR_SHORT, or COMPLEX_QUERY."
                        ),
                        HumanMessage(content=f"Query: {user_query}"),
                    ]

            # Get classification from LLM
            response = await intent_llm.ainvoke(messages)
            classification = response.content.strip().upper()

            logger.info(f"🎯 EARLY_INTENT: LLM classified intent as: {classification}")

            if classification == "SIMPLE_GREETING":
                logger.info(
                    f"✅ EARLY_INTENT: Detected simple greeting - routing to automated_greeting"
                )
                return {
                    "early_intent": "simple_greeting",
                    "routing_decision": "automated_greeting",
                    "current_node": "early_intent_classifier",
                    "debug_info": {
                        "node": "early_intent_classifier",
                        "detected_intent": "simple_greeting",
                        "query": user_query,
                        "action": "route_to_greeting",
                        "llm_classification": classification,
                    },
                }
            elif classification == "UNCLEAR_SHORT":
                logger.info(
                    f"✅ EARLY_INTENT: Detected unclear short query - routing to automated_greeting"
                )
                return {
                    "early_intent": "unclear_short",
                    "routing_decision": "automated_greeting",
                    "current_node": "early_intent_classifier",
                    "debug_info": {
                        "node": "early_intent_classifier",
                        "detected_intent": "unclear_short",
                        "query": user_query,
                        "action": "route_to_greeting",
                        "llm_classification": classification,
                    },
                }
            else:  # COMPLEX_QUERY or any other response
                logger.info(
                    f"✅ EARLY_INTENT: Complex query detected - continue to planning"
                )
                return {
                    "early_intent": "complex_query",
                    "routing_decision": "continue_processing",
                    "current_node": "early_intent_classifier",
                    "debug_info": {
                        "node": "early_intent_classifier",
                        "detected_intent": "complex_query",
                        "query": user_query,
                        "action": "continue_processing",
                        "llm_classification": classification,
                    },
                }

        except Exception as e:
            logger.error(f"❌ EARLY_INTENT: LLM classification failed: {e}")
            # Fallback to simple length-based heuristic
            if len(user_query.strip().split()) <= 3:
                logger.info(
                    f"✅ EARLY_INTENT: Fallback - short query, routing to automated_greeting"
                )
                return {
                    "early_intent": "unclear_short",
                    "routing_decision": "automated_greeting",
                    "current_node": "early_intent_classifier",
                    "debug_info": {
                        "node": "early_intent_classifier",
                        "detected_intent": "fallback_short",
                        "query": user_query,
                        "action": "route_to_greeting",
                        "error": str(e),
                    },
                }
            else:
                logger.info(
                    f"✅ EARLY_INTENT: Fallback - longer query, continue to planning"
                )
                return {
                    "early_intent": "complex_query",
                    "routing_decision": "continue_processing",
                    "current_node": "early_intent_classifier",
                    "debug_info": {
                        "node": "early_intent_classifier",
                        "detected_intent": "fallback_complex",
                        "query": user_query,
                        "action": "continue_processing",
                        "error": str(e),
                    },
                }

    async def enhanced_planning_node(state: ComprehensiveState) -> Dict[str, Any]:
        """Enhanced multi-coach planning node."""
        logger.info("📋 GRAPH: [PLANNING] Starting multi-coach planning node execution")

        if isinstance(state, dict):
            state["current_node"] = "planning"
            state["execution_steps"] = state.get("execution_steps", []) + ["planning"]
            user_query = state.get("user_query", "")
            if not user_query and state.get("messages"):
                for msg in reversed(state["messages"]):
                    if isinstance(msg, HumanMessage):
                        user_query = msg.content
                        break
        else:
            state.current_node = "planning"
            state.execution_steps.append("planning")
            user_query = state.user_query if hasattr(state, "user_query") else ""
            if not user_query and hasattr(state, "messages") and state.messages:
                for msg in reversed(state.messages):
                    if isinstance(msg, HumanMessage):
                        user_query = msg.content
                        break

        logger.info(
            f"🎯 GRAPH: [PLANNING] Analyzing query for multi-coach planning: {user_query[:100]}{'...' if len(user_query) > 100 else ''}"
        )

        try:
            # Use optimized model for planning tasks
            planning_llm = create_azure_chat_openai(node_type="planning")

            # Load enhanced planning prompt from JSON file
            try:
                planning_prompt_config = await prompt_loader.load_prompt(
                    "reasoning/enhanced_planning"
                )
                # Get user_profile and reasoning_output safely
                if isinstance(state, dict):
                    user_profile = state.get("user_profile", "Not available")
                    reasoning_output = state.get("reasoning_output", "Not available")
                else:
                    user_profile = (
                        state.user_profile
                        if hasattr(state, "user_profile") and state.user_profile
                        else "Not available"
                    )
                    reasoning_output = (
                        state.reasoning_output
                        if hasattr(state, "reasoning_output")
                        else "Not available"
                    )

                system_prompt = planning_prompt_config.prompt.system.format(
                    user_query=user_query,
                    user_profile=user_profile,
                    reasoning_output=reasoning_output,
                )
                user_prompt = planning_prompt_config.prompt.user_template.format(
                    user_query=user_query
                )

                messages = [
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=user_prompt),
                ]
            except Exception as prompt_error:
                logger.warning(
                    f"Failed to load enhanced planning prompt, using fallback: {prompt_error}"
                )
                # Load fallback prompt from prompts folder
                try:
                    fallback_planning_config = await prompt_loader.load_prompt(
                        "reasoning/enhanced_planning_fallback"
                    )

                    # Get user_profile and reasoning_output safely for fallback
                    if isinstance(state, dict):
                        fallback_user_profile = state.get(
                            "user_profile", "Not available"
                        )
                        fallback_reasoning_output = state.get(
                            "reasoning_output", "Not available"
                        )
                    else:
                        fallback_user_profile = (
                            state.user_profile
                            if hasattr(state, "user_profile") and state.user_profile
                            else "Not available"
                        )
                        fallback_reasoning_output = (
                            state.reasoning_output
                            if hasattr(state, "reasoning_output")
                            else "Not available"
                        )

                    fallback_system = fallback_planning_config.prompt.system
                    planning_prompt = (
                        fallback_planning_config.prompt.user_template.format(
                            user_query=user_query,
                            user_profile=fallback_user_profile,
                            reasoning_output=fallback_reasoning_output,
                        )
                    )

                    messages = [
                        SystemMessage(content=fallback_system),
                        HumanMessage(content=planning_prompt),
                    ]
                    logger.info("Using versioned fallback planning prompt")
                except Exception as fallback_error:
                    logger.error(
                        f"Failed to load fallback planning prompt: {fallback_error}"
                    )
                    # Ultimate fallback - return minimal plan structure
                    logger.error(
                        "All planning prompts failed, using hardcoded minimal response"
                    )
                    # Skip LLM call and return basic structure
                    return {
                        "required_coaches": ["clarification"],
                        "completed_coaches": [],
                        "coach_focus_areas": {"clarification": "clarifying user needs"},
                        "routing_decision": "clarification",
                        "current_node": "planning",
                        "debug_info": {
                            "node": "planning",
                            "error": "all_prompts_failed",
                            "action": "minimal_fallback",
                        },
                    }

            response = await planning_llm.ainvoke(messages)

            # Parse the response
            if isinstance(response, dict) and "coaches" in response:
                required_coaches = response["coaches"]
                coach_focus_areas = {
                    coach: "general coaching guidance" for coach in required_coaches
                }
                routing_decision = response["routing_decision"]
            else:
                # Fallback to old format (array of coach names)
                required_coaches = (
                    response if isinstance(response, list) else [response]
                )
                coach_focus_areas = {}
                routing_decision = "clarification"

            # Validate coaches
            valid_coaches = [
                "strength_coach",
                "cardio_coach",
                "cycling_coach",
                "nutrition_coach",
                "recovery_coach",
                "mental_coach",
                "clarification",
            ]
            required_coaches = [
                coach for coach in required_coaches if coach in valid_coaches
            ]

            # Filter focus areas to match valid coaches
            coach_focus_areas = {
                coach: focus
                for coach, focus in coach_focus_areas.items()
                if coach in required_coaches
            }

            if not required_coaches:
                required_coaches = ["clarification"]
                coach_focus_areas = {"clarification": "clarifying user needs"}

            # Store multi-coach plan
            if isinstance(state, dict):
                state["required_coaches"] = required_coaches
                state["completed_coaches"] = []
                state["coach_focus_areas"] = coach_focus_areas
                state["routing_decision"] = routing_decision
            else:
                state.required_coaches = required_coaches
                state.completed_coaches = []
                state.coach_focus_areas = coach_focus_areas
                state.routing_decision = routing_decision

            return {
                "required_coaches": required_coaches,
                "completed_coaches": [],
                "coach_focus_areas": coach_focus_areas,
                "routing_decision": routing_decision,
                "current_node": "planning",
                "debug_info": {
                    "node": "planning",
                    "multi_coach_plan": True,
                    "coaches_required": len(required_coaches),
                    "coaches": required_coaches,
                    "focus_areas": coach_focus_areas,
                },
            }

        except Exception as e:
            logger.error(f"❌ GRAPH: [PLANNING] Error in multi-coach planning: {e}")
            # Fallback to single coach
            try:
                result = await planning_node(state)
                logger.info("✅ GRAPH: [PLANNING] Fallback to single coach successful")
                return result
            except Exception as fallback_error:
                logger.error(
                    f"❌ GRAPH: [PLANNING] Fallback also failed: {fallback_error}"
                )
                return {
                    "routing_decision": "clarification",
                    "current_node": "planning",
                    "debug_info": {
                        "node": "planning",
                        "error": str(e),
                        "fallback_error": str(fallback_error),
                    },
                }

    async def complexity_assessment_node(state: ComprehensiveState) -> Dict[str, Any]:
        """Assess query complexity to route to simple or complex path."""
        if isinstance(state, dict):
            state["current_node"] = "complexity_assessment"
            state["execution_steps"] = state.get("execution_steps", []) + [
                "complexity_assessment"
            ]
            user_query = state.get("user_query", "")
            if not user_query and state.get("messages"):
                for msg in reversed(state["messages"]):
                    if isinstance(msg, HumanMessage):
                        user_query = msg.content
                        break
            routing_decision = state.get("routing_decision", "strength_coach")
        else:
            state.current_node = "complexity_assessment"
            state.execution_steps.append("complexity_assessment")
            user_query = (
                state.user_query
                if hasattr(state, "user_query") and state.user_query
                else ""
            )
            if not user_query and hasattr(state, "messages") and state.messages:
                for msg in reversed(state.messages):
                    if isinstance(msg, HumanMessage):
                        user_query = msg.content
                        break
            routing_decision = (
                state.routing_decision
                if hasattr(state, "routing_decision")
                else "strength_coach"
            )

        try:
            # Load complexity assessment prompt
            complexity_prompt_config = await prompt_loader.load_prompt(
                "coaches/complexity_assessment"
            )

            # Determine coach domain from routing decision
            coach_domain_map = {
                "strength_coach": "strength training",
                "cardio_coach": "cardiovascular training",
                "cycling_coach": "cycling",
                "nutrition_coach": "nutrition",
                "recovery_coach": "recovery",
                "mental_coach": "mental training",
            }
            coach_domain = coach_domain_map.get(routing_decision, "fitness")

            # Get domain-specific examples (simplified for now)
            simple_examples = f"• How do I do proper form for {coach_domain}?\n• What are basic {coach_domain} tips?\n• Quick {coach_domain} advice?"
            complex_examples = f"• Design a 12-week {coach_domain} program\n• Create periodized {coach_domain} plan\n• Analyze complex {coach_domain} scenarios"

            user_context = ""
            if isinstance(state, dict):
                user_profile = state.get("user_profile")
            else:
                user_profile = (
                    state.user_profile if hasattr(state, "user_profile") else None
                )

            if user_profile:
                user_context = f"User Context: {user_profile}"

            # Format the prompt
            prompt = complexity_prompt_config.prompt.system.format(
                coach_domain=coach_domain,
                simple_examples=simple_examples,
                complex_examples=complex_examples,
                user_query=user_query,
                user_context=user_context,
            )

            # Use LLM to assess complexity (simplified implementation)
            from langchain_openai import ChatOpenAI

            llm = ChatOpenAI(
                node_type="planning", streaming=False
            )  # Use our service for consistency

            response = await llm.ainvoke([HumanMessage(content=prompt)])
            assessment_text = response.content

            # Parse the response
            complexity_score = 0.5
            complexity_level = "moderate"
            needs_rewoo = False
            reasoning = "Assessment completed"

            lines = assessment_text.split("\n")
            for line in lines:
                if line.startswith("COMPLEXITY_SCORE:"):
                    try:
                        complexity_score = float(line.split(":")[1].strip())
                    except:
                        pass
                elif line.startswith("COMPLEXITY_LEVEL:"):
                    complexity_level = line.split(":")[1].strip()
                elif line.startswith("NEEDS_REWOO:"):
                    needs_rewoo = line.split(":")[1].strip().lower() == "true"
                elif line.startswith("REASONING:"):
                    reasoning = line.split(":", 1)[1].strip()

            # Determine execution path
            execution_path = (
                "complex"
                if complexity_score >= config.complexity_threshold
                else "simple"
            )

            # Store results
            if isinstance(state, dict):
                state["complexity_score"] = complexity_score
                state["complexity_level"] = complexity_level
                state["needs_rewoo"] = needs_rewoo
                state["complexity_reasoning"] = reasoning
                state["execution_path"] = execution_path
            else:
                state.complexity_score = complexity_score
                state.complexity_level = complexity_level
                state.needs_rewoo = needs_rewoo
                state.complexity_reasoning = reasoning
                state.execution_path = execution_path

            return {
                "current_node": "complexity_assessment",
                "complexity_score": complexity_score,
                "complexity_level": complexity_level,
                "needs_rewoo": needs_rewoo,
                "execution_path": execution_path,
                "debug_info": {
                    "node": "complexity_assessment",
                    "score": complexity_score,
                    "path": execution_path,
                    "reasoning": reasoning,
                },
            }

        except Exception as e:
            logger.error(f"Error in complexity assessment: {e}")
            # Fallback to simple path
            execution_path = "simple"
            if isinstance(state, dict):
                state["execution_path"] = execution_path
                state["complexity_score"] = 0.3
            else:
                state.execution_path = execution_path
                state.complexity_score = 0.3

            return {
                "current_node": "complexity_assessment",
                "execution_path": execution_path,
                "complexity_score": 0.3,
                "debug_info": {"node": "complexity_assessment", "error": str(e)},
            }

    async def complex_path_planner(state: ComprehensiveState) -> Dict[str, Any]:
        """ReWOO-style planner for complex queries."""
        if isinstance(state, dict):
            state["current_node"] = "complex_planner"
            state["execution_steps"] = state.get("execution_steps", []) + [
                "complex_planner"
            ]
            user_query = state.get("user_query", "")
            routing_decision = state.get("routing_decision", "strength_coach")
        else:
            state.current_node = "complex_planner"
            state.execution_steps.append("complex_planner")
            user_query = state.user_query if hasattr(state, "user_query") else ""
            routing_decision = (
                state.routing_decision
                if hasattr(state, "routing_decision")
                else "strength_coach"
            )

        try:
            # Create ReWOO-style plan based on domain
            domain_step_patterns = {
                "strength_coach": [
                    "Exercise Selection and Assessment",
                    "Program Structure Design",
                    "Progression Planning",
                    "Safety and Injury Prevention",
                    "Synthesis and Finalization",
                ],
                "nutrition_coach": [
                    "Nutritional Assessment",
                    "Meal Planning Strategy",
                    "Macro and Micronutrient Optimization",
                    "Supplement Recommendations",
                    "Integration and Final Plan",
                ],
                "cardio_coach": [
                    "Cardiovascular Assessment",
                    "Training Zone Determination",
                    "Program Periodization",
                    "Recovery Integration",
                    "Complete Program Synthesis",
                ],
                "cycling_coach": [
                    "Cycling Assessment",
                    "Route and Training Planning",
                    "Equipment Optimization",
                    "Performance Tracking",
                    "Complete Cycling Plan",
                ],
                "recovery_coach": [
                    "Recovery Assessment",
                    "Sleep Optimization Planning",
                    "Active Recovery Design",
                    "Stress Management Integration",
                    "Comprehensive Recovery Protocol",
                ],
                "mental_coach": [
                    "Mental State Assessment",
                    "Goal Setting and Visualization",
                    "Performance Psychology Techniques",
                    "Motivation and Mindset Building",
                    "Mental Training Integration",
                ],
            }

            steps = domain_step_patterns.get(
                routing_decision, domain_step_patterns["strength_coach"]
            )

            plan = f"ReWOO Plan for {routing_decision} - Complex Query: {user_query}\n\nPlanned Steps:\n"
            for i, step in enumerate(steps, 1):
                plan += f"{i}. {step}\n"

            if isinstance(state, dict):
                state["rewoo_plan"] = plan
                state["rewoo_steps"] = steps
                state["current_rewoo_step"] = 0
            else:
                state.rewoo_plan = plan
                state.rewoo_steps = steps
                state.current_rewoo_step = 0

            return {
                "current_node": "complex_planner",
                "rewoo_plan": plan,
                "rewoo_steps": steps,
                "debug_info": {"node": "complex_planner", "steps_count": len(steps)},
            }

        except Exception as e:
            logger.error(f"Error in complex planner: {e}")
            return {
                "current_node": "complex_planner",
                "debug_info": {"node": "complex_planner", "error": str(e)},
            }

    async def complex_path_executor(state: ComprehensiveState) -> Dict[str, Any]:
        """ReWOO-style executor for complex queries."""
        if isinstance(state, dict):
            state["current_node"] = "complex_executor"
            state["execution_steps"] = state.get("execution_steps", []) + [
                "complex_executor"
            ]
        else:
            state.current_node = "complex_executor"
            state.execution_steps.append("complex_executor")

        # This node coordinates the execution of ReWOO steps
        # For now, it passes through to the router which will handle coach selection
        return {
            "current_node": "complex_executor",
            "debug_info": {
                "node": "complex_executor",
                "action": "coordinating_execution",
            },
        }

    async def complex_path_router(state: ComprehensiveState) -> Dict[str, Any]:
        """ReWOO-style router for complex queries."""
        if isinstance(state, dict):
            state["current_node"] = "complex_router"
            state["execution_steps"] = state.get("execution_steps", []) + [
                "complex_router"
            ]
            user_query = state.get("user_query", "")
            coach_responses = state.get("coach_responses", {})
            routing_decision = state.get("routing_decision", "strength_coach")
        else:
            state.current_node = "complex_router"
            state.execution_steps.append("complex_router")
            user_query = state.user_query if hasattr(state, "user_query") else ""
            coach_responses = state.coach_responses
            routing_decision = (
                state.routing_decision
                if hasattr(state, "routing_decision")
                else "strength_coach"
            )

        # Check if we already have coach responses - if so, go to aggregation
        if coach_responses:
            logger.info(
                f"🔀 COMPLEX ROUTER: Found existing responses from {list(coach_responses.keys())}, routing to aggregation"
            )
            routing_decision = "aggregate_responses"
        else:
            # Use existing routing decision from complexity assessment or planning
            logger.info(
                f"🎯 COMPLEX ROUTER: Using routing decision: {routing_decision}"
            )

        return {
            "current_node": "complex_router",
            "routing_decision": routing_decision,
            "debug_info": {"node": "complex_router", "decision": routing_decision},
        }

    # Build the graph
    builder = StateGraph(ComprehensiveState)

    # Add core nodes with new ReWOO+Reflection pattern
    builder.add_node("reasoning", enhanced_reasoning_node)
    builder.add_node("early_intent_classifier", early_intent_classifier)
    builder.add_node("planning", enhanced_planning_node)
    builder.add_node("complexity_assessment", complexity_assessment_node)

    # Add GraphRAG nodes
    builder.add_node("knowledge_assessment", knowledge_assessment_node)
    builder.add_node("graphrag_retrieval", graphrag_retrieval_node)

    # Simple and Complex path nodes
    builder.add_node("complex_planner", complex_path_planner)
    builder.add_node("complex_executor", complex_path_executor)
    builder.add_node("complex_router", complex_path_router)

    # Coach nodes and aggregation (REFLECTION REMOVED)
    builder.add_node("multi_coach_executor", multi_coach_executor_node)
    builder.add_node("aggregation", aggregation_node)
    builder.add_node("clarification", head_coach_clarification_node)
    builder.add_node("automated_greeting", automated_greeting_node)

    # Add React coach nodes
    for coach_name, executor in react_coaches.items():

        async def create_coach_node(executor_instance, prompt_loader_instance):
            """Create a coach node from a ReAct executor, ensuring prompts are cached."""

            async def coach_node(state: ComprehensiveState) -> Dict[str, Any]:
                """Coach node that uses ReAct executor."""
                logger.info(
                    f"🏃‍♂️ COACH: [{executor_instance.coach_name}] Starting execution"
                )

                # Use cached prompt
                prompt_config = await get_cached_prompt_config(
                    executor_instance.coach_name, prompt_loader_instance
                )
                if prompt_config:
                    executor_instance.coach_prompt = prompt_config.prompt.system
                else:
                    logger.warning(
                        f"Failed to load prompt for {executor_instance.coach_name}, using default."
                    )
                    # The executor already has a default prompt from initialization

                if isinstance(state, dict):
                    user_query = state.get("user_query", "")
                    messages = state.get("messages", [])
                    user_profile = state.get("user_profile")
                else:
                    user_query = getattr(state, "user_query", "")
                    messages = getattr(state, "messages", [])
                    user_profile = getattr(state, "user_profile", None)

                # Get the latest user message if user_query is empty
                if not user_query and messages:
                    for msg in reversed(messages):
                        if isinstance(msg, HumanMessage):
                            user_query = msg.content
                            break

                if not user_query:
                    return {
                        "current_node": coach_name,
                        "final_response": "I didn't receive a clear question. How can I help you?",
                        "debug_info": {"node": coach_name, "action": "no_query"},
                    }

                try:
                    # Execute the ReAct pattern with optimized model
                    result = await executor_instance.execute(
                        user_message=user_query,
                        conversation_history=messages,
                        user_profile=user_profile,
                    )

                    final_answer = result.get("final_answer", "I'd be happy to help!")

                    # Ensure final response is a string
                    if not isinstance(final_answer, str):
                        final_answer = json.dumps(final_answer)

                    return {
                        "current_node": coach_name,
                        "final_response": final_answer,
                        "coach_responses": {coach_name: final_answer},
                        "debug_info": {
                            "node": coach_name,
                            "success": str(result.get("success", False)),
                            "iterations": str(result.get("iterations", 0)),
                        },
                    }

                except Exception as e:
                    logger.error(f"Error in {coach_name}: {e}")
                    return {
                        "current_node": coach_name,
                        "final_response": "I apologize, but I encountered an error. Please try again.",
                        "debug_info": {"node": coach_name, "error": str(e)},
                    }

            return coach_node

        coach_node = await create_coach_node(executor, prompt_loader)
        builder.add_node(coach_name, coach_node)

    # Define the main flow: Start → Reasoning → Early Intent Classification
    builder.add_edge(START, "reasoning")
    builder.add_edge("reasoning", "early_intent_classifier")

    # Early Intent Classification routing
    def route_after_early_intent(state: ComprehensiveState) -> str:
        """Route after early intent classification - handle greetings early or continue to planning."""
        if isinstance(state, dict):
            early_intent = state.get("early_intent", "complex_query")
            routing_decision = state.get("routing_decision", "continue_processing")
        else:
            early_intent = getattr(state, "early_intent", "complex_query")
            routing_decision = getattr(state, "routing_decision", "continue_processing")

        logger.info(
            f"🔀 GRAPH: [EARLY_ROUTING] Early intent: {early_intent}, routing decision: {routing_decision}"
        )

        if routing_decision == "automated_greeting":
            logger.info(
                f"  - Simple greeting/unclear detected: routing to automated_greeting"
            )
            return "automated_greeting"
        else:
            logger.info(f"  - Complex query detected: continue to planning")
            return "planning"

    builder.add_conditional_edges(
        "early_intent_classifier",
        route_after_early_intent,
        {
            "automated_greeting": "automated_greeting",
            "planning": "planning",
        },
    )

    # Planning routing - short-circuit to clarification if needed, otherwise continue to complexity assessment
    def route_after_planning(state: ComprehensiveState) -> str:
        """Route after planning - check if clarification is needed to short-circuit expensive processing."""
        if isinstance(state, dict):
            routing_decision = state.get("routing_decision", "continue")
        else:
            routing_decision = getattr(state, "routing_decision", "continue")

        logger.info(
            f"🔀 GRAPH: [PLANNING_ROUTING] Planning decision: {routing_decision}"
        )

        if routing_decision == "clarification":
            logger.info(
                "  - Clarification needed: Short-circuiting to clarification (skipping complexity/knowledge/graphrag)"
            )
            return "clarification"
        else:
            logger.info(
                "  - No clarification needed: Continue to complexity assessment"
            )
            return "complexity_assessment"

    builder.add_conditional_edges(
        "planning",
        route_after_planning,
        {
            "clarification": "clarification",
            "complexity_assessment": "complexity_assessment",
        },
    )

    # Complexity Assessment routes to Knowledge Assessment
    def route_after_complexity_assessment(state: ComprehensiveState) -> str:
        """Route to knowledge assessment after complexity assessment."""
        logger.info(
            f"🔀 GRAPH: [ROUTING] Complexity assessment complete, routing to knowledge assessment"
        )
        return "knowledge_assessment"

    builder.add_conditional_edges(
        "complexity_assessment",
        route_after_complexity_assessment,
        {
            "knowledge_assessment": "knowledge_assessment",
        },
    )

    # Knowledge Assessment routes to GraphRAG Retrieval
    builder.add_edge("knowledge_assessment", "graphrag_retrieval")

    # GraphRAG Retrieval routes to Simple or Complex path
    def route_after_graphrag_retrieval(state: ComprehensiveState) -> str:
        """Route to simple coach, multi-coach, or complex path after GraphRAG retrieval."""
        if isinstance(state, dict):
            force_clarification = state.get("force_clarification", False)
            if force_clarification:
                return "clarification"
            execution_path = state.get("execution_path", "simple")
            complexity_score = state.get("complexity_score", 0.3)
            routing_decision = state.get("routing_decision", "strength_coach")
            required_coaches = state.get("required_coaches", [])
        else:
            force_clarification = state.force_clarification
            if force_clarification:
                return "clarification"
            execution_path = (
                state.execution_path if hasattr(state, "execution_path") else "simple"
            )
            complexity_score = getattr(state, "complexity_score", 0.3)
            routing_decision = (
                state.routing_decision
                if hasattr(state, "routing_decision")
                else "strength_coach"
            )
            required_coaches = (
                state.required_coaches if hasattr(state, "required_coaches") else []
            )

        logger.info(
            f"🔀 GRAPH: [ROUTING] GraphRAG retrieval complete, routing based on complexity:"
        )
        logger.info(f"  - Execution path: {execution_path}")
        logger.info(f"  - Complexity score: {complexity_score}")
        logger.info(f"  - Routing decision: {routing_decision}")
        logger.info(f"  - Required coaches: {required_coaches}")

        # Check for multi-coach routing first
        if routing_decision == "multi_coach" or len(required_coaches) > 1:
            logger.info(f"  - Multi-coach path: routing to multi_coach_executor")
            return "multi_coach_executor"
        elif routing_decision == "clarification":
            logger.info(f"  - Clarification required: routing to clarification_node")
            return "clarification"
        elif execution_path == "simple":
            # For simple single-coach paths, route directly to the appropriate coach
            target_coach = route_to_coach(state)
            logger.info(f"  - Simple path: routing directly to {target_coach}")
            return target_coach
        else:
            # For complex paths, go to complex planner
            logger.info(f"  - Complex path: routing to complex_planner")
            return "complex_planner"

    builder.add_conditional_edges(
        "graphrag_retrieval",
        route_after_graphrag_retrieval,
        {
            "complex_planner": "complex_planner",
            "multi_coach_executor": "multi_coach_executor",
            "strength_coach": "strength_coach",
            "cardio_coach": "cardio_coach",
            "cycling_coach": "cycling_coach",
            "nutrition_coach": "nutrition_coach",
            "recovery_coach": "recovery_coach",
            "mental_coach": "mental_coach",
            "clarification": "clarification",
            "aggregation": "aggregation",
        },
    )

    # Complex path: Planner → Executor → Router
    builder.add_edge("complex_planner", "complex_executor")
    builder.add_edge("complex_executor", "complex_router")

    # Both routers go to coaches
    def route_to_coach(state: ComprehensiveState) -> str:
        """Route to the appropriate coach based on routing decision."""
        if isinstance(state, dict):
            decision = state.get("routing_decision", "strength_coach")
            current_node = state.get("current_node", "unknown")
        else:
            decision = (
                state.routing_decision
                if hasattr(state, "routing_decision")
                else "strength_coach"
            )
            current_node = getattr(state, "current_node", "unknown")

        logger.info(f"🎯 GRAPH: [ROUTING] Coach routing from {current_node}:")
        logger.info(f"  - Routing decision: {decision}")

        # Map routing decisions to coach nodes
        coach_mapping = {
            "strength_coach": "strength_coach",
            "running_coach": "cardio_coach",  # Running is part of cardio
            "cardio_coach": "cardio_coach",
            "cycling_coach": "cycling_coach",
            "nutrition_coach": "nutrition_coach",
            "recovery_coach": "recovery_coach",
            "mental_coach": "mental_coach",
            "clarification": "clarification",
            "aggregate_responses": "aggregation",
        }

        target_coach = coach_mapping.get(decision, "strength_coach")
        logger.info(f"  - Target coach: {target_coach}")

        return target_coach

    # Complex router to coaches and multi-coach
    def route_from_complex_router(state: ComprehensiveState) -> str:
        """Route from complex router to appropriate destination."""
        if isinstance(state, dict):
            routing_decision = state.get("routing_decision", "strength_coach")
            required_coaches = state.get("required_coaches", [])
        else:
            routing_decision = (
                state.routing_decision
                if hasattr(state, "routing_decision")
                else "strength_coach"
            )
            required_coaches = (
                state.required_coaches if hasattr(state, "required_coaches") else []
            )

        # Check for multi-coach routing
        if routing_decision == "multi_coach" or len(required_coaches) > 1:
            return "multi_coach_executor"
        elif routing_decision == "aggregate_responses":
            return "aggregation"
        else:
            return route_to_coach(state)

    builder.add_conditional_edges(
        "complex_router",
        route_from_complex_router,
        {
            "multi_coach_executor": "multi_coach_executor",
            "strength_coach": "strength_coach",
            "cardio_coach": "cardio_coach",
            "cycling_coach": "cycling_coach",
            "nutrition_coach": "nutrition_coach",
            "recovery_coach": "recovery_coach",
            "mental_coach": "mental_coach",
            "aggregation": "aggregation",
        },
    )

    # All coaches go to aggregation
    for coach_name in react_coaches.keys():
        builder.add_edge(coach_name, "aggregation")

    # Multi-coach executor always routes to aggregation to prevent infinite loops
    def route_multi_coach_executor(state: ComprehensiveState) -> str:
        """Route from multi-coach executor - always go to aggregation to prevent loops."""
        logger.info(
            "🔀 MULTI_COACH_EXECUTOR: Routing to aggregation (preventing infinite loops)"
        )
        # Always route to aggregation to prevent infinite loops
        return "aggregation"

    builder.add_conditional_edges(
        "multi_coach_executor",
        route_multi_coach_executor,
        {
            "aggregation": "aggregation",
        },
    )

    # Clarification goes directly to END (no aggregation needed)
    builder.add_edge("clarification", END)

    # Automated greeting goes directly to END (no aggregation needed)
    builder.add_edge("automated_greeting", END)

    # Aggregation goes directly to END (reflection removed)
    builder.add_edge("aggregation", END)

    logger.info("✅ Comprehensive coaching graph built successfully (pre-compilation).")
    return builder


# Legacy entry point for LangGraph Studio - renamed to avoid conflicts
async def _legacy_create_studio_graph(config=None) -> CompiledStateGraph:
    """
    Main entry point for LangGraph Studio.

    This function creates the comprehensive coaching graph with all features:
    - ReAct Coach Executor with proper tool input validation
    - All specialized coaches with domain-specific tools
    - Comprehensive tool integration
    - Memory and human-in-the-loop support

    Args:
        config: RunnableConfig object containing configuration
    """
    logger.info("🚀 Deployment test - Creating studio graph with dynamic prompts")

    # Extract configuration from RunnableConfig if provided
    config_dict = {}
    if config and hasattr(config, "configurable"):
        config_dict = config.configurable
    elif isinstance(config, dict):
        config_dict = config

    # Convert dict to ComprehensiveCoachingConfig instance
    coaching_config = ComprehensiveCoachingConfig(**config_dict)

    return await create_comprehensive_coaching_graph(coaching_config)


# Legacy entry point for LangGraph Studio - renamed to avoid conflicts
async def _legacy_create_studio_graph(config=None) -> CompiledStateGraph:
    """
    Legacy entry point for LangGraph Studio.

    This function creates the comprehensive coaching graph with all features:
    - ReAct Coach Executor with proper tool input validation
    - All specialized coaches with domain-specific tools
    - Comprehensive tool integration
    - Memory and human-in-the-loop support

    Args:
        config: RunnableConfig object containing configuration
    """
    logger.info("🚀 Deployment test - Creating studio graph with dynamic prompts")

    # Extract configuration from RunnableConfig if provided
    config_dict = {}
    if config and hasattr(config, "configurable"):
        config_dict = config.configurable
    elif isinstance(config, dict):
        config_dict = config

    # Convert dict to ComprehensiveCoachingConfig instance
    coaching_config = ComprehensiveCoachingConfig(**config_dict)

    return await create_comprehensive_coaching_graph(coaching_config)


# Example usage and testing
async def create_test_graph(config=None):
    """Create a test graph for development and LangGraph server discovery."""
    # Extract configuration from RunnableConfig if provided
    config_dict = {
        "user_id": "test_user",
        "mongodb_uri": "mongodb://localhost:27017",
        "thread_id": "test_thread",
        "enable_memory": False,
        "use_react_agents": True,
        "max_iterations": 3,
        "enable_human_feedback": True,
    }

    if config and hasattr(config, "configurable"):
        config_dict.update(config.configurable)
    elif isinstance(config, dict):
        config_dict.update(config)

    return await create_comprehensive_coaching_graph(config_dict)


if __name__ == "__main__":
    import asyncio

    async def main():
        graph = await create_test_graph()
        print("✅ Comprehensive coaching graph created successfully!")
        print(f"Graph has {len(graph.nodes)} nodes")

    asyncio.run(main())
