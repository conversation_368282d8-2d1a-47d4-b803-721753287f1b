"""
Production-Ready ReWOO+Reflection Coaching Graph.

Combines the ReWOO (Reasoning Without Observation) parallel coordination pattern
with Phase 1 reflection capabilities for the ultimate coaching experience.

This graph provides:
- Intelligent query analysis and multi-domain task decomposition (ReWOO Planner)
- Parallel execution of coaching specialists (<PERSON><PERSON><PERSON><PERSON> Worker Coordinator)
- Sophisticated response synthesis (ReWOO Solver)
- Safety-focused reflection and iterative improvement (Phase 1 Reflection)
"""

import logging
from typing import Any, Dict, Optional, Union

from langchain_core.messages import BaseMessage, HumanMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.graph.state import CompiledStateGraph

from athlea_langgraph.agents.head_coach import head_coach_node
from athlea_langgraph.agents.planning_node import planning_node

# Import standard coaching components for fallback
from athlea_langgraph.agents.reasoning_node import reasoning_node

# Import reflection components
from athlea_langgraph.agents.reflection_agent import reflection_node
from athlea_langgraph.agents.reflection_controller import (
    reflection_finalization_node,
    reflection_router,
    safety_gate_router,
)
from athlea_langgraph.agents.response_regenerator import response_regeneration_node

# Import ReWOO components
from athlea_langgraph.agents.rewoo_planner import rewoo_planner_node
from athlea_langgraph.agents.rewoo_solver import rewoo_solver_node
from athlea_langgraph.agents.rewoo_worker_coordinator import (
    rewoo_worker_coordinator_node,
)
from athlea_langgraph.agents.coach_manager import strength_coach_node
from athlea_langgraph.states.reflection_state import (
    create_reflection_config,
    enhance_state_with_reflection,
)

# Import state management
from athlea_langgraph.states.rewoo_state import (
    ReWOOState,
    create_default_rewoo_config,
    create_initial_rewoo_state,
    enhance_state_with_rewoo,
)

logger = logging.getLogger(__name__)


def create_rewoo_reflection_state_graph() -> StateGraph:
    """
    Create the state graph with combined ReWOO+Reflection state.

    Returns:
        StateGraph configured with enhanced state
    """
    # Create a combined state that supports both ReWOO and Reflection
    # We'll use ReWOOState as the base since it extends AgentState
    return StateGraph(ReWOOState)


def intelligent_router(state: ReWOOState) -> str:
    """
    Intelligent router to decide between ReWOO and standard coaching.

    Args:
        state: Current state

    Returns:
        Next node to execute
    """
    user_query = state.get("user_query", "")

    # Determine if query is complex enough for ReWOO
    rewoo_triggers = [
        # Multi-domain indicators
        "nutrition and strength",
        "diet and workout",
        "cardio and recovery",
        "complete plan",
        "comprehensive",
        "full program",
        # Multiple question indicators
        " and ",
        " also ",
        " plus ",
        "both ",
        "all ",
        # Complexity indicators
        "detailed",
        "advanced",
        "professional",
        "competition",
        "multiple",
    ]

    query_lower = user_query.lower()
    should_use_rewoo = any(trigger in query_lower for trigger in rewoo_triggers)

    # Also check query length (longer queries often need multi-domain responses)
    if len(user_query) > 100:
        should_use_rewoo = True

    if should_use_rewoo:
        logger.info("🎯 Routing to ReWOO: Multi-domain query detected")
        return "rewoo_planner"
    else:
        logger.info("🔄 Routing to standard coaching: Simple query")
        return "reasoning"


async def intelligent_router_node(state: ReWOOState) -> Dict[str, Any]:
    """
    Router node wrapper for conditional edges.

    Args:
        state: Current state

    Returns:
        State (routing happens in conditional edges)
    """
    return {}


async def reflection_router_node(state: ReWOOState) -> Dict[str, Any]:
    """Wrapper node for reflection router logic."""
    return {}


async def safety_gate_node(state: ReWOOState) -> Dict[str, Any]:
    """Wrapper node for safety gate logic."""
    return {}


def add_core_nodes(graph: StateGraph) -> None:
    """
    Add core routing and processing nodes.

    Args:
        graph: StateGraph to add nodes to
    """
    # Core routing
    graph.add_node("intelligent_router", intelligent_router_node)

    # Standard coaching path (fallback)
    graph.add_node("reasoning", reasoning_node)
    graph.add_node("planning", planning_node)
    graph.add_node("head_coach", head_coach_node)
    graph.add_node("strength_coach", strength_coach_node)


def add_rewoo_nodes(graph: StateGraph) -> None:
    """
    Add ReWOO pattern nodes.

    Args:
        graph: StateGraph to add ReWOO nodes to
    """
    # ReWOO workflow nodes
    graph.add_node("rewoo_planner", rewoo_planner_node)
    graph.add_node("rewoo_worker_coordinator", rewoo_worker_coordinator_node)
    graph.add_node("rewoo_solver", rewoo_solver_node)


def add_reflection_nodes(graph: StateGraph) -> None:
    """
    Add reflection pattern nodes.

    Args:
        graph: StateGraph to add reflection nodes to
    """
    # Reflection workflow nodes
    graph.add_node("reflection", reflection_node)
    graph.add_node("regeneration", response_regeneration_node)
    graph.add_node("reflection_finalization", reflection_finalization_node)

    # Reflection routing nodes
    graph.add_node("reflection_router", reflection_router_node)
    graph.add_node("safety_gate", safety_gate_node)


def add_core_edges(graph: StateGraph) -> None:
    """
    Add core workflow edges.

    Args:
        graph: StateGraph to add core edges to
    """
    # Start with intelligent routing
    graph.add_edge(START, "intelligent_router")

    # Intelligent router decides between ReWOO and standard coaching
    graph.add_conditional_edges(
        "intelligent_router",
        intelligent_router,
        {
            "rewoo_planner": "rewoo_planner",
            "reasoning": "reasoning",
        },
    )

    # Standard coaching path (simplified for production)
    graph.add_edge("reasoning", "planning")
    graph.add_edge("planning", "head_coach")
    graph.add_edge("head_coach", "strength_coach")
    graph.add_edge("strength_coach", "reflection_router")


def add_rewoo_edges(graph: StateGraph) -> None:
    """
    Add ReWOO workflow edges.

    Args:
        graph: StateGraph to add ReWOO edges to
    """
    # ReWOO pattern: Plan -> Worker Coordinator -> Solver
    graph.add_edge("rewoo_planner", "rewoo_worker_coordinator")
    graph.add_edge("rewoo_worker_coordinator", "rewoo_solver")

    # ReWOO solver goes to reflection for quality improvement
    graph.add_edge("rewoo_solver", "reflection_router")


def add_reflection_edges(graph: StateGraph) -> None:
    """
    Add reflection workflow edges.

    Args:
        graph: StateGraph to add reflection edges to
    """
    # Reflection router decides: reflect, regenerate, or finalize
    graph.add_conditional_edges(
        "reflection_router",
        reflection_router,
        {
            "reflect": "reflection",
            "regenerate": "regeneration",
            "finalize": "reflection_finalization",
        },
    )

    # After reflection, go to safety gate
    graph.add_edge("reflection", "safety_gate")

    # Safety gate decides: regenerate or finalize
    graph.add_conditional_edges(
        "safety_gate",
        safety_gate_router,
        {
            "regenerate": "regeneration",
            "finalize": "reflection_finalization",
        },
    )

    # After regeneration, finalize
    graph.add_edge("regeneration", "reflection_finalization")

    # Finalization ends the workflow
    graph.add_edge("reflection_finalization", END)


def create_production_state(
    user_query: str,
    user_profile: Optional[Dict[str, Any]] = None,
    rewoo_config: Optional[Dict[str, Any]] = None,
    reflection_config: Optional[Dict[str, Any]] = None,
) -> ReWOOState:
    """
    Create a production-ready initial state with both ReWOO and Reflection capabilities.

    Args:
        user_query: User's coaching query
        user_profile: User profile and preferences
        rewoo_config: ReWOO configuration
        reflection_config: Reflection configuration

    Returns:
        Combined ReWOO+Reflection state
    """
    # Create base ReWOO state
    rewoo_state = create_initial_rewoo_state(
        user_query=user_query,
        user_profile=user_profile,
        config=rewoo_config or create_default_rewoo_config(),
    )

    # Enhance with reflection capabilities
    reflection_state = enhance_state_with_reflection(
        dict(rewoo_state),
        reflection_config=reflection_config or create_reflection_config(),
    )

    # Enable reflection integration in ReWOO
    reflection_state["reflection_enabled"] = True
    reflection_state["post_rewoo_reflection"] = False

    # Set up initial message
    if user_query:
        reflection_state["messages"] = [HumanMessage(content=user_query)]

    return ReWOOState(reflection_state)


def create_rewoo_reflection_coaching_graph(
    checkpointer=None,
    user_profile: Optional[Dict[str, Any]] = None,
    rewoo_config: Optional[Dict[str, Any]] = None,
    reflection_config: Optional[Dict[str, Any]] = None,
    enable_reflection: bool = True,
    enable_rewoo: bool = True,
) -> CompiledStateGraph:
    """
    Create a production-ready ReWOO+Reflection coaching graph.

    Args:
        checkpointer: Optional checkpointer for conversation persistence
        user_profile: Optional initial user profile
        rewoo_config: Optional ReWOO configuration
        reflection_config: Optional reflection configuration
        enable_reflection: Whether to enable reflection (default: True)
        enable_rewoo: Whether to enable ReWOO (default: True)

    Returns:
        Compiled StateGraph with ReWOO and reflection capabilities
    """
    logger.info("🚀 Creating production-ready ReWOO+Reflection coaching graph")

    # Create state graph
    graph = create_rewoo_reflection_state_graph()

    # Add all node types
    add_core_nodes(graph)
    add_rewoo_nodes(graph)
    add_reflection_nodes(graph)

    # Add all edge types
    add_core_edges(graph)
    add_rewoo_edges(graph)
    add_reflection_edges(graph)

    # Use provided checkpointer or default
    if checkpointer is None:
        checkpointer = MemorySaver()

    # Compile with configuration
    compiled_graph = graph.compile(
        checkpointer=checkpointer,
        interrupt_before=[],  # No interrupts for production
        interrupt_after=[],
    )

    logger.info("✅ Production ReWOO+Reflection coaching graph compiled successfully")
    return compiled_graph


def create_production_config() -> Dict[str, Any]:
    """
    Create optimized production configuration.

    Returns:
        Production configuration dictionary
    """
    return {
        "rewoo_config": {
            "max_parallel_workers": 6,  # All domains
            "task_timeout": 45,  # Longer timeout for production
            "enable_task_dependencies": True,
            "synthesis_strategy": "comprehensive",
            "fallback_to_sequential": True,
            "reflection_integration": True,
            "quality_threshold": 0.75,  # Higher quality threshold
            "enable_caching": True,
        },
        "reflection_config": {
            "max_reflections": 2,
            "safety_threshold": 0.85,  # Higher safety threshold
            "enable_safety_validation": True,
            "reflection_criteria": [
                "safety",
                "contraindications",
                "injury_prevention",
                "evidence_based",
                "personalization",
            ],
        },
    }


# Convenience functions for different use cases
def create_development_graph(checkpointer=None) -> CompiledStateGraph:
    """Create graph optimized for development and testing."""
    dev_config = {
        "rewoo_config": {
            "max_parallel_workers": 3,
            "task_timeout": 20,
            "reflection_integration": True,
            "quality_threshold": 0.6,
        },
        "reflection_config": {
            "max_reflections": 1,
            "safety_threshold": 0.7,
        },
    }

    return create_rewoo_reflection_coaching_graph(
        checkpointer=checkpointer,
        **dev_config,
    )


def create_production_graph(checkpointer=None) -> CompiledStateGraph:
    """Create graph optimized for production use."""
    prod_config = create_production_config()

    return create_rewoo_reflection_coaching_graph(
        checkpointer=checkpointer,
        **prod_config,
    )


def create_high_quality_graph(checkpointer=None) -> CompiledStateGraph:
    """Create graph optimized for maximum quality (slower but better)."""
    quality_config = {
        "rewoo_config": {
            "max_parallel_workers": 6,
            "task_timeout": 60,
            "reflection_integration": True,
            "quality_threshold": 0.85,
            "synthesis_strategy": "comprehensive",
        },
        "reflection_config": {
            "max_reflections": 3,
            "safety_threshold": 0.9,
            "reflection_criteria": [
                "safety",
                "contraindications",
                "injury_prevention",
                "evidence_based",
                "personalization",
                "clarity",
                "completeness",
            ],
        },
    }

    return create_rewoo_reflection_coaching_graph(
        checkpointer=checkpointer,
        **quality_config,
    )


# Studio-compatible wrapper functions for LangGraph Studio
async def _archived_create_studio_production_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """
    Studio-compatible wrapper for creating production ReWOO+Reflection graph.

    Args:
        config: Optional dictionary with configuration values for Studio editing

    Returns:
        Compiled StateGraph optimized for production use
    """
    logger = logging.getLogger(__name__)  # Define logger within function scope
    logger.info("🚀 Creating studio production ReWOO+Reflection graph")

    # Use default production config if none provided
    if config is None:
        config = {}

    # Extract relevant config values or use defaults
    checkpointer = None  # Studio will handle checkpointer
    if config.get("mongodb_uri") and config.get("enable_memory", True):
        try:
            from langgraph.checkpoint.mongo import MongoDBSaver

            checkpointer = MongoDBSaver(
                connection_string=config["mongodb_uri"], db_name="athlea_production"
            )
        except ImportError:
            logger.warning("MongoDB not available, using MemorySaver")
            checkpointer = MemorySaver()

    # Create production configuration
    prod_config = create_production_config()

    # Override with any user-provided config values
    if config.get("max_parallel_workers"):
        prod_config["rewoo_config"]["max_parallel_workers"] = config[
            "max_parallel_workers"
        ]
    if config.get("task_timeout"):
        prod_config["rewoo_config"]["task_timeout"] = config["task_timeout"]
    if config.get("max_reflections"):
        prod_config["reflection_config"]["max_reflections"] = config["max_reflections"]
    if config.get("safety_threshold"):
        prod_config["reflection_config"]["safety_threshold"] = config[
            "safety_threshold"
        ]

    return create_rewoo_reflection_coaching_graph(
        checkpointer=checkpointer,
        **prod_config,
    )


async def _archived_create_studio_development_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """
    Studio-compatible wrapper for creating development ReWOO+Reflection graph.

    Args:
        config: Optional dictionary with configuration values for Studio editing

    Returns:
        Compiled StateGraph optimized for development and testing
    """
    logger = logging.getLogger(__name__)  # Define logger within function scope
    logger.info("🚀 Creating studio development ReWOO+Reflection graph")

    if config is None:
        config = {}

    # Development uses memory checkpointer by default
    checkpointer = MemorySaver()

    # Create development configuration
    dev_config = {
        "rewoo_config": {
            "max_parallel_workers": config.get("max_parallel_workers", 3),
            "task_timeout": config.get("task_timeout", 20),
            "reflection_integration": True,
            "quality_threshold": 0.6,
        },
        "reflection_config": {
            "max_reflections": config.get("max_reflections", 1),
            "safety_threshold": config.get("safety_threshold", 0.7),
        },
    }

    return create_rewoo_reflection_coaching_graph(
        checkpointer=checkpointer,
        **dev_config,
    )


async def _archived_create_studio_high_quality_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """
    Studio-compatible wrapper for creating high-quality ReWOO+Reflection graph.

    Args:
        config: Optional dictionary with configuration values for Studio editing

    Returns:
        Compiled StateGraph optimized for maximum quality
    """
    logger = logging.getLogger(__name__)  # Define logger within function scope
    logger.info("🚀 Creating studio high-quality ReWOO+Reflection graph")

    if config is None:
        config = {}

    # Use memory checkpointer for high-quality testing
    checkpointer = MemorySaver()
    if config.get("mongodb_uri") and config.get("enable_memory", True):
        try:
            from langgraph.checkpoint.mongo import MongoDBSaver

            checkpointer = MongoDBSaver(
                connection_string=config["mongodb_uri"], db_name="athlea_high_quality"
            )
        except ImportError:
            logger.warning("MongoDB not available, using MemorySaver")
            checkpointer = MemorySaver()

    # Create high-quality configuration
    quality_config = {
        "rewoo_config": {
            "max_parallel_workers": config.get("max_parallel_workers", 6),
            "task_timeout": config.get("task_timeout", 60),
            "reflection_integration": True,
            "quality_threshold": 0.85,
            "synthesis_strategy": "comprehensive",
        },
        "reflection_config": {
            "max_reflections": config.get("max_reflections", 3),
            "safety_threshold": config.get("safety_threshold", 0.9),
            "reflection_criteria": config.get(
                "reflection_criteria",
                [
                    "safety",
                    "contraindications",
                    "injury_prevention",
                    "evidence_based",
                    "personalization",
                    "clarity",
                    "completeness",
                ],
            ),
        },
    }

    return create_rewoo_reflection_coaching_graph(
        checkpointer=checkpointer,
        **quality_config,
    )
