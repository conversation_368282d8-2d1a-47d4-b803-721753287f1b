"""
Individual Coach Graphs

This module creates individual graphs for each coach that reuse the same
domain coaches as the optimized coaching graph but with simplified routing.

Instead of duplicating the entire comprehensive graph, we create focused
graphs that:
1. Start with a reasoning node to analyze the user's request
2. Continue to the specified domain coach
3. Have access to the same tools and capabilities
4. Use the same underlying domain coaches
5. Follow a simple flow: START → reasoning → domain_coach → END

This allows each coach to work independently while maintaining the same
setup and capabilities as the comprehensive system, with the added benefit
of request analysis through the reasoning node.
"""

import asyncio
import logging
import os
import time
from typing import Any, Dict, List, Optional, Union

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.runnables import RunnablePassthrough, RunnableLambda
from langchain_core.output_parsers import StrOutputParser
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.graph.state import CompiledStateGraph
from pydantic import BaseModel, Field

# Import domain coaches directly (coach-only architecture)
from ..agents.cardio_coach import cardio_coach
from ..agents.mental_coach import mental_coach
from ..agents.nutrition_coach import nutrition_coach
from ..agents.recovery_coach import recovery_coach
from ..agents.strength_coach import strength_coach

# Import reasoning node
from ..nodes.reasoning_node import reasoning_node

# Import the same state and config as main coaching graph
from ..states.optimized_state import OptimizedCoachingState
from .coaching_graph import ComprehensiveCoachingConfig

# Import Azure OpenAI service
from ..services.azure_openai_service import create_azure_chat_openai

logger = logging.getLogger(__name__)

# Available coaches (same as main coaching graph)
AVAILABLE_COACHES = [
    "strength_coach",
    "cardio_coach",
    "cycling_coach",
    "nutrition_coach",
    "recovery_coach",
    "mental_coach",
]

# Domain coaches mapping (coach-only architecture)
DOMAIN_COACHES = {
    "strength_coach": strength_coach,
    "cardio_coach": cardio_coach,
    "nutrition_coach": nutrition_coach,
    "recovery_coach": recovery_coach,
    "mental_coach": mental_coach,
}

# Global initialization lock
_initialization_lock = asyncio.Lock()
_coaches_initialized = False


async def initialize_domain_coaches():
    """Initialize domain coaches with their tools (coach-only architecture)."""
    global _coaches_initialized

    async with _initialization_lock:
        if _coaches_initialized:
            logger.info("✅ INDIVIDUAL_COACH: Domain coaches already initialized.")
            return

        logger.info("🚀 INDIVIDUAL_COACH: Initializing domain coaches...")

        # Ensure all coaches have their tools loaded
        for coach_name, coach in DOMAIN_COACHES.items():
            if not coach.tools:
                await coach.get_domain_tools()
            logger.info(
                f"✅ INDIVIDUAL_COACH: {coach_name} loaded with {len(coach.tools)} tools"
            )

        _coaches_initialized = True
        logger.info("✅ INDIVIDUAL_COACH: Domain coaches initialized successfully.")


async def create_individual_coach_graph(
    coach_name: str,
    config: Optional[Union[Dict[str, Any], ComprehensiveCoachingConfig]] = None,
) -> CompiledStateGraph:
    """
    Create an individual coach graph using domain coaches directly with reasoning.

    Args:
        coach_name: The specific coach to create a graph for (e.g., "strength_coach")
        config: Optional configuration parameters

    Returns:
        Compiled graph for the individual coach with reasoning node
    """
    if coach_name not in AVAILABLE_COACHES:
        raise ValueError(
            f"Coach '{coach_name}' not available. Choose from: {AVAILABLE_COACHES}"
        )

    logger.info(f"🏗️ INDIVIDUAL_COACH: Creating graph with reasoning for {coach_name}")

    if config is None:
        config = {}

    # Parse configuration (same as main coaching graph)
    if isinstance(config, ComprehensiveCoachingConfig):
        coaching_config = config
    elif isinstance(config, dict):
        coaching_config = ComprehensiveCoachingConfig(**config)
    else:
        coaching_config = ComprehensiveCoachingConfig()

    # Initialize domain coaches
    await initialize_domain_coaches()

    # Handle cycling_coach (uses cardio_coach for now)
    if coach_name == "cycling_coach":
        if "cycling_coach" not in DOMAIN_COACHES:
            logger.info("🚴 INDIVIDUAL_COACH: Using cardio_coach for cycling_coach")
            coach_instance = cardio_coach
        else:
            coach_instance = DOMAIN_COACHES["cycling_coach"]
    else:
        if coach_name not in DOMAIN_COACHES:
            raise ValueError(f"Domain coach not found for {coach_name}")
        coach_instance = DOMAIN_COACHES[coach_name]

    # Build the individual coach graph with reasoning
    uncompiled_graph = build_individual_coach_graph_with_reasoning(
        coach_name, coach_instance
    )

    # Compile with memory if enabled
    if coaching_config.enable_memory:
        logger.info(f"🧠 INDIVIDUAL_COACH: Memory enabled for {coach_name}")
        memory = MemorySaver()
        graph = uncompiled_graph.compile(checkpointer=memory)
    else:
        logger.info(f"🧠 INDIVIDUAL_COACH: Memory disabled for {coach_name}")
        graph = uncompiled_graph.compile()

    logger.info(
        f"✅ INDIVIDUAL_COACH: {coach_name} graph with reasoning compiled successfully"
    )
    return graph


def build_individual_coach_graph_with_reasoning(
    coach_name: str, coach_instance
) -> StateGraph:
    """
    Build the individual coach graph structure with reasoning node.

    Enhanced flow: START → reasoning → coach → END
    """
    logger.info(
        f"🏗️ INDIVIDUAL_COACH: Building graph structure with reasoning for {coach_name}"
    )

    async def individual_reasoning_node(
        state: OptimizedCoachingState,
    ) -> Dict[str, Any]:
        """
        Use the same comprehensive reasoning node as the coaching graph.
        This ensures consistent reasoning quality across individual and coaching graphs.
        """
        logger.info(
            f"🧠 INDIVIDUAL_REASONING: [{coach_name}] Starting comprehensive reasoning analysis (same as coaching graph)"
        )

        # Call the actual reasoning node from the coaching graph
        reasoning_result = await reasoning_node(state)

        # Enhance the result with individual coach context while preserving the full reasoning
        if isinstance(reasoning_result, dict):
            # Add coach-specific context to the reasoning result
            reasoning_result.update(
                {
                    "coach_context": coach_name,
                    "individual_coach_mode": True,
                    "debug_info": {
                        **reasoning_result.get("debug_info", {}),
                        "coach_domain": coach_name,
                        "reasoning_type": "comprehensive_individual",
                        "source": "coaching_graph_reasoning_node",
                    },
                }
            )

            logger.info(
                f"✅ INDIVIDUAL_REASONING: [{coach_name}] Completed comprehensive reasoning analysis"
            )

            return reasoning_result
        else:
            # Fallback if reasoning_node returns unexpected format
            logger.warning(
                f"⚠️ INDIVIDUAL_REASONING: [{coach_name}] Unexpected reasoning result format, creating fallback"
            )
            return {
                "current_node": "reasoning",
                "coach_context": coach_name,
                "execution_steps": ["reasoning"],
                "reasoning_analysis": "Comprehensive analysis completed for individual coach interaction.",
                "debug_info": {
                    "node": "reasoning",
                    "coach_domain": coach_name,
                    "action": "fallback_reasoning",
                    "reasoning_provided": True,
                },
            }

    async def individual_coach_node(state: OptimizedCoachingState) -> Dict[str, Any]:
        """Individual coach node using domain agent directly with streaming support."""
        logger.info(f"🏃‍♂️ INDIVIDUAL_COACH: [{coach_name}] Starting execution")

        if isinstance(state, dict):
            user_query = state.get("user_query", "")
            messages = state.get("messages", [])
            user_profile = state.get("user_profile", {})
            execution_steps = state.get("execution_steps", [])
            reasoning_analysis = state.get(
                "reasoning_analysis", ""
            )  # Get reasoning from previous node
        else:
            user_query = getattr(state, "user_query", "")
            messages = getattr(state, "messages", [])
            user_profile = getattr(state, "user_profile", {})
            execution_steps = getattr(state, "execution_steps", [])
            reasoning_analysis = getattr(
                state, "reasoning_analysis", ""
            )  # Get reasoning from previous node

        # Get the latest user message if user_query is empty
        if not user_query and messages:
            for msg in reversed(messages):
                if isinstance(msg, HumanMessage):
                    user_query = msg.content
                    break

        if not user_query:
            greeting_response = f"Hello! I'm your {coach_name.replace('_', ' ').title()}. I've analyzed your request and I'm ready to help. What specific aspect of {coach_name.replace('_coach', '')} would you like to work on?"
            return {
                "current_node": coach_name,
                "final_response": greeting_response,
                "messages": messages + [AIMessage(content=greeting_response)],
                "execution_steps": execution_steps + [coach_name],
                "debug_info": {
                    "node": coach_name,
                    "action": "greeting_with_reasoning_context",
                },
            }

        try:
            logger.info(
                f"🔄 INDIVIDUAL_COACH: [{coach_name}] Creating streaming-compatible coach chain"
            )

            # Get the domain coach's system prompt and tools
            coach_prompt = coach_instance.get_domain_prompt()
            coach_tools = await coach_instance.get_domain_tools()

            # Create LLM with streaming and proper tags for the domain
            domain_name = coach_name.replace("_coach", "")

            # CRITICAL: Create a Runnable chain instead of calling LLM directly
            # This ensures proper streaming event propagation in LangGraph
            from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
            from langchain_core.runnables import RunnablePassthrough, RunnableLambda
            from langchain_core.output_parsers import StrOutputParser

            # Include reasoning analysis in the system prompt if available
            enhanced_prompt = coach_prompt
            if reasoning_analysis:
                enhanced_prompt = f"{coach_prompt}\n\nPREVIOUS ANALYSIS:\n{reasoning_analysis}\n\nPlease use this analysis to inform your response."
                logger.info(
                    f"🧠 INDIVIDUAL_COACH: [{coach_name}] Enhanced prompt with reasoning analysis"
                )

            # Create a proper prompt template
            prompt_template = ChatPromptTemplate.from_messages(
                [
                    ("system", enhanced_prompt),
                    MessagesPlaceholder(variable_name="chat_history", optional=True),
                    ("human", "{input}"),
                ]
            )

            # Create LLM with proper configuration
            coach_llm = create_azure_chat_openai(temperature=0.7, streaming=True)

            # Bind tools if available
            if coach_tools:
                coach_llm = coach_llm.bind_tools(coach_tools)
                logger.info(
                    f"🔧 INDIVIDUAL_COACH: [{coach_name}] Bound {len(coach_tools)} tools to LLM"
                )

            # Create the chain using LangGraph's Runnable composition
            # This structure ensures streaming events are properly propagated
            coach_chain = (
                {"chat_history": lambda x: messages, "input": RunnablePassthrough()}
                | prompt_template
                | coach_llm.with_config(
                    {
                        "tags": ["final_response", domain_name, "domain_coach"],
                        "metadata": {
                            "langgraph_node": coach_name,
                            "langgraph_step": 1,
                            "langgraph_checkpoint_id": state.get("checkpoint_id", ""),
                        },
                        "run_name": f"{coach_name}_streaming",
                    }
                )
            )

            logger.info(
                f"🔧 INDIVIDUAL_COACH: [{coach_name}] Invoking coach chain with streaming..."
            )

            # Use the chain to get the response
            # The chain structure ensures streaming events bubble up properly
            response = await coach_chain.ainvoke(user_query)

            # Handle tool calls if any
            final_answer = ""
            if hasattr(response, "tool_calls") and response.tool_calls and coach_tools:
                logger.info(
                    f"🔧 INDIVIDUAL_COACH: [{coach_name}] Executing {len(response.tool_calls)} tool calls"
                )

                # Execute tools and collect results
                from langchain_core.messages import ToolMessage

                tool_messages = []
                conversation_with_tools = messages + [
                    HumanMessage(content=user_query),
                    response,
                ]

                for tool_call in response.tool_calls:
                    tool_name = tool_call["name"]
                    tool_args = tool_call["args"]

                    # Find and execute the tool
                    tool = next((t for t in coach_tools if t.name == tool_name), None)
                    if tool:
                        try:
                            tool_result = (
                                await tool.ainvoke(tool_args)
                                if hasattr(tool, "ainvoke")
                                else tool.invoke(tool_args)
                            )
                            tool_message = ToolMessage(
                                content=str(tool_result), tool_call_id=tool_call["id"]
                            )
                            tool_messages.append(tool_message)
                            conversation_with_tools.append(tool_message)
                            logger.info(
                                f"✅ INDIVIDUAL_COACH: [{coach_name}] Tool {tool_name} executed successfully"
                            )
                        except Exception as tool_error:
                            logger.error(
                                f"❌ INDIVIDUAL_COACH: [{coach_name}] Tool {tool_name} failed: {tool_error}"
                            )
                            tool_message = ToolMessage(
                                content=f"Error: {tool_error}",
                                tool_call_id=tool_call["id"],
                            )
                            tool_messages.append(tool_message)
                            conversation_with_tools.append(tool_message)

                # Create synthesis chain for tool results
                if tool_messages:
                    synthesis_prompt = ChatPromptTemplate.from_messages(
                        [
                            ("system", coach_prompt),
                            MessagesPlaceholder(variable_name="conversation"),
                            (
                                "human",
                                "Based on the tool results above, please provide a comprehensive, helpful, and human-readable coaching response. Synthesize this information into actionable advice.",
                            ),
                        ]
                    )

                    synthesis_chain = (
                        {"conversation": lambda x: conversation_with_tools}
                        | synthesis_prompt
                        | coach_llm.with_config(
                            {
                                "tags": ["final_response", domain_name, "synthesis"],
                                "metadata": {
                                    "langgraph_node": coach_name,
                                    "langgraph_step": 2,
                                    "langgraph_checkpoint_id": state.get(
                                        "checkpoint_id", ""
                                    ),
                                },
                                "run_name": f"{coach_name}_synthesis",
                            }
                        )
                        | StrOutputParser()
                    )

                    final_answer = await synthesis_chain.ainvoke({})
                    logger.info(
                        f"✅ INDIVIDUAL_COACH: [{coach_name}] Synthesized tool results into response"
                    )
            else:
                # No tools used, extract content from response
                final_answer = (
                    response.content if hasattr(response, "content") else str(response)
                )

            # Ensure we have a valid response
            if not final_answer or not final_answer.strip():
                final_answer = "I'd be happy to help you!"

            logger.info(
                f"✅ INDIVIDUAL_COACH: [{coach_name}] Coach chain completed successfully"
            )

            return {
                "messages": messages + [AIMessage(content=final_answer)],
                "current_node": coach_name,
                "final_response": final_answer,
                "coach_responses": {coach_name: final_answer},
                "execution_steps": execution_steps + [coach_name],
                "debug_info": {
                    "node": coach_name,
                    "success": True,
                    "action": "coach_chain_complete",
                    "tools_used": (
                        len(response.tool_calls)
                        if hasattr(response, "tool_calls")
                        else 0
                    ),
                },
            }

        except Exception as e:
            logger.error(f"❌ INDIVIDUAL_COACH: [{coach_name}] Error: {e}")
            error_response = "I apologize, but I encountered an error while processing your request. Please try again."
            return {
                "current_node": coach_name,
                "final_response": error_response,
                "messages": messages + [AIMessage(content=error_response)],
                "execution_steps": execution_steps + [coach_name],
                "debug_info": {
                    "node": coach_name,
                    "error": str(e),
                    "action": "error_fallback_with_reasoning",
                },
            }

    # Build the graph - Enhanced flow: START → reasoning → coach → END
    builder = StateGraph(OptimizedCoachingState)

    # Add the reasoning node first
    builder.add_node("reasoning", individual_reasoning_node)

    # Add the coach node
    builder.add_node(coach_name, individual_coach_node)

    # Define enhanced flow with reasoning
    builder.add_edge(START, "reasoning")
    builder.add_edge("reasoning", coach_name)
    builder.add_edge(coach_name, END)

    logger.info(
        f"✅ INDIVIDUAL_COACH: {coach_name} graph structure with reasoning built successfully"
    )
    logger.info(
        f"📊 INDIVIDUAL_COACH: Enhanced flow: START → reasoning → {coach_name} → END"
    )

    return builder


# Factory functions for each coach (for langgraph.json)
async def create_strength_coach_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create strength coach graph with reasoning."""
    return await create_individual_coach_graph("strength_coach", config)


async def create_cardio_coach_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create cardio coach graph with reasoning."""
    return await create_individual_coach_graph("cardio_coach", config)


async def create_cycling_coach_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create cycling coach graph with reasoning."""
    return await create_individual_coach_graph("cycling_coach", config)


async def create_nutrition_coach_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create nutrition coach graph with reasoning."""
    return await create_individual_coach_graph("nutrition_coach", config)


async def create_recovery_coach_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create recovery coach graph with reasoning."""
    return await create_individual_coach_graph("recovery_coach", config)


async def create_mental_coach_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create mental coach graph with reasoning."""
    return await create_individual_coach_graph("mental_coach", config)
