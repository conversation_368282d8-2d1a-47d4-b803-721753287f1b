#!/usr/bin/env python3
"""
Pydantic schemas for standardizing test result JSON structures.
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field


class TestMetadataSchema(BaseModel):
    phase: str = "2.1"
    epic: str = "2.1-individual-coach-accuracy-testing"
    ticket_id: str = "E2.1-T1"
    ticket_name: str = "strength-coach-accuracy-testing"
    scenario_id: str
    execution_timestamp: str
    thread_id: str

class TestExecutionSchema(BaseModel):
    query: str
    user_profile: Dict[str, Any]
    execution_duration_seconds: float
    success: bool
    error: Optional[str] = None

class CoachResponseSchema(BaseModel):
    final_response: str
    response_length_chars: int

class LeverRecommendationSchema(BaseModel):
    lever: int
    lever_name: str
    reason: str
    action: str
    file: str
    priority: float = Field(alias="priority_score")

class QualityAssessmentSchema(BaseModel):
    specificity_score: float
    safety_score: float
    elements_coverage: float
    found_elements_count: int
    missing_elements: List[str]
    critical_issues: List[str]
    lever_recommendations: List[LeverRecommendationSchema] = []

class ScenarioResultSchema(BaseModel):
    test_metadata: TestMetadataSchema
    test_execution: TestExecutionSchema
    coach_response: CoachResponseSchema
    quality_assessment: QualityAssessmentSchema

class QualityMetricsSummarySchema(BaseModel):
    average_specificity_score: float
    average_safety_score: float
    average_coverage_score: float
    average_tool_call_success_rate: float
    composite_score: float

class DeploymentCriteriaSchema(BaseModel):
    pass_rate: Dict[str, Any]
    specificity: Dict[str, Any]
    safety: Dict[str, Any]
    coverage: Dict[str, Any]
    tool_success: Dict[str, Any]

class DeploymentAnalysisSchema(BaseModel):
    criteria_met: int
    total_criteria: int = 5
    recommendation: str
    individual_criteria: DeploymentCriteriaSchema

class StandardizedTestOutputSchema(BaseModel):
    test_suite_metadata: Dict[str, Any]
    deployment_analysis: DeploymentAnalysisSchema
    quality_metrics_summary: QualityMetricsSummarySchema
    lever_analysis: Dict[str, Any]
    test_scenarios: Dict[str, ScenarioResultSchema] 