# User Preferences
- User prefers structured AI assistance with custom modes (COACHING_AGENT, GRAPH_DESIGN, MEMORY_INTEGRATION, TOOL_DEVELOPMENT, API_STREAMING) and wants development to follow Python/LangGraph patterns with emphasis on async/await, Pydantic models, MongoDB integration, and LangGraph Studio compatibility.
- User wants comprehensive analysis of Athlea LangGraph documentation hierarchy (docs/01-05) focusing on architecture patterns, multi-agent coaching structure, memory management, graph factory patterns, testing methodologies, and tool development with circuit breakers to enhance project assistance.
- User prefers automated prompt optimization systems that leverage LangGraph's programmatic capabilities to create self-improving test routines with iterative testing loops, automated prompt generation based on failure analysis, and continuous improvement mechanisms that automatically experiment with solutions to achieve optimal performance.
- User has Azure OpenAI credentials available and suggests environment configuration issues are typically related to proper passing of key, model, and release date parameters.
- User prefers automated prompt optimization cycles with evidence-based deployment criteria (deploy if ≥80% targets achieved, extend testing if significant but <80% improvement, investigate if <20% gain) and wants comprehensive testing documentation including response extraction methods, LangSmith tracing configuration, and complete self-improvement testing loop workflows.

# Testing Preferences
- User prefers testing LangGraph systems using outcome-based validation (black box) and execution path validation with streaming (grey box) rather than traditional state assertions, emphasizing that tests should focus on final results and execution traces rather than intermediate states due to the non-linear, asynchronous nature of multi-agent graphs.
- User prefers breaking down testing epics into domain-specific, agent-executable tickets (2-4 hours each) stored in .ai/tasks/backlog/tickets/ directory and wants to continue this pragmatic granular approach for all testing phases.
- User prefers detailed, accessible test results over high-level summaries and cannot access LangSmith traces via the portal, referencing LangSmith evaluation documentation for LangGraph testing patterns. Instead, the user has LangSmith MCP server integrated with tools for programmatic access to traces, prompts, datasets, and conversation histories, which should be used for testing protocols and trace analysis.
- User prefers using existing test infrastructure in tests/ directory structure rather than creating new test scripts in root, with specific focus on tests/integration/ for integration tests and proper cleanup of root directory test files.
- MANDATORY TESTING DISCIPLINE: All tests MUST use existing test infrastructure in tests/ directory structure, never create standalone test scripts in root directory, enhance existing test files following established patterns.
- User prefers test results organized in structure matching phases/tickets for traceability and wants human-readable timestamp formats in test results rather than technical timestamps.
- User prefers test results with AI-generated failure reasoning, balanced element tracking (found_elements_list + missing_elements), transparent score calculation methods, and human_review_notes fields for collaborative expert input in test outcomes.
- User prefers test failure analysis that maps issues to specific system levels (prompt engineering, tool integration, graph flow, reasoning configuration) with concrete improvement recommendations, confidence scores, and automatic root cause identification in test results.
- User prefers systematic iterative improvement testing with evidence-based deployment criteria: deploy if ≥80% of targets achieved, extend testing if significant but <80% improvement, investigate systematic issues if <20% gain, with specific success criteria (100% pass rate, ≥0.75 specificity, ≥0.9 safety, ≥0.8 elements coverage).
- User prefers consolidated, LLM-readable testing documentation with unified hierarchy, minimal script footprint while preserving core testing capabilities, and maintaining institutional knowledge of working technical solutions (response extraction via result['final_response'], 6-hour LangSmith time window, etc.).
- User expects actual script cleanup and consolidation in tests/integration directory, not just documentation consolidation, when asked to streamline testing infrastructure.
- User prefers comprehensive test directory audits with categorization (ESSENTIAL/CONSOLIDATE/REMOVE) and integration into streamlined testing systems, emphasizing elimination of redundancy while preserving automated optimization testing as the primary workflow.
- User prefers test directory structures that intuitively match system architecture with automated optimization testing as the primary workflow, and wants test organization to be logical for both developers and LLMs rather than organic growth.
- Test directory was restructured from organic growth to logical architecture with core automated optimization testing in tests/core/, individual coach testing in tests/coaches/, system-level testing in tests/system/, and consolidated 91% of redundant scripts while preserving all essential functionality.