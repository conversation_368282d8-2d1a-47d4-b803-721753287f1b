"""
Database Configuration for Different Environments
"""

import os
from typing import Optional


def get_postgres_connection_string() -> Optional[str]:
    """
    Get PostgreSQL connection string based on environment.

    Priority:
    1. POSTGRES_CONNECTION_STRING (direct override)
    2. Azure production database
    3. Local development database
    """

    # Direct override (for production/staging)
    direct_connection = os.getenv("POSTGRES_CONNECTION_STRING")
    if direct_connection:
        return direct_connection

    # Azure production database
    azure_host = os.getenv(
        "AZURE_POSTGRES_HOST", "athlea-langgraph-server.postgres.database.azure.com"
    )
    azure_user = os.getenv(
        "AZURE_POSTGRES_USER", "michwilf"
    )  # Fixed: match connection string username
    azure_password = os.getenv("AZURE_POSTGRES_PASSWORD")
    azure_db = os.getenv("AZURE_POSTGRES_DB", "postgres")

    if azure_password:
        # URL encode the password for special characters
        import urllib.parse

        encoded_password = urllib.parse.quote(azure_password)
        connection_string = f"postgresql://{azure_user}:{encoded_password}@{azure_host}:5432/{azure_db}?sslmode=require"
        print(f"🔗 Generated Azure connection string for user: {azure_user}")
        return connection_string

    # Local development fallback
    local_connection = os.getenv("LOCAL_POSTGRES_CONNECTION_STRING")
    if local_connection:
        return local_connection

    # Default local development
    return "postgresql://postgres:postgres@localhost:5432/athlea_coaching"


# Global cache for checkpointer to avoid recreating PostgreSQL connections
_checkpointer_cache = None
_checkpointer_tested = False


def get_checkpointer():
    """Get the appropriate checkpointer based on environment."""
    global _checkpointer_cache, _checkpointer_tested

    # Return cached checkpointer if already created
    if _checkpointer_cache is not None:
        return _checkpointer_cache

    from langgraph.checkpoint.memory import MemorySaver

    try:
        from langgraph.checkpoint.postgres import PostgresSaver

        connection_string = get_postgres_connection_string()

        if connection_string and not connection_string.startswith(
            "postgresql://postgres:postgres@localhost"
        ):
            # Only test PostgreSQL connection once
            if not _checkpointer_tested:
                print(f"🗄️ Setting up PostgreSQL checkpointer (one-time setup)...")
                _checkpointer_tested = True

                # Test the connection first
                try:
                    # Add connection parameters to handle Azure PostgreSQL issues
                    enhanced_connection_string = f"{connection_string}&connect_timeout=30&application_name=athlea_langgraph"

                    with PostgresSaver.from_conn_string(
                        enhanced_connection_string
                    ) as temp_checkpointer:
                        # Try to setup tables (this will test the connection)
                        temp_checkpointer.setup()
                        print("✅ PostgreSQL connection and setup successful!")

                    # Cache the successful checkpointer
                    _checkpointer_cache = PostgresSaver.from_conn_string(
                        enhanced_connection_string
                    )
                    print("🎯 PostgreSQL checkpointer cached for reuse")
                    return _checkpointer_cache

                except Exception as conn_error:
                    print(f"❌ PostgreSQL connection failed: {conn_error}")
                    if "timeout" in str(conn_error).lower():
                        print(
                            "💡 This looks like a firewall issue - check Azure networking settings"
                        )
                    print("⚠️ Falling back to MemorySaver")
                    # Cache the fallback
                    _checkpointer_cache = MemorySaver()
                    return _checkpointer_cache
            else:
                # Already tested and failed, use cached fallback
                return _checkpointer_cache or MemorySaver()
        else:
            # Use memory for local development
            if not _checkpointer_tested:
                print("⚠️ Using MemorySaver for local development")
                _checkpointer_tested = True
            _checkpointer_cache = MemorySaver()
            return _checkpointer_cache

    except ImportError:
        if not _checkpointer_tested:
            print("⚠️ PostgreSQL checkpointer not available, using MemorySaver")
            _checkpointer_tested = True
        _checkpointer_cache = MemorySaver()
        return _checkpointer_cache
    except Exception as e:
        if not _checkpointer_tested:
            print(f"⚠️ PostgreSQL setup failed ({e}), using MemorySaver")
            _checkpointer_tested = True
        _checkpointer_cache = MemorySaver()
        return _checkpointer_cache


# Environment detection
def is_production() -> bool:
    """Check if running in production environment."""
    return (
        os.getenv("ENVIRONMENT") == "production"
        or os.getenv("AZURE_CLIENT_ID") is not None
    )


def is_development() -> bool:
    """Check if running in development environment."""
    return not is_production()
