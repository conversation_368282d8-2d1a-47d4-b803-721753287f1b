{"name": "shakib-admin", "version": "0.1.0", "private": true, "engines": {"node": ">=18"}, "type": "module", "scripts": {"dev": "next dev", "start": "next start", "lint": "next lint", "clean": "rm -rf .next", "build": "npm run clean && next build", "test:langgraph": "node scripts/test-langgraph.js", "test:direct-langgraph": "node scripts/direct-test-langgraph.js", "test:supervision": "node scripts/test-supervision-tools.js", "test:supervision-suite": "node scripts/test-supervision-suite.js", "test:azure-openai": "node scripts/test-azure-openai.js", "test:azure-openai-direct": "node scripts/test-azure-openai-direct.js", "test:azure-langchain": "node scripts/test-azure-langchain.js", "test:azure": "node scripts/azure-test.js"}, "dependencies": {"@ai-sdk/groq": "^1.1.11", "@ai-sdk/openai": "^1.1.15", "@azure/monitor-opentelemetry": "^1.11.0", "@azure/openai": "^2.0.0-beta.2", "@azure/search-documents": "^12.1.0", "@dhaiwat10/react-link-preview": "^1.15.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@headlessui/tailwindcss": "^0.2.0", "@hookform/resolvers": "^5.0.1", "@humeai/voice": "^0.1.10", "@humeai/voice-react": "^0.1.19", "@kinde-oss/kinde-auth-nextjs": "^2.2.13", "@langchain/community": "^0.2.28", "@langchain/core": "^0.3.43", "@langchain/groq": "^0.2.0", "@langchain/langgraph": "^0.2.63", "@langchain/mcp-adapters": "^0.4.2", "@langchain/mistralai": "^0.2.0", "@langchain/openai": "^0.4.9", "@llamaindex/core": "^0.5.8", "@mastra/client-js": "^0.1.6", "@mastra/core": "^0.4.4", "@mastra/memory": "^0.1.7", "@mastra/rag": "^0.1.7", "@modelcontextprotocol/sdk": "^1.11.2", "@mui/icons-material": "^5.15.20", "@mui/lab": "^5.0.0-alpha.135", "@mui/material": "^5.15.20", "@opentelemetry/api": "^1.9.0", "@opentelemetry/resources": "^1.30.1", "@opentelemetry/semantic-conventions": "^1.33.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.3", "@reduxjs/toolkit": "^2.2.5", "@tanstack/react-query": "^5.73.3", "@tremor/react": "^3.11.0", "@types/react-dom": "latest", "abort-controller": "^3.0.0", "airtable": "^0.12.2", "apexcharts": "^3.41.1", "autoprefixer": "10.4.14", "axios": "^1.8.4", "axios-retry": "^4.4.1", "canvg": "^4.0.3", "cheerio": "^1.0.0", "chromium-bidi": "^0.6.1", "class-variance-authority": "^0.7.1", "cli-table3": "^0.6.5", "clsx": "^2.1.1", "cmdk": "^1.1.1", "csv-parse": "^5.5.6", "d3": "^7.9.0", "date-fns": "^4.1.0", "dayjs": "^1.11.11", "dompurify": "^3.2.5", "dotenv": "^16.5.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "eslint": "8.46.0", "eslint-config-next": "latest", "eventsource": "^3.0.7", "firebase": "^11.0.2", "firebase-admin": "^13.3.0", "framer-motion": "^11.3.19", "googleapis": "^146.0.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "jsvectormap": "^1.5.3", "langchain": "^0.2.20", "langsmith": "^0.3.22", "leaflet": "^1.9.4", "llamaindex": "^0.9.11", "lucide-react": "^0.292.0", "microsoft-cognitiveservices-speech-sdk": "^1.38.0", "mongodb": "^6.15.0", "next": "^15.3.1", "next-themes": "^0.4.6", "openai": "^4.58.1", "pdf-parse": "^1.1.1", "pkce-challenge": "^5.0.0", "playwright": "^1.49.1", "playwright-core": "^1.49.1", "postcss": "8.4.27", "react": "^19.1.0", "react-apexcharts": "^1.4.1", "react-beautiful-dnd": "^13.1.1", "react-circular-progressbar": "^2.1.0", "react-day-picker": "^8.10.1", "react-dom": "^19.1.0", "react-dropzone": "^14.2.3", "react-firebase-hooks": "^5.1.1", "react-hook-form": "^7.57.0", "react-hot-toast": "^2.4.1", "react-jvectormap": "^0.0.16", "react-leaflet": "^4.2.1", "react-markdown": "^10.1.0", "react-modal": "^3.16.1", "react-redux": "^9.1.2", "react-resizable-panels": "^3.0.3", "react-responsive": "^10.0.0", "react-router-dom": "^6.26.0", "react-scan": "^0.1.0", "react-svg": "^16.3.0", "react-virtualized": "^9.22.5", "react-window": "^1.8.10", "react-window-infinite-loader": "^1.0.9", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remeda": "^2.19.0", "sonner": "^2.0.5", "sqlite3": "^5.1.7", "stream": "^0.0.3", "superjson": "^2.2.2", "tailwind-merge": "^2.6.0", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "turndown": "^7.1.2", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "typewriter-effect": "^2.21.0", "uuid": "^9.0.1", "vaul": "^1.1.2", "vm2": "^3.9.19", "wikipedia": "^2.1.2", "xlsx": "^0.18.5", "zod": "^3.25.51", "zustand": "^4.4.6"}, "devDependencies": {"@types/d3": "^7.4.3", "@types/dompurify": "^3.0.5", "@types/leaflet": "^1.9.12", "@types/lodash": "^4.17.6", "@types/node": "^22.14.1", "@types/react": "^19.1.2", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-modal": "^3.16.3", "@types/react-redux": "^7.1.34", "@types/react-responsive": "^8.0.8", "@types/react-virtualized": "^9.21.30", "@types/react-window": "^1.8.8", "@types/react-window-infinite-loader": "^1.0.9", "@types/turndown": "^5.0.5", "@types/uuid": "^9.0.8", "chalk": "^5.4.1", "electron": "^35.0.0", "file-loader": "^6.2.0", "node-fetch": "^3.3.2", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.5.6", "yargs": "^17.7.2"}}