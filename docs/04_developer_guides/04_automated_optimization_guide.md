# Automated Prompt Optimization Guide

## Overview

This guide provides comprehensive instructions for executing automated prompt optimization workflows, from baseline establishment to production deployment. This system transforms manual prompt optimization into an automated, evidence-based process.

## Quick Start

### Complete 2.1.1-strength-coach-accuracy-testing Cycle (Sprint 2.1)

```bash
# Execute enhanced automated optimization test
python -c "
import asyncio
from tests.core.test_automated_optimization import test_strength_coach_accuracy_with_tracing
result = asyncio.run(test_strength_coach_accuracy_with_tracing())
print(f'✅ Test Complete - Pass Rate: {result.get(\"pass_rate\", 0):.2f}')
print(f'🎯 Ticket ID: {result.get(\"ticket_id\", \"N/A\")}')
print(f'📊 Enhanced Metrics Available: tool_call_success_rate, cost_per_scenario, domain_expertise')
"
```

### Multi-Turn Consistency Testing (Sprint 2.1)

```bash
# Execute multi-turn conversation validation
python -c "
import asyncio
from tests.core.test_automated_optimization import test_strength_coach_multi_turn_consistency
result = asyncio.run(test_strength_coach_multi_turn_consistency())
print(f'🔄 Multi-Turn Pass Rate: {result.get(\"pass_rate\", 0):.2f}')
print(f'📋 Scenarios: {len(result.get(\"results\", []))} conversation flows tested')
"
```

### Rollback Safety Testing (Sprint 2.1)

```bash
# Validate rollback safety mechanisms
python -c "
import asyncio
from tests.core.test_automated_optimization import test_rollback_mechanism
result = asyncio.run(test_rollback_mechanism())
print(f'🛡️ Rollback System: {\"ACTIVE\" if result else \"FAILED\"}')
print(f'📊 Baseline Storage: Functional')
"
```

### View Results

```bash
# Check latest test results
ls -la tests/results/ | tail -1

# View baseline metrics (if baseline_results.json exists)
python -c "
import json
try:
    with open('baseline_results.json', 'r') as f:
        data = json.load(f)
    print(f'Composite Score: {data[\"metrics\"][\"composite_score\"]:.3f}')
    print(f'Optimization Priorities: {data[\"optimization_priorities\"]}')
except FileNotFoundError:
    print('No baseline results found - run optimization test first')
"
```

## System Architecture

### Core Components

1. **Baseline Testing Framework** (`tests/core/test_automated_optimization.py`)
   - Function: `test_strength_coach_accuracy_with_tracing()`
   - Purpose: Automated performance measurement and quality assessment

2. **Enhanced Quality Assessment Engine** (Sprint 2.1 improvements)
   - **Core Metrics**:
     - Specificity Score (target: ≥0.75)
     - Safety Score (target: ≥0.90)
     - Coverage Score (target: ≥0.80)
   - **Sprint 2.1 Enhanced Metrics**:
     - Tool Call Success Rate (target: ≥0.80)
     - Cost Per Scenario (USD tracking for ROI analysis)
     - Error Rate (explicit failure tracking)
     - Domain Expertise (strength training knowledge accuracy)
     - Multi-Turn Consistency (context retention across turns)
   - Composite Score (target: ≥0.80)

3. **LangSmith Tracing Integration with User Journey Tracking** (Sprint 2.1)
   - Project: `athlea-coaching-dev`
   - Portal: https://smith.langchain.com/
   - Dashboard: Coach UX Test Dashboard (`coach_ux_dashboard_20250619_150716`)
   - **Critical**: Use "Last 6 hours" time filter
   - **Enhanced Metadata Tagging**:
     ```python
     metadata = {
         "user_id": f"test_user_{scenario_name}",
         "coach_type": "strength_coach",
         "scenario_id": scenario_name,
         "conversation_phase": "accuracy_testing",
         "ticket_id": "2.1.1-strength-coach-accuracy-testing",
         "latency_bucket": "0-1s|1-2s|2-5s|>5s",
         "cost_bucket": "<$0.001|$0.001-$0.01|>$0.01"
     }
     ```

4. **Evidence-Based Deployment System**
   - Deploy: Composite score ≥0.80
   - Extend: Significant improvement (≥0.20) but <0.80
   - Investigate: Low improvement (<0.20)

## Environment Configuration

### Required Environment Variables

```bash
# LangSmith Integration (REQUIRED)
LANGSMITH_API_KEY="***************************************************"
LANGSMITH_TRACING="true"
LANGCHAIN_TRACING_V2="true"
LANGCHAIN_PROJECT="athlea-coaching-dev"

# Azure OpenAI (REQUIRED)
AZURE_OPENAI_API_KEY="your_azure_openai_api_key"
AZURE_OPENAI_ENDPOINT="your_azure_openai_endpoint"

# MongoDB (REQUIRED)
MONGODB_URI="mongodb://localhost:27017/athlea_coaching_tests"
```

### Verification Commands

```bash
# Check environment setup
python -c "
import os
required = ['LANGSMITH_API_KEY', 'AZURE_OPENAI_API_KEY', 'MONGODB_URI']
for var in required:
    status = '✅' if os.getenv(var) else '❌'
    print(f'{status} {var}')
"
```

## Technical Fixes (CRITICAL)

### 1. Response Extraction Fix

**Issue**: Tests failing due to incorrect response extraction
**Solution**: Use `result['final_response']` instead of `result.messages`

**Location**: `tests/core/test_automated_optimization.py`, lines ~548-560

```python
# CORRECT: Fixed response extraction
if isinstance(result, dict) and 'final_response' in result:
    final_response = str(result['final_response'])
    response_length = len(final_response)
```

### 2. LangSmith Trace Visibility Fix

**Issue**: Traces not appearing in portal
**Solution**: Use "Last 6 hours" time filter (not "Last 1 hour")

**Portal Access**:
1. Navigate to https://smith.langchain.com/
2. Select `athlea-coaching-dev` project
3. **Change time filter to "Last 6 hours"**
4. Look for traces: `AzureChatOpenAI`, `comprehensive_strength_assessment`

### 3. Tool Parameter Validation Fix

**Issue**: `Union[str, List[str]]` parameter validation errors
**Solution**: Updated parameter handling in tool wrappers

```python
# Handle both string and list inputs
if isinstance(muscle_groups, str):
    muscle_groups_list = [muscle_groups]
else:
    muscle_groups_list = muscle_groups or []
```

## Quality Metrics Explained

### Specificity Score (Target: ≥0.75)

**Measures**: Technical precision and actionable detail

**High Specificity Example**:
> "Reduce deadlift weight to 185 lbs (82% of current), focus on hip hinge pattern with 15-degree forward lean, maintain neutral spine through thoracic extension, progress by 2.5 lbs every 2 weeks"

**Improvement Strategies**:
- Include specific weight/rep/set recommendations
- Use technical terminology (hip hinge, scapular retraction)
- Provide measurable progression metrics
- Reference anatomical structures

### Safety Score (Target: ≥0.90)

**Measures**: Injury prevention and risk awareness

**High Safety Example**:
> "Given your lower back pain during deadlifts, I recommend immediate weight reduction and medical consultation if pain persists >1 week. Prioritize movement quality over load progression."

**Improvement Strategies**:
- Always acknowledge reported pain immediately
- Recommend medical consultation for persistent pain
- Emphasize conservative progression protocols
- Include injury prevention strategies

### Coverage Score (Target: ≥0.80)

**Measures**: Comprehensive response completeness

**High Coverage Example**:
> Response addresses: form correction, weight adjustment, progression strategy, alternative exercises, recovery protocols, and follow-up assessment timeline.

**Improvement Strategies**:
- Address ALL aspects mentioned in user's question
- Include exercise alternatives and variations
- Provide programming context (sets, reps, frequency)
- Add follow-up recommendations

## Test Execution Workflows

### Single Test Execution

```bash
# Run strength coach accuracy test
python -c "
import asyncio
from tests.core.test_automated_optimization import test_strength_coach_accuracy_with_tracing
result = asyncio.run(test_strength_coach_accuracy_with_tracing())

# Display key metrics
if result and result.get('organized_results'):
    scenarios = result['organized_results'].get('test_scenarios', {})
    for name, data in scenarios.items():
        print(f'\\n🧪 {name.upper()}:')
        if data.get('quality_assessment'):
            qa = data['quality_assessment']
            print(f'  - Specificity: {qa.get(\"specificity_score\", 0):.3f}')
            print(f'  - Safety: {qa.get(\"safety_score\", 0):.3f}')
            print(f'  - Coverage: {qa.get(\"elements_coverage\", 0):.3f}')
"
```

### Batch Test Execution

```bash
# Run multiple test cycles for trend analysis
for i in {1..3}; do
    echo "🔄 Test Cycle $i"
    python -c "
import asyncio
from tests.core.test_automated_optimization import test_strength_coach_accuracy_with_tracing
result = asyncio.run(test_strength_coach_accuracy_with_tracing())
print(f'Cycle $i Complete - Pass Rate: {result.get(\"pass_rate\", 0):.2f}')
    "
    sleep 5
done
```

## Performance Benchmarks

### Expected Values

| Metric | Expected Value | Acceptable Range |
|--------|---------------|------------------|
| Single Test Duration | 8-12 seconds | 5-20 seconds |
| Response Generation Rate | 100% | ≥95% |
| Response Length | 1800-2200 chars | 1000-3000 chars |
| Specificity Score | 0.40-0.75 | 0.30-0.85 |
| Safety Score | 0.67-0.90 | 0.50-1.00 |
| Coverage Score | 0.24-0.80 | 0.20-0.90 |

### Monitoring Commands

```bash
# Check recent test performance
python -c "
import glob, json
files = sorted(glob.glob('tests/results/*.json'))
if files:
    with open(files[-1], 'r') as f:
        data = json.load(f)
    print(f'Latest Test: {files[-1].split(\"/\")[-1]}')
    print(f'Duration: {data.get(\"total_duration\", 0):.2f}s')
    print(f'Pass Rate: {data.get(\"pass_rate\", 0):.2f}')
"
```

## File Locations and Structure

### Key Files

```
tests/
├── core/
│   ├── test_automated_optimization.py   # PRIMARY: Core testing function
│   ├── test_tool_integration.py         # Tool integration testing
│   └── test_streaming_api.py            # API streaming testing
├── results/                              # Test result files
│   └── 2.1.1-strength-coach-accuracy-testing_*.json
└── README.md                            # Quick reference

Root Directory:
├── baseline_results.json                # Baseline metrics (if exists)
├── optimization_demonstration_results.json  # Optimization analysis
└── docs/04_developer_guides/
    └── 04_automated_optimization_guide.md   # This guide
```

### Result File Structure

```json
{
  "timestamp": "2025-06-19T10:05:03.068967",
  "test_scenarios": {
    "deadlift_form_correction": {
      "quality_assessment": {
        "specificity_score": 0.4,
        "safety_score": 1.0,
        "elements_coverage": 0.286
      }
    }
  },
  "pass_rate": 0.0,
  "total_duration": 30.42
}
```

## Troubleshooting Guide

### Issue 1: Response Generation Failure

**Symptoms**:
```
💬 Messages generated: 0
📝 Response length: 0 chars
```

**Solutions**:
1. Verify response extraction uses `result['final_response']`
2. Check tool parameter validation
3. Restart application after environment changes

**Debug Commands**:
```bash
# Test response extraction
python -c "
from tests.core.test_automated_optimization import StrengthCoachAccuracyTest
test = StrengthCoachAccuracyTest()
# Check if test class loads correctly
print('✅ Test class loaded successfully')
"
```

### Issue 2: LangSmith Traces Not Visible

**Symptoms**: 0 runs found in portal

**Solutions**:
1. **Change time filter to "Last 6 hours"** (most common fix)
2. Verify project name: `athlea-coaching-dev`
3. Check environment variables
4. Clear browser cache

**Debug Commands**:
```bash
# Test LangSmith connection
python -c "
from langsmith import Client
client = Client()
projects = list(client.list_projects(limit=10))
print(f'Found {len(projects)} projects')
for p in projects:
    if 'athlea' in p.name.lower():
        print(f'✅ Athlea project: {p.name}')
"
```

### Issue 3: Tool Validation Errors

**Symptoms**: `'str' object has no attribute 'get'`

**Solutions**:
1. Check tool parameter handling for Union types
2. Verify tool schema definitions
3. Update tool wrappers for proper type conversion

**Debug Commands**:
```bash
# Test tool execution
python -c "
from athlea_langgraph.tools.strength import search_strength_exercises
try:
    result = search_strength_exercises('chest', ['barbell'], 'intermediate', 5)
    print(f'✅ Tool executed: {len(str(result))} chars')
except Exception as e:
    print(f'❌ Tool error: {e}')
"
```

### Issue 4: Environment Configuration

**Symptoms**: Authentication or connection errors

**Solutions**:
1. Verify all required environment variables are set
2. Check API key validity
3. Test database connectivity

**Debug Commands**:
```bash
# Comprehensive environment check
python -c "
import os
from langsmith import Client

# Check LangSmith
try:
    client = Client()
    print('✅ LangSmith connection successful')
except Exception as e:
    print(f'❌ LangSmith error: {e}')

# Check required variables
required = {
    'LANGSMITH_API_KEY': 'LangSmith integration',
    'AZURE_OPENAI_API_KEY': 'Azure OpenAI',
    'MONGODB_URI': 'Database connection'
}

for var, desc in required.items():
    status = '✅' if os.getenv(var) else '❌'
    print(f'{status} {var} ({desc})')
"
```

## Advanced Usage

### Custom Test Scenarios

```python
# Create custom test scenario
from tests.core.test_automated_optimization import StrengthCoachAccuracyTest

async def run_custom_test():
    test = StrengthCoachAccuracyTest()
    result = await test.run_single_scenario(
        scenario_name="custom_test",
        user_query="Your custom query here",
        expected_elements=["element1", "element2"],
        thread_id="custom_test_123"
    )
    return result
```

### Continuous Integration

```yaml
# GitHub Actions example
name: Automated Optimization
on:
  schedule:
    - cron: '0 2 * * 1'  # Weekly
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run optimization test
        env:
          LANGSMITH_API_KEY: ${{ secrets.LANGSMITH_API_KEY }}
        run: |
          python -c "
          import asyncio
          from tests.core.test_automated_optimization import test_strength_coach_accuracy_with_tracing
          result = asyncio.run(test_strength_coach_accuracy_with_tracing())
          print(f'Pass Rate: {result.get(\"pass_rate\", 0):.2f}')
          "
```

## Systematic UX Improvement Framework

This framework maps UX optimization levers to specific test scenarios and quality metrics, providing a data-driven approach to improving user experience.

### Lever-to-Test Scenario Mapping

Each UX lever corresponds to specific test scenarios and expected improvements:

#### Lever 2: Coach System Prompt
**Target Metric**: Specificity Score (0.50 → 0.75+)
**Test Scenarios**: 
- `deadlift_form_correction`: Tests technical terminology usage
- `bench_press_plateau`: Tests programming knowledge specificity

**Verification Command**:
```bash
# Test prompt variant with enhanced specificity requirements
python -c "
from tests.core.test_ux_optimization_levers import test_lever_2_coach_prompt
result = test_lever_2_coach_prompt('strength_coach', variant='enhanced_specificity')
print(f'Specificity Improvement: {result[\"before\"]:.2f} → {result[\"after\"]:.2f}')
"
```

#### Lever 3: Tool Scripts
**Target Metric**: Tool Success Rate (0.00 → 0.80+)
**Test Scenarios**:
- Tool invocation failures in `azure_search_retriever`
- Missing fallback handling in exercise database

**Verification Command**:
```bash
# Test tool reliability improvements
python -c "
from tests.core.test_ux_optimization_levers import test_lever_3_tool_reliability
result = test_lever_3_tool_reliability()
print(f'Tool Success Rate: {result[\"success_rate\"]:.2%}')
print(f'Fallback Coverage: {result[\"fallback_coverage\"]:.2%}')
"
```

#### Lever 4: Reasoning Node Logic
**Target Metric**: Safety Score & Domain Routing Accuracy
**Test Scenarios**:
- Early pain detection in injury-related queries
- Domain-fit scoring for multi-coach scenarios

**Verification Command**:
```bash
# Test reasoning improvements
python -c "
from tests.core.test_ux_optimization_levers import test_lever_4_reasoning_node
result = test_lever_4_reasoning_node()
print(f'Pain Detection Rate: {result[\"pain_detection\"]:.2%}')
print(f'Domain Routing Accuracy: {result[\"routing_accuracy\"]:.2%}')
"
```

### Quality Metric Impact Analysis

| Quality Metric | Primary Levers | Secondary Levers | Expected Improvement |
|----------------|----------------|------------------|---------------------|
| **Specificity Score** | 2 (Prompt), 7 (RAG) | 3 (Tools), 11 (Tone) | 0.50 → 0.75 (50% increase) |
| **Safety Score** | 2 (Prompt), 4 (Reasoning) | 10 (Error Recovery) | 0.67 → 0.90 (34% increase) |
| **Coverage Score** | 2 (Prompt), 9 (Memory) | 5 (Planning) | 0.24 → 0.80 (233% increase) |
| **Tool Success Rate** | 3 (Tools), 4 (Reasoning) | 1 (LLM Params) | 0.00 → 0.80 (∞ increase) |
| **Multi-Turn Consistency** | 9 (Memory), 6 (Head Coach) | 4 (Reasoning) | N/A → 0.85 (new metric) |

### Automated Lever Selection

Based on current test failures, the system automatically recommends lever priorities:

```python
def recommend_lever_priorities(test_results):
    """Analyze test failures and recommend which levers to optimize."""
    recommendations = []
    
    # Low specificity → Lever 2 (Coach Prompt)
    if test_results.get("specificity_score", 0) < 0.75:
        recommendations.append({
            "lever": 2,
            "reason": f"Specificity {test_results['specificity_score']:.2f} < 0.75",
            "file": "athlea_langgraph/prompts/coaches/{coach_name}.json",
            "action": "Add technical terminology requirements"
        })
    
    # Tool failures → Lever 3 (Tool Scripts)
    if test_results.get("tool_success_rate", 0) < 0.80:
        recommendations.append({
            "lever": 3,
            "reason": f"Tool success {test_results['tool_success_rate']:.2%} < 80%",
            "file": "athlea_langgraph/tools/",
            "action": "Add fallback handling and error recovery"
        })
    
    # Safety issues → Lever 4 (Reasoning Node)
    if test_results.get("safety_score", 0) < 0.90:
        recommendations.append({
            "lever": 4,
            "reason": f"Safety {test_results['safety_score']:.2f} < 0.90",
            "file": "athlea_langgraph/nodes/reasoning_node.py",
            "action": "Add pain/injury detection logic"
        })
    
    return recommendations
```

### Lever Implementation Examples

#### Example 1: Lever 2 (Coach System Prompt) Enhancement

**File**: `athlea_langgraph/prompts/coaches/strength_coach.json`

**Before**:
```json
{
  "prompt": {
    "system": "You are a certified strength and conditioning coach..."
  }
}
```

**After**:
```json
{
  "prompt": {
    "system": "You are a certified strength and conditioning coach...\n\n## TECHNICAL SPECIFICITY REQUIREMENTS\n- Always include specific weights (lbs/kg) in recommendations\n- Use anatomical terms (e.g., 'hip hinge', 'scapular retraction')\n- Provide exact rep/set schemes (e.g., '3x5', '4x8-12')\n- Reference specific muscle groups by name\n\n## SAFETY PROTOCOLS\n- IMMEDIATELY acknowledge any mention of pain or discomfort\n- Recommend weight reduction before form corrections for injury scenarios\n- Include medical consultation advice when appropriate"
  }
}
```

#### Example 2: Lever 3 (Tool Scripts) Enhancement

**File**: `athlea_langgraph/tools/strength/search_strength_exercises.py`

**Before**:
```python
def search_strength_exercises(muscle_groups, equipment=None):
    results = database.search(muscle_groups, equipment)
    return results
```

**After**:
```python
def search_strength_exercises(muscle_groups, equipment=None):
    try:
        results = database.search(muscle_groups, equipment)
        if not results:
            # Fallback to broader search
            results = database.search(muscle_groups[0] if muscle_groups else "general")
        return results
    except Exception as e:
        logger.error(f"Exercise search failed: {e}")
        # Return helpful fallback response
        return {
            "exercises": [],
            "message": "I couldn't access the exercise database, but I can still provide recommendations based on my training knowledge.",
            "fallback": True
        }
```

### Continuous Improvement Workflow

1. **Baseline Measurement** → Run comprehensive verification
2. **Lever Analysis** → Identify top 3 failing metrics
3. **Implementation** → Apply lever-specific changes
4. **Validation** → Run targeted lever tests
5. **Full Regression** → Ensure no negative impacts
6. **Deploy** → Push improvements to production
7. **Monitor** → Track metrics in LangSmith dashboard

### Integration with LangSmith Monitoring

Track lever-specific improvements in LangSmith:

```python
metadata = {
    "workspace": "athlea-coaching-workspace",
    "optimization_lever": lever_number,
    "before_metric": before_value,
    "after_metric": after_value,
    "improvement_percentage": (after_value - before_value) / before_value * 100
}
```

## Support and Maintenance

### Regular Monitoring

- **Daily**: Check test execution logs
- **Weekly**: Review performance trends
- **Monthly**: Analyze optimization opportunities

### Performance Alerts

Set up alerts for:
- Response generation rate < 95%
- Test duration > 30 seconds
- Composite score < 0.30

### Contact Information

For issues with automated optimization:
1. Check this troubleshooting guide
2. Review test results in `tests/results/`
3. Verify LangSmith traces in portal
4. Consult system logs for detailed errors

---

**Last Updated**: June 19, 2025
**Guide Version**: 1.0.0
**Compatible System**: Athlea LangGraph 3.1.0
