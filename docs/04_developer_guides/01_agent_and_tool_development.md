# Complete Coach Setup Guide

This comprehensive guide walks you through creating a new specialized coach in the Athlea LangGraph system, from initial setup to deployment and testing.

## Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [Prerequisites](#prerequisites)
3. [State Management: The Core of the System](#state-management-the-core-of-the-system)
4. [Step-by-Step Coach Creation](#step-by-step-coach-creation)
5. [Tool Development & Integration](#tool-development--integration)
6. [Graph Integration](#graph-integration)
7. [Prompt Development](#prompt-development)
8. [Testing Strategy](#testing-strategy)
9. [Configuration & Deployment](#configuration--deployment)
10. [Troubleshooting](#troubleshooting)
11. [Reference](#reference)

---

## Architecture Overview

### Coaches vs Nodes
- **Coaches**: Domain-specific experts (e.g., `strength_coach`, `nutrition_coach`) that handle specialized knowledge and tools
- **Nodes**: Execution units in the graph workflow (e.g., `planning_node`, `reasoning_node`) that perform specific functions

### Key Components
- **Coach Manager** (`coach_manager.py`): Manages tool configuration and assignment to coaches
- **ReAct Coach Node** (`react_coach_node.py`): Implements ReAct pattern for coach execution
- **Individual Graphs**: Standalone graphs for each coach domain
- **MCP Servers**: External tool servers using Model Context Protocol
- **Prompt System**: Structured prompt management via PromptLoader

### Architecture Flow
```
User Request → Graph Router → Individual Coach Graph → Coach Node → Tools (via MCP) → Response
```

---

## Prerequisites

Before creating a new coach, ensure you have:

1. **Development Environment Setup**
   ```bash
   # Activate virtual environment
   source venv/bin/activate  # or your venv activation method
   
   # Install dependencies
   pip install -r requirements.txt
   ```

2. **Understanding of Existing Patterns**
   - Review existing coaches in `athlea_langgraph/agents/`
   - Study the coach_manager.py structure
   - Examine individual_graph.py for graph patterns

3. **Domain Knowledge**
   - Clear understanding of the coaching domain you're implementing
   - Identified tools and capabilities needed
   - Defined scope and boundaries

---

## State Management: The Core of the System

**IMPORTANT**: Before creating a coach, you must understand our state management strategy. All graphs in the system share a central, canonical `AgentState`.

- **Single Source of Truth**: The base state is defined in `athlea_langgraph/states/state.py`.
- **Inheritance Model**: For specialized graphs (like `onboarding`), you **must extend** the base `AgentState` rather than creating a new one from scratch.

```python
# Example of extending the base state
from athlea_langgraph.states.state import AgentState

class YogaCoachState(AgentState):
    """Adds only the yoga-specific fields."""
    current_pose: str
    sequence_id: str
```

- **Further Reading**: For a complete guide on state management, versioning, and best practices, please read the **[State Management Guide](./06_state_management.md)**.

Failing to adhere to this pattern will cause type errors and break graph execution.

---

## Step-by-Step Coach Creation

Let's create a **Yoga Coach** as a comprehensive example.

### Step 1: Create the Coach File

Create `athlea_langgraph/agents/yoga_coach.py`:

```python
"""
Yoga Coach Agent for the Athlea coaching system.

Specializes in yoga instruction, pose guidance, meditation, and mindfulness practices.
"""

import logging
from typing import Dict, Any, List

from .base_agent import BaseAgent
from ..states import AgentState

logger = logging.getLogger(__name__)


class YogaCoach(BaseAgent):
    """
    Specialized yoga coaching agent that provides:
    - Yoga pose instruction and sequencing
    - Meditation and mindfulness guidance
    - Flexibility and mobility assessments
    - Breathing technique coaching
    """

    def __init__(self):
        super().__init__(
            agent_name="yoga_coach",
            domain="yoga",
            description="Specialized yoga instructor providing pose guidance, meditation, and mindfulness coaching",
            temperature=0.7,  # Slightly higher for creative sequences
        )

    async def _get_system_context(self, state: AgentState) -> str:
        """Get yoga-specific system context."""
        context = await super()._get_system_context(state)
        
        # Add yoga-specific context
        yoga_context = """
        
YOGA COACHING EXPERTISE:
- Hatha, Vinyasa, and Restorative yoga styles
- Proper alignment and pose modifications
- Breathing techniques (pranayama)
- Meditation and mindfulness practices
- Injury prevention and therapeutic applications
        """
        
        return context + yoga_context

# Create the coach node function
async def yoga_coach_node(state: AgentState) -> Dict[str, Any]:
    """
    Yoga coach node that handles yoga-related queries and guidance.
    
    This node specializes in:
    - Yoga pose instruction and sequencing
    - Meditation and breathing techniques
    - Flexibility assessments
    - Mindfulness coaching
    """
    try:
        logger.info("🧘 YOGA COACH: Starting yoga coaching session")
        
        coach = YogaCoach()
        response = await coach.process_request(state)
        
        logger.info("🧘 YOGA COACH: Yoga coaching completed successfully")
        return response
        
    except Exception as e:
        logger.error(f"❌ YOGA COACH: Error in yoga coaching: {e}")
        return {
            "messages": [
                {
                    "role": "assistant", 
                    "content": f"I apologize, but I encountered an error while providing yoga guidance: {str(e)}. Please try again or contact support if the issue persists."
                }
            ]
        }
```

### Step 2: Update Coach Manager

Update `athlea_langgraph/agents/coach_manager.py` to include the new coach:

```python
# Add import at the top
from .yoga_coach import yoga_coach_node

# Add to the class
async def yoga_coach_node(state: AgentState) -> Dict[str, Any]:
    """Yoga coaching node with tools."""
    # Get yoga-specific tools
    tools = await self._get_yoga_coach_tools()
    
    # Use ReAct pattern with tools
    from .react_coach_node import create_react_coach_node
    react_node = create_react_coach_node(
        coach_function=yoga_coach_node,
        coach_name="yoga_coach",
        tools=tools
    )
    
    return await react_node(state)

async def _get_yoga_coach_tools(self) -> List[Any]:
    """Get tools for yoga coach."""
    tools = []
    
    # Add MCP tools when available
    if hasattr(self, '_yoga_tools') and self._yoga_tools:
        tools.extend(self._yoga_tools)
    
    # Add any standard tools
    if hasattr(self, '_database_tools') and self._database_tools:
        tools.extend(self._database_tools)
    
    logger.info(f"🧘 Yoga coach initialized with {len(tools)} tools")
    return tools
```

### Step 3: Update Imports

Update `athlea_langgraph/agents/__init__.py`:

```python
# Add to existing imports
from .yoga_coach import yoga_coach_node

# Add to __all__ list
__all__ = [
    # ... existing exports
    "yoga_coach_node",
]
```

Update main `athlea_langgraph/__init__.py`:

```python
# Add to coach imports
from .agents.coach_manager import (
    # ... existing coaches
    yoga_coach_node,
)

# Add to __all__ list
__all__ = [
    # ... existing exports
    "yoga_coach_node",
]
```

---

## Tool Development & Integration

### Step 1: Create Tool Functions

Create `athlea_langgraph/tools/yoga/`:

```python
# athlea_langgraph/tools/yoga/__init__.py
"""Yoga-specific tools for the coaching system."""

from .pose_library import search_yoga_poses, get_pose_details
from .sequence_generator import create_yoga_sequence
from .breathing_techniques import get_breathing_exercise

__all__ = [
    "search_yoga_poses",
    "get_pose_details", 
    "create_yoga_sequence",
    "get_breathing_exercise",
]
```

```python
# athlea_langgraph/tools/yoga/pose_library.py
"""Yoga pose library and search functionality."""

import logging
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class YogaPose(BaseModel):
    """Yoga pose model."""
    name: str
    sanskrit_name: Optional[str] = None
    difficulty: str = Field(..., description="Beginner, Intermediate, or Advanced")
    category: str = Field(..., description="Standing, Seated, Backbend, etc.")
    benefits: List[str] = Field(default_factory=list)
    contraindications: List[str] = Field(default_factory=list)
    instructions: str
    modifications: List[str] = Field(default_factory=list)


async def search_yoga_poses(
    category: Optional[str] = None,
    difficulty: Optional[str] = None,
    benefit: Optional[str] = None,
    limit: int = 10
) -> List[Dict[str, Any]]:
    """
    Search for yoga poses based on criteria.
    
    Args:
        category: Pose category (Standing, Seated, Backbend, etc.)
        difficulty: Difficulty level (Beginner, Intermediate, Advanced)
        benefit: Target benefit (flexibility, strength, balance, etc.)
        limit: Maximum number of poses to return
    """
    try:
        # Implementation would connect to pose database
        # For now, return sample data
        sample_poses = [
            {
                "name": "Mountain Pose",
                "sanskrit_name": "Tadasana",
                "difficulty": "Beginner",
                "category": "Standing",
                "benefits": ["posture", "grounding", "awareness"],
                "instructions": "Stand tall with feet hip-width apart, arms at sides..."
            },
            {
                "name": "Downward Dog",
                "sanskrit_name": "Adho Mukha Svanasana", 
                "difficulty": "Beginner",
                "category": "Inversion",
                "benefits": ["strength", "flexibility", "energizing"],
                "instructions": "Start on hands and knees, tuck toes under..."
            }
        ]
        
        logger.info(f"🧘 Found {len(sample_poses)} yoga poses")
        return sample_poses[:limit]
        
    except Exception as e:
        logger.error(f"Error searching yoga poses: {e}")
        return []
```

### Step 2: Create MCP Server

Create `mcp_servers/yoga_mcp/server.py`:

```python
"""
MCP Server for Yoga tools.

Provides yoga pose library, sequence generation, and breathing techniques.
"""

import logging
from typing import Any, Dict, List

from mcp.server.models import InitializationOptions
from mcp.server import NotificationOptions, Server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)

# Import your yoga tools
from athlea_langgraph.tools.yoga import (
    search_yoga_poses,
    get_pose_details,
    create_yoga_sequence,
    get_breathing_exercise
)

logger = logging.getLogger(__name__)

# Define available tools
YOGA_TOOLS = [
    Tool(
        name="search_yoga_poses",
        description="Search for yoga poses based on category, difficulty, or benefits",
        inputSchema={
            "type": "object",
            "properties": {
                "category": {
                    "type": "string",
                    "description": "Pose category (Standing, Seated, Backbend, etc.)",
                    "enum": ["Standing", "Seated", "Backbend", "Forward Fold", "Twist", "Inversion", "Arm Balance"]
                },
                "difficulty": {
                    "type": "string", 
                    "description": "Difficulty level",
                    "enum": ["Beginner", "Intermediate", "Advanced"]
                },
                "benefit": {
                    "type": "string",
                    "description": "Target benefit"
                },
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of poses to return",
                    "default": 10
                }
            },
            "required": []
        }
    ),
    Tool(
        name="create_yoga_sequence",
        description="Generate a custom yoga sequence based on goals and time",
        inputSchema={
            "type": "object",
            "properties": {
                "duration_minutes": {
                    "type": "integer",
                    "description": "Sequence duration in minutes"
                },
                "goal": {
                    "type": "string",
                    "description": "Primary goal (flexibility, strength, relaxation, etc.)"
                },
                "difficulty": {
                    "type": "string",
                    "enum": ["Beginner", "Intermediate", "Advanced"]
                }
            },
            "required": ["duration_minutes", "goal"]
        }
    )
]

# Create server instance
app = Server("yoga-mcp-server")

@app.list_tools()
async def handle_list_tools() -> List[Tool]:
    """List available yoga tools."""
    return YOGA_TOOLS

@app.call_tool()
async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle tool execution."""
    try:
        if name == "search_yoga_poses":
            result = await search_yoga_poses(**arguments)
            return [TextContent(
                type="text",
                text=f"Found {len(result)} yoga poses: {result}"
            )]
            
        elif name == "create_yoga_sequence":
            result = await create_yoga_sequence(**arguments)
            return [TextContent(
                type="text", 
                text=f"Generated yoga sequence: {result}"
            )]
            
        else:
            raise ValueError(f"Tool '{name}' not found")
            
            except Exception as e:
        logger.error(f"Error executing tool {name}: {e}")
        return [TextContent(
            type="text",
            text=f"Error executing {name}: {str(e)}"
        )]

async def main():
    """Run the MCP server."""
    from mcp.server.stdio import stdio_server
    
    async with stdio_server() as (read_stream, write_stream):
        await app.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="yoga-mcp-server",
                server_version="1.0.0",
                capabilities=app.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
```

### Step 3: Configure MCP Server

Add to `.cursor/mcp.json`:

```json
{
  "mcpServers": {
    "athlea-yoga": {
      "command": "python",
      "args": ["-m", "mcp_servers.yoga_mcp.server"],
      "env": {
        "PYTHONPATH": "/path/to/your/project"
      }
    }
  }
}
```

---

## Graph Integration

### Step 1: Add to Individual Graph

Update `athlea_langgraph/graphs/individual_graph.py`:

```python
# Add import
from ..agents.coach_manager import yoga_coach_node

# Add to create_individual_coach_graph function
elif coach_type == "yoga_coach":
    logger.info("🧘 Creating yoga coach graph")
    
    builder.add_node("coach", yoga_coach_node)
    builder.add_edge(START, "coach")
    builder.add_edge("coach", END)
    
    return builder.compile()
```

### Step 2: Add to Main Coaching Graph

Update `athlea_langgraph/graphs/coaching_graph.py`:

```python
# Add import
from ..agents.coach_manager import yoga_coach_node

# Add node to graph builder
builder.add_node("yoga_coach", yoga_coach_node)

# Add routing logic
def route_to_coach(state: AgentState) -> str:
    # ... existing routing logic
    
    # Add yoga routing
    if any(keyword in query.lower() for keyword in [
        "yoga", "meditation", "mindfulness", "breathing", "pranayama",
        "flexibility", "stretching", "pose", "asana"
    ]):
        logger.info("🧘 Routing to yoga coach")
        return "yoga_coach"
```

### Step 3: Update Graph Factory

The graph factory should automatically discover the new coach through the individual_graph.py integration.

---

## Prompt Development

### Step 1: Create Prompt Directory

Create directory structure:
```
athlea_langgraph/prompts/coaches/yoga/
├── system.md
├── instruction.md
├── safety.md
└── examples.md
```

### Step 2: System Prompt

Create `athlea_langgraph/prompts/coaches/yoga/system.md`:

```markdown
# Yoga Coach System Prompt

You are a certified yoga instructor and mindfulness coach with extensive experience in:

## Expertise Areas
- **Yoga Styles**: Hatha, Vinyasa, Restorative, Yin, and therapeutic yoga
- **Anatomy & Alignment**: Proper pose alignment and injury prevention  
- **Breathwork**: Pranayama techniques and breathing patterns
- **Meditation**: Mindfulness practices and guided meditation
- **Philosophy**: Yoga philosophy and lifestyle integration

## Coaching Approach
- Emphasize safety and proper alignment above all
- Provide modifications for different ability levels
- Include breathing instructions with poses
- Consider contraindications and injuries
- Promote mindful movement and awareness

## Communication Style
- Calm, encouraging, and supportive tone
- Use both English and Sanskrit names when appropriate
- Provide clear, step-by-step instructions
- Ask about experience level and any injuries
- Encourage self-awareness and listening to the body

## Safety Guidelines
- Always emphasize proper warm-up
- Provide modifications for beginners
- Warn about contraindications
- Encourage students to honor their limits
- Suggest consulting healthcare providers when needed
```

### Step 3: Instruction Prompts

Create specific instruction prompts for different scenarios in the same directory.

### Step 4: Load Prompts in Coach

Update your coach to use prompts:

```python
async def _get_system_context(self, state: AgentState) -> str:
    """Get yoga-specific system context."""
    try:
        # Load yoga system prompt
        yoga_prompt = await self.prompt_loader.load_prompt("coaches/yoga/system")
        return yoga_prompt.prompt.system
    except Exception as e:
        logger.warning(f"Could not load yoga system prompt: {e}")
        # Fallback to hardcoded prompt
        return super()._get_system_context(state)
```

---

## Testing Strategy

### Step 1: Unit Tests

Create `tests/unit/test_yoga_coach.py`:

```python
"""Unit tests for yoga coach."""

import pytest
from unittest.mock import AsyncMock, patch

from athlea_langgraph.agents.yoga_coach import YogaCoach, yoga_coach_node
from athlea_langgraph.states import AgentState


class TestYogaCoach:
    
    @pytest.fixture
    def sample_state(self) -> AgentState:
        return {
            "messages": [
                {"role": "user", "content": "I need help with yoga poses for beginners"}
            ],
            "user_id": "test_user",
            "thread_id": "test_thread",
            # Initialize all other AgentState fields to default values
            "user_query": "I need help with yoga poses for beginners",
            "user_profile": {},
            "coach_type": "yoga_coach",
            "final_response": None,
            "clarification_output": None,
            "aggregated_response": None,
            "specialist_responses": {},
            "safety_alert": None,
            "safety_details": None,
            "routing_priority": None,
        }
    
    @pytest.mark.asyncio
    async def test_yoga_coach_initialization(self):
        """Test yoga coach initializes correctly."""
        coach = YogaCoach()
        assert coach.agent_name == "yoga_coach"
        assert coach.domain == "yoga"
        assert coach.temperature == 0.7
    
    @pytest.mark.asyncio
    async def test_yoga_coach_node(self, sample_state):
        """Test yoga coach node execution."""
        with patch('athlea_langgraph.agents.yoga_coach.YogaCoach') as mock_coach_class:
            mock_coach = AsyncMock()
            mock_coach.process_request.return_value = {
                "messages": [{"role": "assistant", "content": "Here are some beginner yoga poses..."}]
            }
            mock_coach_class.return_value = mock_coach
            
            result = await yoga_coach_node(sample_state)
            
            assert "messages" in result
            assert len(result["messages"]) > 0
            mock_coach.process_request.assert_called_once_with(sample_state)
```

### Step 2: Integration Tests

Create `tests/integration/test_yoga_integration.py`:

```python
"""Integration tests for yoga coach with tools and graphs."""

import pytest
from athlea_langgraph.graphs.individual_graph import create_individual_coach_graph
from athlea_langgraph.states import AgentState


class TestYogaIntegration:
    
    @pytest.mark.asyncio
    async def test_yoga_graph_execution(self):
        """Test complete yoga coaching graph execution."""
        # Create yoga coach graph
        graph = create_individual_coach_graph("yoga_coach")
        
        # Test state - MUST be a dictionary matching AgentState
        state: AgentState = {
            "messages": [
                {"role": "user", "content": "Create a 15-minute morning yoga sequence"}
            ],
            "user_id": "test_user",
            "thread_id": "test_thread",
            "user_query": "Create a 15-minute morning yoga sequence",
            "user_profile": {},
            "coach_type": "yoga_coach",
            "final_response": None,
            "clarification_output": None,
            "aggregated_response": None,
            "specialist_responses": {},
            "safety_alert": None,
            "safety_details": None,
            "routing_priority": None,
        }
        
        # Execute graph
        result = await graph.ainvoke(state)
        
        # Verify response
        assert "messages" in result
        assert len(result["messages"]) > 1  # Should have user message + assistant response
        
        assistant_message = result["messages"][-1]
        assert assistant_message["role"] == "assistant"
        assert len(assistant_message["content"]) > 0
```

### Step 3: Tool Tests

Create `tests/tools/test_yoga_tools.py`:

```python
"""Tests for yoga tools."""

import pytest
from athlea_langgraph.tools.yoga import search_yoga_poses, create_yoga_sequence


class TestYogaTools:
    
    @pytest.mark.asyncio
    async def test_search_yoga_poses(self):
        """Test yoga pose search functionality."""
        result = await search_yoga_poses(difficulty="Beginner", limit=5)
        
        assert isinstance(result, list)
        assert len(result) <= 5
        
        if result:
            pose = result[0]
            assert "name" in pose
            assert "difficulty" in pose
            assert pose["difficulty"] == "Beginner"
    
    @pytest.mark.asyncio
    async def test_create_yoga_sequence(self):
        """Test yoga sequence generation."""
        result = await create_yoga_sequence(
            duration_minutes=15,
            goal="flexibility",
            difficulty="Beginner"
        )
        
        assert isinstance(result, dict)
        assert "sequence" in result
        assert "duration" in result
        assert result["duration"] == 15
```

### Step 4: End-to-End Tests

Create `tests/e2e/test_yoga_e2e.py`:

```python
"""End-to-end tests for yoga coaching."""

import pytest
import asyncio
from athlea_langgraph.graph_factory import get_graph_factory


class TestYogaE2E:
    
    @pytest.mark.asyncio
    async def test_yoga_coaching_workflow(self):
        """Test complete yoga coaching workflow."""
        # Get graph factory
        factory = get_graph_factory()
        
        # Get yoga coach graph
        graph = await factory.get_graph("yoga_coach")
        
        # Test multiple scenarios
        scenarios = [
            "I'm new to yoga. What poses should I start with?",
            "Create a 20-minute yoga sequence for stress relief",
            "Show me breathing exercises for anxiety",
            "I have lower back pain. What yoga poses can help?"
        ]
        
        for scenario in scenarios:
            state: AgentState = {
                "messages": [{"role": "user", "content": scenario}],
                "user_id": "test_user",
                "thread_id": f"test_{hash(scenario)}",
                "user_query": scenario,
                "user_profile": {},
                "coach_type": "yoga_coach",
                "final_response": None,
                "clarification_output": None,
                "aggregated_response": None,
                "specialist_responses": {},
                "safety_alert": None,
                "safety_details": None,
                "routing_priority": None,
            }
            
            result = await graph.ainvoke(state)
            
            # Verify meaningful response
            assert "messages" in result
            assistant_response = result["messages"][-1]
            assert assistant_response["role"] == "assistant"
            assert len(assistant_response["content"]) > 50  # Meaningful response
```

---

## Configuration & Deployment

### Step 1: Environment Variables

Add to your `.env` file:

```bash
# Yoga Coach Configuration
YOGA_POSES_DATABASE_URL=your_database_url
YOGA_MCP_SERVER_PORT=9003
ENABLE_YOGA_COACH=true
```

### Step 2: Update Configuration Files

Update `mcp_servers/config/mcp_servers.json`:

```json
{
  "servers": {
    "yoga": {
      "module": "mcp_servers.yoga_mcp.server",
      "port": 9003,
      "enabled": true,
      "tools": ["search_yoga_poses", "create_yoga_sequence", "get_breathing_exercise"]
    }
  }
}
```

### Step 3: Database Setup

If using a database for yoga poses, add migration:

```sql
-- migrations/add_yoga_poses_table.sql
CREATE TABLE yoga_poses (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    sanskrit_name VARCHAR(255),
    difficulty VARCHAR(50) NOT NULL,
    category VARCHAR(100) NOT NULL,
    benefits TEXT[],
    contraindications TEXT[],
    instructions TEXT NOT NULL,
    modifications TEXT[],
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### Step 4: Deployment Checklist

- [ ] Coach file created and tested
- [ ] Tools developed and tested
- [ ] MCP server configured and running
- [ ] Graph integration completed
- [ ] Prompts created and loaded
- [ ] Unit tests passing
- [ ] Integration tests passing
- [ ] End-to-end tests passing
- [ ] Environment variables configured
- [ ] Database migrations run (if applicable)
- [ ] Documentation updated

---

## Troubleshooting

### Common Issues

#### 1. Import Errors
```bash
ImportError: cannot import name 'yoga_coach_node'
```
**Solution**: Check import statements in `__init__.py` files and ensure proper module structure.

#### 2. Graph Discovery Issues
```bash
Graph 'yoga_coach' not found in factory
```
**Solution**: Verify graph integration in `individual_graph.py` and check spelling of coach_type.

#### 3. Tool Loading Errors
```bash
No tools available for yoga coach
```
**Solution**: Check MCP server configuration and tool initialization in coach_manager.py.

#### 4. Prompt Loading Errors
```bash
Could not load yoga system prompt
```
**Solution**: Verify prompt file paths and ensure PromptLoader can access the files.

### Debug Commands

```bash
# Test coach import
python -c "from athlea_langgraph.agents.yoga_coach import yoga_coach_node; print('✅ Import successful')"

# Test graph creation
python -c "from athlea_langgraph.graphs.individual_graph import create_individual_coach_graph; graph = create_individual_coach_graph('yoga_coach'); print('✅ Graph created')"

# Test tool loading
python -c "from athlea_langgraph.tools.yoga import search_yoga_poses; print('✅ Tools loaded')"

# Test MCP server
python -m mcp_servers.yoga_mcp.server
```

### Performance Optimization

1. **Lazy Tool Loading**: Initialize tools only when needed
2. **Caching**: Cache frequently accessed poses and sequences
3. **Connection Pooling**: Use connection pooling for database access
4. **Async Operations**: Ensure all operations are properly async

---

## Reference

### File Structure
```
athlea_langgraph/
├── agents/
│   ├── yoga_coach.py              # Main coach implementation
│   ├── coach_manager.py           # Tool management and coach integration
│   └── __init__.py               # Exports
├── tools/
│   └── yoga/                     # Yoga-specific tools
│       ├── __init__.py
│       ├── pose_library.py
│       ├── sequence_generator.py
│       └── breathing_techniques.py
├── prompts/
│   └── coaches/
│       └── yoga/                 # Yoga prompts
│           ├── system.md
│           ├── instruction.md
│           └── examples.md
└── graphs/
    ├── individual_graph.py       # Individual coach graphs
    └── coaching_graph.py         # Main coaching integration

mcp_servers/
└── yoga_mcp/
    ├── server.py                 # MCP server implementation
    └── __init__.py

tests/
├── unit/
│   └── test_yoga_coach.py
├── integration/
│   └── test_yoga_integration.py
├── tools/
│   └── test_yoga_tools.py
└── e2e/
    └── test_yoga_e2e.py
```

### Naming Conventions

- **Coach Files**: `{domain}_coach.py` (e.g., `yoga_coach.py`)
- **Node Functions**: `{domain}_coach_node` (e.g., `yoga_coach_node`)
- **Tool Modules**: `tools/{domain}/` (e.g., `tools/yoga/`)
- **MCP Servers**: `mcp_servers/{domain}_mcp/` (e.g., `mcp_servers/yoga_mcp/`)
- **Prompts**: `prompts/coaches/{domain}/` (e.g., `prompts/coaches/yoga/`)
- **Tests**: Mirror the source structure with `test_` prefix

### Code Patterns

#### Coach Class Pattern
```python
class {Domain}Coach(BaseAgent):
    def __init__(self):
        super().__init__(
            agent_name="{domain}_coach",
            domain="{domain}",
            description="...",
            temperature=0.7
        )
```

#### Node Function Pattern
```python
async def {domain}_coach_node(state: AgentState) -> Dict[str, Any]:
    try:
        logger.info(f"🎯 {DOMAIN} COACH: Starting session")
        coach = {Domain}Coach()
        response = await coach.process_request(state)
        logger.info(f"🎯 {DOMAIN} COACH: Session completed")
        return response
    except Exception as e:
        logger.error(f"❌ {DOMAIN} COACH: Error: {e}")
        return {"messages": [{"role": "assistant", "content": f"Error: {str(e)}"}]}
```

#### Tool Pattern
```python
async def {action}_{domain}_{object}(
    param1: str,
    param2: Optional[int] = None
) -> List[Dict[str, Any]]:
    """
    {Description of what the tool does}.
    
    Args:
        param1: Description
        param2: Description
        
    Returns:
        List of results
    """
    try:
        # Implementation
        return results
    except Exception as e:
        logger.error(f"Error in {tool_name}: {e}")
        return []
```

This comprehensive guide should enable you to create fully functional coaches from scratch. Each section builds upon the previous ones, providing a complete development lifecycle from creation to deployment. 