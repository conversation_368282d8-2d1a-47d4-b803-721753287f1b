# UX Optimization Workflow Guide

This guide provides a step-by-step workflow for systematically improving user experience using the 12-lever optimization framework.

## Overview

The UX optimization workflow follows a data-driven approach where test failures directly map to specific system improvements. Each "lever" represents a modifiable component that impacts user experience.

## Quick Start

```bash
# Run UX lever analysis on latest test results
python -c "
from tests.core.test_ux_optimization_levers import analyze_and_recommend_levers
recommendations = analyze_and_recommend_levers()
for rec in recommendations[:3]:
    print(f'🎯 Lever {rec[\"lever\"]}: {rec[\"action\"]}')
    print(f'   File: {rec[\"file\"]}')
    print(f'   Expected Impact: {rec[\"impact\"]}')
"
```

## The 4-Step Sprint Process

### Step 1: Pick Target Levers

Based on test failures, select 2-3 high-impact levers:

```bash
# Analyze current state and get lever recommendations
python tests/core/test_ux_optimization_levers.py --analyze
```

**Example Output**:
```
🔍 CURRENT STATE ANALYSIS
========================
Specificity Score: 0.50 (Target: 0.75) ❌
Safety Score: 0.67 (Target: 0.90) ❌
Coverage Score: 0.24 (Target: 0.80) ❌
Tool Success Rate: 0.00 (Target: 0.80) ❌

📊 RECOMMENDED LEVER PRIORITIES
==============================
1. Lever 2 (Coach System Prompt) - Fix specificity issues
2. Lever 3 (Tool Scripts) - Fix tool invocation failures
3. Lever 4 (Reasoning Node) - Improve safety detection
```

### Step 2: Make Targeted Edits

For each selected lever, implement specific improvements:

#### Lever 2: Coach System Prompt Enhancement

**File**: `athlea_langgraph/prompts/coaches/strength_coach.json`

```json
{
  "metadata": {
    "version": "3.1.0",
    "ux_optimization": {
      "lever": 2,
      "improvements": ["specificity", "safety", "coverage"]
    }
  },
  "prompt": {
    "system": "You are a certified strength and conditioning coach with 10+ years of experience...\n\n## UX OPTIMIZATION REQUIREMENTS\n\n### TECHNICAL SPECIFICITY (Lever 2)\n- ALWAYS include specific numbers: weights (185 lbs), reps (3x5), rest periods (90 seconds)\n- USE anatomical terminology: 'hip hinge pattern', 'scapular retraction', 'thoracic extension'\n- PROVIDE exact programming: '3 sets of 5 reps at 80% 1RM with 3 minutes rest'\n\n### SAFETY FIRST (Lever 2 + 4)\n- IMMEDIATELY acknowledge ANY mention of pain, discomfort, or injury\n- First response to pain MUST be: 'I hear you're experiencing [specific pain]. Let's address this safely.'\n- ALWAYS recommend load reduction before technique changes for pain scenarios\n\n### COMPREHENSIVE COVERAGE (Lever 2 + 5)\n- Address ALL aspects mentioned in user's query\n- Structure responses with clear sections\n- Include: Assessment → Recommendations → Programming → Follow-up"
  }
}
```

#### Lever 3: Tool Script Enhancement

**File**: `athlea_langgraph/tools/strength/search_strength_exercises.py`

```python
from typing import List, Dict, Optional
import logging

logger = logging.getLogger(__name__)

def search_strength_exercises(
    muscle_groups: List[str], 
    equipment: Optional[List[str]] = None,
    difficulty: str = "intermediate"
) -> Dict:
    """
    Enhanced exercise search with UX optimizations (Lever 3).
    
    UX Improvements:
    - Graceful fallback handling
    - Helpful error messages
    - Always returns actionable content
    """
    try:
        # Primary search attempt
        results = exercise_database.search(
            muscle_groups=muscle_groups,
            equipment=equipment,
            difficulty=difficulty
        )
        
        # Fallback if no results
        if not results or len(results) < 3:
            logger.info(f"Limited results for {muscle_groups}, expanding search")
            
            # Try without equipment filter
            if equipment:
                results = exercise_database.search(
                    muscle_groups=muscle_groups,
                    difficulty=difficulty
                )
            
            # Try broader muscle group
            if not results and len(muscle_groups) > 1:
                results = exercise_database.search(
                    muscle_groups=[muscle_groups[0]],
                    difficulty=difficulty
                )
        
        return {
            "exercises": results,
            "search_params": {
                "muscle_groups": muscle_groups,
                "equipment": equipment,
                "difficulty": difficulty
            },
            "fallback_used": len(results) < len(muscle_groups) * 2
        }
        
    except Exception as e:
        logger.error(f"Exercise search failed: {e}")
        
        # UX-optimized fallback response
        return {
            "exercises": [],
            "fallback_message": "I couldn't access the exercise database, but I can still help you with proven exercises for " + 
                              f"{', '.join(muscle_groups)}. Let me provide recommendations based on fundamental movements.",
            "error_handled": True,
            "suggested_exercises": _get_fallback_exercises(muscle_groups)
        }

def _get_fallback_exercises(muscle_groups: List[str]) -> List[Dict]:
    """Provide sensible fallback exercises for common muscle groups."""
    fallback_map = {
        "chest": [
            {"name": "Push-ups", "difficulty": "beginner"},
            {"name": "Dumbbell Bench Press", "difficulty": "intermediate"},
            {"name": "Barbell Bench Press", "difficulty": "intermediate"}
        ],
        "back": [
            {"name": "Pull-ups", "difficulty": "intermediate"},
            {"name": "Bent-Over Row", "difficulty": "intermediate"},
            {"name": "Lat Pulldown", "difficulty": "beginner"}
        ],
        "legs": [
            {"name": "Squats", "difficulty": "intermediate"},
            {"name": "Lunges", "difficulty": "beginner"},
            {"name": "Deadlifts", "difficulty": "intermediate"}
        ]
    }
    
    exercises = []
    for muscle in muscle_groups:
        exercises.extend(fallback_map.get(muscle.lower(), []))
    
    return exercises[:5]  # Return top 5 to avoid overwhelming
```

#### Lever 4: Reasoning Node Enhancement

**File**: `athlea_langgraph/nodes/reasoning_node.py`

```python
async def enhanced_reasoning_node(state: AgentState) -> Dict:
    """
    Enhanced reasoning with UX optimizations (Lever 4).
    
    UX Improvements:
    - Early pain/injury detection
    - Domain-fit scoring
    - Ambiguity detection
    """
    query = state.user_query.lower()
    
    # Priority 1: Safety Detection (Lever 4)
    safety_concerns = detect_safety_concerns(query)
    if safety_concerns:
        state.safety_alert = True
        state.safety_details = safety_concerns
        state.routing_priority = "safety_first"
    
    # Priority 2: Domain Fit Scoring (Lever 4 + 5)
    domain_scores = calculate_domain_fit(query)
    state.domain_scores = domain_scores
    state.recommended_coaches = [
        coach for coach, score in domain_scores.items() 
        if score > 0.7
    ]
    
    # Priority 3: Ambiguity Detection (Lever 5)
    if len(query.split()) < 3 or is_ambiguous(query):
        state.needs_clarification = True
        state.clarification_prompts = generate_clarification_prompts(query)
    
    return {"reasoning_complete": True}

def detect_safety_concerns(query: str) -> Optional[Dict]:
    """Detect pain, injury, or safety-related keywords."""
    safety_keywords = {
        "pain": ["hurt", "pain", "ache", "sore", "discomfort"],
        "injury": ["injury", "injured", "strain", "sprain", "tear"],
        "medical": ["surgery", "doctor", "diagnosis", "condition"]
    }
    
    concerns = {}
    for category, keywords in safety_keywords.items():
        if any(keyword in query for keyword in keywords):
            concerns[category] = True
            
    return concerns if concerns else None

def calculate_domain_fit(query: str) -> Dict[str, float]:
    """Calculate relevance scores for each coach domain."""
    domain_keywords = {
        "strength_coach": ["lift", "weight", "strength", "muscle", "bench", "squat", "deadlift"],
        "cardio_coach": ["run", "cardio", "endurance", "heart rate", "aerobic"],
        "nutrition_coach": ["eat", "diet", "nutrition", "calorie", "protein", "meal"],
        "recovery_coach": ["recovery", "sleep", "rest", "sore", "fatigue"],
        "mental_coach": ["motivation", "mindset", "stress", "anxiety", "mental"]
    }
    
    scores = {}
    for coach, keywords in domain_keywords.items():
        score = sum(1 for keyword in keywords if keyword in query)
        scores[coach] = min(score / 3, 1.0)  # Normalize to 0-1
        
    return scores
```

### Step 3: Run Tests & Trace

After implementing lever improvements, validate with targeted tests:

```bash
# Test specific lever improvements
python -c "
from tests.core.test_ux_optimization_levers import validate_lever_improvements

# Test Lever 2 (Coach Prompt)
lever_2_results = validate_lever_improvements(2, 'strength_coach')
print(f'Lever 2 Impact: {lever_2_results[\"improvement\"]:.1%}')

# Test Lever 3 (Tools)  
lever_3_results = validate_lever_improvements(3)
print(f'Lever 3 Impact: {lever_3_results[\"improvement\"]:.1%}')

# Test Lever 4 (Reasoning)
lever_4_results = validate_lever_improvements(4)
print(f'Lever 4 Impact: {lever_4_results[\"improvement\"]:.1%}')
"
```

**LangSmith Validation**:
1. Navigate to https://smith.langchain.com/
2. Filter by metadata: `optimization_lever: 2`
3. Compare before/after traces for quality improvements

### Step 4: Iterate Through Lower-Impact Levers

Once high-priority levers show improvement, address secondary optimizations:

```bash
# Run comprehensive lever analysis
python tests/core/test_ux_optimization_levers.py --full-analysis

# Example progression:
# Sprint 1: Levers 2, 4, 5 (Prompts, Reasoning, Planning)
# Sprint 2: Levers 3, 7 (Tools, RAG)
# Sprint 3: Levers 9, 10 (Memory, Error Recovery)
# Sprint 4: Levers 1, 6, 11, 12 (Fine-tuning)
```

## Lever-Specific Implementation Guide

### High-Impact Levers (Priority 1)

#### Lever 2: Coach System Prompt
- **Files**: `athlea_langgraph/prompts/coaches/*.json`
- **Key Changes**: Add specificity requirements, safety protocols, coverage guidelines
- **Validation**: Check specificity_score improvement in test results

#### Lever 4: Reasoning Node Logic  
- **Files**: `athlea_langgraph/nodes/reasoning_node.py`
- **Key Changes**: Add pain detection, domain scoring, ambiguity handling
- **Validation**: Check safety_score and routing accuracy

#### Lever 5: Planner/Routing Node
- **Files**: `athlea_langgraph/nodes/planning_node.py`
- **Key Changes**: Add clarification rules, multi-coach handoffs
- **Validation**: Check ambiguous query handling

### Medium-Impact Levers (Priority 2)

#### Lever 3: Tool Scripts
- **Files**: `athlea_langgraph/tools/**/*.py`
- **Key Changes**: Add fallback handling, better error messages
- **Validation**: Check tool_success_rate improvement

#### Lever 7: RAG Configuration
- **Files**: `athlea_langgraph/services/azure_search_service.py`
- **Key Changes**: Tune chunk size, improve relevance scoring
- **Validation**: Check retrieval precision metrics

#### Lever 9: Memory Management
- **Files**: `athlea_langgraph/memory/advanced_memory_manager.py`
- **Key Changes**: Increase context window, add intent tracking
- **Validation**: Check multi-turn consistency scores

### Lower-Impact Levers (Priority 3)

#### Lever 1: LLM Parameters
- **Files**: `athlea_langgraph/services/azure_openai_service.py`
- **Key Changes**: Adjust temperature, upgrade model version
- **Validation**: Check response consistency

#### Lever 11: UX Tone
- **Files**: System prompts in `prompts/coaches/*.json`
- **Key Changes**: Add empathy, use user's name, adjust formality
- **Validation**: User satisfaction metrics

## Automated Lever Testing Framework

The testing framework automatically maps failures to lever recommendations:

```python
# tests/core/test_ux_optimization_levers.py

class UXLeverOptimizer:
    """Automated UX lever testing and optimization framework."""
    
    def __init__(self):
        self.lever_definitions = {
            1: {"name": "LLM Model & Params", "impact": "medium-high"},
            2: {"name": "Coach System Prompt", "impact": "high"},
            3: {"name": "Tool Scripts", "impact": "medium"},
            4: {"name": "Reasoning Node Logic", "impact": "high"},
            5: {"name": "Planner/Routing Node", "impact": "high"},
            6: {"name": "Head Coach Orchestration", "impact": "medium"},
            7: {"name": "RAG Retriever Config", "impact": "high"},
            8: {"name": "Knowledge Base", "impact": "medium-high"},
            9: {"name": "Memory & Context", "impact": "medium"},
            10: {"name": "Error-Recovery Prompts", "impact": "high"},
            11: {"name": "UX-Tone & Persona", "impact": "medium"},
            12: {"name": "Monitoring & Alerts", "impact": "low-medium"}
        }
    
    def analyze_current_state(self, test_results: Dict) -> List[Dict]:
        """Analyze test results and recommend lever priorities."""
        recommendations = []
        
        # Map quality metrics to levers
        metric_to_lever_map = {
            "specificity_score": [2, 3, 7],
            "safety_score": [2, 4, 10],
            "coverage_score": [2, 5, 9],
            "tool_success_rate": [3, 4],
            "multi_turn_consistency": [9, 6]
        }
        
        for metric, value in test_results.items():
            if metric in metric_to_lever_map:
                target = self.get_target_threshold(metric)
                if value < target:
                    for lever in metric_to_lever_map[metric]:
                        recommendations.append({
                            "lever": lever,
                            "metric": metric,
                            "current": value,
                            "target": target,
                            "gap": target - value,
                            "priority": self.calculate_priority(lever, target - value)
                        })
        
        # Sort by priority
        return sorted(recommendations, key=lambda x: x["priority"], reverse=True)
```

## Monitoring & Validation

### LangSmith Dashboard Setup

Create custom dashboard for UX optimization tracking:

```python
# scripts/setup_ux_optimization_dashboard.py

from langsmith import Client

def setup_ux_dashboard():
    client = Client()
    
    # Create UX Optimization Dashboard
    dashboard = client.create_dashboard(
        name="UX Optimization Levers",
        description="Track improvements from 12-lever optimization"
    )
    
    # Add lever-specific charts
    for lever in range(1, 13):
        client.add_chart(
            dashboard_id=dashboard.id,
            name=f"Lever {lever} Impact",
            query={
                "filter": {"metadata.optimization_lever": lever},
                "metrics": ["specificity_score", "safety_score", "coverage_score"],
                "aggregation": "average",
                "group_by": "metadata.before_after"
            }
        )
```

### Success Criteria

Each lever has specific success criteria:

| Lever | Success Metric | Target Value |
|-------|---------------|--------------|
| 2 | Specificity Score | ≥ 0.75 |
| 3 | Tool Success Rate | ≥ 0.80 |
| 4 | Safety Detection Rate | ≥ 0.95 |
| 5 | Ambiguity Handling | ≥ 0.90 |
| 7 | RAG Precision | ≥ 0.85 |
| 9 | Multi-Turn Consistency | ≥ 0.85 |

## Best Practices

1. **Always Test in Isolation**: Test each lever change independently before combining
2. **Maintain Baselines**: Keep baseline measurements for regression detection
3. **Document Changes**: Use git commits like "Lever 2: Enhanced strength coach prompt for specificity"
4. **Monitor Side Effects**: Some levers may negatively impact others (e.g., specificity vs. response length)
5. **User Feedback Loop**: Collect real user feedback to validate improvements

## Troubleshooting

### Common Issues

**Issue**: Lever improvements don't reflect in tests
- Check if changes are in the correct file path
- Verify the coach/node is actually using the updated component
- Clear any caches that might be using old versions

**Issue**: Regression in other metrics after lever change
- Some levers have trade-offs (e.g., specificity vs. response length)
- Use the full regression test suite before deploying
- Consider adjusting multiple levers together for balance

**Issue**: LangSmith traces don't show improvements
- Ensure metadata includes `optimization_lever` field
- Check time filters (use "Last 6 hours")
- Verify traces are going to correct project

## Next Steps

After completing initial lever optimizations:

1. Run production A/B tests with real users
2. Collect qualitative feedback on improvements
3. Fine-tune based on real-world usage patterns
4. Consider creating coach-specific lever configurations
5. Automate lever optimization in CI/CD pipeline

For detailed implementation examples and code snippets, see:
- [Testing Guide](./03_testing_guide.md#ux-optimization-checklist)
- [Automated Optimization Guide](./04_automated_optimization_guide.md#systematic-ux-improvement-framework) 