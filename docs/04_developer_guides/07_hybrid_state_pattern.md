# Hybrid State Pattern: TypedDict + Pydantic Models

## Overview

This guide explains our hybrid approach to state management, combining TypedDict for static typing with Pydantic models for runtime validation where needed.

## Deleted State Models Analysis

Based on our analysis, we previously had these specialized state models that were deleted:

### 1. **ReWOOState**
- **Purpose**: Multi-step reasoning and parallel execution coordination
- **Key Fields**:
  - `rewoo_plan`: Execution plan details
  - `worker_results`: Results from parallel workers
  - `synthesis_result`: Combined output
  - `planning_start_time`, `synthesis_start_time`: Timing metrics
  - `rewoo_enabled`: Feature flag

### 2. **SpecializedCoachState**
- **Purpose**: Complex coaching workflows with reflection
- **Key Fields**:
  - `complexity_level`, `complexity_score`: Query complexity assessment
  - `execution_path`: Simple vs complex routing
  - `reflection_*`: Multiple reflection-related fields
  - `improvement_*`: Improvement tracking fields
  - Performance metrics

### 3. **ReflectionState**
- **Purpose**: Iterative improvement and quality assessment
- **Key Fields**:
  - `reflection_iteration`: Current iteration count
  - `reflection_assessment`: Quality scores
  - `needs_improvement`: Boolean flag
  - `improvement_guidance`: Specific instructions

### 4. **WebSearchState**
- **Purpose**: Web search and content scraping
- **Key Fields**:
  - `search_query`, `search_results`
  - `scraped_content`: Web page content
  - `search_plan`: Search strategy

### 5. **StateWithMemory**
- **Purpose**: Long-term memory integration
- **Key Fields**:
  - `memory_context`: Retrieved memories
  - `memory_updates`: New memories to store

## Hybrid Pattern Implementation

### 1. Core Principles

```python
# 1. TypedDict for graph communication (static typing)
from typing_extensions import TypedDict

class AgentState(TypedDict):
    """Core state - lightweight, fast, static-typed"""
    messages: List[BaseMessage]
    user_query: Optional[str]
    specialist_responses: Dict[str, Any]  # Store domain data here
    
# 2. Pydantic for business logic (runtime validation)
from pydantic import BaseModel, Field

class ReWOOPlan(BaseModel):
    """Domain model with validation and methods"""
    steps: List[str]
    complexity_score: float = Field(ge=0.0, le=1.0)
    
    def is_complex(self) -> bool:
        return self.complexity_score > 0.7
```

### 2. Pattern Application

#### For ReWOO Functionality

```python
# models/rewoo_models.py
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime

class ReWOOStep(BaseModel):
    """Individual step in ReWOO execution plan"""
    step_id: str
    action: str
    dependencies: List[str] = []
    agent: str
    expected_output: str
    
class ReWOOPlan(BaseModel):
    """Complete ReWOO execution plan"""
    query: str
    steps: List[ReWOOStep]
    complexity_score: float = Field(ge=0.0, le=1.0)
    created_at: datetime = Field(default_factory=datetime.now)
    
    def get_parallel_groups(self) -> List[List[ReWOOStep]]:
        """Group steps that can be executed in parallel"""
        # Implementation here
        pass

class ReWOOWorkerResult(BaseModel):
    """Result from a ReWOO worker execution"""
    step_id: str
    agent: str
    result: str
    execution_time: float
    success: bool = True
    error: Optional[str] = None

class ReWOOContext(BaseModel):
    """Complete ReWOO execution context"""
    plan: ReWOOPlan
    worker_results: Dict[str, ReWOOWorkerResult] = {}
    synthesis_result: Optional[str] = None
    total_execution_time: Optional[float] = None
    
    def add_worker_result(self, result: ReWOOWorkerResult):
        """Add a worker result to the context"""
        self.worker_results[result.step_id] = result
```

#### For Reflection Functionality

```python
# models/reflection_models.py
class ReflectionAssessment(BaseModel):
    """Quality assessment for reflection"""
    overall_score: float = Field(ge=0.0, le=10.0)
    needs_improvement: bool
    improvement_priority: str = Field(pattern="^(low|medium|high)$")
    improvements: List[str]
    strengths: List[str]
    
class ReflectionContext(BaseModel):
    """Complete reflection context"""
    iteration: int = 0
    max_iterations: int = 3
    assessments: List[ReflectionAssessment] = []
    improvement_guidance: Optional[str] = None
    final_response: Optional[str] = None
    
    def should_continue(self) -> bool:
        """Determine if reflection should continue"""
        if self.iteration >= self.max_iterations:
            return False
        if not self.assessments:
            return True
        latest = self.assessments[-1]
        return latest.needs_improvement and latest.improvement_priority != "low"
```

### 3. Integration Pattern

```python
# In your graph nodes
async def rewoo_planner_node(state: AgentState) -> AgentState:
    """Plan execution using domain models"""
    
    # Create plan using Pydantic model
    plan = ReWOOPlan(
        query=state["user_query"],
        steps=[...],
        complexity_score=0.8
    )
    
    # Validate automatically happens on creation
    # Store in state as serialized data
    if "rewoo" not in state["specialist_responses"]:
        state["specialist_responses"]["rewoo"] = {}
    
    state["specialist_responses"]["rewoo"]["plan"] = plan.model_dump()
    
    return state

async def rewoo_worker_node(state: AgentState) -> AgentState:
    """Execute plan using domain models"""
    
    # Reconstruct from state
    plan_data = state["specialist_responses"]["rewoo"]["plan"]
    plan = ReWOOPlan.model_validate(plan_data)
    
    # Use domain model methods
    parallel_groups = plan.get_parallel_groups()
    
    # Execute and store results
    context = ReWOOContext(plan=plan)
    # ... execution logic ...
    
    state["specialist_responses"]["rewoo"]["context"] = context.model_dump()
    
    return state
```

### 4. Migration Strategy

#### Step 1: Identify Domain Models Needed

Based on deleted states, create these domain models:

1. **ReWOO Models** (`models/rewoo_models.py`)
   - ReWOOPlan, ReWOOStep, ReWOOWorkerResult, ReWOOContext

2. **Reflection Models** (`models/reflection_models.py`)
   - ReflectionAssessment, ReflectionContext, ImprovementPlan

3. **Complexity Models** (`models/complexity_models.py`)
   - ComplexityAssessment, ExecutionPath, PerformanceMetrics

4. **Memory Models** (`models/memory_models.py`)
   - MemoryContext, MemoryUpdate, MemoryQuery

#### Step 2: Update Nodes

1. Replace direct state field access with model operations
2. Store complex data in `specialist_responses`
3. Use model validation for data integrity

#### Step 3: Maintain Compatibility

```python
# Backward compatibility helper
def get_rewoo_context(state: AgentState) -> Optional[ReWOOContext]:
    """Safely get ReWOO context from state"""
    if "rewoo" not in state.get("specialist_responses", {}):
        return None
    
    context_data = state["specialist_responses"]["rewoo"].get("context")
    if not context_data:
        return None
        
    return ReWOOContext.model_validate(context_data)
```

## Benefits of This Approach

1. **Type Safety**: Static typing for graph communication
2. **Runtime Validation**: Pydantic validates data at boundaries
3. **Encapsulation**: Domain logic stays with domain models
4. **Performance**: TypedDict has zero runtime overhead
5. **Flexibility**: Easy to add new domains without state proliferation
6. **Testability**: Domain models can be tested independently

## When to Use Each Pattern

### Use TypedDict (AgentState) When:
- Passing data between graph nodes
- Simple fields that don't need validation
- Performance is critical
- Data is already validated

### Use Pydantic Models When:
- Complex domain logic is involved
- Runtime validation is needed
- Data comes from external sources
- You need serialization/deserialization
- Business rules must be enforced

## Example: Complete Implementation

```python
# models/coaching_models.py
from pydantic import BaseModel, Field, validator
from typing import List, Dict, Optional
from datetime import datetime

class CoachingPlan(BaseModel):
    """Comprehensive coaching plan"""
    user_goal: str
    duration_weeks: int = Field(ge=1, le=52)
    coaches_involved: List[str]
    sessions: List['CoachingSession']
    
    @validator('coaches_involved')
    def validate_coaches(cls, v):
        valid_coaches = {'strength', 'cardio', 'nutrition', 'mental', 'recovery'}
        for coach in v:
            if coach not in valid_coaches:
                raise ValueError(f"Invalid coach: {coach}")
        return v
    
    def get_week_sessions(self, week: int) -> List['CoachingSession']:
        """Get sessions for a specific week"""
        return [s for s in self.sessions if s.week_number == week]

class CoachingSession(BaseModel):
    """Individual coaching session"""
    week_number: int
    day_of_week: str
    coach_type: str
    focus_areas: List[str]
    duration_minutes: int = Field(ge=15, le=180)
    
# In your node
async def create_coaching_plan_node(state: AgentState) -> AgentState:
    """Create coaching plan using domain models"""
    
    # Create plan with validation
    plan = CoachingPlan(
        user_goal=state["user_query"],
        duration_weeks=12,
        coaches_involved=["strength", "cardio", "nutrition"],
        sessions=[...]
    )
    
    # Store in state
    if "coaching_plan" not in state["specialist_responses"]:
        state["specialist_responses"]["coaching_plan"] = {}
    
    state["specialist_responses"]["coaching_plan"]["plan"] = plan.model_dump()
    
    # Add message about plan creation
    state["messages"].append(
        AIMessage(content=f"Created {plan.duration_weeks}-week plan with {len(plan.sessions)} sessions")
    )
    
    return state
```

## Conclusion

This hybrid approach gives us the best of both worlds:
- **TypedDict** for fast, static-typed graph communication
- **Pydantic** for validated, feature-rich domain models

By storing Pydantic models in the `specialist_responses` field, we maintain a clean state structure while enabling sophisticated domain-specific functionality.

## Implemented Domain Models

The following domain models have been fully implemented based on the deleted state classes:

### 1. **ReWOO Models** (`athlea_langgraph/models/rewoo_models.py`)
- `ReWOOStep`: Individual execution steps with dependencies
- `ReWOOPlan`: Complete execution plan with parallel grouping
- `ReWOOWorkerResult`: Worker execution results
- `ReWOOSynthesis`: Synthesis of worker results
- `ReWOOContext`: Complete ReWOO execution context

### 2. **Reflection Models** (`athlea_langgraph/models/reflection_models.py`)
- `SafetyValidation`: Safety checks for fitness coaching
- `ReflectionIteration`: Individual reflection iterations
- `ReflectionMetadata`: Tracking reflection progress
- `QualityAssessment`: Response quality evaluation
- `ReflectionContext`: Complete reflection context

### 3. **Specialized Coach Models** (`athlea_langgraph/models/specialized_coach_models.py`)
- `ComplexityAssessment`: Query complexity evaluation
- `SimpleExecutionResult`: Results from simple path
- `ComplexExecutionResult`: Results from complex ReWOO path
- `PerformanceMetrics`: Execution performance tracking
- `SpecializedCoachContext`: Complete coach execution context

### 4. **Memory Models** (`athlea_langgraph/models/memory_models.py`)
- `Memory`: Individual memory items with metadata
- `UserPreference`: User preferences with confidence
- `InteractionSummary`: Coaching session summaries
- `PersonalizationData`: User personalization settings
- `MemoryContext`: Complete memory context

### 5. **Web Search Models** (`athlea_langgraph/models/web_search_models.py`)
- `WebSearchResult`: Individual search results
- `ScrapedWebContent`: Scraped web page content
- `WebSearchPlan`: Search strategy and execution plan
- `WebSearchContext`: Complete web search context

Each module provides:
- Helper functions: `{domain}_to_state()`, `state_to_{domain}()`, `create_{domain}_context()`
- Full type hints and validation
- Business logic methods
- Backward compatibility support

See the [State Migration Guide](../05_maintenance/05_state_migration_guide.md) for detailed migration examples and best practices.
