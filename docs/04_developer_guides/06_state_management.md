# State Management in Athlea LangGraph

**Owner:** Core Architecture Team
**Version:** 1.1.0
**Last Updated:** 2025-06-20

## 1. Overview

This document outlines the best practices for managing `TypedDict` state objects within the Athlea LangGraph ecosystem. To ensure consistency, prevent code duplication, and improve type safety, all state definitions **MUST** follow the principles of centralization and inheritance from a canonical source.

---

## 2. The Canonical `AgentState`

### 2.1. The Single Source of Truth

The single source of truth for all core graph states is the `AgentState` TypedDict located at:
`athlea_langgraph/states/state.py`

This base state contains all the fields that are common and essential across the majority of coaching and operational graphs. All other specialized states **MUST** inherit from `AgentState`.

### 2.2. Base Schema

The `AgentState` inherits from LangGraph's `MessagesState` to enable chat history tracking and support for LangGraph Studio's Chat Mode.

```python
# athlea_langgraph/states/state.py

class AgentState(MessagesState):
    """
    Core state for the Athlea coaching system.
    All other states should inherit from this.
    """
    # Core conversation fields
    user_query: Optional[str]
    user_profile: Optional[Dict[str, Any]]
    
    # Session and user management
    thread_id: Optional[str]
    user_id: Optional[str]

    # ... other universal fields ...
```

---

## 3. How to Extend the Base State

You **MUST NOT** add new fields directly to the base `AgentState` unless they are universally required by >80% of graphs.

Instead, for any graph that requires additional, specialized fields, you **MUST** extend the `AgentState` via TypedDict inheritance in a separate file within the `athlea_langgraph/states/` directory.

### 3.1. Example: Creating `OnboardingState`

1.  Create a new file, e.g., `athlea_langgraph/states/onboarding_state.py`.
2.  Import the base `AgentState` from `.state`.
3.  Define the new state, inheriting from `AgentState`.
4.  Add only the fields specific to the new state.

```python
# In athlea_langgraph/states/onboarding_state.py
from typing import Optional
from .state import AgentState

class OnboardingState(AgentState):
    """
    Adds only the onboarding-specific fields.
    """
    onboarding_stage: str
    current_question_field: Optional[str]
    generated_plan: Optional[dict]
```

### 3.2. Centralized Exports

To make importing states consistent, all legitimate state classes are exported from the main package initializer:
`athlea_langgraph/states/__init__.py`

Any code that needs a state object should import it directly from this `__init__.py` file.

```python
# Correct usage:
from athlea_langgraph.states import AgentState, OnboardingState

# Incorrect usage (will cause errors):
# from athlea_langgraph.states.state import AgentState 
```

---

## 4. Enforcement and CI Checks

To maintain this structure, the following checks are in place:

*   **Static Typing (`mypy`/`pyright`)**: The CI pipeline will fail if direct assignments are made to a state object that do not conform to its `TypedDict` definition.
*   **Centralized Exports**: The `athlea_langgraph/states/__init__.py` file is the single gateway for state imports. `ModuleNotFoundError` will occur if code attempts to import state files that do not exist or are not exported.

---

## 5. State Versioning

The canonical state definition in `athlea_langgraph/states/state.py` includes a `__version__` constant.

```python
# athlea_langgraph/states/state.py
__version__ = "1.1.0"
```

This version number should be incremented according to [Semantic Versioning](https://semver.org/) rules whenever a breaking change is made to the base `AgentState`. All changes should be documented in the project's main `CHANGELOG.md`. 

## Handling Domain-Specific Functionality

When implementing specialized functionality (like web search, document analysis, etc.), follow these patterns:

### 1. Use Domain Models, Not State Extensions

Instead of creating specialized state classes for every feature, use domain-specific Pydantic models:

```python
# athlea_langgraph/models/web_search_models.py
from pydantic import BaseModel, Field
from typing import List, Optional

class WebSearchResult(BaseModel):
    """Individual web search result."""
    title: str
    url: str
    snippet: str
    position: int
    source_domain: str
    relevance_score: Optional[float] = None

class WebSearchContext(BaseModel):
    """Complete context for a web search operation."""
    research_question: str
    search_results: List[WebSearchResult] = Field(default_factory=list)
    # ... other fields
```

### 2. Store Complex Data as Serialized Objects

In the state, store domain objects as dictionaries or JSON:

```python
def web_search_node(state: AgentState) -> AgentState:
    # Create domain object
    search_context = WebSearchContext(
        research_question=state["user_query"]
    )
    
    # Do work with the domain object
    search_context.search_results = perform_search(...)
    
    # Store in state as dict
    state["specialist_responses"]["web_search"] = search_context.model_dump()
    
    return state
```

### 3. Use State Fields Appropriately

Map domain data to existing state fields:

- `user_query` - The main query/question
- `specialist_responses` - Store domain-specific results
- `aggregated_response` - Combined/processed results
- `final_response` - User-facing output

### 4. Example: Web Search Implementation

```python
# In a web search node
def execute_web_search(state: AgentState) -> AgentState:
    # Extract query
    query = state["user_query"]
    
    # Create domain context
    context = WebSearchContext(research_question=query)
    
    # Execute search
    results = search_tool.search(query)
    for r in results:
        context.add_search_result(WebSearchResult(...))
    
    # Store results
    state["specialist_responses"]["web_search"] = {
        "context": context.model_dump(),
        "summary": context.get_research_summary(),
        "metrics": context.get_research_metrics()
    }
    
    # Update final response if needed
    state["final_response"] = context.research_summary
    
    return state
```

### 5. Benefits of This Approach

1. **Type Safety**: Domain models provide full type checking
2. **Encapsulation**: Business logic stays in domain models
3. **Testability**: Domain models can be tested independently
4. **Flexibility**: Easy to evolve domain models without changing state
5. **Reusability**: Domain models can be used across different graphs

### 6. When to Extend State

Only extend the base `AgentState` when:

1. Multiple nodes need to share workflow-specific data
2. The data is fundamental to the graph's operation
3. The extension is minimal and well-documented

Example of appropriate extension:

```python
class OnboardingState(AgentState):
    """Minimal extension for onboarding workflow."""
    onboarding_completed: bool
    fitness_goals: List[str]
    initial_assessment: Dict[str, Any]
```

### 7. Migration Strategy

For existing code using specialized states:

1. Create domain models for the specialized fields
2. Map data to existing `AgentState` fields
3. Store complex data in `specialist_responses`
4. Remove the specialized state class
5. Update nodes to use domain models

This approach maintains the benefits of centralized state management while providing the flexibility needed for domain-specific functionality. 