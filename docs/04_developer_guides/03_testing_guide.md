# Developer Testing Guide

This guide explains the comprehensive testing strategy for the Athlea coaching system, including general testing workflows and automated prompt optimization.

## Overview

Our testing architecture is organized into several categories, each serving a specific purpose. This structure allows developers to run targeted tests for quick validation or comprehensive suites for release readiness.

### Testing Categories

- **General Testing**: Unit, integration, and E2E tests for system validation
- **Automated Optimization**: Specialized prompt optimization and quality assessment
- **Performance Monitoring**: LangSmith tracing and metrics collection

The `tests/` directory is structured as follows:

-   **/core**: 🎯 **PRIMARY** - Automated optimization and core functionality testing
-   **/coaches**: Individual coach and routing logic testing
-   **/system**: System-level configuration and infrastructure testing
-   **/tools**: Tool-specific functionality testing
-   **/external**: Third-party integration testing (N8N, GraphRAG, etc.)
-   **/e2e**: End-to-end tests that validate complete user workflows
-   **/utilities**: Test utilities, fixtures, and runners

## How to Run Tests

### 1. Running Specific Test Categories

You can run tests for a specific category using `pytest`. All commands should be run from the project root.

**🎯 Core Tests (PRIMARY WORKFLOW)**
For automated optimization and core functionality testing.
```bash
poetry run pytest tests/core/
```
Key core tests include:
-   `test_automated_optimization.py`: **PRIMARY** - Automated prompt optimization with quality assessment
-   `test_tool_integration.py`: Comprehensive tool integration testing
-   `test_streaming_api.py`: API streaming functionality testing

**🤖 Coach Tests**
For individual coach and routing logic testing.
```bash
poetry run pytest tests/coaches/
```

**⚙️ System Tests**
For system-level configuration and infrastructure testing.
```bash
poetry run pytest tests/system/
```

**🔧 Tool Tests**
For tool-specific functionality testing.
```bash
poetry run pytest tests/tools/
```

**End-to-End (E2E) Tests**
To validate a full user workflow from the API down to the agent response.
```bash
poetry run pytest tests/e2e/
```
Key E2E tests include:
-   `test_onboarding_complex.py`: Simulates a full, multi-turn onboarding conversation.
-   `test_api_single_coach.py`: Validates that the `singleCoach` API parameter correctly routes to a specific coach.
-   `test_coaching_stream_endpoint.py`: Tests the real-time streaming API to ensure events are correctly formatted.

### 2. Using Test Runners

The `tests/utilities/runners/` directory contains convenience scripts for executing common testing scenarios.

**Run All Core Tests**
This script runs the main core, system, and tool tests.
```bash
poetry run python tests/utilities/runners/run_tests.py
```

### 3. 🎯 Automated Optimization Testing (PRIMARY WORKFLOW)

**Run Complete 2.1.1-strength-coach-accuracy-testing**
Execute the primary automated optimization workflow with Sprint 2.1 enhancements:
```bash
python -c "
import asyncio
from tests.core.test_automated_optimization import test_strength_coach_accuracy_with_tracing
result = asyncio.run(test_strength_coach_accuracy_with_tracing())
print(f'✅ Pass Rate: {result.get(\"pass_rate\", 0):.2f}')
print(f'📊 Composite Score: {result.get(\"organized_results\", {}).get(\"quality_metrics_summary\", {}).get(\"composite_score\", 0):.3f}')
print(f'🎯 Ticket ID: {result.get(\"ticket_id\", \"N/A\")}')
"
```

**Run Multi-Turn Consistency Testing (Sprint 2.1)**
Execute multi-turn conversation validation:
```bash
python -c "
import asyncio
from tests.core.test_automated_optimization import test_strength_coach_multi_turn_consistency
result = asyncio.run(test_strength_coach_multi_turn_consistency())
print(f'🔄 Multi-Turn Pass Rate: {result.get(\"pass_rate\", 0):.2f}')
print(f'📋 Scenarios Tested: {len(result.get(\"results\", []))}')
"
```

**Run Rollback Safety Testing (Sprint 2.1)**
Validate rollback safety mechanisms:
```bash
python -c "
import asyncio
from tests.core.test_automated_optimization import test_rollback_mechanism
result = asyncio.run(test_rollback_mechanism())
print(f'🛡️ Rollback System: {\"ACTIVE\" if result else \"FAILED\"}')
"
```

**Run Individual Core Tests**
```bash
# Core optimization testing
pytest tests/core/test_automated_optimization.py -v

# Tool integration testing
pytest tests/core/test_tool_integration.py -v

# Streaming API testing
pytest tests/core/test_streaming_api.py -v
```

## Interpreting Test Output

When running tests, pay attention to the output for key information:

-   **Specialist Routing**: In integration tests, look for logs like `Specialists consulted: ['strength', 'nutrition']` to verify that the planning node correctly routed the query.
-   **Tool Usage**: For ReAct agent tests, the output will show the "thought" process and which tools were invoked.
-   **Memory Context**: Memory-related tests will indicate if context was successfully retrieved from past conversations.
-   **Assertions**: A successful test run will end with `PASSED`. Any `FAILED` tests will show a detailed error trace.

## Prerequisites for Testing

Before running most integration or E2E tests, ensure your local environment is correctly configured:

1.  **MongoDB Database**: A local or cloud-hosted MongoDB instance must be running and accessible.
2.  **Environment Variables**: Your `.env` file must be populated with the necessary credentials for Azure, OpenAI, and other external services.
3.  **Dependencies**: Ensure all project dependencies are installed with `poetry install`.

**Required Environment Variables:**
```bash
# Required for memory storage
MONGODB_URI="mongodb://localhost:27017/athlea_coaching_tests" # Use a dedicated test DB

# Required for agent execution
AZURE_OPENAI_API_KEY="your_azure_openai_api_key"
AZURE_OPENAI_ENDPOINT="your_azure_openai_endpoint"

# Optional: For full tool functionality
AIRTABLE_API_KEY="your_airtable_api_key"
GOOGLE_MAPS_API_KEY="your_google_maps_api_key"
# ... and other external service keys
```

## Running the Tests

We have several scripts available to test different aspects of the system.

### 1. Comprehensive Specialized Coaches Test

This is the main test suite for the specialized coaches.

```bash
python test_specialized_coaches.py
```

This script provides a comprehensive validation of the entire specialized coach stack, including:
-   Individual coach node functionality.
-   **Tool Integration**: Verifies that the `SpecializedCoachToolsManager` initializes correctly and that coaches receive their domain-specific tools.
-   **Memory Integration**: Confirms that conversations with specialized coaches are persisted in MongoDB and that context is correctly retrieved across sessions.
-   **Specialist Routing**: Checks that the main coaching graph routes queries to the appropriate specialist.

### 2. Quick Specialist Verification

For a faster check to ensure the specialists are importable and their dependencies are met, you can run:

```bash
python test_memory_coaching_with_specialists.py
```

This script is useful for a quick sanity check during development.

## What to Look For in Test Output

When you run the tests, you should see output that confirms the key features are working.

**Successful Test Output Example:**
```
🔧 Quick Specialist Test
==============================
✅ Specialist coaches imported successfully
✅ Tools manager initialized
✅ Strength coach tools: 3 available
...

🏃‍♂️ Memory Coaching with Specialized Coaches Demo
============================================================
...
📝 Conversation 1: First interaction...
User: Hi! I want to start a new workout routine.
----------------------------------------
🤖 Coach Response:
Hello! I'm excited to help you start your fitness journey...
👥 Specialists consulted: strength, nutrition
🧠 Memory Context: 1 relevant memories found
```

### Key Areas to Monitor in Output:

-   **Specialist Routing**: The `Specialists consulted` line should show which coach(es) were activated. Verify that a question about strength training routes to the `strength` coach.
-   **Memory Context**: The `Memory Context` line shows how many relevant memories were found from past conversations. For a new user, this should be 0. For a returning user, it should be greater than 0.
-   **Tool Usage**: For tests involving tools, you may see output indicating that a tool was called (e.g., searching an Airtable database). If API keys for external services are not provided, you should see warnings that those tools failed to initialize, but the tests should still pass, demonstrating graceful degradation.

## Troubleshooting Tests

-   **MongoDB Connection Error**: Ensure your MongoDB instance is running and the `MONGODB_URI` is correct. Check for firewall issues.
-   **Tool Initialization Warnings**: This is expected if you haven't provided all optional API keys in your `.env` file. The coaches should still function, but without the capabilities of the uninitialized tools.
-   **Import Errors**: This usually indicates a problem with your Python environment. Make sure you have installed all dependencies from `pyproject.toml` (`poetry install`).
-   **Authentication Errors**: If you see errors from Azure OpenAI, verify that your API key and endpoint are correct.

By running these tests regularly, you can ensure that the specialized coaches are functioning correctly and that their complex integrations with tools and memory remain stable.

## Sprint 2.1 Enhanced Quality Metrics

The 2.1.1-strength-coach-accuracy-testing includes enhanced quality metrics for comprehensive performance analysis:

### Core Quality Metrics
- **Specificity Score**: Technical precision and detail level (target: ≥0.75)
- **Safety Score**: Injury prevention awareness (target: ≥0.90)
- **Coverage Score**: Response completeness (target: ≥0.80)

### Sprint 2.1 Enhanced Metrics
- **Tool Call Success Rate**: % of expected tools invoked (target: ≥0.80)
- **Cost Per Scenario**: USD cost per test scenario for ROI analysis
- **Error Rate**: Explicit failure rate tracking
- **Domain Expertise**: Strength training knowledge accuracy
- **Multi-Turn Consistency**: Context retention across conversation turns

### User Journey Tracing (Sprint 2.1)
All test runs include comprehensive LangSmith metadata for dashboard grouping:
```python
metadata = {
    "user_id": f"test_user_{scenario_name}",
    "coach_type": "strength_coach",
    "scenario_id": scenario_name,
    "conversation_phase": "accuracy_testing",
    "ticket_id": "2.1.1-strength-coach-accuracy-testing",
    "latency_bucket": "0-1s|1-2s|2-5s|>5s",
    "cost_bucket": "<$0.001|$0.001-$0.01|>$0.01"
}
```

## Automated Optimization & UX Levers

This section covers the primary workflow for improving coach performance using our automated testing and optimization framework, centered around the 12 UX Levers.

### The 4-Step Sprint Process

1.  **Assess Current State**: Run automated tests to identify failing quality metrics.
2.  **Select Target Levers**: Pick 2-3 high-impact levers based on test failures.
3.  **Make Targeted Edits**: Implement changes in the specified files.
4.  **Validate Improvements**: Run the test suite and trace in LangSmith to measure the impact of your change.

### Running the Primary Optimization Test

This is the main test for validating coach performance and generating the standardized report.

```bash
python -c "
import asyncio
from tests.core.test_automated_optimization import test_strength_coach_accuracy_with_tracing
result = asyncio.run(test_strength_coach_accuracy_with_tracing())
print(f'Test Complete - Pass Rate: {result.get(\"pass_rate\", 0):.2f}')
"
```

### Interpreting the Standardized JSON Report

The test produces a comprehensive JSON file in `tests/results/`. This report is the single source of truth for performance analysis. Key sections include:

-   `deployment_analysis`: Shows which of the 5 deployment criteria are met.
-   `quality_metrics_summary`: Provides average scores for specificity, safety, etc.
-   `lever_analysis`: **Most importantly**, provides data-driven recommendations on which UX Levers to pull next.
-   `test_scenarios`: Contains the detailed results for each individual test case.

### The 12 UX Optimization Levers

The following checklist provides a systematic approach to improving user experience. Each lever represents a specific component that can be modified.

| Lever | Component / File | UX Impact |
|---|---|---|
| **1. LLM Model & Params** | `athlea_langgraph/services/azure_openai_service.py` | Medium → High |
| **2. Coach System Prompt** | `athlea_langgraph/prompts/coaches/{coach_name}.json` | High |
| **3. Tool Scripts** | `athlea_langgraph/tools/{domain}/{tool_name}.py` | Medium |
| **4. Reasoning Node** | `athlea_langgraph/nodes/reasoning_node.py` | High |
| **5. Planner / Routing Node**| `athlea_langgraph/nodes/planning_node.py` | High |
| **6. Head Coach** | `athlea_langgraph/agents/head_coach.py` | Medium |
| **7. RAG Retriever** | `athlea_langgraph/services/azure_search_service.py` | High |
| **8. Knowledge Base** | Azure Cognitive Search / GraphRAG indexes | Medium → High |
| **9. Memory & Context** | `athlea_langgraph/memory/advanced_memory_manager.py` | Medium |
| **10. Error-Recovery** | `athlea_langgraph/agents/base_agent.py` | High |
| **11. UX-Tone & Persona** | System prompt persona sentences | Medium |
| **12. Monitoring & Alerts** | `scripts/setup_langsmith_monitoring.py` | Low → Medium |

*For a detailed implementation guide for each lever, see the [UX Optimization Workflow Guide](./05_ux_optimization_workflow.md).*

## Environment Setup for Optimization Testing

In addition to the standard environment variables, optimization testing requires:

```bash
# LangSmith Integration (Required for tracing)
LANGSMITH_API_KEY="your_langsmith_api_key"
LANGSMITH_TRACING="true"
LANGCHAIN_TRACING_V2="true"
LANGCHAIN_PROJECT="athlea-coaching-dev"
```

## Quick Troubleshooting

### Common Issues and Solutions

#### Response Generation Failure
**Symptoms**: `message_count: 0`, `final_response: ""`
**Solution**: Verify response extraction uses `result['final_response']`

#### LangSmith Traces Not Visible
**Symptoms**: 0 runs found in portal
**Solution**: Change time filter to "Last 6 hours" in LangSmith portal

#### Tool Validation Errors
**Symptoms**: `'str' object has no attribute 'get'`
**Solution**: Check tool parameter handling for Union types

For detailed troubleshooting, see the [Automated Optimization Guide](./04_automated_optimization_guide.md).