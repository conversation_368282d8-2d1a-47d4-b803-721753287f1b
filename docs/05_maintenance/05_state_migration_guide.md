# State Migration Guide: From Custom States to Domain Models

## Overview

This guide documents the migration from custom state classes (ReflectionState, SpecializedCoachState, etc.) to the hybrid TypedDict + Pydantic domain models pattern. This migration centralizes state management while preserving all functionality through domain-specific Pydantic models.

## Migration Summary

### Deleted State Classes → New Domain Models

| Deleted State Class | New Domain Model | Location |
|-------------------|------------------|----------|
| `ReflectionState` | `ReflectionContext` | `athlea_langgraph/models/reflection_models.py` |
| `SpecializedCoachState` | `SpecializedCoachContext` | `athlea_langgraph/models/specialized_coach_models.py` |
| `StateWithMemory` | `MemoryContext` | `athlea_langgraph/models/memory_models.py` |
| `ReWOOState` | `ReWOOContext` | `athlea_langgraph/models/rewoo_models.py` |
| `WebSearchState` | `WebSearchContext` | `athlea_langgraph/models/web_search_models.py` |

## Key Concepts

### 1. State Storage Pattern

All domain models are stored in `AgentState`''s `specialist_responses` field:

```python
# Store domain model in state
state["specialist_responses"]["reflection"] = reflection_to_state(reflection_context)

# Retrieve domain model from state
reflection_context = state_to_reflection(state["specialist_responses"].get("reflection", {}))
```

### 2. Helper Functions

Each domain model module provides three helper functions:

- `{domain}_to_state()`: Convert domain model to state-compatible dict
- `state_to_{domain}()`: Reconstruct domain model from state data
- `create_{domain}_context()`: Create new domain context with defaults

## Migration Examples

### Example 1: Migrating ReflectionState Usage

**Before (using ReflectionState):**
```python
from athlea_langgraph.states.reflection_state import ReflectionAgentState

def reflection_node(state: ReflectionAgentState) -> ReflectionAgentState:
    # Direct field access
    if state["reflection_count"] < state["max_reflections"]:
        state["reflection_count"] += 1
        state["reflection_feedback"] = "Needs improvement"
        state["safety_validated"] = check_safety(state["original_response"])
    return state
```

**After (using ReflectionContext):**
```python
from athlea_langgraph.states import AgentState
from athlea_langgraph.models import (
    ReflectionContext, 
    reflection_to_state, 
    state_to_reflection
)

def reflection_node(state: AgentState) -> AgentState:
    # Retrieve or create reflection context
    reflection_data = state.get("specialist_responses", {}).get("reflection", {})
    context = state_to_reflection(reflection_data)
    
    # Use domain model methods
    if context.metadata.can_reflect():
        context.metadata.add_iteration(
            feedback="Needs improvement",
            improvements=["Add more detail", "Check form safety"],
            quality_score=0.75
        )
        context.safety_validation.form_safety_validated = True
    
    # Store back in state
    state["specialist_responses"]["reflection"] = reflection_to_state(context)
    return state
```

### Example 2: Migrating SpecializedCoachState Usage

**Before:**
```python
from athlea_langgraph.states.specialized_coach_state import SpecializedCoachState

def complexity_assessment_node(state: SpecializedCoachState) -> SpecializedCoachState:
    state["complexity_score"] = assess_complexity(state["user_query"])
    state["complexity_level"] = "complex" if state["complexity_score"] > 0.7 else "simple"
    state["execution_path"] = "complex" if is_complex_query(state) else "simple"
    log_execution_step(state, "complexity_assessment", {"score": state["complexity_score"]})
    return state
```

**After:**
```python
from athlea_langgraph.states import AgentState
from athlea_langgraph.models import (
    SpecializedCoachContext,
    ComplexityAssessment,
    ComplexityLevel,
    ComplexityAssessmentMethod,
    ExecutionPath,
    specialized_coach_to_state,
    state_to_specialized_coach
)

def complexity_assessment_node(state: AgentState) -> AgentState:
    # Retrieve or create context
    coach_data = state.get("specialist_responses", {}).get("specialized_coach", {})
    context = state_to_specialized_coach(coach_data)
    
    # Create complexity assessment
    score = assess_complexity(state["user_query"])
    context.complexity_assessment = ComplexityAssessment(
        level=ComplexityLevel.COMPLEX if score > 0.7 else ComplexityLevel.SIMPLE,
        score=score,
        reasoning="Query involves multiple training concepts",
        assessment_time=0.5,
        assessment_method=ComplexityAssessmentMethod.LLM_BASED,
        factors=["multiple_goals", "technical_terms"]
    )
    
    # Determine execution path using domain logic
    context.execution_path = context.complexity_assessment.get_execution_path()
    
    # Log using domain methods
    context.log_step("complexity_assessment", {"score": score})
    
    # Store back in state
    state["specialist_responses"]["specialized_coach"] = specialized_coach_to_state(context)
    return state
```

### Example 3: Migrating Memory State Usage

**Before:**
```python
from athlea_langgraph.states.state_with_memory import MemoryEnhancedAgentState

def memory_retrieval_node(state: MemoryEnhancedAgentState) -> MemoryEnhancedAgentState:
    state["relevant_memories"] = retrieve_memories(state["user_query"])
    state["user_preferences"]["workout_time"] = "morning"
    state["personalization_data"]["communication_style"] = "motivational"
    return state
```

**After:**
```python
from athlea_langgraph.states import AgentState
from athlea_langgraph.models import (
    MemoryContext,
    Memory,
    MemoryType,
    memory_to_state,
    state_to_memory
)

def memory_retrieval_node(state: AgentState) -> AgentState:
    # Retrieve or create memory context
    memory_data = state.get("specialist_responses", {}).get("memory", {})
    context = state_to_memory(memory_data)
    
    # Retrieve and add memories
    memories = retrieve_memories(state["user_query"])
    for mem in memories:
        context.add_memory(Memory(
            id=mem["id"],
            type=MemoryType.USER_PREFERENCE,
            content=mem["content"],
            relevance_score=mem["score"]
        ))
    
    # Update preferences using domain methods
    context.update_preference("workout_time", "morning", confidence=0.9)
    context.personalization_data.communication_style = "motivational"
    
    # Generate context prompt for downstream use
    state["memory_context_prompt"] = context.to_context_prompt()
    
    # Store back in state
    state["specialist_responses"]["memory"] = memory_to_state(context)
    return state
```

## Best Practices

### 1. Context Initialization

Always check if a context exists before creating a new one:

```python
# Good: Preserve existing context
existing_data = state.get("specialist_responses", {}).get("reflection", {})
context = state_to_reflection(existing_data) if existing_data else create_reflection_context(
    original_response=state.get("final_response", ""),
    coach_domain=state.get("coach_type")
)

# Bad: Always create new context (loses data)
context = create_reflection_context(...)  # Don''t do this if context might exist
```

### 2. Partial Updates

Use domain model methods for updates rather than recreating:

```python
# Good: Update specific fields
context.safety_validation.merge_warnings(["Check form", "Reduce weight"])
context.metadata.add_iteration(feedback="Improved", improvements=[])

# Bad: Replace entire objects
context.safety_validation = SafetyValidation(...)  # Loses existing data
```

### 3. State Field Mapping

Map commonly used fields to AgentState for compatibility:

```python
# After updating domain model, sync key fields
state["specialist_responses"]["reflection"] = reflection_to_state(context)

# Also update commonly accessed fields
state["safety_alert"] = not context.safety_validation.is_safe()
state["final_response"] = context.get_final_response()
```

### 4. Error Handling

Handle missing or malformed data gracefully:

```python
try:
    context = state_to_specialized_coach(state_data)
except Exception as e:
    logger.warning(f"Failed to reconstruct context: {e}")
    context = create_specialized_coach_context(
        coach_domain=state.get("coach_type", "unknown")
    )
```

## Common Patterns

### Pattern 1: Multi-Stage Processing

```python
def process_with_reflection(state: AgentState) -> AgentState:
    # Stage 1: Generate initial response
    response = generate_response(state["user_query"])
    
    # Stage 2: Create reflection context
    context = create_reflection_context(
        original_response=response,
        coach_domain=state.get("coach_type")
    )
    
    # Stage 3: Assess quality
    context.quality_before = assess_quality(response)
    
    # Stage 4: Reflect if needed
    if context.should_reflect():
        improved_response = improve_response(
            response, 
            context.get_improvement_prompt()
        )
        context.quality_after = assess_quality(improved_response)
        response = improved_response
    
    # Stage 5: Store results
    state["final_response"] = response
    state["specialist_responses"]["reflection"] = reflection_to_state(context)
    
    return state
```

### Pattern 2: Cross-Domain Integration

```python
def integrated_coach_node(state: AgentState) -> AgentState:
    # Retrieve multiple contexts
    memory_context = state_to_memory(
        state.get("specialist_responses", {}).get("memory", {})
    )
    coach_context = state_to_specialized_coach(
        state.get("specialist_responses", {}).get("specialized_coach", {})
    )
    
    # Use memory to enhance coach response
    personalization_prompt = memory_context.to_context_prompt()
    coach_context.log_step("memory_integration", {
        "memories_used": len(memory_context.relevant_memories)
    })
    
    # Generate response with personalization
    response = generate_coach_response(
        query=state["user_query"],
        personalization=personalization_prompt,
        complexity=coach_context.complexity_assessment
    )
    
    # Update both contexts
    memory_context.add_interaction(InteractionSummary(
        session_id=state.get("thread_id", "unknown"),
        timestamp=datetime.now(),
        user_query=state["user_query"],
        coach_response_summary=response[:100] + "...",
        coaches_involved=[coach_context.coach_domain],
        key_topics=extract_topics(state["user_query"])
    ))
    
    # Store all updates
    state["final_response"] = response
    state["specialist_responses"]["memory"] = memory_to_state(memory_context)
    state["specialist_responses"]["specialized_coach"] = specialized_coach_to_state(coach_context)
    
    return state
```

## Testing Migration

### Unit Test Pattern

```python
import pytest
from athlea_langgraph.models import create_reflection_context, reflection_to_state

def test_reflection_context_migration():
    # Create context
    context = create_reflection_context(
        original_response="Do 3 sets of squats",
        coach_domain="strength"
    )
    
    # Test domain logic
    assert context.should_reflect() == False  # No quality assessment yet
    
    # Add quality assessment
    context.quality_before = QualityAssessment(
        overall_score=0.6,
        criteria_scores={"safety": 0.5, "clarity": 0.7},
        weaknesses=["No form guidance"]
    )
    
    assert context.should_reflect() == True  # Below threshold
    
    # Test state conversion
    state_data = reflection_to_state(context)
    assert state_data["quality_score"] is None  # No "after" score yet
    
    # Test reconstruction
    reconstructed = state_to_reflection(state_data)
    assert reconstructed.original_response == "Do 3 sets of squats"
    assert reconstructed.quality_before.overall_score == 0.6
```

## Troubleshooting

### Issue: "KeyError: specialist_responses"

**Solution:** Initialize the field if it doesn''t exist:
```python
if "specialist_responses" not in state:
    state["specialist_responses"] = {}
```

### Issue: "Cannot reconstruct domain model"

**Solution:** Use try-except with fallback:
```python
try:
    context = state_to_reflection(state_data)
except Exception:
    context = create_reflection_context(original_response="")
```

### Issue: "Lost data after update"

**Solution:** Always retrieve existing context first:
```python
# Retrieve existing
existing = state.get("specialist_responses", {}).get("domain", {})
context = state_to_domain(existing) if existing else create_domain_context()

# Make updates
context.update_something()

# Store back
state["specialist_responses"]["domain"] = domain_to_state(context)
```

## Summary

The migration from custom state classes to domain models provides:

1. **Centralized State**: Single `AgentState` for all graph communication
2. **Domain Logic**: Encapsulated in Pydantic models with validation
3. **Type Safety**: Full type hints and runtime validation
4. **Backward Compatibility**: Existing code can be migrated incrementally
5. **Better Testing**: Domain models can be tested independently

Follow this guide to migrate your code from the old state classes to the new domain model pattern.
