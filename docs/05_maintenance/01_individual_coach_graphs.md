# Individual Coach Graphs

This document explains the individual coach graphs implementation that allows each coach to work independently while maintaining the same setup and capabilities as the comprehensive hybrid system.

## Overview

The individual coach graphs solve the problem of having to pass in a specific coach parameter to get responses from a single coach in the optimized hybrid graph. Instead of duplicating the entire comprehensive hybrid graph for each coach, we created focused graphs that:

1. **Reuse existing components** - Same ReAct executors, tools, and capabilities
2. **Simplified routing** - Direct flow to the specific coach
3. **Optional GraphRAG** - Research capabilities when needed
4. **No duplication** - Efficient reuse of the comprehensive graph setup

## Architecture

### Flow Structure
```
START → reasoning → coach → END
```

### Key Components

1. **Enhanced Reasoning Node** - Sets the primary coach and routing decision
2. **Individual Coach Node** - Reuses the same ReAct executor logic with all tools available

## Available Graphs

The following individual coach graphs are available in `langgraph.json`:

- `strength_coach` - Strength training and powerlifting expertise
- `cardio_coach` - Cardiovascular training and endurance
- `cycling_coach` - Cycling-specific training and techniques  
- `nutrition_coach` - Diet, nutrition, and meal planning
- `recovery_coach` - Rest, sleep, and recovery optimization
- `mental_coach` - Mental health and mindset coaching

## Configuration

Each graph accepts the same configuration schema:

```json
{
  "user_id": "string (default: studio_user)",
  "thread_id": "string (default: studio_thread)", 
  "enable_memory": "boolean (default: true)",
  "use_react_agents": "boolean (default: true)",
  "max_iterations": "integer (default: 5, min: 1, max: 10)"
}
```

## Usage Examples

### Creating Individual Graphs

```python
from athlea_langgraph.graphs.individual_coach_graphs import (
    create_strength_coach_graph,
    create_nutrition_coach_graph,
    create_individual_coach_graph
)

# Using factory functions
strength_graph = await create_strength_coach_graph()
nutrition_graph = await create_nutrition_coach_graph()

# Using the generic function
cardio_graph = await create_individual_coach_graph("cardio_coach")
```

### With Configuration

```python
config = {
    "user_id": "user123",
    "thread_id": "thread456", 
    "enable_memory": True,
    "max_iterations": 3
}

graph = await create_strength_coach_graph(config)
```

### Running a Query

```python
from langchain_core.messages import HumanMessage
from athlea_langgraph.states import AgentState

# Input must be a dictionary that conforms to the AgentState TypedDict
input_data: AgentState = {
    "messages": [HumanMessage(content="How do I improve my deadlift form?")],
    "user_query": "How do I improve my deadlift form?",
    "user_profile": {"name": "Test User", "fitness_level": "intermediate"},
    "thread_id": "thread456",
    "user_id": "user123",
    "coach_type": "strength_coach",
    # Initialize other fields to their default/None values
    "final_response": None,
    "clarification_output": None,
    "aggregated_response": None,
    "specialist_responses": {},
    "safety_alert": None,
    "safety_details": None,
    "routing_priority": None,
}

result = await graph.ainvoke(input_data)
print(result.get("final_response"))
```

## Tool Integration

The graphs have all tools integrated directly with each coach:

- **Research Capabilities**: Each coach has access to research tools when needed
- **Direct Flow**: `reasoning → coach` - coaches handle research through their own tools
- **Tool Suite**: Each coach has access to all relevant tools for their domain

Example research query:
```
"What does the latest research say about protein timing for muscle growth?"
```

The nutrition coach will use their research tools directly to provide evidence-based answers.

## Benefits

1. **No Duplication** - Reuses all existing components from the comprehensive graph
2. **Efficient** - Simplified flow for single-coach queries  
3. **Consistent** - Same tools, prompts, and capabilities as the main system
4. **Direct Access** - All tools are available directly to each coach
5. **Studio Compatible** - Full integration with LangGraph Studio

## Implementation Details

### File Structure
```
athlea_langgraph/graphs/individual_coach_graphs.py  # Main implementation
tests/test_individual_coach_graphs.py               # Tests
docs/individual_coach_graphs.md                     # This documentation
```

### Key Functions
- `create_individual_coach_graph(coach_name, config)` - Generic factory
- `create_strength_coach_graph(config)` - Strength coach factory
- `create_cardio_coach_graph(config)` - Cardio coach factory
- (etc. for each coach)

### State Management
Uses the canonical `AgentState` for consistency with the rest of the system. For more details on state management, see the **[State Management Guide](../04_developer_guides/06_state_management.md)**.

## Testing

Run the tests to verify functionality:

```bash
cd python-langgraph
poetry run python tests/test_individual_coach_graphs.py
```

Or with pytest:
```bash
cd python-langgraph  
poetry run pytest tests/test_individual_coach_graphs.py -v
```

## Comparison with Comprehensive Graph

| Feature | Comprehensive Graph | Individual Coach Graph |
|---------|-------------------|----------------------|
| Coach Selection | Intelligence Hub routing | Fixed coach |
| Node Count | 8-12 nodes | 2 nodes |
| Flow Complexity | Multi-path routing | Simple linear flow |
| Tools | Shared across coaches | Direct access per coach |
| Use Case | Complex multi-coach queries | Single coach expertise |
| Performance | Higher latency | Lower latency |

## Future Enhancements

1. **Coach-Specific Routing** - Specialized flows per coach type
2. **Enhanced Research** - Coach-specific research strategies  
3. **Tool Optimization** - Coach-specific tool filtering
4. **Performance Metrics** - Individual coach analytics 