# Web Search State Migration Guide

This guide explains how to migrate from the old `WebSearchState` to the new pattern using centralized `AgentState` with domain models.

## Overview

The web search functionality previously used a custom `WebSearchState` class that extended the base state with many specialized fields. This has been refactored to use:

1. The centralized `AgentState` 
2. Domain models in `athlea_langgraph/models/web_search_models.py`
3. The `specialist_responses` field to store web search data

## Key Changes

### Before (Custom State)

```python
from athlea_langgraph.states.web_search_state import WebSearchState

class WebSearchState(AgentState):
    research_question: str
    search_plan: Optional[WebSearchPlan]
    search_queries: List[str]
    search_results: List[WebSearchResult]
    scraped_contents: List[ScrapedWebContent]
    # ... many more fields
```

### After (Domain Models + Centralized State)

```python
from athlea_langgraph.states.state import AgentState
from athlea_langgraph.models.web_search_models import WebSearchContext

# Use standard AgentState
state: AgentState = {
    "user_query": "research question here",
    "specialist_responses": {
        "web_search": {
            "context": web_search_context.model_dump(),
            "summary": "...",
            "metrics": {...}
        }
    },
    # ... other standard fields
}
```

## Migration Steps

### 1. Update Imports

```python
# Old
from athlea_langgraph.states.web_search_state import WebSearchState

# New
from athlea_langgraph.states.state import AgentState
from athlea_langgraph.models.web_search_models import (
    WebSearchContext,
    WebSearchResult,
    WebSearchPlan,
    ScrapedWebContent
)
```

### 2. Update Node Functions

#### Old Pattern
```python
def web_search_node(state: WebSearchState) -> WebSearchState:
    state.search_results.append(result)
    state.research_summary = "..."
    return state
```

#### New Pattern
```python
def web_search_node(state: AgentState) -> AgentState:
    # Create or retrieve context
    context = WebSearchContext(
        research_question=state["user_query"]
    )
    
    # Work with domain model
    context.search_results.append(result)
    context.research_summary = "..."
    
    # Store in state
    state["specialist_responses"]["web_search"] = {
        "context": context.model_dump(),
        "summary": context.research_summary,
        "metrics": context.get_research_metrics()
    }
    
    return state
```

### 3. Update Graph Creation

```python
# Old
graph = StateGraph(WebSearchState)

# New  
graph = StateGraph(AgentState)
```

### 4. Field Mapping

| Old WebSearchState Field | New Location |
|-------------------------|--------------|
| `research_question` | `state["user_query"]` |
| `search_plan` | `context.search_plan` |
| `search_results` | `context.search_results` |
| `scraped_contents` | `context.scraped_contents` |
| `research_summary` | `state["final_response"]` or `context.research_summary` |
| `planner_messages` | `state["messages"]` |
| `quality_score` | `context.quality_score` |

### 5. Accessing Web Search Data

```python
# Old
summary = state.research_summary
results = state.search_results

# New
web_data = state["specialist_responses"]["web_search"]
summary = web_data["summary"]
context = WebSearchContext(**web_data["context"])
results = context.search_results
```

## Complete Example

See `athlea_langgraph/graphs/tools/web_search_refactored_example.py` for a complete working example of the new pattern.

## Benefits of the New Approach

1. **Consistency**: All graphs use the same base state
2. **Type Safety**: Domain models provide full typing
3. **Encapsulation**: Web search logic is contained in domain models
4. **Flexibility**: Easy to evolve without changing state structure
5. **Testability**: Domain models can be tested independently

## Troubleshooting

### Import Errors
If you see `ImportError: cannot import name 'WebSearchState'`, update your imports to use `AgentState` and the domain models.

### Attribute Errors
If you see `AttributeError: 'dict' object has no attribute 'search_results'`, remember that `AgentState` is a TypedDict (dict), not a class. Access fields using dictionary syntax.

### Type Errors
Ensure you're creating the state as a dictionary:
```python
# Correct
state: AgentState = {"messages": [], ...}

# Incorrect  
state = AgentState(messages=[], ...)  # TypedDict can't be instantiated
```

## Next Steps

1. Review the example implementation
2. Update your web search nodes to use domain models
3. Test thoroughly to ensure data flows correctly
4. Remove any imports of the old `WebSearchState` 