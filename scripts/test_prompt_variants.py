#!/usr/bin/env python3
"""Test prompt variants through full strength coach flow."""

import asyncio
import json
import os
import shutil
from pathlib import Path
from datetime import datetime
import sys
sys.path.append(str(Path(__file__).parent.parent))

from tests.core.test_automated_optimization import test_strength_coach_accuracy_with_tracing


async def test_prompt_variant(variant_name: str, variant_path: Path):
    """Test a single prompt variant by replacing the production prompt temporarily."""
    
    print(f"\n{'='*60}")
    print(f"🧪 Testing Variant: {variant_name}")
    print(f"📄 Path: {variant_path}")
    print('='*60)
    
    # Backup original prompt
    original_prompt_path = Path("athlea_langgraph/prompts/coaches/strength_coach.json")
    backup_path = original_prompt_path.with_suffix('.json.backup')
    
    try:
        # Create backup
        shutil.copy2(original_prompt_path, backup_path)
        
        # Replace with variant
        shutil.copy2(variant_path, original_prompt_path)
        
        # Clear any cached prompts
        if hasattr(sys.modules.get('athlea_langgraph.agents.strength_coach'), 'strength_coach'):
            coach = sys.modules['athlea_langgraph.agents.strength_coach'].strength_coach
            coach._prompt_loaded = False
        
        # Run the test
        print(f"🚀 Running full-flow test for {variant_name}...")
        result = await test_strength_coach_accuracy_with_tracing()
        
        # Extract key metrics
        if result and result.get('organized_results'):
            metrics = result['organized_results'].get('quality_metrics_summary', {})
            scenarios = result['organized_results'].get('test_scenarios', {})
            
            # Calculate average element coverage
            coverage_scores = []
            generic_advice_count = 0
            for scenario_data in scenarios.values():
                qa = scenario_data.get('quality_assessment', {})
                if 'elements_coverage' in qa:
                    coverage_scores.append(qa['elements_coverage'])
                if 'generic_advice' in qa.get('critical_issues', []):
                    generic_advice_count += 1
            
            avg_coverage = sum(coverage_scores) / len(coverage_scores) if coverage_scores else 0
            
            variant_result = {
                'variant_name': variant_name,
                'timestamp': datetime.now().isoformat(),
                'pass_rate': result.get('pass_rate', 0),
                'specificity': metrics.get('average_specificity_score', 0),
                'safety': metrics.get('average_safety_score', 0),
                'coverage': avg_coverage,
                'generic_advice_count': generic_advice_count,
                'total_scenarios': len(scenarios),
                'raw_results_file': result.get('filename', ''),
                'improvements': {
                    'specificity_delta': metrics.get('average_specificity_score', 0) - 0.50,
                    'coverage_delta': avg_coverage - 0.686,
                    'generic_advice_eliminated': generic_advice_count == 0
                }
            }
            
            print(f"\n📊 Results for {variant_name}:")
            print(f"  - Pass Rate: {variant_result['pass_rate']:.2%}")
            print(f"  - Specificity: {variant_result['specificity']:.3f} (Δ{variant_result['improvements']['specificity_delta']:+.3f})")
            print(f"  - Coverage: {variant_result['coverage']:.3f} (Δ{variant_result['improvements']['coverage_delta']:+.3f})")
            print(f"  - Generic Advice: {'❌ Detected' if generic_advice_count > 0 else '✅ Eliminated'}")
            
            return variant_result
        else:
            print(f"❌ Test failed for {variant_name}")
            return {
                'variant_name': variant_name,
                'error': 'Test execution failed',
                'timestamp': datetime.now().isoformat()
            }
            
    finally:
        # Always restore original prompt
        if backup_path.exists():
            shutil.copy2(backup_path, original_prompt_path)
            backup_path.unlink()
            print(f"✅ Restored original prompt")


async def test_all_variants():
    """Test all prompt variants and compare results."""
    
    print("🚀 PROMPT VARIANT TESTING")
    print("=" * 80)
    print(f"⏰ Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Get all variants
    variants_dir = Path("scripts/prompt_variants")
    variant_files = sorted(variants_dir.glob("v*.json"))
    
    print(f"\n📁 Found {len(variant_files)} variants to test")
    
    results = []
    
    # Test each variant
    for variant_path in variant_files:
        variant_name = variant_path.stem
        try:
            result = await test_prompt_variant(variant_name, variant_path)
            results.append(result)
            
            # Small delay between tests
            await asyncio.sleep(5)
            
        except Exception as e:
            print(f"❌ Error testing {variant_name}: {e}")
            import traceback
            traceback.print_exc()
            results.append({
                'variant_name': variant_name,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
    
    # Compare results
    print("\n" + "="*80)
    print("📊 COMPARATIVE ANALYSIS")
    print("="*80)
    
    # Find best performers
    best_specificity = max(results, key=lambda x: x.get('specificity', 0))
    best_coverage = max(results, key=lambda x: x.get('coverage', 0))
    best_overall = max(results, key=lambda x: x.get('pass_rate', 0))
    
    print("\n🏆 Best Performers:")
    print(f"  - Specificity: {best_specificity['variant_name']} ({best_specificity.get('specificity', 0):.3f})")
    print(f"  - Coverage: {best_coverage['variant_name']} ({best_coverage.get('coverage', 0):.3f})")
    print(f"  - Overall Pass Rate: {best_overall['variant_name']} ({best_overall.get('pass_rate', 0):.2%})")
    
    # Detailed comparison table
    print("\n📈 Detailed Comparison:")
    print(f"{'Variant':<20} {'Pass Rate':>10} {'Specificity':>12} {'Coverage':>10} {'Generic Advice':>15}")
    print("-" * 70)
    
    for result in results:
        if 'error' not in result:
            generic_status = '✅ None' if result.get('generic_advice_count', 0) == 0 else f'❌ {result.get("generic_advice_count", 0)} found'
            print(f"{result['variant_name']:<20} "
                  f"{result.get('pass_rate', 0):>10.2%} "
                  f"{result.get('specificity', 0):>12.3f} "
                  f"{result.get('coverage', 0):>10.3f} "
                  f"{generic_status:>15}")
        else:
            print(f"{result['variant_name']:<20} {'ERROR':>10} {'-':>12} {'-':>10} {'-':>15}")
    
    # Save comparison results
    comparison_file = Path("scripts/prompt_variants/comparison_results.json")
    with open(comparison_file, 'w', encoding='utf-8') as f:
        json.dump({
            'test_timestamp': datetime.now().isoformat(),
            'baseline_metrics': {
                'pass_rate': 0.0,
                'specificity': 0.50,
                'coverage': 0.686,
                'generic_advice_count': 1
            },
            'variant_results': results,
            'best_performers': {
                'specificity': best_specificity['variant_name'],
                'coverage': best_coverage['variant_name'],
                'overall': best_overall['variant_name']
            }
        }, f, indent=2)
    
    print(f"\n💾 Results saved to: {comparison_file}")
    
    # Select top 2 variants for further testing
    sorted_by_improvement = sorted(
        [r for r in results if 'error' not in r],
        key=lambda x: (x.get('specificity', 0) - 0.50) + (x.get('coverage', 0) - 0.686),
        reverse=True
    )
    
    top_variants = sorted_by_improvement[:2] if len(sorted_by_improvement) >= 2 else sorted_by_improvement
    
    print(f"\n🎯 Selected for Full Testing:")
    for i, variant in enumerate(top_variants, 1):
        print(f"  {i}. {variant['variant_name']} (Combined improvement: "
              f"{(variant.get('specificity', 0) - 0.50) + (variant.get('coverage', 0) - 0.686):.3f})")
    
    return results, top_variants


if __name__ == "__main__":
    results, top_variants = asyncio.run(test_all_variants())
    print(f"\n✅ Variant testing complete!") 