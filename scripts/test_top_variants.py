#!/usr/bin/env python3
"""Test top 2 prompt variants with detailed comparison."""

import asyncio
import json
import shutil
from pathlib import Path
from datetime import datetime
import sys
sys.path.append(str(Path(__file__).parent.parent))

from tests.core.test_automated_optimization import test_strength_coach_accuracy_with_tracing


async def test_variant(variant_name: str):
    """Test a single variant and return detailed metrics."""
    
    print(f"\n{'='*80}")
    print(f"🧪 Testing {variant_name}")
    print('='*80)
    
    # Paths
    original_prompt = Path("athlea_langgraph/prompts/coaches/strength_coach.json")
    variant_prompt = Path(f"scripts/prompt_variants/{variant_name}.json")
    backup_prompt = original_prompt.with_suffix('.json.backup')
    
    try:
        # Backup and replace
        shutil.copy2(original_prompt, backup_prompt)
        shutil.copy2(variant_prompt, original_prompt)
        
        # Clear cached prompts
        if 'athlea_langgraph.agents.strength_coach' in sys.modules:
            del sys.modules['athlea_langgraph.agents.strength_coach']
        if 'athlea_langgraph.utils.prompt_loader' in sys.modules:
            # Reset prompt loader cache
            loader = sys.modules.get('athlea_langgraph.utils.prompt_loader')
            if hasattr(loader, '_prompt_cache'):
                loader._prompt_cache.clear()
        
        # Run test with tracing
        print(f"🚀 Running full-flow test...")
        result = await test_strength_coach_accuracy_with_tracing()
        
        # Extract detailed metrics
        if result and result.get('organized_results'):
            scenarios = result['organized_results'].get('test_scenarios', {})
            metrics = result['organized_results'].get('quality_metrics_summary', {})
            
            # Calculate detailed metrics
            coverage_scores = []
            element_details = {}
            generic_advice_count = 0
            
            for scenario_name, scenario_data in scenarios.items():
                qa = scenario_data.get('quality_assessment', {})
                coverage_scores.append(qa.get('elements_coverage', 0))
                element_details[scenario_name] = {
                    'found': qa.get('found_elements_list', []),
                    'missing': qa.get('missing_elements', []),
                    'coverage': qa.get('elements_coverage', 0)
                }
                if 'generic_advice' in qa.get('critical_issues', []):
                    generic_advice_count += 1
            
            avg_coverage = sum(coverage_scores) / len(coverage_scores) if coverage_scores else 0
            
            return {
                'variant': variant_name,
                'pass_rate': result.get('pass_rate', 0),
                'metrics': {
                    'specificity': metrics.get('average_specificity_score', 0),
                    'safety': metrics.get('average_safety_score', 0),
                    'coverage': avg_coverage,
                    'composite': (
                        metrics.get('average_specificity_score', 0) * 0.25 +
                        metrics.get('average_safety_score', 0) * 0.25 +
                        avg_coverage * 0.25 +
                        (1.0 if generic_advice_count == 0 else 0) * 0.25
                    )
                },
                'element_details': element_details,
                'generic_advice_count': generic_advice_count,
                'raw_file': result.get('filename', '')
            }
        else:
            return None
            
    finally:
        # Restore original
        if backup_prompt.exists():
            shutil.copy2(backup_prompt, original_prompt)
            backup_prompt.unlink()


async def main():
    """Test top variants and provide detailed comparison."""
    
    print("🎯 PROMPT OPTIMIZATION - TOP VARIANTS TESTING")
    print("=" * 80)
    print(f"⏰ Start: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Define baseline metrics
    baseline = {
        'pass_rate': 0.0,
        'metrics': {
            'specificity': 0.50,
            'safety': 1.00,
            'coverage': 0.686,
            'composite': 0.0
        },
        'generic_advice_count': 1
    }
    
    # Test variants
    variants_to_test = ['v2_specificity', 'v4_combined']
    results = {}
    
    for variant in variants_to_test:
        result = await test_variant(variant)
        if result:
            results[variant] = result
        
        # Delay between tests
        await asyncio.sleep(5)
    
    # Detailed comparison
    print("\n" + "="*80)
    print("📊 DETAILED COMPARISON ANALYSIS")
    print("="*80)
    
    # Summary table
    print("\n📈 Metrics Comparison:")
    print(f"{'Metric':<20} {'Baseline':<15} {'v2_specificity':<20} {'v4_combined':<15}")
    print("-" * 75)
    
    metrics_to_compare = ['specificity', 'safety', 'coverage', 'composite']
    for metric in metrics_to_compare:
        baseline_val = baseline['metrics'][metric]
        v2_val = results.get('v2_specificity', {}).get('metrics', {}).get(metric, 0)
        v4_val = results.get('v4_combined', {}).get('metrics', {}).get(metric, 0)
        
        print(f"{metric.capitalize():<20} "
              f"{baseline_val:<15.3f} "
              f"{v2_val:<10.3f} ({v2_val-baseline_val:+.3f}) "
              f"{v4_val:<10.3f} ({v4_val-baseline_val:+.3f})")
    
    # Generic advice
    print(f"{'Generic Advice':<20} "
          f"{baseline['generic_advice_count']:<15} "
          f"{results.get('v2_specificity', {}).get('generic_advice_count', 0):<20} "
          f"{results.get('v4_combined', {}).get('generic_advice_count', 0):<15}")
    
    # Element coverage details
    print("\n📋 Element Coverage Details:")
    for variant_name, result in results.items():
        print(f"\n{variant_name}:")
        for scenario, details in result.get('element_details', {}).items():
            print(f"  {scenario}:")
            print(f"    - Coverage: {details['coverage']:.2%}")
            print(f"    - Found: {', '.join(details['found'])}")
            print(f"    - Missing: {', '.join(details['missing'])}")
    
    # LangSmith traces
    print("\n🔗 LangSmith Traces:")
    print("Navigate to: https://smith.langchain.com/")
    print("Project: athlea-coaching-dev")
    print("Time filter: Last 6 hours")
    print("Look for threads starting with: strength_accuracy_")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS:")
    
    best_variant = None
    best_composite = 0
    
    for variant_name, result in results.items():
        composite = result['metrics']['composite']
        if composite > best_composite:
            best_composite = composite
            best_variant = variant_name
    
    if best_variant:
        improvement = best_composite - baseline['metrics']['composite']
        print(f"\n✅ Best performer: {best_variant}")
        print(f"   - Composite score: {best_composite:.3f} (+{improvement:.3f})")
        
        # Check deployment criteria
        variant_result = results[best_variant]
        criteria_met = 0
        total_criteria = 5
        
        print("\n📊 Deployment Criteria Check:")
        
        criteria = [
            ('Pass Rate', variant_result['pass_rate'] >= 0.80, f"{variant_result['pass_rate']:.2%}", "≥80%"),
            ('Specificity', variant_result['metrics']['specificity'] >= 0.75, f"{variant_result['metrics']['specificity']:.3f}", "≥0.75"),
            ('Safety', variant_result['metrics']['safety'] >= 0.90, f"{variant_result['metrics']['safety']:.3f}", "≥0.90"),
            ('Coverage', variant_result['metrics']['coverage'] >= 0.80, f"{variant_result['metrics']['coverage']:.3f}", "≥0.80"),
            ('Generic Advice', variant_result['generic_advice_count'] == 0, f"{variant_result['generic_advice_count']} issues", "0 issues")
        ]
        
        for name, passed, actual, target in criteria:
            status = "✅" if passed else "❌"
            print(f"  {status} {name}: {actual} (target: {target})")
            if passed:
                criteria_met += 1
        
        print(f"\n🎯 Criteria met: {criteria_met}/{total_criteria}")
        
        if criteria_met >= 4:
            print("✅ RECOMMENDATION: Deploy this variant")
        elif criteria_met >= 2:
            print("🔄 RECOMMENDATION: Iterate - significant improvement but more work needed")
        else:
            print("❌ RECOMMENDATION: Investigate - systematic issues remain")
    
    # Save results
    output_file = Path("scripts/prompt_variants/top_variants_comparison.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({
            'timestamp': datetime.now().isoformat(),
            'baseline': baseline,
            'results': results,
            'best_variant': best_variant,
            'deployment_recommendation': 'iterate' if best_variant else 'investigate'
        }, f, indent=2)
    
    print(f"\n💾 Results saved to: {output_file}")
    
    return results


if __name__ == "__main__":
    results = asyncio.run(main())
    print("\n✅ Testing complete!") 