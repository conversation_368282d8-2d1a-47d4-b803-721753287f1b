#!/usr/bin/env python3
"""Fix version numbers in prompt variants to be valid semantic versions."""

import json
from pathlib import Path


def fix_versions():
    """Fix version numbers in all prompt variants."""
    
    variants_dir = Path("scripts/prompt_variants")
    
    # Version mappings
    version_fixes = {
        "v1_baseline": "3.0.0",
        "v2_specificity": "3.1.0",
        "v3_coverage": "3.2.0", 
        "v4_combined": "3.3.0"
    }
    
    for variant_file in variants_dir.glob("v*.json"):
        variant_name = variant_file.stem
        
        if variant_name in version_fixes:
            with open(variant_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Fix version
            old_version = data['metadata']['version']
            new_version = version_fixes[variant_name]
            data['metadata']['version'] = new_version
            
            # Update description to remove version suffix
            if '_' in old_version:
                data['metadata']['description'] = data['metadata']['description'].replace(f" - {old_version.split('_')[1].title()} Enhanced", "")
                data['metadata']['description'] += f" - {variant_name.replace('v1_baseline', 'Baseline').replace('v2_', '').replace('v3_', '').replace('v4_', '').replace('_', ' ').title()} Variant"
            
            # Save fixed version
            with open(variant_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Fixed {variant_name}: {old_version} → {new_version}")
    
    print("\n✅ All versions fixed!")


if __name__ == "__main__":
    fix_versions() 