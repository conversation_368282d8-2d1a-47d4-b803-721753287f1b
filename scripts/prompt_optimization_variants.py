#!/usr/bin/env python3
"""Create prompt variants for strength coach optimization."""

import json
import os
from copy import deepcopy
from pathlib import Path


def create_prompt_variants():
    """Create prompt variants targeting specific improvement areas."""
    
    # Load current prompt
    prompt_path = Path("athlea_langgraph/prompts/coaches/strength_coach.json")
    with open(prompt_path, 'r', encoding='utf-8') as f:
        baseline_prompt = json.load(f)
    
    # Create variants directory
    variants_dir = Path("scripts/prompt_variants")
    variants_dir.mkdir(exist_ok=True)
    
    # Variant 1: Baseline (current)
    v1_baseline = deepcopy(baseline_prompt)
    
    # Variant 2: Enhanced Specificity
    v2_specificity = deepcopy(baseline_prompt)
    v2_specificity['metadata']['version'] = "3.1.0_specificity"
    v2_specificity['metadata']['description'] += " - Specificity Enhanced"
    
    # Add specificity requirements to system prompt
    specificity_addition = """

# SPECIFICITY REQUIREMENTS (CRITICAL FOR QUALITY)

**You MUST include specific numbers and technical details in EVERY response:**

1. **Weight Recommendations**: ALWAYS provide specific weight numbers
   - Use percentages: "Reduce to 60-70% (135-155 lbs based on your 225 lbs)"
   - Give exact progressions: "Week 1: 135 lbs, Week 2: 155 lbs, Week 3: 185 lbs"
   - Never say "reduce weight" without specific numbers

2. **Rep/Set Schemes**: ALWAYS provide exact numbers
   - Specific schemes: "3x5", "4x6-8", "5x3"
   - Never say "more reps" - say "increase from 3x5 to 4x8"
   - Include rest periods: "3x5 with 3-5 minutes rest"

3. **Technical Terminology**: Use precise biomechanical language
   - Say "hip hinge pattern" not "proper hip movement"
   - Say "scapular retraction" not "squeeze shoulder blades"
   - Say "neutral spine with 15-20° forward lean" not "keep back straight"

4. **Timeline Specificity**: Provide exact timeframes
   - "4-6 weeks" not "a few weeks"
   - "Deload every 4th week" not "periodic deloads"
   - "Progress by 5 lbs every 2 weeks" not "gradual progression"

5. **Muscle/Anatomy References**: Name specific muscles
   - "Engage glutes and hamstrings" not "use posterior chain"
   - "Strengthen erector spinae" not "work lower back muscles"
   - "Target pectoralis major and anterior deltoids" not "chest and shoulders"
"""
    
    # Insert after tool specifications
    system_prompt = v2_specificity['prompt']['system']
    insertion_point = system_prompt.find("# EMOTIONAL EMPHASIS")
    v2_specificity['prompt']['system'] = (
        system_prompt[:insertion_point] + 
        specificity_addition + 
        "\n" + 
        system_prompt[insertion_point:]
    )
    
    # Variant 3: Enhanced Coverage
    v3_coverage = deepcopy(baseline_prompt)
    v3_coverage['metadata']['version'] = "3.1.0_coverage"
    v3_coverage['metadata']['description'] += " - Coverage Enhanced"
    
    # Add coverage checklist to system prompt
    coverage_addition = """

# COMPREHENSIVE COVERAGE CHECKLIST (MANDATORY)

**For EVERY strength training response, ensure you cover ALL applicable elements:**

## Pain/Injury Scenarios MUST Include:
✓ Explicit pain acknowledgment in first sentence
✓ Hip hinge pattern explanation with angle specifics
✓ Neutral spine maintenance technique
✓ Bar path optimization (2-3 inches from body)
✓ Specific weight reduction (60-70% of current)
✓ Form assessment recommendation
✓ Medical consultation threshold (pain >1 week)
✓ Progressive return timeline (4-8 weeks)

## Plateau Scenarios MUST Include:
✓ Deload protocol (50-60% for 1 week every 4-6 weeks)
✓ Multiple progression strategies (5/3/1, wave loading, linear)
✓ Rep manipulation options (3x5→4x3, 3x5→5x8)
✓ Frequency adjustment (1x→2x per week)
✓ Specific timeline expectations (4-6 weeks to break plateau)
✓ Volume/intensity trade-offs
✓ Accessory exercise recommendations

## General Requirements for ALL Responses:
✓ Opening that directly addresses the user's concern
✓ At least 3 specific numbered recommendations
✓ Safety considerations explicitly stated
✓ Progression timeline with specific milestones
✓ Alternative exercises when applicable
✓ Form cues with anatomical references
✓ Follow-up question or offer for program creation
"""
    
    # Insert after specificity requirements (for v3)
    system_prompt = v3_coverage['prompt']['system']
    insertion_point = system_prompt.find("# EMOTIONAL EMPHASIS")
    v3_coverage['prompt']['system'] = (
        system_prompt[:insertion_point] + 
        coverage_addition + 
        "\n" + 
        system_prompt[insertion_point:]
    )
    
    # Variant 4: Combined (Specificity + Coverage)
    v4_combined = deepcopy(baseline_prompt)
    v4_combined['metadata']['version'] = "3.1.0_combined"
    v4_combined['metadata']['description'] += " - Specificity & Coverage Enhanced"
    
    # Add both enhancements
    system_prompt = v4_combined['prompt']['system']
    insertion_point = system_prompt.find("# EMOTIONAL EMPHASIS")
    v4_combined['prompt']['system'] = (
        system_prompt[:insertion_point] + 
        specificity_addition + 
        "\n" +
        coverage_addition + 
        "\n" + 
        system_prompt[insertion_point:]
    )
    
    # Save all variants
    variants = {
        "v1_baseline": v1_baseline,
        "v2_specificity": v2_specificity,
        "v3_coverage": v3_coverage,
        "v4_combined": v4_combined
    }
    
    for name, variant in variants.items():
        output_path = variants_dir / f"{name}.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(variant, f, indent=2, ensure_ascii=False)
        print(f"✅ Created variant: {name} -> {output_path}")
    
    # Create summary
    summary = {
        "variants": list(variants.keys()),
        "improvements": {
            "v1_baseline": "Current production prompt",
            "v2_specificity": "Added specific number requirements, technical terminology mandates",
            "v3_coverage": "Added comprehensive element checklists for scenarios",
            "v4_combined": "Combined specificity and coverage improvements"
        },
        "target_metrics": {
            "specificity": {"current": 0.50, "target": 0.75},
            "coverage": {"current": 0.686, "target": 0.80},
            "safety": {"current": 1.00, "target": 0.90},
            "generic_advice": {"current": "detected", "target": "eliminated"}
        }
    }
    
    with open(variants_dir / "variants_summary.json", 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 Created {len(variants)} prompt variants")
    print(f"📁 Saved to: {variants_dir}")
    return variants


if __name__ == "__main__":
    create_prompt_variants() 