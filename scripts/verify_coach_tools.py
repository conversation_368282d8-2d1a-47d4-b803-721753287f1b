import asyncio
import logging
from athlea_langgraph.agents.strength_coach import strength_coach
from athlea_langgraph.agents.cardio_coach import cardio_coach
from athlea_langgraph.agents.nutrition_coach import nutrition_coach
from athlea_langgraph.agents.recovery_coach import recovery_coach
from athlea_langgraph.agents.mental_coach import mental_coach

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_coach_tools():
    """
    Initializes each domain coach and prints their loaded tools to verify standardization.
    """
    coaches = {
        "Strength": strength_coach,
        "Cardio": cardio_coach,
        "Nutrition": nutrition_coach,
        "Recovery": recovery_coach,
        "Mental": mental_coach,
    }

    expected_tools = {
        "graphrag_search",
        "azure_search_retriever",
        "web_search_graph",
        "maps_workflow",
    }

    all_tests_passed = True

    for name, coach in coaches.items():
        print("-" * 50)
        logger.info(f"🚀 Testing {name} Coach...")
        try:
            tools = await coach.get_domain_tools()
            tool_names = {tool.name for tool in tools}

            logger.info(f"✅ {name} Coach loaded {len(tools)} tools.")
            logger.info(f"🔧 Tools: {sorted(list(tool_names))}")

            # Verification
            missing_tools = expected_tools - tool_names
            if not missing_tools:
                logger.info(f"PASS: {name} Coach has all standardized universal tools.")
            else:
                logger.error(
                    f"FAIL: {name} Coach is MISSING standardized tools: {missing_tools}"
                )
                all_tests_passed = False

            # Check for domain-specific session generation tool
            session_tool_name = f"generate_{name.lower()}_session"
            if name == "Recovery":
                session_tool_name = "generate_recovery_protocol"
            if name == "Mental":
                session_tool_name = "generate_mental_training_session"

            if (
                session_tool_name not in tool_names
                and f"generate_{name.lower()}_plan" not in tool_names
            ):
                logger.error(
                    f"FAIL: {name} Coach is MISSING its domain-specific session generation tool."
                )
                all_tests_passed = False
            else:
                logger.info(
                    f"PASS: {name} Coach has its domain-specific session generation tool."
                )

        except Exception as e:
            logger.error(
                f"❌ An error occurred while testing {name} Coach: {e}", exc_info=True
            )
            all_tests_passed = False
        print("-" * 50)

    print("\n" + "=" * 50)
    if all_tests_passed:
        logger.info(
            "✅✅✅ ALL TESTS PASSED! Tool standardization is complete and verified. ✅✅✅"
        )
    else:
        logger.error("❌❌❌ SOME TESTS FAILED! Please review the logs. ❌❌❌")
    print("=" * 50)


if __name__ == "__main__":
    asyncio.run(test_coach_tools())
