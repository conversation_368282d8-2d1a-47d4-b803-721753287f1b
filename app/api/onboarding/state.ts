import { BaseMessage } from "@langchain/core/messages";
// Remove unused imports related to annotations/types
// import { Pregel, PregelInterfaceFns } from "@langchain/langgraph/pregel";
// import { type } from "os";
// Import PlanDetails type
import { PlanDetails } from "@/types/plan";
import { AIMessage, HumanMessage } from "@langchain/core/messages";
import { Document } from "@langchain/core/documents";

// --- Onboarding State Definition ---

// Define the possible stages of the onboarding process
export type OnboardingStage =
  | "initial"
  | "greeting"
  | "collecting_goals"
  | "summarizing_history"
  | "generating_plan"
  | "presenting_plan"
  | "complete"
  | "error";

// Define the fields we need to collect
export type OnboardingField =
  | "goal"
  | "experienceLevel"
  | "timeCommitment"
  | "equipment";

// Define the structure for user goals
export interface UserGoals {
  exists: boolean; // Flag indicating if goals have been captured
  list: string[]; // List of captured goal strings
}

// Define the structure for user history summary items
export interface SummaryItem {
  category: string; // e.g., "Training Frequency", "Dietary Habits", "Injuries"
  details: string; // The summarized detail
}

// Define a simple sidebar state for tracking progress
export interface SidebarStateData {
  currentStage: string;
  goals: {
    list: string[];
    exists: boolean;
  };
  // Remove summary string, add categorized items array
  // summary: string;
  summaryItems?: SummaryItem[];
  generatedPlan?: PlanDetails;
  // NEW: Add sport suggestions here as well for streaming
  sportSuggestions?: { label: string; value: string }[];
  // ADD: Also include selectedSport for display consistency
  selectedSport?: string | null;
  selectedSports?: string[]; // Add array for multiple sports
}

// Define the state interface
export interface OnboardingState {
  messages: BaseMessage[];
  userId: string;
  currentQuestionField?: OnboardingField; // Keep for potential use
  onboardingStage: OnboardingStage; // Track the current stage

  // User profile information
  goal?: string;
  experienceLevel?: string;
  timeCommitment?: string;
  equipment?: string;

  // Tracking for sidebar UI
  sidebarData?: SidebarStateData;

  // Flag for the new dynamic gatherer node
  hasEnoughInfo?: boolean;

  // Boolean flag specifically for indicating if input is needed
  needsInput?: boolean;

  // Field to hold the generated plan structure
  generatedPlan?: PlanDetails; // Make optional if it might not exist initially

  // LangGraph internal field for interrupt handling
  _langgraph_interrupt?: any;

  // Additional fields for the new structure
  userInput?: string; // From ?message= or ?initialMessage=
  resumeValue?: string; // From ?resumeValue=
  userName: string | null;
  relevantDocs: Document[]; // Retrieved documents relevant to history/context
  error: string | null; // To store any errors encountered
  // selectedSport: string | null; // NEW: To store the validated sport key (e.g., 'running', 'strength')
  // selectedSports?: string[]; // Add array for multiple sports

  // Metadata or control flags
  requiresInput: boolean;
  inputPrompt: string | null;
  currentTaskDescription: string | null; // Description of the current step

  // NEW: Field for sport suggestions on the first turn
  sportSuggestions?: { label: string; value: string }[];

  infoGathered: boolean;
  pendingGoalClarification: boolean;
  systemPrompt: string;
}

// Helper function to create the initial state (ensure all required fields have defaults)
export function createInitialOnboardingState(): Omit<
  OnboardingState,
  "userId"
> {
  return {
    messages: [],
    userName: null,
    relevantDocs: [],
    generatedPlan: undefined, // Use undefined for optional field
    error: null,
    onboardingStage: "initial", // Use the defined type
    requiresInput: false, // Default to false initially
    inputPrompt: null,
    currentTaskDescription: null,
    userInput: undefined,
    resumeValue: undefined,
    _langgraph_interrupt: undefined,
    // Add missing fields from OnboardingState
    currentQuestionField: undefined,
    goal: undefined,
    experienceLevel: undefined,
    timeCommitment: undefined,
    equipment: undefined,
    // Initialize sidebarData with default structure
    sidebarData: {
      currentStage: "initial",
      goals: { list: [], exists: false },
      summaryItems: [],
      generatedPlan: undefined,
      sportSuggestions: undefined,
      selectedSport: null,
      selectedSports: [],
    },
    hasEnoughInfo: false, // Default to false
    needsInput: false, // Default to false initially
    infoGathered: false,
    pendingGoalClarification: false,
    systemPrompt: "",
  };
}

// Utility functions for state manipulation (examples)

export function addHumanMessage(
  state: OnboardingState,
  content: string,
): OnboardingState {
  return {
    ...state,
    messages: [...state.messages, new HumanMessage({ content })],
    userInput: content, // Update raw userInput
    resumeValue: undefined, // Clear resumeValue when a new human message is added
  };
}

export function addAiMessage(
  state: OnboardingState,
  content: string,
): OnboardingState {
  // Prevent adding duplicate empty AI messages if one is already last
  const lastMessage = state.messages[state.messages.length - 1];
  if (
    lastMessage instanceof AIMessage &&
    lastMessage.content === "" &&
    content === ""
  ) {
    return state;
  }
  return {
    ...state,
    messages: [...state.messages, new AIMessage({ content })],
  };
}

export function setUserId(
  state: OnboardingState,
  userId: string,
): OnboardingState {
  return { ...state, userId };
}

export function setUserName(
  state: OnboardingState,
  userName: string,
): OnboardingState {
  return { ...state, userName };
}

export function addGoal(state: OnboardingState, goal: string): OnboardingState {
  const currentSidebarData =
    state.sidebarData || createInitialOnboardingState().sidebarData!;
  return {
    ...state,
    sidebarData: {
      ...currentSidebarData,
      goals: {
        exists: true,
        list: [...currentSidebarData.goals.list, goal],
      },
    },
  };
}

export function setGoals(
  state: OnboardingState,
  goals: string[],
): OnboardingState {
  const currentSidebarData =
    state.sidebarData || createInitialOnboardingState().sidebarData!;
  return {
    ...state,
    sidebarData: {
      ...currentSidebarData,
      goals: {
        exists: goals.length > 0,
        list: goals,
      },
    },
  };
}

export function addSummaryItem(
  state: OnboardingState,
  item: SummaryItem,
): OnboardingState {
  const currentSidebarData =
    state.sidebarData || createInitialOnboardingState().sidebarData!;
  const currentSummaryItems = currentSidebarData.summaryItems || [];
  return {
    ...state,
    sidebarData: {
      ...currentSidebarData,
      summaryItems: [...currentSummaryItems, item],
    },
  };
}

// Add more utility functions as needed for other state properties...

// Define channels with explicit reducers
export const onboardingChannels = {
  messages: {
    value: (x: BaseMessage[], y: BaseMessage[]) => x.concat(y),
    default: (): BaseMessage[] => [],
  },
  // Explicitly define others as last value takes precedence
  userId: { value: (_: any, y: any) => y, default: (): string => "" },
  onboardingStage: {
    value: (_: any, y: any) => y,
    default: (): OnboardingStage => "greeting",
  }, // Default stage
  currentQuestionField: {
    value: (_: any, y: any) => y,
    default: (): OnboardingField | undefined => undefined,
  },
  goal: { value: (_: any, y: any) => y, default: (): string => "" },
  experienceLevel: {
    value: (_: any, y: any) => y,
    default: (): string => "",
  },
  timeCommitment: {
    value: (_: any, y: any) => y,
    default: (): string => "",
  },
  equipment: {
    value: (_: any, y: any) => y,
    default: (): string => "",
  },
  sidebarData: {
    value: (_: any, y: any) => y,
    default: (): SidebarStateData => ({
      currentStage: "greeting",
      goals: { list: [], exists: false },
      summaryItems: [],
      generatedPlan: undefined,
      sportSuggestions: undefined,
      selectedSport: null,
      selectedSports: [],
    }),
  },
  hasEnoughInfo: {
    value: (_: any, y: any) => y,
    default: (): boolean => false,
  }, // Default to false
  needsInput: {
    value: (_: any, y: any) => y,
    default: (): boolean => true, // Default to true - we initially need input
  },
  generatedPlan: {
    value: (_: any, y: any) => y,
    default: (): PlanDetails | undefined => undefined,
  },
  _langgraph_interrupt: {
    value: (_: any, y: any) => y,
    default: (): any => null,
  },
};

// Re-export PlanDetails, SummaryItem is already exported via its interface definition
export type { PlanDetails };

// If you need functions to update specific parts of the state later, define them here.
// Rename and adapt to handle multiple selected sports
export function addSelectedSport(
  state: OnboardingState,
  sportValue: string,
): OnboardingState {
  const currentSidebarData =
    state.sidebarData || createInitialOnboardingState().sidebarData!;
  const currentSelectedSports = currentSidebarData.selectedSports || [];
  // Avoid duplicates
  if (!currentSelectedSports.includes(sportValue)) {
    return {
      ...state,
      sidebarData: {
        ...currentSidebarData,
        selectedSports: [...currentSelectedSports, sportValue],
      },
    };
  }
  return state; // Return unchanged state if sport already selected
}
