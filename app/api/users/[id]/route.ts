import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/app/lib/mongodb";

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ id: string }> },
) {
  console.log("GET request received for user!");
  try {
    const client = await clientPromise;
    const db = client.db("AthleaUserData");

    // Await the params before using
    const params = await Promise.resolve((await context.params));
    const user_id = params.id;

    if (!user_id) {
      console.log("Missing user id");
      return NextResponse.json({ error: "Missing user id" }, { status: 400 });
    }

    const collection = db.collection("users");
    // Fetch the user's full data, including the training_profile
    const userInfo = await collection.findOne({ user_id });

    if (!userInfo) {
      return NextResponse.json({ userData: {}, isNewUser: true });
    }

    // Determine if the user is new based on the training_profile
    const isNewUser =
      !userInfo.training_profile ||
      Object.keys(userInfo.training_profile).length === 0;

    return NextResponse.json({ ...userInfo, isNewUser });
  } catch (error) {
    console.error("Error fetching user data:", error);
    return NextResponse.json(
      { error: "Failed to fetch user data" },
      { status: 500 },
    );
  }
}

export async function PUT(req: NextRequest) {
  try {
    const client = await clientPromise;
    const db = client.db("AthleaUserData");
    const collection = db.collection("users");

    const user_id = req.nextUrl.pathname.split("/").pop();
    const { personal_info, connection } = await req.json();
    let result;
    if (!user_id) {
      return new NextResponse(
        JSON.stringify({ error: "Missing user_id" }),
        { status: 400 },
      );
    }
    if (connection) {
         result = await collection.updateOne(
      { user_id },
      { $addToSet: { connections: connection } }
    );
    }else{
        if (!personal_info) {
      return new NextResponse(
        JSON.stringify({ error: "Missing personal_info" }),
        { status: 400 },
      );
    }
      result = await collection.updateOne(
       { user_id },
       { $set: { personal_info } },
     );

    }

    if (result.matchedCount === 0) {
      return new NextResponse(JSON.stringify({ error: "User not found" }), {
        status: 404,
      });
    }

    return new NextResponse(JSON.stringify({ success: true }), { status: 200 });
  } catch (error) {
    console.error("Error updating personal info:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to update personal info" }),
      { status: 500 },
    );
  }
}

export async function PATCH(req: NextRequest) {
  try {
    const client = await clientPromise;
    const db = client.db("AthleaUserData");
    const collection = db.collection("users");

    const user_id = req.nextUrl.pathname.split("/").pop();
    const updates = await req.json();

    if (!user_id || Object.keys(updates).length === 0) {
      return new NextResponse(
        JSON.stringify({ error: "Missing user_id or update data" }),
        { status: 400 },
      );
    }

    const result = await collection.updateOne({ user_id }, { $set: updates });

    if (result.matchedCount === 0) {
      return new NextResponse(JSON.stringify({ error: "User not found" }), {
        status: 404,
      });
    }

    // Fetch the updated user data
    const updatedUser = await collection.findOne({ user_id });

    return new NextResponse(JSON.stringify(updatedUser), { status: 200 });
  } catch (error) {
    console.error("Error updating user data:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to update user data" }),
      { status: 500 },
    );
  }
}
