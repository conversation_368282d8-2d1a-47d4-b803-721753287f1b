import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/app/lib/mongodb";

export async function GET(req: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  console.log("GET request received for user!");
  try {
    const client = await clientPromise;
    const db = client.db("AthleaUserData");

    const user_id = params.id;
    console.log("userID:", user_id);

    if (!user_id) {
      console.log("Missing user id");
      return NextResponse.json({ error: "Missing user id" }, { status: 400 });
    }

    const userCollection = db.collection("users");
    const trainingPlanCollection = db.collection("trainingPlans");

    // Fetch the user's full data, including the training_profile
    const userInfo = await userCollection.findOne({ user_id });

    if (!userInfo) {
      return NextResponse.json({ userData: {}, isNewUser: true });
    }

    // Check for onboarding training plan
    const onboardingPlan = await trainingPlanCollection.findOne({
      user_id,
      isOnboarding: true,
    });

    // Determine if the user is new based on the training_profile and onboarding plan
    const isNewUser =
      (!userInfo.training_profile ||
        Object.keys(userInfo.training_profile).length === 0) &&
      !onboardingPlan;

    return NextResponse.json({ ...userInfo, isNewUser });
  } catch (error) {
    console.error("Error fetching user data:", error);
    return NextResponse.json(
      { error: "Failed to fetch user data" },
      { status: 500 },
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const client = await clientPromise;
    const db = client.db("AthleaUserData");
    const userCollection = db.collection("users");
    const trainingPlanCollection = db.collection("trainingPlans");

    const userInfo = await req.json();
    const user_id = userInfo?.user_id;

    if (!user_id || !userInfo) {
      console.log("Missing user_id or userInfo");
      return new NextResponse(
        JSON.stringify({ error: "Missing user_id or userInfo" }),
        { status: 400 },
      );
    }

    // Check if the user already exists
    const existingUser = await userCollection.findOne({ user_id });
    let isNewUser = false;

    if (existingUser) {
      // Update the existing user data
      await userCollection.updateOne(
        { user_id },
        {
          $set: {
            email: userInfo?.email,
            family_name: userInfo?.family_name,
            given_name: userInfo?.given_name,
            picture: userInfo?.picture,
            training_profile: existingUser.training_profile || {},
            currentOnboardingStep: existingUser.currentOnboardingStep || 1,
          },
        },
      );
      isNewUser =
        !existingUser.training_profile ||
        Object.keys(existingUser.training_profile).length === 0;
    } else {
      // Create a new user entry with empty training_profile
      const dateAdded = new Date().toISOString();
      await userCollection.insertOne({
        email: userInfo?.email,
        family_name: userInfo?.family_name,
        given_name: userInfo?.given_name,
        user_id: userInfo?.user_id,
        picture: userInfo?.picture,
        training_profile: {},
        dateAdded,
        currentOnboardingStep: 1,
      });
      isNewUser = true;
    }

    // Fetch the updated or newly inserted user data
    const updatedUser = await userCollection.findOne({ user_id });

    if (!updatedUser) {
      console.error("Failed to fetch updated user data");
      return new NextResponse(
        JSON.stringify({ error: "Failed to fetch updated user data" }),
        { status: 500 },
      );
    }

    // Check for onboarding training plan
    const onboardingPlan = await trainingPlanCollection.findOne({
      user_id,
      isOnboarding: true,
    });

    // Determine if the user is new based on the training_profile and onboarding plan
    isNewUser =
      (!updatedUser.training_profile ||
        Object.keys(updatedUser.training_profile).length === 0) &&
      !onboardingPlan;

    return new NextResponse(
      JSON.stringify({
        ...updatedUser,
        isNewUser,
        currentOnboardingStep: updatedUser.currentOnboardingStep || 1,
      }),
      {
        status: 200,
      },
    );
  } catch (error) {
    console.error("Error saving user data:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to save user data" }),
      { status: 500 },
    );
  }
}
