import { NextRequest, NextResponse } from "next/server";
import { Mongo<PERSON>lient, ServerApiVersion } from "mongodb";

// Placeholder: Replace with your actual Firebase Admin SDK initialization and user retrieval logic
async function getUserIdFromRequest(
  request: NextRequest,
): Promise<string | null> {
  // In a real app, you would likely:
  // 1. Use the 'state' parameter to link this callback to an authenticated user session.
  //    The 'state' would have been generated before redirecting to Cronofy and stored
  //    with the user's ID.
  // 2. Or, if your session management allows, retrieve the user ID from a secure session cookie.
  // For now, this is a placeholder.
  // const session = await getIronSession(request, sessionOptions); // Example with iron-session
  // if (!session.userId) return null;
  // return session.userId;

  // THIS IS A TEMPORARY PLACEHOLDER - DO NOT USE IN PRODUCTION WITHOUT A REAL USER ID MECHANISM
  console.warn(
    "TODO: Implement proper user identification in Cronofy callback.",
  );
  // As a temporary measure for testing, you might hardcode a user ID or expect it in a specific non-standard way.
  // For example, if you stored it in a cookie accessible here (less ideal for OAuth state):
  // const tempUserId = request.cookies.get('temp_user_id_for_cronofy_oauth')?.value;
  // if (tempUserId) return tempUserId;

  // For the purpose of this example, let's assume you'll add a way to get the user ID.
  // We can't directly get it from an Authorization header here as this is a server-to-server call from Cronofy.
  return "temp-firebase-user-id"; // REPLACE THIS
}

async function getMongoClient() {
  const uri = process.env.MONGODB_URI;
  if (!uri) {
    throw new Error("MONGODB_URI environment variable is not set.");
  }
  // Create a MongoClient with a MongoClientOptions object to set the Stable API version
  const client = new MongoClient(uri, {
    serverApi: {
      version: ServerApiVersion.v1,
      strict: true,
      deprecationErrors: true,
    },
  });
  await client.connect();
  return client;
}

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const code = searchParams.get("code");
  const userId = searchParams.get("userId"); // Important for CSRF and user linking

  // TODO: Verify 'returnedState' against a stored state to prevent CSRF
  // and retrieve the associated user ID.

  // const userId = await getUserIdFromRequest(request); // Placeholder for user identification

  if (!userId) {
    console.error("User ID could not be determined in Cronofy callback.");
    return NextResponse.json(
      { error: "User authentication error or state mismatch." },
      { status: 401 },
    );
  }

  if (!code) {
    return NextResponse.json(
      { error: "Authorization code not found." },
      { status: 400 },
    );
  }

  const clientId = process.env.CRONOFY_CLIENT_ID;
  const clientSecret = process.env.CRONOFY_CLIENT_SECRET;
  const redirectUri = process.env.NODE_ENV === "development" ? process.env.CRONOFY_REDIRECT_URI_DEV : process.env.CRONOFY_REDIRECT_URI_PROD;
  const sdkIdentifier = process.env.CRONOFY_SDK_IDENTIFIER;

  if (!clientId || !clientSecret || !redirectUri) {
    console.error(
      "Cronofy client credentials or redirect URI are not configured.",
    );
    return NextResponse.json(
      { error: "Server configuration error." },
      { status: 500 },
    );
  }

  let tokenUrl = "https://api.cronofy.com/oauth/token"; // Default for US

  // Adjust token URL for other data centers if necessary, based on sdkIdentifier
  // This logic should mirror your API host logic (api.cronofy.com, api-de.cronofy.com, etc.)
  if (sdkIdentifier && sdkIdentifier !== "us") {
    tokenUrl = `https://api-${sdkIdentifier}.cronofy.com/oauth/token`;
  }

  const params = new URLSearchParams();
  params.append("client_id", clientId);
  params.append("client_secret", clientSecret);
  params.append("grant_type", "authorization_code");
  params.append("code", code);
  params.append("redirect_uri", redirectUri);

  let mongoClient;

  try {
    const cronofyResponse = await fetch(tokenUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: params,
    });
    console.log('----cronofyResponse---',cronofyResponse);

    const tokenData = await cronofyResponse.json();

    if (!cronofyResponse.ok) {
      console.error("Cronofy token exchange error:", tokenData);
      return NextResponse.json(
        {
          error: "Failed to exchange authorization code for token.",
          details: tokenData,
        },
        { status: cronofyResponse.status },
      );
    }

    // Store tokens in MongoDB
    mongoClient = await getMongoClient();
    const db = mongoClient.db(); // Or your specific database name
    const tokensCollection = db.collection("cronofy_tokens");

    const now = new Date();
    const expiresAt = new Date(now.getTime() + tokenData.expires_in * 1000);

    const tokenDocument = {
      userId: userId, // Firebase User UID
      cronofyAccountId: tokenData.account_id,
      cronofySub: tokenData.sub, // Cronofy's unique user identifier
      accessToken: tokenData.access_token,
      refreshToken: tokenData.refresh_token,
      tokenType: tokenData.token_type,
      expiresAt: expiresAt,
      scope: tokenData.scope,
      lastUpdated: now,
    };

    await tokensCollection.updateOne(
      { userId: userId, cronofyAccountId: tokenData.account_id },
      { $set: tokenDocument },
      { upsert: true },
    );

    console.log("Cronofy tokens stored successfully for user:", userId);

    // Clear temporary user ID cookie if used (example placeholder)
    // const response = NextResponse.redirect(...);
    // response.cookies.delete('temp_user_id_for_cronofy_oauth');
    // return response;

    const successRedirectUrl = new URL("/", request.nextUrl.origin);
    successRedirectUrl.searchParams.set("cronofy_connected", "true");
    return NextResponse.redirect(successRedirectUrl.toString());
  } catch (error) {
    console.error("Error during token exchange or DB operation:", error);
    return NextResponse.json(
      { error: "Internal server error during token processing." },
      { status: 500 },
    );
  } finally {
    if (mongoClient) {
      await mongoClient.close();
    }
  }
}
