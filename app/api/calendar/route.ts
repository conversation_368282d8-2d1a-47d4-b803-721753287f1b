import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/app/lib/mongodb";

interface Task {
  id: string;
  text: string;
}

interface CalendarTask {
  user_id: string;
  tasks: {
    [date: string]: {
      AM: Task[];
      PM: Task[];
    };
  };
}
interface Notification {
  user_id: string;
  user_notifications: {
    [date: string]: [
      {
        title: string;
        description: string;
        dateAdded: Date
      },
    ];
  };
  unread_count: number
}

export async function GET(req: NextRequest) {
  console.log("GET request received!");
  try {
    const userId = req.nextUrl.searchParams.get("user_id");
    console.log("User ID:", userId);
    if (!userId) {
      return new NextResponse(JSON.stringify({ error: "Missing user_id" }), {
        status: 400,
      });
    }
    const client = await clientPromise;
    const db = client.db("AthleaUserData");

    const collection = db.collection<CalendarTask>("calendarTasks");
    const calendarData = await collection.findOne({ user_id: userId });

    return new NextResponse(
      JSON.stringify({ tasks: calendarData?.tasks || {} }),
      { status: 200 },
    );
  } catch (error) {
    console.error("Error fetching calendar tasks:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to fetch calendar tasks" }),
      { status: 500 },
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const userId = req.nextUrl.searchParams.get("user_id");
    console.log("User ID:", userId);
    if (!userId) {
      return new NextResponse(JSON.stringify({ error: "Missing user_id" }), {
        status: 400,
      });
    }
    const client = await clientPromise;
    const db = client.db("AthleaUserData");

    const { tasks, title, description } = await req.json();

    if (!tasks) {
      console.log("Missing tasks");
      return new NextResponse(JSON.stringify({ error: "Missing tasks" }), {
        status: 400,
      });
    }

    const collection = db.collection<CalendarTask>("calendarTasks");
    const notificatiuonCollection =
      db.collection<Notification>("notifications");
    if (Array.isArray(tasks)) {
      // Handle adding new tasks
      for (const task of tasks) {
        await collection.updateOne(
          { user_id: userId },
          {
            $push: {
              [`tasks.${task.date}.tasks`]: {
                ...task,
                id: task.id,
                text: task.text,
                day: task.day,
              },
              // [`tasks.${task.date}.${task.slot}`]: {
              //   id: task.id,
              //   text: task.text,
              // },
            },
          },
          { upsert: true },
        );
      }
    } else {
      // Handle updating entire tasks object
      await collection.updateOne(
        { user_id: userId },
        { $set: { tasks } },
        { upsert: true },
      );
    }
    const founduserNotification = await notificatiuonCollection.findOne({
      user_id: userId,
    });
    const todaysNotifications =
      founduserNotification?.user_notifications[
      new Date().toLocaleDateString()
      ];
    if (founduserNotification && todaysNotifications) {
      await notificatiuonCollection.updateOne(
        { user_id: userId },
        {
          $push: {
            [`user_notifications.${new Date().toLocaleDateString()}`]: {
              title: title || "Task notification updated in current date",
              description:
                description || "",
              dateAdded: new Date()
            },
          },
          $set: { unread_count: founduserNotification.unread_count ? founduserNotification.unread_count + 1 : 1 }
        },
        { upsert: true },
      );
    } else if (founduserNotification && !todaysNotifications) {
      await notificatiuonCollection.updateOne(
        { user_id: userId },
        {
          $set: {
            user_notifications: {
              ...founduserNotification.user_notifications,
              [new Date().toLocaleDateString()]: [
                {
                  title: title || "Task notification updated",
                  description:
                    description || "",
                  dateAdded: new Date()
                },
              ],
            },
            unread_count: founduserNotification.unread_count ? founduserNotification.unread_count + 1 : 1
          },
        },
        { upsert: true },
      );
    } else {
      await notificatiuonCollection.insertOne({
        user_id: userId,
        user_notifications: {
          [new Date().toLocaleDateString()]: [
            {
              title: title || "New task notification created",
              description:
                description || "",
              dateAdded: new Date()
            },
          ],
        },
        unread_count: 1
      });
    }
    return new NextResponse(JSON.stringify({ success: true }), { status: 200 });
  } catch (error) {
    console.error("Error saving calendar tasks:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to save calendar tasks" }),
      { status: 500 },
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const userId = req.nextUrl.searchParams.get("user_id");
    const taskId = req.nextUrl.searchParams.get("taskId");

    if (!userId || !taskId) {
      return new NextResponse(
        JSON.stringify({ error: "Missing user_id or taskId" }),
        { status: 400 },
      );
    }

    const client = await clientPromise;
    const db = client.db("AthleaUserData");
    const collection = db.collection<CalendarTask>("calendarTasks");

    // Fetch the current tasks to dynamically build the $pull object
    const calendarData = await collection.findOne({ user_id: userId });
    if (!calendarData) {
      return new NextResponse(JSON.stringify({ error: "No tasks found" }), {
        status: 404,
      });
    }

    const pullObject: { [key: string]: { id: string } } = {};
    for (const date in calendarData.tasks) {
      if (calendarData.tasks.hasOwnProperty(date)) {
        pullObject[`tasks.${date}.AM`] = { id: taskId };
        pullObject[`tasks.${date}.PM`] = { id: taskId };
      }
    }

    // Perform the update with dynamically built $pull object
    const result = await collection.updateOne(
      { user_id: userId },
      { $pull: pullObject },
    );

    if (result.modifiedCount === 0) {
      return new NextResponse(JSON.stringify({ error: "Task not found" }), {
        status: 404,
      });
    }

    return new NextResponse(JSON.stringify({ success: true }), { status: 200 });
  } catch (error) {
    console.error("Error deleting task:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to delete task" }),
      { status: 500 },
    );
  }
}

export type { Notification };
