import { NextRequest, NextResponse } from "next/server";
// import crypto from 'crypto'; // Uncomment if/when implementing state parameter

export async function GET(request: NextRequest) {
  const clientId = process.env.CRONOFY_CLIENT_ID;
  const redirectUri = process.env.NODE_ENV === "development" ? process.env.CRONOFY_REDIRECT_URI_DEV : process.env.CRONOFY_REDIRECT_URI_PROD;
  const sdkIdentifier = process.env.CRONOFY_SDK_IDENTIFIER;

  const searchParams = request.nextUrl.searchParams;
  const provider = searchParams.get("provider"); // Get the provider from query params

  if (!clientId || !redirectUri) {
    console.error(
      "Cronofy Client ID or Redirect URI is not configured in environment variables.",
    );
    return NextResponse.json(
      { error: "Server configuration error." },
      { status: 500 },
    );
  }

  const scopes = ["read_write"];
  const scopeString = scopes.join(" ");

  let cronofySiteHost;

  // Determine Cronofy site host based on SDK identifier from documentation
  // https://docs.cronofy.com/developers/data-centers/
  switch (sdkIdentifier) {
    case "us":
      cronofySiteHost = "https://app.cronofy.com";
      break;
    case "au":
      cronofySiteHost = "https://app-au.cronofy.com";
      break;
    case "ca":
      cronofySiteHost = "https://app-ca.cronofy.com";
      break;
    case "de":
      cronofySiteHost = "https://app-de.cronofy.com";
      break;
    case "sg":
      cronofySiteHost = "https://app-sg.cronofy.com";
      break;
    case "uk":
      cronofySiteHost = "https://app-uk.cronofy.com";
      break;
    default:
      // Fallback to the general URL if sdkIdentifier is not recognized or not set
      // This also covers the US case as per documentation if sdkIdentifier is 'us' or undefined and app is US based.
      console.warn(
        `Unknown or undefined CRONOFY_SDK_IDENTIFIER: '${sdkIdentifier}'. Defaulting to https://app.cronofy.com. Ensure this is correct for your app region.`,
      );
      cronofySiteHost = "https://app.cronofy.com";
  }

  // The 'state' parameter is recommended for preventing CSRF attacks.
  // For simplicity in this initial step, we'll omit it, but it should be added for production.
  // const state = crypto.randomBytes(16).toString('hex');
  // You would typically store this state (e.g., in a short-lived cookie or session)
  // and verify it on the callback.

  const authUrl = new URL(`${cronofySiteHost}/oauth/authorize`);
  authUrl.searchParams.append("response_type", "code");
  authUrl.searchParams.append("client_id", clientId);
  authUrl.searchParams.append("redirect_uri", redirectUri);
  authUrl.searchParams.append("scope", scopeString);
  // Add state parameter for CSRF protection in production
  // const state = crypto.randomBytes(16).toString('hex');
  // authUrl.searchParams.append('state', state);
  // Store state in session/cookie to verify in callback

  if (provider) {
    authUrl.searchParams.append("provider_name", provider);
  }

  return NextResponse.redirect(authUrl.toString());
}
