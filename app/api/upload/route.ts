import { NextRequest, NextResponse } from "next/server";
import { Collection, MongoClient, ObjectId } from "mongodb";
import { OpenAIEmbeddings } from "@langchain/openai";
import { PDFLoader } from "@langchain/community/document_loaders/fs/pdf";
import { RecursiveCharacterTextSplitter } from "langchain/text_splitter";
import axios from "axios";
import { parse } from "csv-parse";
import { Readable } from "stream";
import { pipeline } from "stream/promises";
import * as XLSX from "xlsx";
import { AzureOpenAI } from "openai";

const MONGODB_URI = process.env.MONGODB_URI;
const AZURE_OPENAI_KEY = process.env.AZURE_OPENAI_KEY;
const AZURE_OPENAI_ENDPOINT = process.env.AZURE_OPENAI_ENDPOINT;
const AZURE_DEPLOYMENT_NAME = process.env.AZURE_DEPLOYMENT_NAME;
const EMBEDDING_API_KEY = process.env.EMBEDDING_API_KEY;
const EMBEDDING_ENDPOINT = process.env.EMBEDDING_ENDPOINT;

// Initialize Azure OpenAI client for Chat
const azureClient = new AzureOpenAI({
  apiKey: AZURE_OPENAI_KEY as string,
  endpoint: AZURE_OPENAI_ENDPOINT as string,
  apiVersion: "2024-02-15-preview",
  deployment: AZURE_DEPLOYMENT_NAME,
});

// Initialize Azure OpenAI Embeddings Configuration
const initializeEmbeddings = () => {
  return new OpenAIEmbeddings({
    modelName: "text-embedding-ada-002",
    openAIApiKey: EMBEDDING_API_KEY,
    batchSize: 1,
    timeout: 60000,
  });
};

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_FILE_TYPES = [
  "text/plain",
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "text/csv",
  "application/vnd.ms-excel",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "image/jpeg",
  "image/png",
  "image/gif",
  "image/webp",
];

interface Documents {
  _id: ObjectId;
  userId: string;
  fileName: string;
  chunkIndex?: number;
  chunkContent?: string;
  vector: number[];
  createdAt: Date;
  content?: any;
  imageBuffer?: Buffer;
}

if (!MONGODB_URI) {
  throw new Error("MONGODB_URI is not defined in the environment variables.");
}

let client: MongoClient | null = null;

async function getMongoClient() {
  if (!MONGODB_URI) {
    throw new Error("MONGODB_URI is not defined in the environment variables.");
  }

  if (!client) {
    client = new MongoClient(MONGODB_URI as string);
    console.log("Connecting to MongoDB...");
    await client.connect();
    console.log("Connected to MongoDB.");
  }
  return client;
}

async function checkAndCreateIndex(client: MongoClient) {
  console.log("Checking if vector index exists...");
  const db = client.db("AthleaUserData");
  const collection = db.collection("documents");

  const indexExists = await collection.indexExists("vector_index");

  if (!indexExists) {
    console.log("Vector index does not exist. Creating now...");
    try {
      await collection.createSearchIndex({
        name: "vector_index",
        definition: {
          fields: [
            {
              type: "vector",
              path: "vector",
              numDimensions: 1536,
              similarity: "cosine",
            },
            {
              type: "filter",
              path: "userId",
            },
            {
              type: "text",
              path: "fileName",
            },
          ],
        },
      });
      console.log("Vector index created successfully.");
    } catch (error) {
      console.error("Error creating vector index:", error);
    }
  } else {
    console.log("Vector index already exists.");
  }
}

async function analyzeImageWithAzure(imageBuffer: Buffer): Promise<string> {
  try {
    console.log(
      `Analyzing image with Azure OpenAI, buffer size: ${imageBuffer.length} bytes`,
    );

    const base64Image = imageBuffer.toString("base64");
    const response = await azureClient.chat.completions.create({
      model: AZURE_DEPLOYMENT_NAME as string,
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: "Describe this image in detail." },
            {
              type: "image_url",
              image_url: { url: `data:image/jpeg;base64,${base64Image}` },
            },
          ],
        },
      ],
      max_tokens: 300,
    });

    const analysisResult = response.choices[0].message.content;
    if (analysisResult === null) {
      throw new Error("Azure OpenAI analysis returned null result");
    }
    console.log("Azure OpenAI Image analysis result:", analysisResult);
    return analysisResult;
  } catch (error) {
    console.error("Error analyzing image with Azure OpenAI:", error);
    throw error;
  }
}

async function processCsvStream(
  stream: Readable,
  userId: string,
  fileName: string,
  embeddings: OpenAIEmbeddings,
  collection: any,
) {
  const batchSize = 100;
  let batch: any[] = [];
  let rowCount = 0;

  const parser = parse({
    delimiter: ",",
    columns: true,
  });

  await pipeline(
    stream,
    parser,
    async function* (source) {
      for await (const record of source) {
        rowCount++;
        const content = JSON.stringify(record);
        const vector = await embeddings.embedQuery(content);

        batch.push({
          _id: new ObjectId(),
          userId,
          fileName,
          chunkIndex: rowCount,
          chunkContent: content,
          vector,
          createdAt: new Date(),
        });

        if (batch.length >= batchSize) {
          yield batch;
          batch = [];
        }
      }

      if (batch.length > 0) {
        yield batch;
      }
    },
    async function (source) {
      for await (const batch of source) {
        await collection.insertMany(batch);
        console.log(`Inserted batch of ${batch.length} documents`);
      }
    },
  );

  console.log(`Processed ${rowCount} rows from CSV file`);
}

async function processXlsxStream(
  buffer: ArrayBuffer,
  userId: string,
  fileName: string,
  embeddings: OpenAIEmbeddings,
  collection: any,
) {
  const batchSize = 100;
  let batch: any[] = [];
  let rowCount = 0;

  const workbook = XLSX.read(new Uint8Array(buffer), { type: "array" });
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  const data = XLSX.utils.sheet_to_json(worksheet);

  for (const record of data) {
    rowCount++;
    const content = JSON.stringify(record);
    console.log("before embading");

    const vector = await embeddings.embedQuery(content);
    console.log("after embading");

    batch.push({
      _id: new ObjectId(),
      userId,
      fileName,
      chunkIndex: rowCount,
      chunkContent: content,
      vector,
      createdAt: new Date(),
    });

    if (batch.length >= batchSize) {
      await collection.insertMany(batch);
      console.log(`Inserted batch of ${batch.length} documents`);
      batch = [];
    }
  }

  if (batch.length > 0) {
    await collection.insertMany(batch);
    console.log(`Inserted final batch of ${batch.length} documents`);
  }

  console.log(`Processed ${rowCount} rows from XLSX file`);
}

async function processDocumentStream(
  buffer: ArrayBuffer,
  userId: string,
  fileName: string,
  fileType: string,
  embeddings: OpenAIEmbeddings,
  collection: any,
) {
  const batchSize = 100;
  let batch: any[] = [];
  let chunkIndex = 0;

  let docs;
  if (fileType === "application/pdf") {
    const loader = new PDFLoader(new Blob([buffer]));
    docs = await loader.load();
  } else {
    const text = new TextDecoder().decode(buffer);
    docs = [{ pageContent: text, metadata: { source: fileName } }];
  }

  const textSplitter = new RecursiveCharacterTextSplitter({
    chunkSize: 1000,
    chunkOverlap: 200,
  });
  const chunks = await textSplitter.splitDocuments(docs);

  for (const chunk of chunks) {
    chunkIndex++;
    const vector = await embeddings.embedQuery(chunk.pageContent);

    batch.push({
      _id: new ObjectId(),
      userId,
      fileName,
      chunkIndex,
      chunkContent: chunk.pageContent,
      vector,
      createdAt: new Date(),
    });

    if (batch.length >= batchSize) {
      await collection.insertMany(batch);
      console.log(`Inserted batch of ${batch.length} documents`);
      batch = [];
    }
  }

  if (batch.length > 0) {
    await collection.insertMany(batch);
    console.log(`Inserted final batch of ${batch.length} documents`);
  }

  console.log(`Processed ${chunkIndex} chunks from ${fileType} file`);
}

export async function POST(request: NextRequest) {
  try {
    console.log("Received POST request");

    const formData = await request.formData();
    const file = formData.get("file") as File | null;
    const userId = formData.get("user_id") as string | null;

    console.log("FormData extracted:", {
      fileName: file?.name,
      userId,
      fileType: file?.type,
      fileSize: file?.size,
    });

    if (!file || !userId) {
      console.error("Missing file or user ID", { file, userId });
      return NextResponse.json(
        { error: "Missing file or user ID" },
        { status: 400 },
      );
    }

    if (file.size > MAX_FILE_SIZE) {
      console.error("File size exceeds the maximum limit", {
        fileName: file.name,
        fileSize: file.size,
      });
      return NextResponse.json(
        { error: "File size exceeds the maximum limit" },
        { status: 400 },
      );
    }

    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      console.error("File type not allowed", {
        fileName: file.name,
        fileType: file.type,
      });
      return NextResponse.json(
        { error: "File type not allowed" },
        { status: 400 },
      );
    }

    const id = new ObjectId();
    console.log("Generated document ID:", id);

    const embeddings = initializeEmbeddings();
    console.log(
      "Initialized Azure OpenAI Embeddings with text-embedding-ada-002 model",
    );

    const client = await getMongoClient();
    await checkAndCreateIndex(client);
    const db = client.db("AthleaUserData");
    const collection = db.collection<Documents>("documents");

    const existingFile = await collection.findOne({
      fileName: file.name,
      userId,
    });

    if (existingFile) {
      console.log("existing file", existingFile);
      return NextResponse.json(
        { message: `${file.name} already exists` },
        { status: 200 },
      );
    }

    if (file.type.startsWith("image/")) {
      console.log("Processing image file:", file.name);
      const buffer = await file.arrayBuffer();
      const imageBuffer = Buffer.from(buffer);

      console.log(`Image size: ${imageBuffer.length} bytes`);
      console.log(`Image type: ${file.type}`);

      if (imageBuffer.length > 4 * 1024 * 1024) {
        return NextResponse.json(
          { error: "Image size exceeds 4MB limit" },
          { status: 400 },
        );
      }

      try {
        console.log("Analyzing image with Azure OpenAI...");
        const imageDescription = await analyzeImageWithAzure(imageBuffer);
        console.log("Azure OpenAI Image analysis result:", imageDescription);

        const content = `Image: ${file.name}. ${imageDescription}`;
        console.log("Generated content:", content);

        console.log("Generating embedding...");
        const vector = await embeddings.embedQuery(content);
        console.log(
          "Embedding generated. First 10 values:",
          vector.slice(0, 10),
        );

        const document: Documents = {
          _id: id,
          userId: userId,
          fileName: file.name,
          content: content,
          vector: vector,
          createdAt: new Date(),
          imageBuffer: imageBuffer,
        };

        console.log("Inserting document into MongoDB...");
        await collection.insertOne(document);
        console.log(
          "Image document uploaded:",
          JSON.stringify({ ...document, imageBuffer: "Buffer data" }, null, 2),
        );

        return NextResponse.json(
          {
            message: "Image uploaded and processed successfully",
            content: content,
          },
          { status: 200 },
        );
      } catch (analysisError) {
        console.error("Error in Azure OpenAI image analysis:", analysisError);
        return NextResponse.json(
          {
            error: `Error analyzing image: ${(analysisError as Error).message || "Unknown error"}`,
          },
          { status: 500 },
        );
      }
    } else if (
      file.type === "text/csv" ||
      file.type.includes("spreadsheetml") ||
      file.type ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    ) {
      console.log("Processing CSV or Spreadsheet file:", file.name);
      const buffer = await file.arrayBuffer();

      if (file.type === "text/csv") {
        const stream = Readable.from(Buffer.from(buffer));
        await processCsvStream(
          stream,
          userId,
          file.name,
          embeddings,
          collection,
        );
      } else {
        await processXlsxStream(
          buffer,
          userId,
          file.name,
          embeddings,
          collection,
        );
      }

      console.log("Spreadsheet file processed and uploaded successfully.");
      return NextResponse.json(
        { message: "Spreadsheet file processed and uploaded successfully" },
        { status: 200 },
      );
    } else if (file.type === "application/pdf" || file.type === "text/plain") {
      console.log(`Processing ${file.type} file:`, file.name);
      const buffer = await file.arrayBuffer();

      await processDocumentStream(
        buffer,
        userId,
        file.name,
        file.type,
        embeddings,
        collection,
      );

      console.log(`${file.type} file processed and uploaded successfully.`);
      return NextResponse.json(
        { message: `${file.type} file processed and uploaded successfully` },
        { status: 200 },
      );
    } else {
      // Handle other file types if necessary
      return NextResponse.json(
        { error: "Unsupported file type" },
        { status: 400 },
      );
    }
  } catch (error) {
    console.error("Error processing file:", error);
    return NextResponse.json(
      { error: "Error processing file" },
      { status: 500 },
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const docName = req.nextUrl.searchParams.get("docName");
    const userId = req.nextUrl.searchParams.get("userId");

    console.log("DELETE request received for doc:", docName);

    if (!docName) {
      return new NextResponse(
        JSON.stringify({ error: "Doc name is required" }),
        {
          status: 400,
        },
      );
    }

    if (!userId) {
      return new NextResponse(
        JSON.stringify({ error: "User id is required" }),
        {
          status: 400,
        },
      );
    }

    const client = await getMongoClient();
    const db = client.db("AthleaUserData");
    const collection = db.collection<Documents>("documents");
    const result = await collection.deleteMany({ fileName: docName, userId });
    if (result.deletedCount === 0) {
      return new NextResponse(JSON.stringify({ error: "Document not found" }), {
        status: 404,
      });
    }
    return new NextResponse(JSON.stringify({ message: "Document deleted" }), {
      status: 200,
    });
  } catch (error) {
    console.error("Error deleting document:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to delete Document" }),
      { status: 500 },
    );
  }
}
