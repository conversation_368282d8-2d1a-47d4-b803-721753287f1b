"use client";

import { MarkdownComponents } from "@/components/Chat/MarkdownComponents";
import {
  AIMessage,
  // PlannerMessage, // These are handled by AIMessage based on domain
  // ReasoningMessage,
  UserMessage,
} from "@/components/Chat/MessageTypes"; // Corrected path
import {
  Message,
  StreamEventData,
  ToolCall,
  isPlannerOrReasoningDomain,
  MessageDomain,
} from "@/app/types/chat";
import { getFormattedTime } from "@/app/utils/formatUtils";
import { fetchKeywords } from "@/app/utils/keywordUtils";
import SystemMessage from "@/components/Chat/Messages/SystemMessage";
import SidebarWrapper from "@/components/Chat/SidebarWrapper";
import UserInput from "@/components/Chat/UserInput";
import PlanGenerationSidebar from "@/components/PlanGenerationSidebar";
import { Card, CardContent } from "@/components/ui/cards";
import { useKeyword } from "@/context/KeywordContext";
import { useAppDispatch, useAppSelector } from "@/store/jobHook";
import { updatePlanNameThunk } from "@/store/slices/chatSlice";
import {
  setNeedInitialMessages,
  setSidebarItemName,
} from "@/store/slices/jobSlice";
import { RootState } from "@/store/store";
import { debounce } from "lodash";
import dynamic from "next/dynamic";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import OnboardingSidebar from "@/components/Chat/OnboardingSidebar";
import { SidebarStateData } from "@/app/api/onboarding/state";
import SuggestionBubbles from "@/components/Chat/SuggestionBubbles";
import { useQuery } from "@tanstack/react-query";
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from "@/components/ui/resizable";
import { parseToolOutputToSteps, ParsedToolSteps } from "@/utils/toolStepUtils";

// Keep DEBUG flag for easy toggling of these specific logs
const DEBUG_TOOL_STEP_PARSING = true;
const debugToolLog = (...args: any[]) => {
  if (DEBUG_TOOL_STEP_PARSING) {
    console.log("[ToolStepParsingDebug]", ...args);
  }
};

const LocationMap = dynamic(() => import("@/components/Chat/Map/LocationMap"), {
  ssr: false,
  loading: () => <p>Loading map...</p>,
});

export default function LangGraphChatPage() {
  const params = useParams<{ id: string }>();
  const threadIdFromUrl = params?.id;
  const router = useRouter();
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();

  const { initialMessage, needInitialMessages, selectedCoach } = useAppSelector(
    (state: RootState) => state.job,
  );

  const { setKeyWordList } = useKeyword();

  const planTitle = useAppSelector((state) => state.job.planTitle);
  const lastPlanNameUpdateTime = useAppSelector(
    (state) => state.chat.lastPlanNameUpdateTime,
  );
  const isRightDrawerOpen = useAppSelector(
    (state) => state.calendar.isRightDrawerOpen,
  );
  const userData = useSelector((state: RootState) => state.user.userData);
  const userId = userData?.user_id;

  const [messages, setMessages] = useState<Message[]>([]);
  const [toolCalls, setToolCalls] = useState<ToolCall[]>([]);
  const [sourceDocuments, setSourceDocuments] = useState([]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [waitingForInput, setWaitingForInput] = useState(false);
  const [messageDone, setMessageDone] = useState(false);
  const [inputPrompt, setInputPrompt] = useState("");
  const [collapsedMessages, setCollapsedMessages] = useState<
    Record<string, boolean>
  >({});
  const wasDisabledRef = useRef<boolean>(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const toolCallsEndRef = useRef<HTMLDivElement>(null);
  const currentAgentMessageIdsRef = useRef<Record<string, string>>({});
  const hasReceivedFirstTokenThisTurn = useRef(false);
  const lastStreamingAgentRef = useRef<string | null>(null);
  const submissionInProgressRef = useRef(false);
  const currentTurnPlannerMsgIdRef = useRef<string | null>(null);
  const initialCallAttemptedRef = useRef(false);

  // State for sidebar data - initialize with default structure
  const [sidebarData, setSidebarData] = useState<SidebarStateData | null>(null);
  // ADD: State to track selected sport from main state
  const [selectedSportState, setSelectedSportState] = useState<string | null>(
    null,
  );

  // State for persisting panel sizes
  const [panelSizes, setPanelSizes] = useState(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("chatPanelSizes");
      return saved ? JSON.parse(saved) : [75, 25];
    }
    return [75, 25];
  });

  // Save panel sizes to localStorage when they change
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("chatPanelSizes", JSON.stringify(panelSizes));
    }
  }, [panelSizes]);

  console.log("---isRightDrawerOpen---", isRightDrawerOpen);

  // useEffect(() => {
  //   alert(`---runn ${isRightDrawerOpen}---`,)
  // }, [isRightDrawerOpen])

  // Use a ref for pending suggestions to avoid state timing issues
  const pendingSuggestionsRef = useRef<
    { label: string; value: string }[] | undefined
  >(undefined);
  const eventSourceRef = useRef<EventSource | null>(null); // Use ref for eventSource

  const fetchMessage = async () => {
    if (threadIdFromUrl) {
      try {
        const response = await fetch(`/api/messages/${threadIdFromUrl}`);

        if (response.ok) {
          const data = await response.json();
          // Return the whole data object
          return data;
        } else {
          console.error(`Failed to fetch messages. Status: ${response.status}`);
          return undefined;
        }
      } catch (error) {
        console.error("Error fetching messages:", error);
        return undefined;
      }
    } else {
      console.log("No selectedJobId, skipping fetch");
      return undefined;
    }
  };

  const { data: q } = useQuery({
    queryKey: ["messages", threadIdFromUrl],
    queryFn: fetchMessage,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    retry: false,
  });

  const toggleCollapse = (messageId: string) => {
    setCollapsedMessages((prev) => ({
      ...prev,
      [messageId]: !prev[messageId],
    }));
  };

  const toggleToolCallExpanded = (id: string) => {
    setToolCalls((prev) =>
      prev.map((tool) =>
        tool.id === id ? { ...tool, expanded: !tool.expanded } : tool,
      ),
    );
  };

  useEffect(() => {
    if (q) {
      // q is now the full data object { messageList, artifacts, sourceDocuments }
      setMessages(q.messageList || []); // Fallback to empty array
      setToolCalls(q.artifacts || []); // Fallback for artifacts
      setSourceDocuments(q.sourceDocuments || []); // Fallback for sourceDocuments

      // Adjust logic to use q.messageList
      if (q.messageList && q.messageList.length >= 4 && threadIdFromUrl) {
        console.log("---runn chat name update---", q.messageList.length);
        const msg = q.messageList[3]?.content.slice(0, 150);
        dispatch(setSidebarItemName({ id: threadIdFromUrl, name: msg }));
      }
    }
  }, [q, threadIdFromUrl, dispatch]);

  useEffect(() => {
    if (
      messageDone ||
      messages.length > 0 ||
      toolCalls.length > 0 ||
      sourceDocuments.length > 0
    ) {
      console.log("---message save effect triggered---", {
        messageDone,
        messagesLength: messages.length,
        toolCallsLength: toolCalls.length,
        sourceDocumentsLength: sourceDocuments.length,
        threadIdFromUrl,
        initialMessage,
      });

      // Add a guard to prevent saving during initial load
      if (!threadIdFromUrl) {
        console.log("---message save effect skipped: no threadId---");
        return;
      }

      fetch(`/api/messages/${threadIdFromUrl}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          sourceDocuments,
          toolCalls,
          messageList: messages,
          initialMessage:
            initialMessage && initialMessage !== "Hi" && messages.length === 0
              ? initialMessage
              : undefined,
        }),
      }).then((r) => {
        setMessageDone(false);
        console.log("---message save response---", r.status);
      });
    }
  }, [
    threadIdFromUrl,
    messages,
    messageDone,
    toolCalls,
    sourceDocuments,
    initialMessage,
  ]);

  const processBatchKeywords = useCallback(
    (keywords: string[]) => {
      if (keywords && keywords.length > 0) {
        console.log("Processing batch keywords:", keywords);
        setKeyWordList(keywords);
      }
    },
    [setKeyWordList],
  );

  const componentMarkdownComponents = {
    ...MarkdownComponents,
  };

  const processEventSource = useCallback(
    async (eventSourceUrl: string) => {
      console.log(
        "CLIENT: [processEventSource] Starting with URL:",
        eventSourceUrl?.substring(0, 100) + "...",
      );

      // Close any existing EventSource before creating a new one
      if (eventSourceRef.current) {
        console.log(
          "CLIENT: [processEventSource] Closing existing EventSource connection",
        );
        try {
          eventSourceRef.current.close();
        } catch (e) {
          console.warn("CLIENT: Error closing existing EventSource:", e);
        }
        eventSourceRef.current = null;
      }

      // Small delay to ensure the previous connection is fully closed
      await new Promise((resolve) => setTimeout(resolve, 100));

      console.log(
        "CLIENT: [processEventSource] Creating new EventSource with URL:",
        eventSourceUrl,
      );

      return new Promise<void>((resolve, reject) => {
        const newEventSource = new EventSource(eventSourceUrl);
        eventSourceRef.current = newEventSource; // Store in ref

        let connectionEstablished = false;
        let messageReceived = false;
        let streamCompleted = false; // Track if stream has completed
        let isCleanedUp = false; // Prevent multiple cleanup calls

        // Cleanup function to properly close and cleanup EventSource
        const cleanupEventSource = () => {
          if (isCleanedUp) return; // Prevent multiple cleanups
          isCleanedUp = true;

          console.log("CLIENT: [processEventSource] Starting cleanup...");

          try {
            // Remove all event listeners to prevent further events
            newEventSource.removeEventListener("agent_start", handleSSEEvent);
            newEventSource.removeEventListener("token", handleSSEEvent);
            newEventSource.removeEventListener("tool_result", handleSSEEvent);
            newEventSource.removeEventListener(
              "sidebar_update",
              handleSSEEvent,
            );
            newEventSource.removeEventListener("need_input", handleSSEEvent);
            newEventSource.removeEventListener("complete", handleSSEEvent);
            newEventSource.removeEventListener("done", handleSSEEvent);
            newEventSource.removeEventListener("error", handleSSEEvent);

            // Clear default handlers
            newEventSource.onmessage = null;
            newEventSource.onerror = null;
            newEventSource.onopen = null;

            console.log("CLIENT: [processEventSource] Event listeners removed");
          } catch (e) {
            console.warn("CLIENT: Error removing event listeners:", e);
          }

          try {
            // Close the EventSource
            if (newEventSource.readyState !== EventSource.CLOSED) {
              newEventSource.close();
              console.log("CLIENT: [processEventSource] EventSource closed");
            }
          } catch (e) {
            console.warn("CLIENT: Error closing EventSource:", e);
          }

          // Clear the ref if this is the current EventSource
          if (eventSourceRef.current === newEventSource) {
            eventSourceRef.current = null;
            console.log("CLIENT: [processEventSource] EventSource ref cleared");
          }
        };

        // Add connection timeout
        const connectionTimeout = setTimeout(() => {
          if (!connectionEstablished) {
            console.error(
              "CLIENT: [processEventSource] Connection timeout after 10 seconds",
            );
            cleanupEventSource();
            reject(new Error("Connection timeout"));
          }
        }, 10000);

        newEventSource.onopen = () => {
          console.log(
            "CLIENT: [processEventSource] !!!!! SSE Connection OPENED (onopen event handler) !!!!!",
          );
          connectionEstablished = true; // Make sure this is being set
          console.log(
            "CLIENT: [processEventSource] connectionEstablished SET TO TRUE.",
          );
          clearTimeout(connectionTimeout);
          console.log(
            "CLIENT: [processEventSource] Cleared connectionTimeout.",
          );
          setIsStreaming(true);
          setWaitingForInput(false);
          currentAgentMessageIdsRef.current = {};
        };

        newEventSource.onerror = (error) => {
          // Skip if already cleaned up
          if (isCleanedUp) {
            console.log(
              "CLIENT: [processEventSource] Ignoring error after cleanup",
            );
            return;
          }

          // EventSource fires onerror when connection closes, even on normal completion
          // Check readyState to determine if this is a real error or just connection closure
          const isConnectionClosed =
            newEventSource.readyState === EventSource.CLOSED;
          const isConnecting =
            newEventSource.readyState === EventSource.CONNECTING;

          console.log("CLIENT: [processEventSource] SSE onerror triggered:", {
            readyState: newEventSource.readyState,
            readyStateText: isConnectionClosed
              ? "CLOSED"
              : isConnecting
                ? "CONNECTING"
                : "OPEN",
            streamCompleted,
            isLoading,
            isStreaming,
            isCleanedUp,
            url: newEventSource.url,
          });

          clearTimeout(connectionTimeout);

          // If stream already completed successfully, ignore the error
          if (streamCompleted) {
            console.log(
              "CLIENT: [processEventSource] Ignoring error after successful stream completion",
            );
            cleanupEventSource();
            resolve();
            return;
          }

          // If connection is closed and we're not actively loading/streaming,
          // this is likely a normal closure after completion
          if (isConnectionClosed && !isLoading && !isStreaming) {
            console.log(
              "CLIENT: [processEventSource] Connection closed normally, not retrying",
            );
            cleanupEventSource();
            resolve();
            return;
          }

          // Only treat as error if we're still expecting data
          if (isLoading || isStreaming) {
            setIsLoading(false);
            setIsStreaming(false);
            setWaitingForInput(false);

            cleanupEventSource();

            setMessages((prev) => {
              const lastMsg = prev[prev.length - 1];
              if (lastMsg?.id?.startsWith("err-")) return prev;
              const messagesWithoutIndicator = prev.filter(
                (msg) => msg.id !== "typing-indicator",
              );
              return [
                ...messagesWithoutIndicator,
                {
                  id: `err-connect-${Date.now()}`,
                  type: "system",
                  domain: "general",
                  content: "Connection error. Please try again.",
                  lastUpdated: Date.now(),
                },
              ];
            });

            currentAgentMessageIdsRef.current = {};
            lastStreamingAgentRef.current = null;
            reject(new Error("SSE connection error"));
          } else {
            // Not actively loading/streaming, just resolve
            console.log(
              "CLIENT: [processEventSource] Connection closed, not actively streaming",
            );
            cleanupEventSource();
            resolve();
          }
        };

        // Handler for all SSE events
        const handleSSEEvent = async (event: MessageEvent) => {
          // Skip processing if already cleaned up or stream completed
          if (isCleanedUp || streamCompleted) {
            console.log(
              "CLIENT: [SSE] Ignoring event after cleanup/completion:",
              event.type,
            );
            return;
          }

          // ADD THIS LOG:
          console.log(
            `CLIENT: [SSE Raw Event Monitor] Received event with type: "${event.type}", data: ${event.data ? event.data.substring(0, 100) + "..." : "NO DATA"}`,
          );

          // Check if event.data is undefined or empty
          if (!event.data) {
            console.warn("🔄 CLIENT: [SSE] Received event with no data:", {
              type: event.type,
              timestamp: new Date().toISOString(),
            });
            return; // Skip processing if no data
          }

          console.log("🔄 CLIENT: [SSE] Raw event received:", {
            type: event.type,
            data_length: event.data?.length || 0,
            data_preview:
              event.data?.substring(0, 200) +
              (event.data?.length > 200 ? "..." : ""),
            timestamp: new Date().toISOString(),
          });

          try {
            const eventData = JSON.parse(event.data) as StreamEventData;

            console.log("📊 CLIENT: [SSE] Parsed event:", {
              event_type: eventData.type,
              agent: eventData.agent || "none",
              has_content: !!eventData.content,
              content_length: eventData.content?.length || 0,
              timestamp: new Date().toISOString(),
            });

            switch (eventData.type) {
              case "agent_start":
                if (eventData.agent) {
                  const agentDomain = eventData.agent as MessageDomain;
                  console.log("🚀 CLIENT: [AGENT_START]", {
                    agent: agentDomain,
                    timestamp: new Date().toISOString(),
                    current_agents: Object.keys(
                      currentAgentMessageIdsRef.current,
                    ),
                  });

                  // Register agent start, await first token for message creation
                  currentAgentMessageIdsRef.current[agentDomain] = "";
                  console.log(
                    "✅ CLIENT: [AGENT_START] Agent registered, awaiting first token:",
                    {
                      agent: agentDomain,
                      all_agents: Object.keys(
                        currentAgentMessageIdsRef.current,
                      ),
                    },
                  );
                }
                break;

              case "token":
                if (eventData.agent && eventData.content) {
                  const agentDomain = eventData.agent as MessageDomain;
                  const tokenContent = eventData.content;

                  console.log("💬 CLIENT: [TOKEN]", {
                    agent: agentDomain,
                    content_length: tokenContent.length,
                    content_preview:
                      tokenContent.substring(0, 50) +
                      (tokenContent.length > 50 ? "..." : ""),
                    first_token_this_turn:
                      !hasReceivedFirstTokenThisTurn.current,
                    timestamp: new Date().toISOString(),
                  });

                  setMessages((prevMessages) => {
                    console.log(
                      "🔧 CLIENT: [TOKEN] Processing token, current message count:",
                      prevMessages.length,
                    );

                    let updatedMessages = [...prevMessages];
                    const messageIdFromRef =
                      currentAgentMessageIdsRef.current[agentDomain];
                    let existingMsgIndex = messageIdFromRef
                      ? updatedMessages.findIndex(
                          (msg) => msg.id === messageIdFromRef,
                        )
                      : -1;

                    console.log("🔍 CLIENT: [TOKEN] Message lookup:", {
                      agent: agentDomain,
                      message_id_from_ref: messageIdFromRef,
                      existing_index: existingMsgIndex,
                      has_first_token: hasReceivedFirstTokenThisTurn.current,
                    });

                    if (hasReceivedFirstTokenThisTurn.current === false) {
                      const typingIndicatorsBefore = updatedMessages.filter(
                        (msg) =>
                          msg.id === "typing-indicator" ||
                          (typeof msg.id === "string" &&
                            msg.id.startsWith("typing-indicator-")),
                      ).length;

                      updatedMessages = updatedMessages.filter(
                        (msg) =>
                          !(
                            typeof msg.id === "string" &&
                            msg.id.startsWith("typing-indicator-")
                          ) && msg.id !== "typing-indicator",
                      );

                      const typingIndicatorsAfter = updatedMessages.filter(
                        (msg) =>
                          msg.id === "typing-indicator" ||
                          (typeof msg.id === "string" &&
                            msg.id.startsWith("typing-indicator-")),
                      ).length;

                      console.log(
                        "🧹 CLIENT: [TOKEN] Cleaned typing indicators:",
                        {
                          before: typingIndicatorsBefore,
                          after: typingIndicatorsAfter,
                          removed:
                            typingIndicatorsBefore - typingIndicatorsAfter,
                        },
                      );

                      if (messageIdFromRef) {
                        existingMsgIndex = updatedMessages.findIndex(
                          (msg) => msg.id === messageIdFromRef,
                        );
                        console.log(
                          "🔍 CLIENT: [TOKEN] Re-found message after cleanup:",
                          existingMsgIndex,
                        );
                      }
                      hasReceivedFirstTokenThisTurn.current = true;
                    }

                    const messageName =
                      agentDomain === "Athlea" ? "Athlea" : agentDomain;
                    const isPlanner = agentDomain === "planner";
                    const isReasoning = agentDomain === "reasoning";

                    console.log("📝 CLIENT: [TOKEN] Message processing:", {
                      agent: agentDomain,
                      message_name: messageName,
                      is_planner: isPlanner,
                      is_reasoning: isReasoning,
                      will_update_existing: existingMsgIndex > -1,
                    });

                    if (messageIdFromRef && existingMsgIndex > -1) {
                      const currentMessage = updatedMessages[existingMsgIndex];
                      const isFirstUpdateForThisMessage =
                        currentMessage.isLoading === true ||
                        currentMessage.content === "" ||
                        currentMessage.content ===
                          "Reviewing search details...";

                      console.log(
                        "🔄 CLIENT: [TOKEN] Updating existing message:",
                        {
                          message_id: messageIdFromRef,
                          index: existingMsgIndex,
                          is_first_update: isFirstUpdateForThisMessage,
                          current_content_length:
                            currentMessage.content?.length || 0,
                          current_loading_state: currentMessage.isLoading,
                        },
                      );

                      updatedMessages[existingMsgIndex] = {
                        ...currentMessage,
                        name: isFirstUpdateForThisMessage
                          ? messageName
                          : currentMessage.name,
                        isLoading: false, // Always false once tokens arrive for it
                        domain: agentDomain,
                        content:
                          isFirstUpdateForThisMessage &&
                          currentMessage.content ===
                            "Reviewing search details..."
                            ? tokenContent // Overwrite placeholder
                            : currentMessage.content + tokenContent, // Append otherwise
                        lastUpdated: Date.now(),
                        metadata: {
                          ...(currentMessage.metadata || {}),
                          isCollapsed: false,
                          hideUntilContent: false,
                        },
                        // inlineToolTrace is already on currentMessage from its creation if it was a trace message
                      };

                      if (
                        isPlanner &&
                        isFirstUpdateForThisMessage &&
                        currentTurnPlannerMsgIdRef.current !== messageIdFromRef
                      ) {
                        console.log(
                          "📌 CLIENT: [TOKEN] Tracking planner message:",
                          {
                            message_id: messageIdFromRef,
                            previous_planner:
                              currentTurnPlannerMsgIdRef.current,
                          },
                        );
                        currentTurnPlannerMsgIdRef.current = messageIdFromRef;
                        setCollapsedMessages((prevCollapsed) => {
                          const newCollapsed = { ...prevCollapsed };
                          delete newCollapsed[messageIdFromRef];
                          return newCollapsed;
                        });
                      }
                    } else {
                      // This path should ideally not be taken for messages that are supposed to have an inlineToolTrace,
                      // as those should be pre-created by the 'tool_result' handler.
                      // If we end up here for an agent that *should* have had a trace, it implies a logic gap.
                      const newMsgId =
                        messageIdFromRef || `msg-${agentDomain}-${Date.now()}`;
                      if (!messageIdFromRef) {
                        currentAgentMessageIdsRef.current[agentDomain] =
                          newMsgId;
                        console.log(
                          "🆕 CLIENT: [TOKEN] Created new message ID:",
                          {
                            agent: agentDomain,
                            new_id: newMsgId,
                            all_agent_ids: currentAgentMessageIdsRef.current,
                          },
                        );
                      }

                      const newMessage: Message = {
                        id: newMsgId,
                        type: "ai",
                        domain: agentDomain,
                        name: messageName,
                        content: tokenContent,
                        isLoading: false, // First token means content is starting
                        lastUpdated: Date.now(),
                        metadata: {
                          isCollapsed:
                            isPlanner || isReasoning ? false : undefined,
                          hideUntilContent: false,
                        },
                        // inlineToolTrace should NOT be added here from a pendingRef
                      };

                      console.log("➕ CLIENT: [TOKEN] Adding new message:", {
                        id: newMsgId,
                        agent: agentDomain,
                        content_length: tokenContent.length,
                        is_planner: isPlanner,
                        is_reasoning: isReasoning,
                      });

                      updatedMessages.push(newMessage);

                      if (isPlanner) {
                        console.log(
                          "📌 CLIENT: [TOKEN] New planner message tracking:",
                          {
                            message_id: newMsgId,
                            previous_planner:
                              currentTurnPlannerMsgIdRef.current,
                          },
                        );
                        currentTurnPlannerMsgIdRef.current = newMsgId;
                        setCollapsedMessages((prevCollapsed) => {
                          const newCollapsed = { ...prevCollapsed };
                          delete newCollapsed[newMsgId];
                          return newCollapsed;
                        });
                      }
                    }

                    // Collapse logic (can run after message creation/update)
                    if (!isPlanner && currentTurnPlannerMsgIdRef.current) {
                      console.log(
                        `[Token Handler] Coach (${agentDomain}) token received. Collapsing planner message: ${currentTurnPlannerMsgIdRef.current}`,
                      );
                      const plannerMsgIdToCollapse =
                        currentTurnPlannerMsgIdRef.current;
                      setCollapsedMessages((prev) => ({
                        ...prev,
                        [plannerMsgIdToCollapse]: true,
                      }));
                      currentTurnPlannerMsgIdRef.current = null; // Clear after collapsing
                    }

                    if (!isReasoning) {
                      const reasoningMsgsToCollapse = updatedMessages.filter(
                        (msg) =>
                          msg.domain === "reasoning" &&
                          !collapsedMessages[msg.id],
                      );
                      if (reasoningMsgsToCollapse.length > 0) {
                        console.log(
                          `[Token Handler] Non-reasoning agent (${agentDomain}) token received. Collapsing ${reasoningMsgsToCollapse.length} reasoning messages.`,
                        );
                        setCollapsedMessages((prev) => {
                          const updatedCollapsed = { ...prev };
                          reasoningMsgsToCollapse.forEach((msg) => {
                            updatedCollapsed[msg.id] = true;
                          });
                          return updatedCollapsed;
                        });
                      }
                    }

                    lastStreamingAgentRef.current = agentDomain;
                    // setIsLoading(false);
                    return updatedMessages;
                  });
                }
                break;

              case "tool_result": {
                const { agent, toolName, content } = eventData;
                const callingAgentDomain = (agent ||
                  "general") as MessageDomain;
                const timestamp = Date.now();
                const toolCallId = `tc-${toolName || "unknown"}-${timestamp}-${Math.random().toString(36).substring(2, 7)}`;

                // --- Log incoming tool_result details ---
                console.log(
                  `[Page.tsx] tool_result event: toolName='${toolName}', agent='${agent}', contentType='${typeof content}'`,
                );
                // --- End log ---

                let extractedContent = content;
                let localRelevanceAssessments: any[] | undefined = undefined;
                let allProcessedAssessmentsFromMeta: any[] | undefined =
                  undefined;

                // Define the tool name that is expected to provide relevance assessments.
                // Adjust this if the actual tool name is different.
                const targetToolForRelevanceAssessments = "azure_search_graph";

                if (toolName === targetToolForRelevanceAssessments) {
                  // --- Log raw content for the target tool ---
                  console.log(
                    `[Page.tsx] Raw content from ${targetToolForRelevanceAssessments} tool_result:`,
                    JSON.stringify(content, null, 2),
                  );
                  // --- End log ---

                  // Extract relevance assessments from content
                  if (typeof content === "string") {
                    try {
                      const parsedString = JSON.parse(content);
                      console.log(
                        `[Page.tsx] Successfully parsed string content to JSON for ${targetToolForRelevanceAssessments}`,
                      );

                      if (parsedString && typeof parsedString === "object") {
                        if (Array.isArray(parsedString)) {
                          console.log(
                            `[Page.tsx] Content is an array with ${parsedString.length} items`,
                          );
                          localRelevanceAssessments = parsedString;
                        } else if (
                          parsedString.relevanceAssessments &&
                          Array.isArray(parsedString.relevanceAssessments)
                        ) {
                          console.log(
                            `[Page.tsx] Found relevanceAssessments array with ${parsedString.relevanceAssessments.length} items`,
                          );
                          localRelevanceAssessments =
                            parsedString.relevanceAssessments;
                        } else if (
                          parsedString.meta &&
                          parsedString.meta.allProcessedAssessments &&
                          Array.isArray(
                            parsedString.meta.allProcessedAssessments,
                          )
                        ) {
                          console.log(
                            `[Page.tsx] Found meta.allProcessedAssessments with ${parsedString.meta.allProcessedAssessments.length} items`,
                          );
                          allProcessedAssessmentsFromMeta =
                            parsedString.meta.allProcessedAssessments;
                        }
                      }
                    } catch (e) {
                      console.error(
                        `[Page.tsx] Failed to parse string content as JSON: ${e}`,
                      );
                      /* Not a single JSON */
                    }
                  } else if (content && typeof content === "object") {
                    console.log(
                      `[Page.tsx] Content is already an object for ${targetToolForRelevanceAssessments}`,
                    );
                    if (Array.isArray(content)) {
                      console.log(
                        `[Page.tsx] Content is an array with ${(content as any[]).length} items`,
                      );
                      localRelevanceAssessments = content as any[];
                    } else if (
                      (content as any).relevanceAssessments &&
                      Array.isArray((content as any).relevanceAssessments)
                    ) {
                      console.log(
                        `[Page.tsx] Found relevanceAssessments array with ${(content as any).relevanceAssessments.length} items`,
                      );
                      localRelevanceAssessments = (content as any)
                        .relevanceAssessments;
                    } else if (
                      (content as any).meta &&
                      (content as any).meta.allProcessedAssessments &&
                      Array.isArray(
                        (content as any).meta.allProcessedAssessments,
                      )
                    ) {
                      console.log(
                        `[Page.tsx] Found meta.allProcessedAssessments with ${(content as any).meta.allProcessedAssessments.length} items`,
                      );
                      allProcessedAssessmentsFromMeta = (content as any).meta
                        .allProcessedAssessments;
                    }
                  }

                  // --- Log extracted localRelevanceAssessments ---
                  console.log(
                    `[Page.tsx] localRelevanceAssessments for ${targetToolForRelevanceAssessments} before use in finalInlineTraceObject:`,
                    JSON.stringify(localRelevanceAssessments, null, 2),
                  );
                  // --- End log ---
                } else {
                  // For tools other than the target, try to parse if string (original general logic)
                  if (typeof content === "string") {
                    try {
                      extractedContent = JSON.parse(content);
                    } catch (e) {
                      /* Keep as string if not parsable */
                    }
                  }
                }

                // --- Extract allProcessedAssessments from meta if available ---
                if (
                  toolName === targetToolForRelevanceAssessments &&
                  extractedContent &&
                  typeof extractedContent === "object" &&
                  (extractedContent as any).meta
                ) {
                  allProcessedAssessmentsFromMeta = (extractedContent as any)
                    .meta.allProcessedAssessments;
                  console.log(
                    `[Page.tsx] Extracted allProcessedAssessmentsFromMeta for ${targetToolForRelevanceAssessments}:`,
                    JSON.stringify(allProcessedAssessmentsFromMeta, null, 2),
                  );
                }
                // --- End extraction ---

                const parsedOutputFromUtil = parseToolOutputToSteps({
                  name: toolName || "unknown_tool",
                  output: extractedContent,
                  metadata:
                    localRelevanceAssessments &&
                    localRelevanceAssessments.length > 0
                      ? { relevanceAssessments: localRelevanceAssessments }
                      : undefined,
                });

                // Log parsed output
                console.log(
                  `[Page.tsx] parseToolOutputToSteps returned:`,
                  JSON.stringify(parsedOutputFromUtil, null, 2),
                );

                // --- Augment steps with allProcessedAssessmentsFromMeta ---
                let stepsForTrace = parsedOutputFromUtil?.steps;
                if (
                  toolName === targetToolForRelevanceAssessments &&
                  allProcessedAssessmentsFromMeta &&
                  stepsForTrace
                ) {
                  console.log(
                    `[Page.tsx] Attempting to augment steps with allProcessedAssessmentsFromMeta, steps count: ${Array.isArray(stepsForTrace) ? stepsForTrace.length : "unknown"}`,
                  );

                  // Type assertion to fix linter error
                  stepsForTrace = (stepsForTrace as any[]).map((step) => {
                    // Try to identify the relevance filter step by title or a typical number (e.g., step 2)
                    if (
                      step.title === "Relevance Filter" ||
                      (step.number === 2 &&
                        step.title?.toLowerCase().includes("relevance"))
                    ) {
                      console.log(
                        `[Page.tsx] Augmenting step titled '${step.title}' (number ${step.number}) with allProcessedAssessmentsFromMeta`,
                      );
                      return {
                        ...step,
                        allProcessedAssessments:
                          allProcessedAssessmentsFromMeta,
                      };
                    }
                    return step;
                  });
                }
                // --- End augmentation ---

                // Conditional log for parsedOutputFromUtil
                if (toolName === targetToolForRelevanceAssessments) {
                  console.log(
                    `[Page.tsx] parsedOutputFromUtil for ${targetToolForRelevanceAssessments} (steps might be augmented):`,
                    JSON.stringify(parsedOutputFromUtil, null, 2), // This will show original steps
                    "Augmented stepsForTrace:",
                    JSON.stringify(stepsForTrace, null, 2), // This shows the potentially modified steps
                  );
                }

                const finalInlineTraceObject: any = {
                  toolName: toolName || "unknown_tool",
                  summary: parsedOutputFromUtil?.summary,
                  steps: stepsForTrace,
                  relevanceAssessments: localRelevanceAssessments,
                };

                // Conditional log for finalInlineTraceObject
                if (toolName === targetToolForRelevanceAssessments) {
                  console.log(
                    `[Page.tsx] finalInlineTraceObject with relevanceAssessments for ${targetToolForRelevanceAssessments}:`,
                    JSON.stringify(finalInlineTraceObject, null, 2),
                  );
                }

                if (
                  parsedOutputFromUtil &&
                  parsedOutputFromUtil.steps &&
                  parsedOutputFromUtil.steps.length > 0
                ) {
                  const traceMessageId = `trace-msg-${callingAgentDomain}-${Date.now()}`;

                  const newTraceMessage: Message = {
                    id: traceMessageId,
                    type: "ai",
                    domain: callingAgentDomain,
                    name:
                      callingAgentDomain === "Athlea"
                        ? "Athlea"
                        : callingAgentDomain,
                    content: "Reviewing search details...",
                    isLoading: true,
                    inlineToolTrace: finalInlineTraceObject,
                    lastUpdated: Date.now(),
                    metadata: { hideUntilContent: false },
                  };

                  // Log created message with trace object
                  console.log(
                    `[Page.tsx] Creating new trace message with ID ${traceMessageId}, hasInlineTrace: ${!!newTraceMessage.inlineToolTrace}, stepsCount: ${newTraceMessage.inlineToolTrace?.steps?.length || 0}`,
                  );

                  setMessages((prevMessages) => {
                    let updatedMessages = prevMessages.filter(
                      (msg) =>
                        !(
                          msg.id === `typing-indicator-${callingAgentDomain}` ||
                          msg.id === "typing-indicator"
                        ),
                    );
                    updatedMessages.push(newTraceMessage);
                    return updatedMessages;
                  });

                  currentAgentMessageIdsRef.current[callingAgentDomain] =
                    traceMessageId;

                  // Conditional log for trace message creation
                  if (toolName === targetToolForRelevanceAssessments) {
                    console.log(
                      `[Page.tsx] Created trace message ID ${traceMessageId} for ${targetToolForRelevanceAssessments} tool result`,
                    );
                  }

                  hasReceivedFirstTokenThisTurn.current = false;
                  lastStreamingAgentRef.current = callingAgentDomain;
                } else {
                  // Conditional warning if no steps are parsed for the target tool
                  if (toolName === targetToolForRelevanceAssessments) {
                    console.warn(
                      `[Page.tsx] No steps found in parsedOutputFromUtil for ${targetToolForRelevanceAssessments}. No trace message created.`,
                    );
                  }
                }

                setToolCalls((prev) => [
                  ...prev,
                  {
                    id: toolCallId,
                    domain: callingAgentDomain,
                    toolName: toolName || "unknown_tool",
                    timestamp,
                    content: extractedContent,
                  },
                ]);
                break;
              }

              case "sidebar_update":
                if (eventData.sidebarData) {
                  const sidebarUpdate =
                    eventData.sidebarData as SidebarStateData;
                  setSidebarData(sidebarUpdate);

                  // --- Update selected sport state if present in sidebar data ---
                  // Note: The actual selectedSport is primarily managed in OnboardingState on the backend.
                  // We sync it here if the backend sends it via sidebarData, mainly for display.
                  if (sidebarUpdate.selectedSport !== undefined) {
                    // Check if it changed from the current state
                    if (sidebarUpdate.selectedSport !== selectedSportState) {
                      console.log(
                        `CLIENT: Updating selectedSport state from sidebar: ${sidebarUpdate.selectedSport}`,
                      );
                      setSelectedSportState(sidebarUpdate.selectedSport);
                    }
                  }
                  // --- End selected sport update ---

                  // --- Store pending suggestions in REF ---
                  if (
                    sidebarUpdate.sportSuggestions &&
                    sidebarUpdate.sportSuggestions.length > 0
                  ) {
                    console.log(
                      "CLIENT: Storing pending suggestions in ref:",
                      sidebarUpdate.sportSuggestions,
                    );
                    // Directly update the ref's current value
                    pendingSuggestionsRef.current =
                      sidebarUpdate.sportSuggestions;
                  }
                  // --- End storing pending suggestions ---
                } else {
                  console.warn(
                    "CLIENT: Received sidebar_update event with no data.",
                  );
                }
                break;

              case "need_input":
                // Add safeguard to prevent inappropriate need_input handling
                if (streamCompleted) {
                  console.warn(
                    "CLIENT: [SSE] Ignoring need_input event after stream completion",
                  );
                  break;
                }

                debugToolLog("Received need_input event:", eventData);
                console.log(
                  "CLIENT: Received need_input event:",
                  "Prompt:",
                  eventData.prompt || "None",
                  "Field:",
                  eventData.field || "None",
                );
                setInputPrompt(eventData.prompt || "Please provide input.");
                setWaitingForInput(true);
                // setIsLoading(false);
                setIsStreaming(false);
                cleanupEventSource();
                currentAgentMessageIdsRef.current = {};
                setMessages((prev) => {
                  console.log(
                    "CLIENT: Cleaning up typing indicators after need_input",
                  );
                  return prev.filter(
                    (msg) =>
                      msg.id !== "typing-indicator" &&
                      !(
                        typeof msg.id === "string" &&
                        msg.id.startsWith("typing-indicator-")
                      ),
                  );
                });
                break;

              case "complete":
              case "done":
                // Mark stream as completed to prevent further processing
                streamCompleted = true;
                console.log(
                  "CLIENT: [SSE] Stream marked as completed, type:",
                  eventData.type,
                );

                // ... (existing done/complete logic, maybe clear sidebarData?) ...
                // Optional: Clear sidebar data when flow completes?
                // setSidebarData(null);
                setMessageDone(true);
                setMessages((prevMessages) => {
                  let finalMessages = prevMessages.filter(
                    (msg) =>
                      msg.id !== "typing-indicator" &&
                      !(
                        typeof msg.id === "string" &&
                        msg.id.startsWith("typing-indicator-")
                      ),
                  );

                  // Clean up content for messages with azure_search_graph traces
                  finalMessages = finalMessages.map((msg) => {
                    if (
                      msg.type === "ai" &&
                      msg.inlineToolTrace &&
                      msg.inlineToolTrace.toolName === "azure_search_graph" &&
                      msg.content &&
                      typeof msg.content === "string"
                    ) {
                      const trimmedContent = msg.content.trim();
                      let isContentJustJson = false;
                      if (
                        (trimmedContent.startsWith("{") &&
                          trimmedContent.endsWith("}")) ||
                        (trimmedContent.startsWith("[") &&
                          trimmedContent.endsWith("]"))
                      ) {
                        try {
                          JSON.parse(trimmedContent);
                          isContentJustJson = true;
                        } catch (e) {
                          // Not valid JSON
                        }
                      }

                      if (isContentJustJson) {
                        console.log(
                          `[SSE Done Handler] Message ${msg.id} content is just JSON. Replacing with generic text.`,
                        );
                        return {
                          ...msg,
                          content:
                            "Tool results processed. See details in the trace.",
                          isLoading: false, // Ensure isLoading is false
                        };
                      } else if (
                        msg.content === "Reviewing search details..." ||
                        msg.content === ""
                      ) {
                        // If content is still placeholder or empty
                        console.log(
                          `[SSE Done Handler] Setting generic content for trace message ${msg.id} with empty/placeholder content.`,
                        );
                        return {
                          ...msg,
                          content:
                            "Tool action completed. See details in the trace.",
                          isLoading: false, // Ensure isLoading is false
                        };
                      }
                    }
                    return msg;
                  });

                  // Restore Keyword fetching logic
                  finalMessages.forEach((msg) => {
                    if (
                      msg.type === "ai" &&
                      msg.content &&
                      msg.content.trim() !== ""
                      //  &&
                      // !msg.keywords &&
                      // msg.domain !== "reasoning" &&
                      // msg.domain !== "planner"
                    ) {
                      console.log(
                        `[SSE Done Handler] Triggering keyword fetch for message: ${msg.id}`,
                      );
                      // Pass necessary state/functions to fetchKeywords
                      fetchKeywords(
                        msg.id,
                        msg.content,
                        threadIdFromUrl, // Assuming threadIdFromUrl is accessible here
                        finalMessages, // Pass the filtered messages
                        setMessages, // Pass the state setter
                        processBatchKeywords, // Pass the callback
                      );
                    }
                  });

                  return finalMessages;
                });

                hasReceivedFirstTokenThisTurn.current = false;
                currentAgentMessageIdsRef.current = {};
                currentTurnPlannerMsgIdRef.current = null;

                setIsLoading(false);
                setIsStreaming(false);
                setWaitingForInput(false); // Ensure this is false on completion

                lastStreamingAgentRef.current = null;

                // Cleanup and resolve - this will prevent any further reconnection attempts
                cleanupEventSource();
                clearTimeout(connectionTimeout);
                resolve();
                break;

              case "error":
                streamCompleted = true; // Mark as completed on error too
                console.error(
                  "❌ CLIENT: [SSE] Error event received:",
                  eventData,
                );

                // Check if we should prevent retry
                const preventRetry = (eventData as any).preventRetry === true;

                setIsLoading(false);
                setIsStreaming(false);
                setWaitingForInput(false);

                // Show error message
                setMessages((prev) => {
                  const messagesWithoutIndicator = prev.filter(
                    (msg) => msg.id !== "typing-indicator",
                  );
                  return [
                    ...messagesWithoutIndicator,
                    {
                      id: `err-${Date.now()}`,
                      type: "system",
                      domain: "general",
                      content:
                        (eventData as any).message ||
                        "An error occurred. Please try again.",
                      lastUpdated: Date.now(),
                    },
                  ];
                });

                currentAgentMessageIdsRef.current = {};
                lastStreamingAgentRef.current = null;

                // Cleanup the connection
                cleanupEventSource();

                // Reject the promise to prevent retries if preventRetry is true
                if (preventRetry) {
                  reject(new Error("Backend error - retry prevented"));
                }
                break;

              case "heartbeat":
                // Handle heartbeat events
                console.log("💓 CLIENT: [SSE] Heartbeat received:", eventData);
                break;

              // case "history_loaded": // Likely not needed if starting fresh
              //   break;

              default:
                console.warn(
                  "CLIENT: Received unknown SSE event type:",
                  event.type,
                );
            }
          } catch (e: any) {
            // ... (existing parsing error logic) ...
            console.error(
              "CLIENT: Error parsing SSE message:",
              e,
              "Raw data:",
              event.data,
            );
            setIsLoading(false);
            setIsStreaming(false);
            setWaitingForInput(false);
            setMessages((prev) => {
              const indicator = prev.find(
                (msg) => msg.id === "typing-indicator",
              );
              if (
                indicator &&
                indicator.content &&
                indicator.content.length > 0
              ) {
                return prev.filter((msg) => msg.id !== "typing-indicator");
              } else {
                return prev;
              }
            });

            // Cleanup and reject the promise when there's a parsing error
            cleanupEventSource();
            clearTimeout(connectionTimeout);
            reject(new Error(`Failed to parse SSE message: ${e.message}`));
          }
        };

        // Add event listeners for each custom event type
        newEventSource.addEventListener("agent_start", handleSSEEvent);
        newEventSource.addEventListener("token", handleSSEEvent);
        newEventSource.addEventListener("tool_result", handleSSEEvent);
        newEventSource.addEventListener("sidebar_update", handleSSEEvent);
        newEventSource.addEventListener("need_input", handleSSEEvent);
        newEventSource.addEventListener("complete", handleSSEEvent);
        newEventSource.addEventListener("done", handleSSEEvent);
        newEventSource.addEventListener("error", handleSSEEvent);

        // Also add the default message handler for any events without a type
        newEventSource.onmessage = handleSSEEvent;
      });
    },
    [
      setMessages,
      setIsStreaming,
      setInputPrompt,
      setWaitingForInput,
      setToolCalls,
      setIsLoading,
      setSidebarData,
      processBatchKeywords,
      threadIdFromUrl,
      setKeyWordList,
    ],
  );

  // Cleanup eventSource on unmount
  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        console.log("Closing EventSource connection on component unmount.");
        eventSourceRef.current.close();
      }
    };
  }, []);

  // --- Define Profile Update Function --- START
  const checkForProfileUpdate = useCallback(
    (messageContent: string) => {
      if (!userId) {
        console.log("[Profile Update Check - Direct Call] Exiting: No userId.");
        return;
      }

      console.log(
        "[Profile Update Check - Direct Call] Executing checkForProfileUpdate. Fetching /api/update-profile...",
      );
      fetch("/api/update-profile", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId: userId,
          message: messageContent, // Use the passed message content
        }),
      })
        .then((response) => {
          console.log(
            "[Profile Update Check - Direct Call] Fetch response status:",
            response.status,
          );
          if (!response.ok) {
            console.error(
              "CLIENT: Error response from /api/update-profile:",
              response.status,
            );
            return response.text().then((text) => {
              console.error(
                "CLIENT: Error body from /api/update-profile:",
                text,
              );
              return null;
            });
          }
          return response.json();
        })
        .then((data) => {
          console.log(
            "[Profile Update Check - Direct Call] Fetch response data:",
            data,
          );
          if (!data) return;

          console.log("CLIENT: /api/update-profile response:", data);

          if (data.updated && data.systemMessage) {
            if (
              !data.systemMessage?.startsWith(
                "Your Connection error profile was updated with",
              ) &&
              !data.systemMessage?.startsWith("Error updating profile")
            ) {
              console.log(
                "CLIENT: Profile updated. Adding system message to chat.",
              );
              const systemUpdateMessage: Message = {
                id: `system-update-${Date.now()}`,
                type: "system",
                content: data.systemMessage,
                lastUpdated: Date.now(),
                domain: "general",
              };
              // Use functional update
              setMessages((prevMessages) => [
                ...prevMessages,
                systemUpdateMessage,
              ]);
            } else {
              console.warn(
                "CLIENT: Suppressed potentially malformed system message from API:",
                data.systemMessage,
              );
            }
          } else {
            console.log(
              "CLIENT: Profile update check complete, no update needed or no system message.",
            );
          }
        })
        .catch((error) => {
          console.error(
            "[Profile Update Check - Direct Call] Fetch CATCH block error:",
            error,
          );
          console.error("CLIENT: Error calling /api/update-profile:", error);
        });
    },
    [userId, setMessages], // Dependencies for the check function
  );
  // --- Define Profile Update Function --- END

  const submitUserInput = useCallback(
    async (userInputText: string) => {
      setWaitingForInput(false);
      const currentPrompt = inputPrompt;
      setInputPrompt("");
      setIsLoading(true);
      hasReceivedFirstTokenThisTurn.current = false;
      lastStreamingAgentRef.current = null;

      console.log(
        "CLIENT: submitUserInput invoked. Input:",
        userInputText,
        "Prompt was:",
        currentPrompt,
        "Thread ID:",
        threadIdFromUrl,
        "UserID:",
        userId,
        `SingleCoach: ${selectedCoach || "None"}`,
      );
      if (!threadIdFromUrl || !userId) {
        console.error(
          "CLIENT Error: threadId or userId is not available. Cannot resume.",
        );
        setMessages((prev) => [
          ...prev,
          {
            id: "err-resume-no-id",
            type: "system",
            domain: "general",
            content: "Error: Cannot resume chat. Thread ID missing.",
            lastUpdated: Date.now(),
          },
        ]);
        setIsLoading(false);
        setIsStreaming(false);
        return;
      }

      const inputResponseMessage: Message = {
        id: `user-resp-${Date.now()}`,
        type: "human",
        content: `(Response to: "${currentPrompt}") ${userInputText}`,
        lastUpdated: Date.now(),
        domain: "general",
      };
      const typingIndicatorMessage: Message = {
        id: "typing-indicator",
        type: "ai",
        domain: "general",
        name: undefined,
        content: "",
        isLoading: true,
        lastUpdated: Date.now(),
        metadata: { hideUntilContent: false },
      };
      setMessages((prev) => [
        ...prev,
        inputResponseMessage,
        typingIndicatorMessage,
      ]);
      currentAgentMessageIdsRef.current = {};

      console.log(
        `CLIENT: Resuming thread with ID: ${threadIdFromUrl} for User ID: ${userId}`,
      );

      // Determine if it's an onboarding flow
      const isDirectOnboarding =
        searchParams?.get("direct_onboarding") === "true";
      const isResumedOnboarding = searchParams?.get("onboarding") === "true";
      const isOnboardingFlow = isDirectOnboarding || isResumedOnboarding;

      // --- >>> ADDED DETAILED LOGGING START <<< ---
      console.log("--- submitUserInput Routing Check ---");
      console.log(
        `searchParams.get("onboarding"): ${isResumedOnboarding ? "true" : "null"}`,
      );
      console.log(
        `searchParams.get("direct_onboarding"): ${isDirectOnboarding ? "true" : "null"}`,
      );
      console.log(`Resulting isOnboardingFlow: ${isOnboardingFlow}`);
      // --- >>> ADDED DETAILED LOGGING END <<< ---

      try {
        let eventSourceUrl: string;

        if (isOnboardingFlow) {
          // Onboarding resume uses specialized parameters
          console.warn(
            "CLIENT: Resuming onboarding via GET to /api/onboarding-python with proper parameters.",
          );
          const resumeParams = new URLSearchParams({
            threadId: threadIdFromUrl!,
            userId: userId!,
            resumeValue: userInputText, // Use resumeValue for user input in onboarding
          });
          eventSourceUrl = `/api/onboarding-python?${resumeParams.toString()}`; // Target Python onboarding proxy endpoint
        } else {
          // Regular chat resume uses GET to /api/coaching-python
          const resumeParams = new URLSearchParams({
            threadId: threadIdFromUrl!,
            userInput: userInputText, // Regular param name
            userId: userId!,
          });
          if (selectedCoach) {
            resumeParams.set("singleCoach", selectedCoach);
            console.log(
              `CLIENT: Adding singleCoach=${selectedCoach} to resume params.`,
            );
          }
          eventSourceUrl = `/api/coaching-python?${resumeParams.toString()}`;
        }

        console.log(
          `CLIENT: Sending resume request via GET to: ${eventSourceUrl}`,
        );
        await processEventSource(eventSourceUrl); // Use standard SSE processing
      } catch (error) {
        console.error("CLIENT Error during submitUserInput:", error);
        setIsLoading(false);
        setIsStreaming(false);
        setMessages((prev) => [
          ...prev.filter((msg) => msg.id !== "typing-indicator"),
          {
            id: `err-resume-submit-${Date.now()}`,
            type: "system",
            domain: "general",
            content: "Sorry, there was an error resuming the conversation.",
            lastUpdated: Date.now(),
          },
        ]);
      }
    },
    // Dependencies need careful review - avoid including processEventSource directly if possible
    [
      inputPrompt,
      threadIdFromUrl,
      userId,
      selectedCoach,
      /* processEventSource */ setIsLoading,
      setIsStreaming,
      setMessages,
      setWaitingForInput,
      setInputPrompt,
      searchParams,
      processEventSource,
    ],
  );

  // Define handleSubmit before it's used in the initial message useEffect
  const handleSubmit = useCallback(
    async (messageToSend: string, isInitialSubmit = false) => {
      console.log(
        `CLIENT: [handleSubmit ENTRY] Called with message: '${messageToSend}', isInitialSubmit: ${isInitialSubmit}`,
        `waitingForInput: ${waitingForInput}`,
        `isLoading: ${isLoading}`,
        `isStreaming: ${isStreaming}`,
      );

      // --- Prevent duplicate initial call ---
      if (isInitialSubmit) {
        if (initialCallAttemptedRef.current) {
          console.log(
            "CLIENT: [handleSubmit] Initial call already attempted, exiting.",
          );
          submissionInProgressRef.current = false;
          return;
        }
        console.log("CLIENT: [handleSubmit] Processing initial call attempt.");
        // Don't set initialCallAttemptedRef here - let the initial effect handle it
      }

      // Submission guard logic
      if (submissionInProgressRef.current) {
        console.log(
          "CLIENT: Submission already in progress, ignoring duplicate call.",
        );
        return;
      }
      submissionInProgressRef.current = true;

      if (!messageToSend || !threadIdFromUrl || !userId) {
        console.error("CLIENT Error: Missing input, threadId, or userId.");
        setMessages((prev) => [
          ...prev.filter((msg) => msg.type !== "system"),
          {
            id: `error-${Date.now()}`,
            type: "system",
            content:
              "An error occurred while communicating with the server. Please try again.",
            lastUpdated: Date.now(),
            domain: "general",
          },
        ]);
        submissionInProgressRef.current = false;
        return;
      }

      setIsLoading(true);
      hasReceivedFirstTokenThisTurn.current = false;
      lastStreamingAgentRef.current = null;
      setMessages((prev) => prev.filter((msg) => msg.type !== "system"));

      // Add safeguard for waitingForInput
      if (waitingForInput) {
        console.log(
          "CLIENT: [handleSubmit] waitingForInput=true detected.",
          "InputPrompt:",
          inputPrompt,
          "IsStreaming:",
          isStreaming,
          "IsLoading:",
          isLoading,
        );

        // Only proceed with submitUserInput if we have a valid input prompt
        if (
          inputPrompt &&
          inputPrompt.trim() !== "" &&
          !isStreaming &&
          !isLoading
        ) {
          console.log(
            "CLIENT: [handleSubmit] Valid input prompt found, calling submitUserInput",
          );
          await submitUserInput(messageToSend);
          submissionInProgressRef.current = false;
          return;
        } else {
          console.warn(
            "CLIENT: [handleSubmit] waitingForInput=true but conditions not met for submitUserInput.",
            "Resetting waitingForInput to false and proceeding with normal flow.",
          );
          setWaitingForInput(false);
        }
      }

      // Typing indicator logic
      const uniqueTypingId = `typing-indicator-${Date.now()}`;
      let initialIndicatorDomain: MessageDomain = "general";

      if (selectedCoach) {
        initialIndicatorDomain = selectedCoach as MessageDomain;
      }

      const isOnboardingFlow =
        searchParams?.get("onboarding") === "true" ||
        searchParams?.get("direct_onboarding") === "true";

      if (isOnboardingFlow) {
        initialIndicatorDomain = "Athlea";
      }

      console.log(
        `[handleSubmit] Creating typing indicator ${uniqueTypingId} with domain: ${initialIndicatorDomain}`,
      );

      const typingIndicatorMessage: Message = {
        id: uniqueTypingId,
        type: "ai",
        domain: initialIndicatorDomain,
        name:
          initialIndicatorDomain === "Athlea"
            ? "Athlea"
            : initialIndicatorDomain,
        content: "",
        isLoading: true,
        lastUpdated: Date.now(),
        metadata: { hideUntilContent: isInitialSubmit },
      };

      currentAgentMessageIdsRef.current = {};

      // Conditionally add user message locally
      if (!isInitialSubmit) {
        const userMessage: Message = {
          id: `user-${Date.now()}`,
          type: "human",
          content: messageToSend,
          lastUpdated: Date.now(),
          domain: "general",
        };
        setMessages((prev) => [...prev, userMessage, typingIndicatorMessage]);
      } else {
        setMessages((prev) => [...prev, typingIndicatorMessage]);
        console.log(
          "CLIENT: Initial submit, skipping local user message addition.",
        );
      }

      try {
        // Determine if it's an onboarding flow
        const isDirectOnboarding =
          searchParams?.get("direct_onboarding") === "true";
        const isResumedOnboarding = searchParams?.get("onboarding") === "true";
        const isOnboardingFlow = isDirectOnboarding || isResumedOnboarding;

        console.log("--- handleSubmit Routing Check ---");
        console.log(
          `searchParams.get("onboarding"): ${isResumedOnboarding ? "true" : "null"}`,
        );
        console.log(
          `searchParams.get("direct_onboarding"): ${isDirectOnboarding ? "true" : "null"}`,
        );
        console.log(`Resulting isOnboardingFlow: ${isOnboardingFlow}`);

        let eventSourceUrl: string;

        if (isOnboardingFlow) {
          console.warn(
            "CLIENT: [handleSubmit] Onboarding detected, creating EventSource connection to /api/onboarding-python with proper parameters.",
          );
          const params = new URLSearchParams({
            threadId: threadIdFromUrl!,
            userId: userId!,
          });

          if (isDirectOnboarding) {
            params.set("initialMessage", messageToSend);
          } else {
            params.set("message", messageToSend);
          }

          eventSourceUrl = `/api/onboarding-python?${params.toString()}`;
        } else {
          // Regular chat uses GET to /api/coaching-python with message in URL params
          const params = new URLSearchParams({
            message: messageToSend,
            threadId: threadIdFromUrl!,
            userId: userId!,
          });
          if (selectedCoach) {
            params.set("singleCoach", selectedCoach);
            console.log(
              `CLIENT: Adding singleCoach=${selectedCoach} to message params.`,
            );
          }
          eventSourceUrl = `/api/coaching-python?${params.toString()}`;
        }

        console.log(
          `CLIENT: [handleSubmit] Connecting to EventSource URL: ${eventSourceUrl}`,
        );
        console.log(
          "CLIENT: [handleSubmit] >>> Calling processEventSource... <<< ",
        );
        await processEventSource(eventSourceUrl);
        console.log(
          "CLIENT: [handleSubmit] <<< processEventSource call COMPLETE >>> ",
        );
      } catch (error) {
        console.error("CLIENT Error during handleSubmit:", error);
        setMessages((prev) => [
          ...prev.filter(
            (msg) =>
              msg.id !== "typing-indicator" &&
              !msg.id.startsWith("typing-indicator-"),
          ),
          {
            id: `error-${Date.now()}`,
            type: "system",
            content:
              "An error occurred while communicating with the server. Please try again.",
            lastUpdated: Date.now(),
            domain: "general",
          },
        ]);
        setIsLoading(false);
        setIsStreaming(false);
      } finally {
        submissionInProgressRef.current = false;
      }
    },
    [
      threadIdFromUrl,
      userId,
      waitingForInput,
      submitUserInput,
      selectedCoach,
      searchParams,
      processEventSource,
      inputPrompt,
      isStreaming,
      isLoading,
      setIsLoading,
      setIsStreaming,
      setMessages,
      setWaitingForInput,
    ],
  );

  // Add back initial message handling, particularly for onboarding
  useEffect(() => {
    let timerId: NodeJS.Timeout | null = null; // Declare timerId here

    // Log dependencies at the start of the effect
    // console.log("--- Initial Message useEffect Check ---");
    // console.log(`Current needInitialMessages: ${needInitialMessages}`);
    // console.log(`Current userId: ${userId}`);
    // console.log(`Current threadIdFromUrl: ${threadIdFromUrl}`);
    // console.log(`Current initialMessage: ${initialMessage}`);
    console.log(
      `initialCallAttemptedRef.current: ${initialCallAttemptedRef.current}`,
    );
    // console.log(
    //   `Current searchParams.get("direct_onboarding"): ${searchParams?.get("direct_onboarding")}`,
    // );
    // console.log(
    //   `Current searchParams.get("onboarding"): ${searchParams?.get("onboarding")}`,
    // );

    // Add guard to prevent multiple initial calls
    if (initialCallAttemptedRef.current) {
      console.log(
        "CLIENT: [Initial Effect] Already attempted initial call, skipping.",
      );
      return;
    }

    if (needInitialMessages && userId && threadIdFromUrl) {
      console.log(
        "CLIENT: [Initial Effect] Conditions MET. Preparing to call handleSubmit.",
        `Direct Onboarding: ${searchParams?.get("direct_onboarding") === "true"}`,
        `Resumed Onboarding: ${searchParams?.get("onboarding") === "true"}`,
        `Initial Message from Redux: "${initialMessage}"`, // Added logging
      );

      // Check if this is an onboarding session or a regular coach chat
      const isOnboardingSession =
        searchParams?.get("direct_onboarding") === "true" ||
        searchParams?.get("onboarding") === "true";

      // Use appropriate initial message based on context
      const messageToSend = isOnboardingSession
        ? "start_onboarding" // Only use "start_onboarding" for actual onboarding
        : initialMessage || "Hi, how can you help me today?"; // Use initialMessage or default greeting for coach chats

      const isInitial = true; // Explicitly true for clarity

      console.log(
        `CLIENT: [Initial Effect] Calling handleSubmit with message: '${messageToSend}', isInitialSubmit: ${isInitial}`,
        `(initialMessage was: "${initialMessage}")`, // Added logging
      );

      timerId = setTimeout(() => {
        console.log(
          "CLIENT: [Initial Effect] Executing delayed handleSubmit call",
        );
        handleSubmit(messageToSend, isInitial);
        // Mark as attempted AFTER the handleSubmit call
        initialCallAttemptedRef.current = true;
      }, 0);

      dispatch(setNeedInitialMessages(false));
    } else {
      console.log(
        "CLIENT: [Initial Effect] Initial message conditions NOT met.",
      ); // Added identifier
    }

    // Cleanup function: ONLY reset submissionInProgressRef.
    // DO NOT clear the timerId here.
    return () => {
      console.log(
        "CLIENT: [Initial Effect Cleanup] Resetting submissionInProgressRef.", // Modify log message
      );
      // if (timerId) clearTimeout(timerId); // <-- REMOVE THIS LINE or comment out
      submissionInProgressRef.current = false;
    };
  }, [
    needInitialMessages,
    userId,
    threadIdFromUrl,
    searchParams,
    initialMessage,
    handleSubmit,
    dispatch,
  ]);

  // ... (scroll effects remain) ...
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  useEffect(() => {
    toolCallsEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [toolCalls]);

  // Remove plan name update logic or adapt if needed
  // useEffect(() => { updatePlanNameThunk... }, ...);

  // --- FIXED: Message Grouping with useCallback to prevent infinite loops --- START
  const processMessageGrouping = useCallback((currentMessages: Message[]) => {
    if (currentMessages.length === 0) return currentMessages;

    const reasoningMessages = currentMessages.filter(
      (msg) => msg.domain === "reasoning",
    );

    if (reasoningMessages.length > 1) {
      console.log(
        `[Message Group Effect] Found ${reasoningMessages.length} reasoning messages. Keeping only the latest one.`,
      );

      const latestReasoningMsg =
        reasoningMessages[reasoningMessages.length - 1];

      // Filter out all reasoning messages except the latest one
      const processedMessages = currentMessages.filter(
        (msg) => msg.domain !== "reasoning" || msg.id === latestReasoningMsg.id,
      );

      console.log(
        `[Message Group Effect] Reduced message count from ${currentMessages.length} to ${processedMessages.length}`,
      );

      return processedMessages;
    }

    return currentMessages;
  }, []);

  // Use useEffect with ref to track previous messages and prevent infinite loops
  const previousMessagesRef = useRef<Message[]>([]);

  useEffect(() => {
    // Only process if messages actually changed (deep comparison by length and last message ID)
    const currentMessages = messages;
    const previousMessages = previousMessagesRef.current;

    const messagesChanged =
      currentMessages.length !== previousMessages.length ||
      (currentMessages.length > 0 &&
        previousMessages.length > 0 &&
        currentMessages[currentMessages.length - 1].id !==
          previousMessages[previousMessages.length - 1].id);

    if (messagesChanged) {
      const processedMessages = processMessageGrouping(currentMessages);

      // Only update state if processing actually changed something
      if (processedMessages.length !== currentMessages.length) {
        setMessages(processedMessages);
      }

      // Update ref to current messages
      previousMessagesRef.current = processedMessages;
    }
  }, [messages, processMessageGrouping]);
  // --- FIXED: Message Grouping with useCallback to prevent infinite loops --- END

  // --- FIXED: Profile update check effect with debouncing --- START
  const lastHumanMessageIdRef = useRef<string | null>(null);

  useEffect(() => {
    if (!userId || messages.length === 0) {
      return;
    }

    const lastMessage = messages[messages.length - 1];

    // Check only for the latest *human* message that isn't the typing indicator
    if (
      lastMessage.type === "human" &&
      lastMessage.id !== "typing-indicator" &&
      !lastMessage.id.startsWith("typing-indicator-") &&
      lastMessage.id !== lastHumanMessageIdRef.current // Only if it's a NEW human message
    ) {
      console.log(
        "CLIENT: New human message detected. Checking for profile updates...",
        `Message ID: ${lastMessage.id}`,
      );

      // Update the ref to track this message as processed
      lastHumanMessageIdRef.current = lastMessage.id;

      // Debounce the profile update call to prevent rapid API calls
      const timeoutId = setTimeout(() => {
        checkForProfileUpdate(lastMessage.content);
      }, 1000); // 1 second debounce

      // Cleanup timeout on re-render
      return () => {
        clearTimeout(timeoutId);
      };
    }
  }, [messages, userId, checkForProfileUpdate]); // Keep only necessary dependencies
  // --- FIXED: Profile update check effect with debouncing --- END

  // Determine if it's an onboarding session
  const isOnboarding =
    searchParams?.get("onboarding") === "true" ||
    searchParams?.get("direct_onboarding") === "true";

  // Determine current step for onboarding sidebar based on streamed data
  const calculateCurrentOnboardingStep = () => {
    if (!sidebarData) return 1; // Default if no data yet
    // Check if a plan exists
    if (sidebarData.generatedPlan) return 4; // Step 4: Plan Generated
    // Check if summary items exist (implies goals and history were processed)
    if (sidebarData.summaryItems && sidebarData.summaryItems.length > 0)
      return 3; // Step 3: History/Summary Generated
    // Check if goals exist
    if (sidebarData.goals.exists && sidebarData.goals.list.length > 0) return 2; // Step 2: Goals Captured
    return 1; // Default to step 1 (Greeting/Asking Goals)
  };

  const currentOnboardingStep = isOnboarding
    ? calculateCurrentOnboardingStep()
    : undefined;

  return (
    <div className="h-[calc(100vh-3.5rem)]">
      <Card className="h-full border-0 bg-[#F9FBFC] shadow-none">
        {/* ResizablePanelGroup is the main container for the chat interface */}
        <ResizablePanelGroup
          direction="horizontal"
          className="h-full"
          onLayout={(sizes) => setPanelSizes(sizes)}
        >
          <ResizablePanel defaultSize={panelSizes[0]} minSize={50}>
            <div className="relative flex h-full flex-col">
              <div className="scrollbar-hide flex-1 overflow-y-auto pb-24">
                <CardContent className="px-4 pt-4">
                  {messages.map((message) => {
                    if (message.type === "system") {
                      return (
                        <div key={message.id} className="mb-4">
                          <SystemMessage
                            message={message.content}
                            onRemove={async (domain, key) => {
                              // ... (onRemove logic)
                            }}
                          />
                        </div>
                      );
                    }

                    // --- Adjusted AI Message Rendering ---
                    if (
                      message.type === "ai" ||
                      isPlannerOrReasoningDomain(message.domain)
                    ) {
                      const isPlannerOrReasoning = isPlannerOrReasoningDomain(
                        message.domain,
                      );
                      return (
                        <div key={message.id} className="mb-4">
                          <AIMessage
                            message={message}
                            onToggleCollapse={
                              isPlannerOrReasoning ? toggleCollapse : undefined
                            }
                          />
                          {(() => {
                            if (
                              message.sportSuggestions &&
                              message.sportSuggestions.length > 0
                            ) {
                              console.log(
                                `Rendering suggestions for message ${message.id}:`,
                                message.sportSuggestions,
                              );
                              return (
                                <SuggestionBubbles
                                  suggestions={message.sportSuggestions}
                                  onSelect={handleSubmit}
                                />
                              );
                            } else if (message.type === "ai") {
                              console.log(
                                `Not rendering suggestions for message ${message.id} (suggestions: ${JSON.stringify(message.sportSuggestions)})`,
                              );
                            }
                            return null;
                          })()}
                        </div>
                      );
                    }

                    if (
                      message.type === "human" &&
                      !(
                        typeof message.id === "string" &&
                        message.id.startsWith("typing-indicator-")
                      )
                    ) {
                      return (
                        <div key={message.id} className="mb-4">
                          <UserMessage message={message} />
                        </div>
                      );
                    }
                    return null;
                  })}
                  <div ref={messagesEndRef} />
                </CardContent>
              </div>
              <div className="border-gray-100 absolute bottom-0 left-0 right-0 border-t bg-[#F9FBFC] p-4">
                <UserInput
                  onSubmitRegularMessage={handleSubmit}
                  onSubmitWaitingInput={submitUserInput}
                  isLoading={isLoading}
                  isStreaming={isStreaming}
                  waitingForInput={waitingForInput}
                  inputPrompt={inputPrompt}
                />
              </div>
            </div>
          </ResizablePanel>

          {/* Conditionally render the sidebar */}
          {(isOnboarding || toolCalls.some((call) => call.content)) && (
            <>
              <ResizableHandle
                withHandle
                className="bg-gray-200 hover:bg-gray-300 transition-colors"
              />
              <ResizablePanel
                defaultSize={panelSizes[1]}
                minSize={20}
                maxSize={40}
                style={{ marginRight: isRightDrawerOpen ? "385px" : "0px" }}
              >
                {isOnboarding ? (
                  <OnboardingSidebar
                    sidebarData={sidebarData}
                    selectedSport={selectedSportState}
                  />
                ) : (
                  <SidebarWrapper
                    toolCalls={toolCalls}
                    setSourceDocuments={setSourceDocuments}
                    sourceDocuments={sourceDocuments}
                    toggleToolCallExpanded={toggleToolCallExpanded}
                    getFormattedTime={getFormattedTime}
                  />
                )}
              </ResizablePanel>
            </>
          )}
        </ResizablePanelGroup>
      </Card>

      <PlanGenerationSidebar />
    </div>
  );
}
