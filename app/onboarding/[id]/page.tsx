"use client";

import { MarkdownComponents } from "@/components/Chat/MarkdownComponents";
import {
  AIMessage,
  // PlannerMessage, // These are handled by AIMessage based on domain
  // ReasoningMessage,
  UserMessage,
} from "@/components/Chat/MessageTypes"; // Corrected path
import {
  Message,
  StreamEventData,
  ToolCall,
  isPlannerOrReasoningDomain,
  MessageDomain,
} from "@/app/types/chat";
import { getFormattedTime } from "@/app/utils/formatUtils";
import { fetchKeywords } from "@/app/utils/keywordUtils";
import SystemMessage from "@/components/Chat/Messages/SystemMessage";
import OnboardingSidebar from "@/components/Chat/OnboardingSidebar";
import ToolSidebar from "@/components/Chat/ToolSidebar";
import UserInput from "@/components/Chat/UserInput";
import PlanGenerationSidebar from "@/components/PlanGenerationSidebar";
import { Card, CardContent } from "@/components/ui/cards";
import { useKeyword } from "@/context/KeywordContext";
import { useAppDispatch, useAppSelector } from "@/store/jobHook";
import { updatePlanNameThunk } from "@/store/slices/chatSlice";
import { setNeedInitialMessages } from "@/store/slices/jobSlice";
import { RootState } from "@/store/store";
import { debounce } from "lodash";
import dynamic from "next/dynamic";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { SidebarStateData } from "@/app/api/onboarding/state";
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from "@/components/ui/resizable";
import { Button } from "@/components/ui/button";
import { RotateCcw, HelpCircle } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Add a utility function to help with debugging
const DEBUG = false; // Set to true to enable debug logs
const debugLog = (...args: any[]) => {
  if (DEBUG) {
    console.log("[StreamDebug]", ...args);
  }
};

const LocationMap = dynamic(() => import("@/components/Chat/Map/LocationMap"), {
  ssr: false,
  loading: () => <p>Loading map...</p>,
});

export default function LangGraphChatPage() {
  const params = useParams<{ id: string }>();
  const threadIdFromUrl = params?.id;
  const router = useRouter();
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();

  const { initialMessage, needInitialMessages, selectedCoach } = useAppSelector(
    (state: RootState) => state.job,
  );

  const { setKeyWordList } = useKeyword();

  const planTitle = useAppSelector((state) => state.job.planTitle);
  const lastPlanNameUpdateTime = useAppSelector(
    (state) => state.chat.lastPlanNameUpdateTime,
  );

  const userData = useSelector((state: RootState) => state.user.userData);
  const userId = userData?.user_id;

  const [messages, setMessages] = useState<Message[]>([]);
  const [toolCalls, setToolCalls] = useState<ToolCall[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [collapsedMessages, setCollapsedMessages] = useState<
    Record<string, boolean>
  >({});
  const wasDisabledRef = useRef<boolean>(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const toolCallsEndRef = useRef<HTMLDivElement>(null);
  const currentAgentMessageIdsRef = useRef<Record<string, string>>({});
  const hasReceivedFirstTokenThisTurn = useRef(false);
  const lastStreamingAgentRef = useRef<string | null>(null);
  const submissionInProgressRef = useRef(false);
  const currentTurnPlannerMsgIdRef = useRef<string | null>(null);
  const initialCallAttemptedRef = useRef(false);

  const eventSourceRef = useRef<EventSource | null>(null);

  const [sidebarData, setSidebarData] = useState<SidebarStateData | null>(null);
  
  // State for persisting panel sizes
  const [panelSizes, setPanelSizes] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('onboardingPanelSizes');
      return saved ? JSON.parse(saved) : [60, 40];
    }
    return [60, 40];
  });

  // Save panel sizes to localStorage when they change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('onboardingPanelSizes', JSON.stringify(panelSizes));
    }
  }, [panelSizes]);

  // Keyboard shortcut to reset panel sizes
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + R to reset panel sizes
      if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
        event.preventDefault();
        setPanelSizes([60, 40]);
        console.log('Panel sizes reset to default');
      }
      
      // Arrow keys for fine-tuning (when focus is on handle)
      if (event.target && (event.target as HTMLElement).getAttribute('data-panel-resize-handle')) {
        if (event.key === 'ArrowLeft') {
          event.preventDefault();
          setPanelSizes((prev: number[]) => [Math.max(40, prev[0] - 1), Math.min(60, prev[1] + 1)]);
        } else if (event.key === 'ArrowRight') {
          event.preventDefault();
          setPanelSizes((prev: number[]) => [Math.min(70, prev[0] + 1), Math.max(30, prev[1] - 1)]);
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  console.log("---sidebarData---", sidebarData);

  const toggleCollapse = (messageId: string) => {
    setCollapsedMessages((prev) => ({
      ...prev,
      [messageId]: !prev[messageId],
    }));
  };

  const toggleToolCallExpanded = (id: string) => {
    setToolCalls((prev) =>
      prev.map((tool) =>
        tool.id === id ? { ...tool, expanded: !tool.expanded } : tool,
      ),
    );
  };

  const processBatchKeywords = useCallback(
    (keywords: string[]) => {
      if (keywords && keywords.length > 0) {
        console.log("Processing batch keywords:", keywords);
        setKeyWordList(keywords);
      }
    },
    [setKeyWordList],
  );

  const componentMarkdownComponents = {
    ...MarkdownComponents,
  };

  const processEventSource = useCallback(
    async (eventSourceUrl: string) => {
      console.log(
        "CLIENT: [processEventSource ENTRY] Attempting to connect to:",
        eventSourceUrl,
      );
      const agentMessageIds = currentAgentMessageIdsRef.current;

      // Close existing connection if any
      if (eventSourceRef.current) {
        console.log("Closing previous EventSource connection.");
        eventSourceRef.current.close();
      }

      try {
        console.log(
          "CLIENT: [processEventSource] Creating new EventSource object...",
        );
        const newEventSource = new EventSource(eventSourceUrl);
        eventSourceRef.current = newEventSource; // Store in ref

        newEventSource.onopen = () => {
          console.log(
            "CLIENT: [processEventSource] SSE Connection Opened (onopen triggered)",
          );
          setIsStreaming(true);
          currentAgentMessageIdsRef.current = {};
        };

        newEventSource.onerror = (error) => {
          console.error(
            "CLIENT: [processEventSource] SSE Connection Error (onerror triggered):",
            error,
            "ReadyState:",
            newEventSource.readyState,
          );
          if (isLoading || isStreaming) {
            setIsLoading(false);
            setIsStreaming(false);
            try {
              newEventSource.close();
            } catch (e) {
              console.warn(
                "CLIENT: Error closing EventSource in onerror (might be already closed):",
                e,
              );
            }
            setMessages((prev) => {
              const lastMsg = prev[prev.length - 1];
              if (lastMsg?.id?.startsWith("err-")) return prev;
              const messagesWithoutIndicator = prev.filter(
                (msg) => msg.id !== "typing-indicator",
              );
              return [
                ...messagesWithoutIndicator,
                {
                  id: `err-connect-${Date.now()}`,
                  type: "system",
                  domain: "general",
                  content: "Connection error. Please try again.",
                  lastUpdated: Date.now(),
                },
              ];
            });
            currentAgentMessageIdsRef.current = {};
            lastStreamingAgentRef.current = null;
          }
        };

        newEventSource.onmessage = async (event) => {
          console.log(
            "CLIENT: [processEventSource] SSE Message received (onmessage triggered)",
          );
          try {
            debugLog(
              "Raw SSE event data:",
              event.data.substring(0, 100) +
                (event.data.length > 100 ? "..." : ""),
            );
            // Use the updated shared StreamEventData type
            const eventData = JSON.parse(event.data) as StreamEventData;
            debugLog("Parsed event type:", eventData.type);

            switch (eventData.type) {
              case "agent_start":
                if (eventData.agent) {
                  const agentDomain = eventData.agent as MessageDomain;
                  console.log(
                    `CLIENT: Agent domain '${agentDomain}' starting.`,
                  );

                  // Register agent start, await first token for message creation
                  currentAgentMessageIdsRef.current[agentDomain] = "";
                  console.log(
                    `CLIENT: Agent domain ${agentDomain} started. Awaiting first token to create message bubble.`,
                  );
                }
                break;

              case "token":
                console.log(
                  "CLIENT: Received token event:",
                  eventData.agent ? `Agent: ${eventData.agent}` : "No agent",
                  eventData.content
                    ? `Content (first 30 chars): ${eventData.content.substring(0, 30)}...`
                    : "No content",
                );
                if (eventData.agent && eventData.content) {
                  const agentDomain = eventData.agent as MessageDomain;
                  const tokenContent = eventData.content;
                  debugLog(
                    `Token from \"${agentDomain}\": \"${tokenContent.substring(0, 30)}${tokenContent.length > 30 ? "..." : ""}\"`,
                  );

                  if (agentDomain === "planner") {
                    console.log(
                      `CLIENT: Received token for 'planner' domain. Content: ${tokenContent.substring(0, 50)}...`,
                    );
                  }

                  setMessages((prevMessages) => {
                    console.log(
                      `[Token Handler] Processing token for domain: ${agentDomain}. Current message ID in ref: ${currentAgentMessageIdsRef.current[agentDomain] || "none"}`,
                    );
                    let updatedMessages = [...prevMessages];
                    const domain = agentDomain;
                    console.log(
                      `[Token Handler] Using domain from event: "${domain}"`,
                    );
                    const messageName = domain === "Athlea" ? "Athlea" : domain;

                    const isPlanner = domain === "planner";
                    const isReasoning = domain === "reasoning";
                    let messageId = currentAgentMessageIdsRef.current[domain];
                    let existingMsgIndex = -1;

                    // Try to find the message using the ID from the ref *before* modifying updatedMessages
                    if (messageId) {
                      existingMsgIndex = updatedMessages.findIndex(
                        (msg) => msg.id === messageId,
                      );
                    }

                    if (hasReceivedFirstTokenThisTurn.current === false) {
                      console.log(
                        "[Token Handler] First token of the turn received. Removing typing indicator(s).",
                      );
                      updatedMessages = updatedMessages.filter(
                        (msg) =>
                          !(
                            typeof msg.id === "string" &&
                            msg.id.startsWith("typing-indicator-")
                          ) && msg.id !== "typing-indicator",
                      );
                      // Re-calculate index after filtering if needed, although it might be safer to filter *after* message creation/update logic
                      if (messageId) {
                        existingMsgIndex = updatedMessages.findIndex(
                          (msg) => msg.id === messageId,
                        );
                      }
                      hasReceivedFirstTokenThisTurn.current = true;
                    }

                    // --- Revised Logic ---
                    if (messageId && existingMsgIndex > -1) {
                      // --- Case 1: Message ID exists in ref AND was found in prevMessages ---
                      console.log(
                        `[Token Handler - Update] Updating existing message ${messageId} for domain ${domain}.`,
                      );
                      const isFirstUpdate =
                        updatedMessages[existingMsgIndex].content === ""; // Check if it was just an empty shell

                      updatedMessages[existingMsgIndex] = {
                        ...updatedMessages[existingMsgIndex],
                        ...(isFirstUpdate && {
                          // Apply name/domain only on first actual content update if needed
                          name: messageName,
                          domain: domain,
                          isLoading: false, // Ensure loading is off
                        }),
                        content:
                          updatedMessages[existingMsgIndex].content +
                          tokenContent,
                        lastUpdated: Date.now(),
                        metadata: {
                          ...(updatedMessages[existingMsgIndex].metadata || {}),
                          hideUntilContent: false,
                        },
                      };

                      // Planner/Reasoning specific logic for EXISTING messages
                      if (
                        isPlanner &&
                        isFirstUpdate &&
                        currentTurnPlannerMsgIdRef.current !== messageId
                      ) {
                        console.log(
                          "[Token Handler - Update] Tracking current turn planner message ID:",
                          messageId,
                        );
                        currentTurnPlannerMsgIdRef.current = messageId;
                        setCollapsedMessages((prevCollapsed) => {
                          const newCollapsed = { ...prevCollapsed };
                          delete newCollapsed[messageId];
                          return newCollapsed;
                        });
                      }
                    } else if (messageId && existingMsgIndex === -1) {
                      // --- Case 2: Message ID exists in ref BUT wasn't found in prevMessages (or after filtering) ---
                      // Create the message using the ID from the ref.
                      console.log(
                        `[Token Handler - Create (Delayed)] First effective token for ${domain}. Creating message bubble with stored ID: ${messageId}`,
                      );
                      const newMessage: Message = {
                        id: messageId, // Use the ID from the ref
                        type: "ai",
                        domain: domain,
                        name: messageName,
                        content: tokenContent, // Start with the current token's content
                        isLoading: false,
                        lastUpdated: Date.now(),
                        metadata: {
                          isCollapsed:
                            isPlanner || isReasoning ? false : undefined,
                          hideUntilContent: false,
                        },
                      };
                      updatedMessages.push(newMessage);
                      // No need to update the ref here, it already holds the correct ID.

                      // Planner specific logic for NEW messages created this way
                      if (isPlanner) {
                        console.log(
                          "[Token Handler - Create (Delayed)] Tracking current turn planner message ID:",
                          messageId,
                        );
                        currentTurnPlannerMsgIdRef.current = messageId;
                        setCollapsedMessages((prevCollapsed) => {
                          const newCollapsed = { ...prevCollapsed };
                          delete newCollapsed[messageId];
                          return newCollapsed;
                        });
                      }
                    } else if (!messageId) {
                      // --- Case 3: No Message ID in ref ---
                      // This is the *very first* token received for this agent in this turn.
                      messageId = `msg-${domain}-${Date.now()}`;
                      console.log(
                        `[Token Handler - Create (Initial)] First token for ${domain}. Creating new message bubble with ID: ${messageId}`,
                      );
                      currentAgentMessageIdsRef.current[domain] = messageId; // Store the new ID
                      const newMessage: Message = {
                        id: messageId,
                        type: "ai",
                        domain: domain,
                        name: messageName,
                        content: tokenContent,
                        isLoading: false,
                        lastUpdated: Date.now(),
                        metadata: {
                          isCollapsed:
                            isPlanner || isReasoning ? false : undefined,
                          hideUntilContent: false,
                        },
                      };
                      updatedMessages.push(newMessage);

                      // Planner specific logic for NEW messages created initially
                      if (isPlanner) {
                        console.log(
                          "[Token Handler - Create (Initial)] Tracking current turn planner message ID:",
                          messageId,
                        );
                        currentTurnPlannerMsgIdRef.current = messageId;
                        setCollapsedMessages((prevCollapsed) => {
                          const newCollapsed = { ...prevCollapsed };
                          delete newCollapsed[messageId];
                          return newCollapsed;
                        });
                      }
                    } else {
                      // Should not happen with the logic above, but add a safeguard log
                      console.error(
                        `[Token Handler - Error] Unexpected state: messageId=${messageId}, existingMsgIndex=${existingMsgIndex}`,
                      );
                    }

                    // Collapse logic (can run after message creation/update)
                    if (!isPlanner && currentTurnPlannerMsgIdRef.current) {
                      console.log(
                        `[Token Handler] Coach (${agentDomain}) token received. Collapsing planner message: ${currentTurnPlannerMsgIdRef.current}`,
                      );
                      const plannerMsgIdToCollapse =
                        currentTurnPlannerMsgIdRef.current;
                      setCollapsedMessages((prev) => ({
                        ...prev,
                        [plannerMsgIdToCollapse]: true,
                      }));
                      currentTurnPlannerMsgIdRef.current = null; // Clear after collapsing
                    }

                    if (!isReasoning) {
                      const reasoningMsgsToCollapse = updatedMessages.filter(
                        // Check potentially updated list
                        (msg) =>
                          msg.domain === "reasoning" &&
                          !collapsedMessages[msg.id], // Only collapse if not already set
                      );
                      if (reasoningMsgsToCollapse.length > 0) {
                        console.log(
                          `[Token Handler] Non-reasoning agent (${agentDomain}) token received. Collapsing ${reasoningMsgsToCollapse.length} reasoning messages.`,
                        );
                        setCollapsedMessages((prev) => {
                          const updatedCollapsed = { ...prev };
                          reasoningMsgsToCollapse.forEach((msg) => {
                            updatedCollapsed[msg.id] = true;
                          });
                          return updatedCollapsed;
                        });
                      }
                    }

                    lastStreamingAgentRef.current = domain;
                    setIsLoading(false);
                    return updatedMessages;
                  });
                } else {
                  debugLog(
                    "Received token with missing agent or content",
                    eventData,
                  );
                }
                break;

              case "tool_result": {
                const { agent, toolName, content } = eventData;
                const domain = (agent || "general") as MessageDomain;
                const timestamp = Date.now();
                const toolCallId = `${toolName}-${timestamp}`;
                const contentAsAny = content as any;
                const extractedContent =
                  contentAsAny &&
                  typeof contentAsAny === "object" &&
                  contentAsAny.kwargs &&
                  contentAsAny.kwargs.content
                    ? contentAsAny.kwargs.content
                    : contentAsAny;

                setToolCalls((prev) => [
                  ...prev,
                  {
                    id: toolCallId,
                    domain,
                    toolName: toolName || "unknown_tool",
                    timestamp,
                    content: extractedContent,
                  },
                ]);
                break;
              }

              case "need_input":
                debugLog("Received need_input event:", eventData);
                console.log(
                  "CLIENT: Received need_input event:",
                  "Prompt:",
                  eventData.prompt || "None",
                  "Field:",
                  eventData.field || "None",
                );
                setIsLoading(false);
                setIsStreaming(false);
                newEventSource.close();
                currentAgentMessageIdsRef.current = {};
                setMessages((prev) => {
                  console.log(
                    "CLIENT: Cleaning up typing indicators after need_input",
                  );
                  return prev.filter(
                    (msg) =>
                      msg.id !== "typing-indicator" &&
                      !(
                        typeof msg.id === "string" &&
                        msg.id.startsWith("typing-indicator-")
                      ),
                  );
                });
                break;

              case "complete":
              case "done":
                // ... (existing done/complete logic, maybe clear sidebarData?) ...
                // Optional: Clear sidebar data when flow completes?
                // setSidebarData(null);

                setMessages((prevMessages) => {
                  const finalMessages = prevMessages.filter(
                    (msg) =>
                      msg.id !== "typing-indicator" &&
                      !(
                        typeof msg.id === "string" &&
                        msg.id.startsWith("typing-indicator-")
                      ),
                  );

                  // Restore Keyword fetching logic
                  finalMessages.forEach((msg) => {
                    if (
                      msg.type === "ai" &&
                      msg.content &&
                      msg.content.trim() !== "" &&
                      !msg.keywords &&
                      msg.domain !== "reasoning" &&
                      msg.domain !== "planner"
                    ) {
                      console.log(
                        `[SSE Done Handler] Triggering keyword fetch for message: ${msg.id}`,
                      );
                      // Pass necessary state/functions to fetchKeywords
                      fetchKeywords(
                        msg.id,
                        msg.content,
                        threadIdFromUrl, // Assuming threadIdFromUrl is accessible here
                        finalMessages, // Pass the filtered messages
                        setMessages, // Pass the state setter
                        processBatchKeywords, // Pass the callback
                      );
                    }
                  });

                  return finalMessages;
                });

                hasReceivedFirstTokenThisTurn.current = false;
                currentAgentMessageIdsRef.current = {};
                currentTurnPlannerMsgIdRef.current = null;

                setIsLoading(false);
                setIsStreaming(false);
                try {
                  newEventSource.close();
                  console.log(
                    "CLIENT: 'done'/'complete' received, closed EventSource.",
                  );
                } catch (e) {
                  console.warn(
                    "CLIENT: Error closing EventSource in 'done'/'complete' handler:",
                    e,
                  );
                }
                lastStreamingAgentRef.current = null;
                break;

              case "error":
                // ... (existing error logic) ...
                console.error(
                  "CLIENT: Received error from server:",
                  eventData.message,
                );
                setIsLoading(false);
                setIsStreaming(false);
                newEventSource.close();
                setMessages((prev) => {
                  const indicator = prev.find(
                    (msg) => msg.id === "typing-indicator",
                  );
                  let messagesWithoutIndicator = prev;
                  if (
                    indicator &&
                    indicator.content &&
                    indicator.content.length > 0
                  ) {
                    messagesWithoutIndicator = prev.filter(
                      (msg) => msg.id !== "typing-indicator",
                    );
                  }
                  return [
                    ...messagesWithoutIndicator,
                    {
                      id: `err-${Date.now()}`,
                      type: "system",
                      domain: "general",
                      content: `Error: ${eventData.message || "Unknown server error"}`,
                      lastUpdated: Date.now(),
                    },
                  ];
                });
                currentAgentMessageIdsRef.current = {};
                break;

              case "sidebar_update":
                console.log(
                  "CLIENT: Received sidebar_update event:",
                  eventData.sidebarData,
                );
                if (eventData.sidebarData) {
                  setSidebarData(eventData.sidebarData as SidebarStateData);
                }
                break;

              // case "history_loaded": // Likely not needed if starting fresh
              //   break;

              default:
                console.warn(
                  "CLIENT: Received unknown SSE event type:",
                  (eventData as any).type,
                );
            }
          } catch (e) {
            // ... (existing parsing error logic) ...
            console.error(
              "CLIENT: Error parsing SSE message:",
              e,
              "Raw data:",
              event.data,
            );
            setIsLoading(false);
            setIsStreaming(false);
            setMessages((prev) => {
              const indicator = prev.find(
                (msg) => msg.id === "typing-indicator",
              );
              if (
                indicator &&
                indicator.content &&
                indicator.content.length > 0
              ) {
                return prev.filter((msg) => msg.id !== "typing-indicator");
              } else {
                return prev;
              }
            });
          }
        };
      } catch (error) {
        // ... (existing connection error logic) ...
        console.error("CLIENT: Failed to establish SSE connection:", error);
        setIsLoading(false);
        setIsStreaming(false);
        setMessages((prev) => {
          const indicator = prev.find((msg) => msg.id === "typing-indicator");
          let messagesWithoutIndicator = prev;
          if (indicator && indicator.content && indicator.content.length > 0) {
            messagesWithoutIndicator = prev.filter(
              (msg) => msg.id !== "typing-indicator",
            );
          }
          return [
            ...messagesWithoutIndicator,
            {
              id: `err-connect-${Date.now()}`,
              type: "system",
              domain: "general",
              content: "Sorry, failed to establish a connection.",
              lastUpdated: Date.now(),
            },
          ];
        });
      }
    },
    [
      setMessages,
      setIsStreaming,
      setToolCalls,
      setIsLoading,
      setSidebarData,
      processBatchKeywords,
    ],
  );

  // Cleanup eventSource on unmount
  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        console.log("Closing EventSource connection on component unmount.");
        eventSourceRef.current.close();
      }
    };
  }, []);

  const handleSubmit = useCallback(
    async (messageToSend: string, isInitialSubmit = false) => {
      console.log(
        `CLIENT: [handleSubmit] Called with message: '${messageToSend}', isInitialSubmit: ${isInitialSubmit}`,
      );

      // --- Prevent duplicate initial call ---
      if (isInitialSubmit) {
        if (initialCallAttemptedRef.current) {
          console.log(
            "CLIENT: [handleSubmit] Initial call already attempted, exiting.",
          );
          return;
        }
        console.log("CLIENT: [handleSubmit] Processing initial call attempt.");
        initialCallAttemptedRef.current = true;
      }

      // Prevent submission if already loading/streaming
      if (isLoading || isStreaming || submissionInProgressRef.current) {
        console.log(
          "CLIENT: Submission already in progress or loading, ignoring call.",
        );
        return;
      }
      submissionInProgressRef.current = true;

      if (!messageToSend || !threadIdFromUrl || !userId) {
        // ... (error handling) ...
        console.error("CLIENT Error: Missing input, threadId, or userId.");
        setMessages((prev) => [
          ...prev.filter((msg) => msg.type !== "system"),
          {
            id: `error-${Date.now()}`,
            type: "system",
            content:
              "An error occurred while communicating with the server. Please try again.",
            lastUpdated: Date.now(),
            domain: "general", // Add domain for consistency
          },
        ]);
        submissionInProgressRef.current = false;
        return;
      }

      setIsLoading(true);
      hasReceivedFirstTokenThisTurn.current = false;
      lastStreamingAgentRef.current = null;
      // Clear system messages *before* adding user message or indicator
      setMessages((prev) => prev.filter((msg) => msg.type !== "system"));

      // Add user message locally (only if not initial submit, or if it's a non-default initial message)
      if (
        !isInitialSubmit ||
        (isInitialSubmit && messageToSend !== "start_onboarding")
      ) {
        const userMessage: Message = {
          id: `user-${Date.now()}`,
          type: "human",
          content: messageToSend,
          lastUpdated: Date.now(),
          domain: "general",
        };
        setMessages((prev) => [...prev, userMessage]);
      } else if (isInitialSubmit) {
        console.log(
          "CLIENT: Initial submit with default trigger - initiating onboarding.",
        );
        // Optionally add a different placeholder message for onboarding start
        setMessages((prev) => [
          ...prev,
          {
            id: `system-onboarding-start-${Date.now()}`,
            type: "system",
            content: "Starting your onboarding process...",
            lastUpdated: Date.now(),
            domain: "general",
          },
        ]);
      }

      // Add typing indicator *after* potentially adding user message
      const uniqueTypingId = `typing-indicator-${Date.now()}`;
      // Onboarding always uses Athlea domain
      const initialIndicatorDomain: MessageDomain = "Athlea";
      const initialIndicatorName = "Athlea";
      console.log(
        `[handleSubmit] Creating typing indicator ${uniqueTypingId} with domain: ${initialIndicatorDomain}`,
      );
      const typingIndicatorMessage: Message = {
        id: uniqueTypingId,
        type: "ai",
        domain: initialIndicatorDomain,
        name: initialIndicatorName,
        content: "",
        isLoading: true,
        lastUpdated: Date.now(),
        metadata: { hideUntilContent: true },
      };
      setMessages((prev) => [...prev, typingIndicatorMessage]);
      currentAgentMessageIdsRef.current = {};

      try {
        // Determine if it's an onboarding flow (for endpoint selection)
        const isDirectOnboarding =
          searchParams?.get("direct_onboarding") === "true";
        const isResumedOnboarding = searchParams?.get("onboarding") === "true";
        const isOnboardingFlow = isDirectOnboarding || isResumedOnboarding;

        console.log("--- handleSubmit Routing Check ---");
        console.log(`isOnboardingFlow: ${isOnboardingFlow}`);

        // Always use EventSource now, choose endpoint based on flow type
        let endpointUrl = "";
        if (isOnboardingFlow) {
          endpointUrl = "/api/onboarding-python"; // Use the Python onboarding proxy endpoint
          console.log(
            `CLIENT: [handleSubmit] Onboarding flow detected - using GET to ${endpointUrl}`,
          );
        } else {
          endpointUrl = "/api/coaching"; // Use the coaching SSE endpoint
          console.log(
            `CLIENT: [handleSubmit] Regular chat message - using GET to ${endpointUrl}`,
          );
        }

        const params = new URLSearchParams({
          message: messageToSend, // Send the message that triggered the submit
          threadId: threadIdFromUrl!,
          userId: userId!,
        });

        // Add singleCoach param only if NOT an onboarding flow
        if (!isOnboardingFlow && selectedCoach) {
          params.set("singleCoach", selectedCoach);
          console.log(
            `CLIENT: Adding singleCoach=${selectedCoach} to message params.`,
          );
        }

        const fullUrl = endpointUrl + `?${params.toString()}`;

        console.log(
          `CLIENT: [handleSubmit] Connecting via EventSource (GET)... URL: ${fullUrl}`,
        );
        await processEventSource(fullUrl);
      } catch (error) {
        console.error("CLIENT Error during handleSubmit:", error);
        setMessages((prev) => [
          // Ensure indicator is removed on error
          ...prev.filter((msg) => !msg.id?.startsWith("typing-indicator")),
          // Add error message
          {
            id: `error-submit-${Date.now()}`,
            type: "system",
            content:
              "An error occurred while communicating with the server. Please try again.",
            lastUpdated: Date.now(),
            domain: "general", // Add domain
          },
        ]);
        setIsLoading(false);
        setIsStreaming(false);
      } finally {
        submissionInProgressRef.current = false;
      }
    },
    // Updated dependencies
    [
      threadIdFromUrl,
      userId,
      selectedCoach,
      setIsLoading,
      setIsStreaming,
      setMessages,
      processEventSource, // Keep for GET requests
      searchParams,
      dispatch, // Assuming dispatch is used inside handleSubmit
    ],
  );

  // Add back initial message handling, particularly for onboarding
  useEffect(() => {
    let timerId: NodeJS.Timeout | null = null; // Declare timerId here

    // Log dependencies at the start of the effect
    console.log("--- Initial Message useEffect Check ---");
    console.log(`Current needInitialMessages: ${needInitialMessages}`);
    console.log(`Current userId: ${userId}`);
    console.log(`Current threadIdFromUrl: ${threadIdFromUrl}`);
    console.log(`Current initialMessage: ${initialMessage}`);
    console.log(
      `Current searchParams.get("direct_onboarding"): ${searchParams?.get("direct_onboarding")}`,
    );
    console.log(
      `Current searchParams.get("onboarding"): ${searchParams?.get("onboarding")}`,
    );
    console.log(
      `Initial call attempted ref: ${initialCallAttemptedRef.current}`,
    ); // Log initial call attempt status

    if (
      needInitialMessages &&
      userId &&
      threadIdFromUrl &&
      !initialCallAttemptedRef.current
    ) {
      // Added check for initialCallAttemptedRef
      console.log(
        "CLIENT: [Initial Effect] Conditions MET. Preparing to call handleSubmit.", // Added identifier
        `Direct Onboarding: ${searchParams?.get("direct_onboarding") === "true"}`,
        `Resumed Onboarding: ${searchParams?.get("onboarding") === "true"}`,
        initialMessage
          ? `With message: ${initialMessage}`
          : "With default trigger message.",
      );

      // Use initialMessage if available, otherwise send a default trigger
      const messageToSend = initialMessage || "start_onboarding"; // Use a specific trigger
      console.log(
        `CLIENT: [Initial Effect] Calling handleSubmit with message: '${messageToSend}', isInitialSubmit: true`,
      ); // Added identifier
      // Wrap handleSubmit in setTimeout to defer execution slightly
      timerId = setTimeout(() => {
        // Assign to outer variable
        console.log(
          "CLIENT: [Initial Effect] Executing delayed handleSubmit call",
        );
        handleSubmit(messageToSend, true);
      }, 100); // Added a slight delay (e.g., 100ms)

      // Reset the flag after scheduling the call
      console.log(
        "CLIENT: [Initial Effect] Setting needInitialMessages to false.",
      );
      dispatch(setNeedInitialMessages(false));
    } else {
      console.log(
        "CLIENT: [Initial Effect] Initial message conditions NOT met or already attempted.",
        `needInitialMessages: ${needInitialMessages}`,
        `userId: ${Boolean(userId)}`,
        `threadIdFromUrl: ${Boolean(threadIdFromUrl)}`,
        `initialCallAttemptedRef: ${initialCallAttemptedRef.current}`,
      ); // Added identifier and detailed reasons
    }

    // Cleanup function: ONLY reset submissionInProgressRef.
    // DO NOT clear the timerId here.
    return () => {
      console.log(
        "CLIENT: [Initial Effect Cleanup] Running cleanup. Resetting submissionInProgressRef.", // Modify log message
      );
      // if (timerId) clearTimeout(timerId); // Keep this commented out or removed
      submissionInProgressRef.current = false; // Reset submission lock on cleanup
    };
  }, [
    needInitialMessages,
    userId,
    threadIdFromUrl,
    searchParams,
    initialMessage,
    handleSubmit,
    dispatch,
  ]);

  // ... (scroll effects remain) ...
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  useEffect(() => {
    toolCallsEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [toolCalls]);

  // Remove plan name update logic or adapt if needed
  // useEffect(() => { updatePlanNameThunk... }, ...);

  // Restore profile update check effect
  useEffect(() => {
    if (!userId || messages.length === 0) {
      return;
    }

    const lastMessage = messages[messages.length - 1];

    // Check only for the latest *human* message that isn't the typing indicator
    if (lastMessage.type === "human" && lastMessage.id !== "typing-indicator") {
      console.log(
        "CLIENT: New human message detected. Checking for profile updates...",
        `Message ID: ${lastMessage.id}`,
      );

      const checkForProfileUpdate = () => {
        fetch("/api/update-profile", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            userId: userId,
            message: lastMessage.content,
          }),
        })
          .then((response) => {
            if (!response.ok) {
              console.error(
                "CLIENT: Error response from /api/update-profile:",
                response.status,
              );
              return null;
            }
            return response.json();
          })
          .then((data) => {
            if (!data) return;

            console.log("CLIENT: /api/update-profile response:", data);

            if (data.updated && data.systemMessage) {
              // Avoid adding system messages starting with common errors
              if (
                !data.systemMessage?.startsWith(
                  "Your Connection error profile was updated with",
                ) &&
                !data.systemMessage?.startsWith("Error updating profile")
              ) {
                console.log(
                  "CLIENT: Profile updated. Adding system message to chat.",
                );
                const systemUpdateMessage: Message = {
                  id: `system-update-${Date.now()}`,
                  type: "system",
                  content: data.systemMessage,
                  lastUpdated: Date.now(),
                  domain: "general",
                };
                // Use functional update to avoid stale closure issues
                setMessages((prevMessages) => [
                  ...prevMessages,
                  systemUpdateMessage,
                ]);
              } else {
                console.warn(
                  "CLIENT: Suppressed potentially malformed system message from API:",
                  data.systemMessage,
                );
              }
            } else {
              console.log(
                "CLIENT: Profile update check complete, no update needed or no system message.",
              );
            }
          })
          .catch((error) => {
            console.error("CLIENT: Error calling /api/update-profile:", error);
          });
      };

      // Debounce or throttle this if it causes issues, but for now, call directly
      checkForProfileUpdate();
    }
  }, [messages, userId, setMessages]); // Depend only on messages and userId

  // Keyword processing logic (keep if still relevant)
  useEffect(() => {
    const allKeywords = messages
      .filter((msg) => msg.keywords && msg.keywords.length > 0)
      .flatMap((msg) => msg.keywords || []);
    if (allKeywords.length > 0) {
      const uniqueKeywords = [...new Set(allKeywords)];
      processBatchKeywords(uniqueKeywords);
    }
  }, [messages, processBatchKeywords]);

  // Determine if it's an onboarding session
  const isOnboarding =
    searchParams?.get("onboarding") === "true" ||
    searchParams?.get("direct_onboarding") === "true";

  // Add back effect to consolidate reasoning messages
  useEffect(() => {
    if (messages.length === 0) return;

    let processedMessages = [...messages];
    const reasoningMessages = processedMessages.filter(
      (msg) => msg.domain === "reasoning",
    );

    if (reasoningMessages.length > 1) {
      console.log(
        `[Message Group Effect] Found ${reasoningMessages.length} reasoning messages. Keeping only the latest one.`,
      );

      const latestReasoningMsg =
        reasoningMessages[reasoningMessages.length - 1];

      // Filter out all reasoning messages except the latest one
      processedMessages = processedMessages.filter(
        (msg) => msg.domain !== "reasoning" || msg.id === latestReasoningMsg.id,
      );

      // Only update state if messages actually changed
      if (processedMessages.length !== messages.length) {
        console.log(
          `[Message Group Effect] Reduced message count from ${messages.length} to ${processedMessages.length}`,
        );
        setMessages(processedMessages);
      }
    }
  }, [messages]); // Rerun whenever messages state updates

  return (
    <div>
      <Card className="border-0 bg-[#F9FBFC] shadow-none">
        {/* Panel Size Indicator */}
        <div className="absolute top-4 right-4 z-50 flex items-center gap-2">
          <div className="bg-gray-800 text-white px-2 py-1 rounded text-xs font-mono">
            {Math.round(panelSizes[0])}% / {Math.round(panelSizes[1])}%
          </div>
          <Button
            size="sm"
            variant="outline"
            onClick={() => setPanelSizes([60, 40])}
            className="h-6 w-6 p-0"
            title="Reset panel sizes (Ctrl/Cmd + R)"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                >
                  <HelpCircle className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <div className="max-w-xs">
                  <p className="font-medium mb-1">Resizable Panels</p>
                  <ul className="text-xs space-y-1">
                    <li>• Drag the handle to resize panels</li>
                    <li>• Use Ctrl/Cmd + R to reset</li>
                    <li>• Panels remember your preference</li>
                    <li>• Keyboard accessible</li>
                  </ul>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        <ResizablePanelGroup 
          direction="horizontal" 
          className="h-[calc(100vh-4rem)]"
          onLayout={(sizes) => setPanelSizes(sizes)}
          autoSaveId="onboarding-panels"
        >
          <ResizablePanel 
            defaultSize={panelSizes[0]} 
            minSize={40}
            className="flex flex-col"
            aria-label="Main content area"
          >
            <div className="flex flex-1 flex-col">
              <div className="scrollbar-hide flex-1 overflow-y-auto">
                <CardContent className="px-4 pt-4">
                  {messages.map((message) => {
                    if (message.type === "system") {
                      return (
                        <div key={message.id} className="mb-4">
                          <SystemMessage
                            message={message.content}
                            onRemove={async (domain, key) => {
                              // ... (onRemove logic)
                            }}
                          />
                        </div>
                      );
                    }
                    // Use AIMessage for planner messages as well, handled by domain prop
                    if (message.domain === "planner") {
                      return <AIMessage key={message.id} message={message} />;
                    }
                    // Use AIMessage for reasoning messages
                    if (message.domain === "reasoning") {
                      const isCollapsed = collapsedMessages[message.id] ?? false;
                      return (
                        <AIMessage
                          key={message.id}
                          message={message}
                          onToggleCollapse={toggleCollapse}
                        />
                      );
                    }
                    if (
                      (message.type === "human" ||
                        (message.type === "ai" &&
                          !isPlannerOrReasoningDomain(message.domain))) &&
                      !(
                        typeof message.id === "string" &&
                        message.id.startsWith("typing-indicator-")
                      )
                    ) {
                      if (message.type === "human") {
                        return <UserMessage key={message.id} message={message} />;
                      } else {
                        return <AIMessage key={message.id} message={message} />;
                      }
                    }
                    return null;
                  })}
                  <div ref={messagesEndRef} />
                </CardContent>
              </div>
              <div className="p-4">
                <UserInput
                  onSubmitRegularMessage={handleSubmit}
                  isLoading={isLoading}
                  isStreaming={isStreaming}
                  suggestions={sidebarData?.sportSuggestions || []}
                />
              </div>
            </div>
          </ResizablePanel>

          <ResizableHandle 
            withHandle 
            className="bg-gray-200 hover:bg-gray-300 transition-colors focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2" 
            aria-label="Resize panels"
          />
          
          <ResizablePanel 
            defaultSize={panelSizes[1]} 
            minSize={30} 
            maxSize={60}
            className="bg-white"
            aria-label="Onboarding sidebar"
          >
            <OnboardingSidebar sidebarData={sidebarData} />
          </ResizablePanel>
        </ResizablePanelGroup>
      </Card>

      <PlanGenerationSidebar />
    </div>
  );
}
