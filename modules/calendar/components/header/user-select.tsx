import { useCalendar } from "@/modules/calendar/contexts/calendar-context";

import { Avatar, AvatarImage, AvatarFallback } from "@/components/uiCalendar/avatar";
import {
  Select,
  SelectItem,
  SelectContent,
  SelectTrigger,
  SelectValue,
} from "@/components/uiCalendar/select";
import { AvatarGroup } from "@/components/uiCalendar/avatar-group";

export function UserSelect() {
  const { users, selectedUserId, filterEventsBySelectedUser } = useCalendar();

  return (
    <Select value={selectedUserId!} onValueChange={filterEventsBySelectedUser}>
      <SelectTrigger className="w-full flex-row">
        <SelectValue placeholder="Select a user" />
      </SelectTrigger>
      <SelectContent align="end" >
        <SelectItem value="all" className="flex-row" >
          <AvatarGroup className="mx-2 flex  items-center" max={3}>
            {users.map((user) => (
              <Avatar key={user.id} className="size-6 text-xxs">
                <AvatarImage
                  src={user?.picturePath ?? undefined}
                  alt={user?.name}
                />
                <AvatarFallback className="text-xxs">
                  {/* {user?.name[0]} */}
                  johndoe
                </AvatarFallback>
              </Avatar>
            ))}
          </AvatarGroup>
          All
        </SelectItem>

        {/* {users.map((user) => (
          <SelectItem key={user.id} value={user.id} className="flex-1 cursor-pointer">
            <div className="flex items-center gap-2">
              <Avatar key={user.id} className="size-6">
                <AvatarImage
                  src={user?.picturePath ?? undefined}
                  alt={user?.name}
                />
                <AvatarFallback className="text-xxs">
                  {user?.name[0]}
                  johndoe
                </AvatarFallback>
              </Avatar>

              <p className="truncate">{user?.name}</p>
            </div>
          </SelectItem>
        ))} */}
      </SelectContent>
    </Select>
  );
}
