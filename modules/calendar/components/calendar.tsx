"use client";
import React, { useEffect, useState, useMemo } from "react";
import {CalendarProvider} from "@/modules/calendar/contexts/calendar-context";
import {CalendarHeader} from "@/modules/calendar/components/header/calendar-header";
import {CalendarBody} from "@/modules/calendar/components/calendar-body";

import {EventUpdate<PERSON><PERSON><PERSON>} from "@/modules/calendar/components/event-update-handler";
import {DragDropProvider} from "@/modules/calendar/contexts/drag-drop-context";
import {getEvents, getUsers} from "@/modules/calendar/requests";
import { useSelector, useDispatch } from "react-redux";
import { loadTasks } from "@/store/slices/calendarSlice";
import { RootState } from "@/store/store";
import dayjs from "dayjs";
import { COLORS } from "@/modules/calendar/constants";
import type { IEvent } from "@/modules/calendar/interfaces";
import CalenderDetailsSidebar from "@/components/Calender/CalenderDetailsSidebar";

export function Calendar() {
    const dispatch = useDispatch();
    const { tasks } = useSelector((state: RootState) => state.calendar);
    const { userData } = useSelector((state: RootState) => state.user);
    // For demo, use a static user (replace with real user logic as needed)
  

    useEffect(() => {
        if (userData?.user_id && (!tasks || Object.keys(tasks).length === 0)) {
            dispatch(loadTasks(userData.user_id));
        }
    }, [userData, dispatch, tasks]);

    // Map Redux tasks to IEvent[] for the calendar
    const events: IEvent[] = useMemo(() => {
        if (!tasks) return [];
        let eventList: IEvent[] = [];
        Object.entries(tasks).forEach(([date, calendarData]) => {
            if (!calendarData.tasks) return;
            calendarData.tasks.forEach((task, idx) => {
                let parsedTask: any = null;
                if (typeof task.text === "string") {
                    try {
                        parsedTask = JSON.parse(task.text);
                    } catch {
                        parsedTask = null;
                    }
                } else if (typeof task.text === "object" && task.text !== null) {
                    parsedTask = task.text;
                }
                // Fallbacks
                const title = parsedTask?.activity_name || parsedTask?.session_type || "Task";
                // Use date and time if available, else fallback to date only
                const startDate = dayjs(date + (task.time ? `T${task.time}` : "")).toISOString();
                // For now, set endDate = startDate + 1 hour (customize as needed)
                const endDate = dayjs(startDate).add(1, "hour").toISOString();
                eventList.push({
                    id: typeof task.id === "number" ? task.id : idx,
                    startDate,
                    endDate,
                    title,
                    color: COLORS[idx % COLORS.length],
                    description: parsedTask?.rationale || "",
                    user: userData,
                });
            });
        });
        return eventList;
    }, [tasks, userData]);

    // Remove local loading state, use Redux state if needed
    // if (loading) return <div>Loading calendar...</div>;

    return (
        <DragDropProvider>
            <CalendarProvider events={[]} users={[userData]} view="month">
                <div className="w-full border rounded-xl">
                    <EventUpdateHandler />
                    <CalendarHeader/>
                    <CalendarBody/>
                </div>
            </CalendarProvider>
        </DragDropProvider>
    );
}