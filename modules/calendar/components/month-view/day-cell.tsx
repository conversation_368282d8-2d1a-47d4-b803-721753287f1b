"use client";

import {SetStateAction, useMemo, useRef, useState} from "react";
import {isToday, startOfDay} from "date-fns";
import {motion} from "framer-motion";

import {EventBullet} from "@/modules/calendar/components/month-view/event-bullet";
import {MonthEventBadge} from "@/modules/calendar/components/month-view/month-event-badge";

import {getMonthCellEvents} from "@/modules/calendar/helpers";
import {staggerContainer, transition} from "@/modules/calendar/animations";

import type {ICalendarCell, IEvent} from "@/modules/calendar/interfaces";
import {cn} from "@/lib/utils";
import {cva} from "class-variance-authority";
import {DroppableArea} from "@/modules/calendar/components/dnd/droppable-area";
import {EventListDialog} from "@/modules/calendar/components/dialogs/events-list-dialog";
import CalenderDetailsSidebar from "@/components/Calender/CalenderDetailsSidebar";
import React from "react";
import dayjs from "dayjs";

interface IProps {
    cell: ICalendarCell;
    events: IEvent[];
    eventPositions: Record<string, number>;
}

const MAX_VISIBLE_EVENTS = 3;

export const dayCellVariants = cva(
    "text-white", {
        variants: {
            color: {
                blue: "bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-400 ",
                green: "bg-green-600 dark:bg-green-500 hover:bg-green-700 dark:hover:bg-green-400",
                red: "bg-red-600 dark:bg-red-500 hover:bg-red-700 dark:hover:bg-red-400",
                yellow: "bg-yellow-600 dark:bg-yellow-500 hover:bg-yellow-700 dark:hover:bg-yellow-400",
                purple: "bg-purple-600 dark:bg-purple-500 hover:bg-purple-700 dark:hover:bg-purple-400",
                orange: "bg-orange-600 dark:bg-orange-500 hover:bg-orange-700 dark:hover:bg-orange-400",
                gray: "bg-gray-600 dark:bg-gray-500 hover:bg-gray-700 dark:hover:bg-gray-400",
            },
        },
        defaultVariants: {
            color: "blue",
        },
    }
)


export function DayCell({cell, events, eventPositions}: IProps) {
    const {day, currentMonth, date} = cell;

    const cellEvents = useMemo(
        () => getMonthCellEvents(date, events, eventPositions),
        [date, events, eventPositions]
    );
    const isSunday = date.getDay() === 0;
    const timeMenuRef = useRef<HTMLDivElement>(null);
    const [selectedDate, setSelectedDate] = useState<any[]>([]);
    const [openDate, setOpenDate] = useState<number | null>(null);
    const timeArr = [
        "0:00",
        "1:00",
        "2:00",
        "3:00",
        "4:00",
        "5:00",
        "6:00",
        "7:00",
        "8:00",
        "9:00",
        "10:00",
        "11:00",
        "12:00",
        "13:00",
        "14:00",
        "15:00",
        "16:00",
        "17:00",
        "18:00",
        "19:00",
        "20:00",
        "21:00",
        "22:00",
        "23:00",
      ];
    return (
        <motion.div
            className={cn(
                "flex flex-col gap-1 border-l border-t py-1.5",
                isSunday && "border-l-0"
            )}
            initial={{opacity: 0, y: 10}}
            animate={{opacity: 1, y: 0}}
            transition={transition}
            onClick={() => {
                console.log("clicked");
                const day = date.getDay();
                setOpenDate(day + 1);
                // alert(JSON.stringify(day));
            }}
        >
            <DroppableArea date={date}>
                <motion.span
                    className={cn(
                        "h-6 w-6 px-1 flex translate-x-1 items-center justify-center rounded-full text-xs font-semibold lg:px-2 mb-1", // mb-0.5 here, always applied
                        !currentMonth && "text-muted-foreground",
                        isToday(date) && " bg-primary text-primary-foreground"
                    )}
                    whileHover={{ scale: 1.1 }}
                    transition={transition}
                >
                    {day}
                </motion.span>


                <motion.div
                    className={cn(
                        "flex h-6 gap-1 px-2 lg:min-h-[94px] lg:flex-col lg:gap-2 lg:px-0",
                        !currentMonth && "opacity-50"
                    )}
                    variants={staggerContainer}
                >
                    {[0, 1, 2].map((position) => {
                        const event = cellEvents.find((e) => e.position === position);
                        const eventKey = event
                            ? `event-${event.id}-${position}`
                            : `empty-${position}`;

                        return (
                            <motion.div
                                key={eventKey}
                                className="lg:flex-1"
                                initial={{opacity: 0, x: -10}}
                                animate={{opacity: 1, x: 0}}
                                transition={{delay: position * 0.1, ...transition}}
                            >
                                {event && (
                                    <>
                                        <EventBullet className="lg:hidden" color={event.color}/>
                                        <MonthEventBadge
                                            className="hidden lg:flex"
                                            event={event}
                                            cellDate={startOfDay(date)}
                                        />
                                    </>
                                )}
                            </motion.div>
                        );
                    })}
                </motion.div>

                {cellEvents.length > MAX_VISIBLE_EVENTS && (
                    <motion.div
                        className={cn(
                            "h-4.5 px-1.5 my-2 text-end text-xs font-semibold text-muted-foreground",
                            !currentMonth && "opacity-50"
                        )}
                        initial={{opacity: 0, y: 5}}
                        animate={{opacity: 1, y: 0}}
                        transition={{delay: 0.3, ...transition}}
                    >
                       <EventListDialog date={date} events={cellEvents} />
                    </motion.div>
                )}

            {openDate && <CalenderDetailsSidebar
                open={openDate === day}
                handleDrawerClose={() => { 
                    alert('close');
                     setOpenDate(null) } }
                day={day} // Convert number to Dayjs object
                date={date}
                timeArr={timeArr}
                parsedTask={null} tasks={{}} dateString={""} i={0} domainColors={{}} foundDomain={undefined} handleTimeClick={function (event: React.MouseEvent<HTMLButtonElement>): void {
                    () => {}
                } } isTimeMenuopen={false} 
                timeMenuRef={timeMenuRef} 
                timeAnchorEl={null} handleTimeClose={function (event: React.MouseEvent<HTMLButtonElement>): void {
                    throw new Error("Function not implemented.");
                } } handleChooseTime={function (event: React.MouseEvent<HTMLLIElement, MouseEvent>, dateString: string, time: string, noteIndex: number): void {
                    throw new Error("Function not implemented.");
                } } selectedDate={[]} 
                setSelectedDate={setSelectedDate} 
                bgColor={""} dropdownRef={React.createRef()} setSetDateDropdown={function (value: SetStateAction<string | null>): void {
                    throw new Error("Function not implemented.");
                } } setIsDropdownOpen={function (value: SetStateAction<boolean>): void {
                    throw new Error("Function not implemented.");
                } } isDropdownOpen={false} iconMapping={{}} dateDropdown={null} sessionCoach={[]} handleSelectDomain={function (domain: string, date: string): void {
                    throw new Error("Function not implemented.");
                } } setNoteText={function (value: SetStateAction<Record<string, string>>): void {
                    throw new Error("Function not implemented.");
                } } noteText={{}} handleSubmitNote={function (dateString: string, value: string): void {
                    throw new Error("Function not implemented.");
                } } handleOpen={function (): void {
                    throw new Error("Function not implemented.");
                } }            />}
            </DroppableArea>

        </motion.div>
    );
}
