import {useCalendar} from "@/modules/calendar/contexts/calendar-context";
import {toast} from "sonner";
import {Button} from "@/components/uiCalendar/button";
import {TrashIcon} from "lucide-react";
import {
    AlertDialog,
    AlertDialogAction, AlertDialogCancel,
    AlertDialogContent, AlertDialogDescription, AlertDialogFooter,
    AlertDialogHeader, AlertDialogTitle,
    AlertDialogTrigger
} from "@/components/uiCalendar/alert-dialog";

interface DeleteEventDialogProps {
    eventId: number;
}

export default function DeleteEventDialog(
    {
        eventId
    }: DeleteEventDialogProps
) {

    const {removeEvent} = useCalendar()

    const deleteEvent = () => {
        try {
            removeEvent(eventId);
            toast.success("Event deleted successfully.");
        } catch {
            toast.error("Error deleting event.");
        }
    }

    if (!eventId) {
        return null;
    }

    return (
        <AlertDialog>
            <AlertDialogTrigger asChild>
                <Button variant="destructive"
                >
                    <TrashIcon />
                    Delete
                </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                        This action cannot be undone. This will permanently delete your
                        event and remove event data from our servers.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={deleteEvent}>Continue</AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    )
}