import type { TEventColor } from "@/modules/calendar/types";

// export interface IUser {
//   id: string;
//   name: string;
//   picturePath: string | null;
// }

export interface IEvent {
  id: number;
  startDate: string;
  endDate: string;
  title: string;
  color: TEventColor;
  description: string;
  day: any
  // user: any;
}

export interface ICalendarCell {
  day: number;
  currentMonth: boolean;
  date: Date;
}
