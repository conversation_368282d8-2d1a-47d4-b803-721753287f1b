"use client";

import React, { createContext, useContext, useEffect, useState } from "react";

import type { IEvent, IUser } from "@/modules/calendar/interfaces";
import { TCalendarView, TEventColor } from "@/modules/calendar/types";
import {useLocalStorage} from "@/modules/calendar/hooks";
import axios from "axios";
import { RootState } from "@/store/store";
import { useSelector } from "react-redux";

interface ICalendarContext {
    selectedDate: Date;
    view: TCalendarView;
    setView: (view: TCalendarView) => void;
    agendaModeGroupBy: "date" | "color";
    setAgendaModeGroupBy: (groupBy: "date" | "color") => void;
    use24HourFormat: boolean;
    toggleTimeFormat: () => void;
    setSelectedDate: (date: Date | undefined) => void;
    selectedUserId: IUser["id"] | "all";
    setSelectedUserId: (userId: IUser["id"] | "all") => void;
    badgeVariant: "dot" | "colored";
    setBadgeVariant: (variant: "dot" | "colored") => void;
    selectedColors: TEventColor[];
    filterEventsBySelectedColors: (colors: TEventColor) => void;
    filterEventsBySelectedUser: (userId: IUser["id"] | "all") => void;
    users: IUser[];
    events: IEvent[];
    addEvent: (event: IEvent) => void;
    updateEvent: (event: IEvent) => void;
    removeEvent: (eventId: number) => void;
    clearFilter: () => void;
}

interface CalendarSettings {
    badgeVariant: "dot" | "colored";
    view: TCalendarView;
    use24HourFormat: boolean;
    agendaModeGroupBy: "date" | "color";
}

const DEFAULT_SETTINGS: CalendarSettings = {
    badgeVariant: "colored",
    view: "day",
    use24HourFormat: true,
    agendaModeGroupBy: "date"
};

const CalendarContext = createContext({} as ICalendarContext);

export function CalendarProvider({
                                     children,
                                     users,
                                     events,
                                     badge = "colored",
                                     view = "day",
                                 }: {
    children: React.ReactNode;
    users: IUser[];
    events: IEvent[];
    view?: TCalendarView;
    badge?: "dot" | "colored";
}) {
    const [settings, setSettings] = useLocalStorage<CalendarSettings>("calendar-settings", {
        ...DEFAULT_SETTINGS,
        badgeVariant: badge,
        view: view
    });

    const [badgeVariant, setBadgeVariantState] = useState<"dot" | "colored">(settings.badgeVariant);
    const [currentView, setCurrentViewState] = useState<TCalendarView>(settings.view);
    const [use24HourFormat, setUse24HourFormatState] = useState<boolean>(settings.use24HourFormat);
    const [agendaModeGroupBy, setAgendaModeGroupByState] = useState<"date" | "color">(settings.agendaModeGroupBy);

    const [selectedDate, setSelectedDate] = useState(new Date());
    const [selectedUserId, setSelectedUserId] = useState<IUser["id"] | "all">("all");
    const [selectedColors, setSelectedColors] = useState<TEventColor[]>([]);
    const [data, setData] = useState(events || []);
    const { userData } = useSelector((state: RootState) => state.user);
    const { tasks } = useSelector((state: RootState) => state.calendar);
    console.log("userData", userData);
    console.log("tasks", tasks);
    useEffect(() => {
        if (!tasks) return;
        // Convert tasks to IEvent[] format ensuring type safety
        const convertedEvents = Object.entries(tasks).flatMap(([date, dateData]) =>
            dateData.tasks.map(task => ({
                id: typeof task.id === 'number' ? task.id : parseInt(task.id as string),
                title: task.text || '',
                description: '',
                startDate: task.startDate,
                endDate: task.endDate,
                color: task.color
            } satisfies IEvent))
        );
        setData(convertedEvents);
    }, [tasks]);

    const updateSettings = (newPartialSettings: Partial<CalendarSettings>) => {
        setSettings({
            ...settings,
            ...newPartialSettings
        });
    };

    const setBadgeVariant = (variant: "dot" | "colored") => {
        setBadgeVariantState(variant);
        updateSettings({ badgeVariant: variant });
    };

    const setView = (newView: TCalendarView) => {
        setCurrentViewState(newView);
        updateSettings({ view: newView });
    };

    const toggleTimeFormat = () => {
        const newValue = !use24HourFormat;
        setUse24HourFormatState(newValue);
        updateSettings({ use24HourFormat: newValue });
    };

    const setAgendaModeGroupBy = (groupBy: "date" | "color") => {
        setAgendaModeGroupByState(groupBy);
        updateSettings({ agendaModeGroupBy: groupBy });
    };

    const filterEventsBySelectedColors = (color: TEventColor) => {
        const isColorSelected = selectedColors.includes(color);
        const newColors = isColorSelected
            ? selectedColors.filter((c) => c !== color)
            : [...selectedColors, color];

        if (newColors.length > 0) {
            const filteredEvents = events.filter((event) => {
                const eventColor = event.color || "blue";
                return newColors.includes(eventColor);
            });
            setData(filteredEvents);
        } else setData(events);

        setSelectedColors(newColors);
    };

    const filterEventsBySelectedUser = (userId: IUser["id"] | "all") => {
        setSelectedUserId(userId);
        if (userId === "all") {
            setData(events);
        } else {
            // const filteredEvents = events.filter((event) => event.user.id === userId);
            setData([]);
        }
    };


    const handleSelectDate = (date: Date | undefined) => {
        if (!date) return;
        setSelectedDate(date);
    };

    const addEvent = async (event: IEvent) => {
        console.log("updatedEvent", event);
        // alert(JSON.stringify(event));
        setData((prevEvents) => [...prevEvents, event]);
        const finalEvent = {
            title: event.title,
            description: event.description,
            tasks: [{
                ...event,
                id: event.id,
                text: event.title,
                day: event.day,
                date: event.startDate,
            }]
        }
        await axios.post(`/api/calendar?user_id=${userData?.user_id}`, finalEvent);
    };

    const updateEvent = (event: IEvent) => {
        const newEvent: IEvent = {
            ...event,
            startDate: new Date(event.startDate).toISOString(),
            endDate: new Date(event.endDate).toISOString()
        };

        setData((prevEvents) => {
            const index = prevEvents.findIndex((e) => e.id === event.id);
            if (index !== -1) {
                const updatedEvents = [...prevEvents];
                updatedEvents[index] = newEvent;
                return updatedEvents;
            }
            return prevEvents;
        });
    };

    const removeEvent = (eventId: number) => {
        setData((prevEvents) => prevEvents.filter((event) => event.id !== eventId));
    };

    const clearFilter = () => {
        setData(events);
        setSelectedColors([]);
    };

    const value = {
        selectedDate,
        setSelectedDate: handleSelectDate,
        selectedUserId,
        setSelectedUserId,
        badgeVariant,
        setBadgeVariant,
        users,
        selectedColors,
        filterEventsBySelectedColors,
        filterEventsBySelectedUser,
        events: data,
        view: currentView,
        use24HourFormat,
        toggleTimeFormat,
        setView,
        agendaModeGroupBy,
        setAgendaModeGroupBy,
        addEvent,
        updateEvent,
        removeEvent,
        clearFilter
    };

    return (
        <CalendarContext.Provider value={value}>
            {children}
        </CalendarContext.Provider>
    );
}

export function useCalendar(): ICalendarContext {
    const context = useContext(CalendarContext);
    if (!context)
        throw new Error("useCalendar must be used within a CalendarProvider.");
    return context;
}