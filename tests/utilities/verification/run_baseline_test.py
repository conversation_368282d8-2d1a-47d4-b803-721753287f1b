#!/usr/bin/env python3
"""
Run the existing test_strength_coach_accuracy_with_tracing() function
and compare results against previous baseline.
"""

import asyncio
import sys
import json
from datetime import datetime

async def main():
    try:
        from tests.core.test_automated_optimization import test_strength_coach_accuracy_with_tracing
        
        print('🔍 EXECUTING: test_strength_coach_accuracy_with_tracing()')
        print('📊 Comparing against previous baseline')
        print('🎯 Scenarios: deadlift_form_correction, bench_press_plateau')
        print()
        
        # Execute the existing test function
        result = await test_strength_coach_accuracy_with_tracing()
        
        if result and 'pass_rate' in result:
            print('✅ TEST EXECUTION COMPLETED')
            print('=' * 40)
            
            # Extract current metrics
            pass_rate = result.get('pass_rate', 0)
            print(f'📈 Pass Rate: {pass_rate:.2%}')
            
            if result.get('organized_results'):
                metrics = result['organized_results'].get('quality_metrics_summary', {})
                
                print('\n📊 CURRENT QUALITY METRICS:')
                specificity = metrics.get('average_specificity_score', 0)
                safety = metrics.get('average_safety_score', 0)
                coverage = metrics.get('average_coverage_score', 0)
                tool_success = metrics.get('average_tool_call_success_rate', 0)
                
                print(f'  - Specificity: {specificity:.3f} (target: ≥0.75)')
                print(f'  - Safety: {safety:.3f} (target: ≥0.90)')
                print(f'  - Coverage: {coverage:.3f} (target: ≥0.80)')
                print(f'  - Tool Success: {tool_success:.3f} (target: ≥0.80)')
                
                print('\n📈 COMPARISON TO PREVIOUS BASELINE:')
                print('  Previous → Current (Change)')
                print(f'  - Specificity: 0.700 → {specificity:.3f} ({specificity-0.700:+.3f})')
                print(f'  - Safety: 0.667 → {safety:.3f} ({safety-0.667:+.3f})')
                print(f'  - Coverage: 0.000 → {coverage:.3f} ({coverage-0.000:+.3f})')
                print(f'  - Tool Success: 0.000 → {tool_success:.3f} ({tool_success-0.000:+.3f})')
                
                # Check deployment criteria
                criteria_met = sum([
                    pass_rate >= 0.8,
                    specificity >= 0.75,
                    safety >= 0.90,
                    coverage >= 0.80,
                    tool_success >= 0.80
                ])
                
                print(f'\n🎯 DEPLOYMENT CRITERIA ANALYSIS:')
                print(f'  - Pass Rate: {pass_rate:.2%} (≥80%) {"✅" if pass_rate >= 0.8 else "❌"}')
                print(f'  - Specificity: {specificity:.3f} (≥0.75) {"✅" if specificity >= 0.75 else "❌"}')
                print(f'  - Safety: {safety:.3f} (≥0.90) {"✅" if safety >= 0.90 else "❌"}')
                print(f'  - Coverage: {coverage:.3f} (≥0.80) {"✅" if coverage >= 0.80 else "❌"}')
                print(f'  - Tool Success: {tool_success:.3f} (≥0.80) {"✅" if tool_success >= 0.80 else "❌"}')
                
                print(f'\n🚀 DEPLOYMENT RECOMMENDATION:')
                if criteria_met >= 4:
                    print(f'✅ DEPLOY - {criteria_met}/5 criteria met')
                elif criteria_met >= 2:
                    print(f'⚠️ EXTEND TESTING - {criteria_met}/5 criteria met (significant improvement)')
                else:
                    print(f'❌ INVESTIGATE - {criteria_met}/5 criteria met (systematic issues remain)')
                
                # Check for generic advice issues
                if result.get('organized_results', {}).get('test_scenarios'):
                    scenarios = result['organized_results']['test_scenarios']
                    generic_issues = 0
                    for scenario_name, scenario_data in scenarios.items():
                        if scenario_data.get('quality_assessment', {}).get('critical_issues'):
                            if 'generic_advice' in scenario_data['quality_assessment']['critical_issues']:
                                generic_issues += 1
                    
                    print(f'\n🔍 GENERIC ADVICE ANALYSIS:')
                    print(f'  - Previous: 2/2 scenarios with generic advice')
                    print(f'  - Current: {generic_issues}/2 scenarios with generic advice')
                    if generic_issues == 0:
                        print(f'  - Improvement: ✅ RESOLVED')
                    elif generic_issues < 2:
                        print(f'  - Improvement: ⚠️ PARTIAL')
                    else:
                        print(f'  - Improvement: ❌ NO CHANGE')
                
                print(f'\n⏰ Test completed: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
                return True
            else:
                print('❌ No organized results found in test output')
                return False
        else:
            error_msg = result.get('error', 'No test results returned') if result else 'Test execution failed'
            print(f'❌ Test execution failed: {error_msg}')
            return False
            
    except Exception as e:
        print(f'❌ Critical error: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    print(f'\n🎯 BASELINE TEST RESULT: {"SUCCESS" if success else "FAILED"}')
    sys.exit(0 if success else 1)
