"""
Tests for the self-contained Strength Agent and its direct tool integration.
"""

import asyncio
import json
from unittest.mock import AsyncMock, patch

import pytest
from athlea_langgraph.agents.strength_agent import StrengthAgent
from athlea_langgraph.states import AgentState

# Mark all tests in this module as asyncio
pytestmark = pytest.mark.asyncio


@pytest.fixture
def strength_agent_fixture():
    """Provides a fresh instance of the StrengthAgent for each test."""
    return StrengthAgent()


async def test_strength_agent_initialization(strength_agent_fixture: StrengthAgent):
    """Test that the StrengthAgent initializes correctly without tools loaded."""
    agent = strength_agent_fixture
    assert agent.name == "strength_agent"
    assert agent.domain == "strength_training"
    assert not agent.tools  # Tools should be loaded lazily
    assert not agent._tools_loaded


async def test_get_domain_tools_loading(strength_agent_fixture: StrengthAgent):
    """Test the get_domain_tools method correctly loads the self-contained tools."""
    agent = strength_agent_fixture
    tools = await agent.get_domain_tools()

    assert len(tools) == 3
    assert agent._tools_loaded
    tool_names = {tool.name for tool in tools}
    assert "search_strength_exercises" in tool_names
    assert "get_exercise_progression" in tool_names
    assert "comprehensive_strength_assessment" in tool_names

    # Test that calling it again returns the cached tools without reloading
    with patch.object(agent, "_tools_loaded", True):
        cached_tools = await agent.get_domain_tools()
        assert cached_tools is agent.tools  # Should be the exact same object
        assert agent._tools_loaded


@patch("athlea_langgraph.tools.strength.search_strength_exercises")
async def test_strength_agent_calls_exercise_search_tool(
    mock_search: AsyncMock, strength_agent_fixture: StrengthAgent
):
    """Verify the agent has the exercise search tool available."""
    agent = strength_agent_fixture
    mock_response = {
        "exercises": [
            {
                "name": "Barbell Squat",
                "muscle_groups": ["quadriceps", "glutes", "hamstrings"],
                "equipment": "barbell",
                "difficulty": "intermediate",
                "description": "Compound lower body movement",
            },
            {
                "name": "Romanian Deadlift",
                "muscle_groups": ["hamstrings", "glutes", "erector_spinae"],
                "equipment": "barbell",
                "difficulty": "intermediate",
                "description": "Hip hinge movement targeting posterior chain",
            },
        ],
        "total_found": 2,
        "search_criteria": {"muscle_groups": ["legs"], "equipment": "barbell"},
    }
    mock_search.return_value = json.dumps(mock_response)

    state = AgentState(
        messages=[
            {
                "role": "user",
                "content": "I'm looking for barbell exercises that target my legs. What options do I have?",
            }
        ],
        user_query="I'm looking for barbell exercises that target my legs. What options do I have?",
    )
    config = {}

    result = await agent.process(state, config)

    # Verify the agent has tools available
    tools = await agent.get_domain_tools()
    tool_names = [tool.name for tool in tools]
    assert "search_strength_exercises" in tool_names

    # Verify the result is processed correctly
    assert "response" in result
    assert len(result["response"]) > 0


@patch("athlea_langgraph.tools.strength.get_exercise_progression")
async def test_strength_agent_calls_progression_tool(
    mock_progression: AsyncMock, strength_agent_fixture: StrengthAgent
):
    """Verify the agent has the exercise progression tool available."""
    agent = strength_agent_fixture
    mock_response = {
        "current_exercise": "Push-up",
        "progression_path": [
            {"name": "Incline Push-up", "difficulty": "beginner"},
            {"name": "Standard Push-up", "difficulty": "intermediate"},
            {"name": "Decline Push-up", "difficulty": "advanced"},
            {"name": "One-arm Push-up", "difficulty": "expert"},
        ],
        "next_exercise": "Decline Push-up",
        "progression_tips": [
            "Master 3 sets of 12-15 reps before progressing",
            "Focus on perfect form over speed",
            "Gradually increase difficulty angle",
        ],
    }
    mock_progression.return_value = json.dumps(mock_response)

    state = AgentState(
        messages=[
            {
                "role": "user",
                "content": "I can do 3 sets of 15 standard push-ups easily now. What's the next progression?",
            }
        ],
        user_query="I can do 3 sets of 15 standard push-ups easily now. What's the next progression?",
    )
    config = {}

    result = await agent.process(state, config)

    # Verify the agent has tools available
    tools = await agent.get_domain_tools()
    tool_names = [tool.name for tool in tools]
    assert "get_exercise_progression" in tool_names

    # Verify the result is processed correctly
    assert "response" in result
    assert len(result["response"]) > 0


@patch("athlea_langgraph.tools.strength.comprehensive_strength_assessment")
async def test_strength_agent_calls_assessment_tool(
    mock_assessment: AsyncMock, strength_agent_fixture: StrengthAgent
):
    """Verify the agent has the strength assessment tool available."""
    agent = strength_agent_fixture
    mock_response = {
        "overall_strength_level": "Intermediate",
        "strength_ratios": {
            "squat_to_deadlift": 0.85,
            "bench_to_squat": 0.75,
            "overhead_to_bench": 0.65,
        },
        "imbalances": ["Weak posterior chain", "Limited shoulder mobility"],
        "one_rep_maxes": {
            "squat": 140,
            "bench_press": 105,
            "deadlift": 165,
            "overhead_press": 70,
        },
        "recommendations": [
            "Focus on posterior chain development",
            "Add mobility work for shoulders",
            "Increase training frequency for bench press",
        ],
    }
    mock_assessment.return_value = json.dumps(mock_response)

    state = AgentState(
        messages=[
            {
                "role": "user",
                "content": "I want a comprehensive strength assessment. My maxes are: squat 140kg, bench 105kg, deadlift 165kg, OHP 70kg.",
            }
        ],
        user_query="I want a comprehensive strength assessment. My maxes are: squat 140kg, bench 105kg, deadlift 165kg, OHP 70kg.",
    )
    config = {}

    result = await agent.process(state, config)

    # Verify the agent has tools available
    tools = await agent.get_domain_tools()
    tool_names = [tool.name for tool in tools]
    assert "comprehensive_strength_assessment" in tool_names

    # Verify the result is processed correctly
    assert "response" in result
    assert len(result["response"]) > 0


async def test_strength_agent_tool_loading_efficiency(
    strength_agent_fixture: StrengthAgent,
):
    """Test that tools are loaded efficiently and cached properly."""
    agent = strength_agent_fixture

    # First call should load tools
    tools_first = await agent.get_domain_tools()
    assert len(tools_first) == 3
    assert agent._tools_loaded

    # Second call should return cached tools without reloading
    tools_second = await agent.get_domain_tools()
    assert tools_second is agent.tools  # Should be the exact same object
    assert len(tools_second) == 3


async def test_strength_agent_prompt_loading(strength_agent_fixture: StrengthAgent):
    """Test that the system prompt is loaded correctly."""
    agent = strength_agent_fixture

    # Initially should use fallback prompt
    assert agent.system_prompt == agent.fallback_prompt
    assert not agent._prompt_loaded

    # Loading prompt should update the system prompt
    await agent._load_system_prompt()
    assert agent._prompt_loaded
    # The prompt should contain tool usage guidance
    assert "search_strength_exercises" in agent.system_prompt
    assert "get_exercise_progression" in agent.system_prompt
    assert "comprehensive_strength_assessment" in agent.system_prompt


async def test_strength_agent_process_method(strength_agent_fixture: StrengthAgent):
    """Test that the process method correctly loads tools and prompt before processing."""
    agent = strength_agent_fixture

    state = AgentState(
        messages=[{"role": "user", "content": "Hello strength coach"}],
        user_query="Hello strength coach",
    )

    # Mock the parent process method to avoid actual LLM calls
    with patch.object(
        agent.__class__.__bases__[0], "process", new_callable=AsyncMock
    ) as mock_process:
        mock_process.return_value = {
            "response": "Hello! I'm your strength training coach ready to help you build muscle and power.",
            "specialist_completed": True,
        }

        result = await agent.process(state, {})

        # Verify tools and prompt were loaded
        assert agent.tools  # Tools should be loaded
        assert agent._tools_loaded
        assert agent._prompt_loaded

        # Verify parent process was called
        mock_process.assert_called_once_with(state, {})

        # Verify result
        assert (
            result["response"]
            == "Hello! I'm your strength training coach ready to help you build muscle and power."
        )


async def test_strength_agent_exercise_database_integration(
    strength_agent_fixture: StrengthAgent,
):
    """Test that the strength agent properly integrates with the exercise database."""
    agent = strength_agent_fixture

    # Load tools
    tools = await agent.get_domain_tools()

    # Verify that exercise-related tools are available
    tool_names = {tool.name for tool in tools}
    assert "search_strength_exercises" in tool_names
    assert "get_exercise_progression" in tool_names

    # Verify tools are properly configured
    search_tool = next(
        tool for tool in tools if tool.name == "search_strength_exercises"
    )
    progression_tool = next(
        tool for tool in tools if tool.name == "get_exercise_progression"
    )

    assert search_tool is not None
    assert progression_tool is not None


async def test_strength_agent_assessment_integration(
    strength_agent_fixture: StrengthAgent,
):
    """Test that the strength agent properly integrates with the assessment tool."""
    agent = strength_agent_fixture

    # Load tools
    tools = await agent.get_domain_tools()

    # Verify assessment tool is available
    tool_names = {tool.name for tool in tools}
    assert "comprehensive_strength_assessment" in tool_names

    # Verify assessment tool is properly configured
    assessment_tool = next(
        tool for tool in tools if tool.name == "comprehensive_strength_assessment"
    )
    assert assessment_tool is not None


@patch("athlea_langgraph.tools.strength.search_strength_exercises")
async def test_strength_agent_muscle_group_filtering(
    mock_search: AsyncMock, strength_agent_fixture: StrengthAgent
):
    """Verify the agent can handle muscle group specific requests."""
    agent = strength_agent_fixture
    mock_response = {
        "exercises": [
            {
                "name": "Lat Pulldown",
                "muscle_groups": ["latissimus_dorsi", "rhomboids", "biceps"],
                "equipment": "cable_machine",
                "difficulty": "beginner",
            },
            {
                "name": "Pull-ups",
                "muscle_groups": ["latissimus_dorsi", "rhomboids", "biceps"],
                "equipment": "pull_up_bar",
                "difficulty": "intermediate",
            },
        ],
        "total_found": 2,
        "search_criteria": {"muscle_groups": ["back"]},
    }
    mock_search.return_value = json.dumps(mock_response)

    state = AgentState(
        messages=[
            {
                "role": "user",
                "content": "I want to target my back muscles specifically. What exercises would you recommend?",
            }
        ],
        user_query="I want to target my back muscles specifically. What exercises would you recommend?",
    )
    config = {}

    result = await agent.process(state, config)

    # Verify the agent has tools available
    tools = await agent.get_domain_tools()
    tool_names = [tool.name for tool in tools]
    assert "search_strength_exercises" in tool_names

    # Verify the result is processed correctly
    assert "response" in result
    assert len(result["response"]) > 0
