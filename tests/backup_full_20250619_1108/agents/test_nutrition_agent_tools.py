"""
Tests for the self-contained Nutrition Agent and its direct tool integration.
"""

import asyncio
import json
from unittest.mock import AsyncMock, patch

import pytest
from athlea_langgraph.agents.nutrition_agent import NutritionAgent
from athlea_langgraph.states import AgentState

# Mark all tests in this module as asyncio
pytestmark = pytest.mark.asyncio


@pytest.fixture
def nutrition_agent_fixture():
    """Provides a fresh instance of the NutritionAgent for each test."""
    return NutritionAgent()


async def test_nutrition_agent_initialization(nutrition_agent_fixture: NutritionAgent):
    """Test that the NutritionAgent initializes correctly without tools loaded."""
    agent = nutrition_agent_fixture
    assert agent.name == "nutrition_agent"
    assert agent.domain == "nutrition"
    assert not agent.tools  # Tools should be loaded lazily
    assert not agent._tools_loaded


async def test_get_domain_tools_loading(nutrition_agent_fixture: NutritionAgent):
    """Test the get_domain_tools method correctly loads the self-contained tools."""
    agent = nutrition_agent_fixture
    tools = await agent.get_domain_tools()

    # Should load available tools (handling cases where some might not be available)
    assert (
        len(tools) >= 2
    )  # At least calculate_daily_calories and calculate_macro_targets
    assert agent._tools_loaded
    tool_names = {tool.name for tool in tools}

    # Check for core tools that should always be available
    assert "calculate_daily_calories" in tool_names
    assert "calculate_macro_targets" in tool_names

    # Test that calling it again returns the cached tools without reloading
    with patch.object(agent, "_tools_loaded", True):
        cached_tools = await agent.get_domain_tools()
        assert cached_tools is agent.tools  # Should be the exact same object
        assert agent._tools_loaded


@patch("athlea_langgraph.tools.nutrition.calculate_daily_calories")
async def test_nutrition_agent_calls_calorie_calculator_tool(
    mock_calorie_calc: AsyncMock, nutrition_agent_fixture: NutritionAgent
):
    """Verify the agent has the calorie calculator tool available."""
    agent = nutrition_agent_fixture
    mock_response = {
        "daily_calories": 2500,
        "bmr": 1800,
        "activity_factor": 1.55,
        "goal_factor": 1.1,
        "breakdown": {
            "maintenance": 2790,
            "cutting": 2290,
            "bulking": 3290,
        },
        "recommendations": [
            "Aim for 2500 calories daily for muscle gain",
            "Spread calories across 4-5 meals",
            "Include post-workout nutrition within 30 minutes",
        ],
    }
    mock_calorie_calc.return_value = json.dumps(mock_response)

    state = AgentState(
        messages=[
            {
                "role": "user",
                "content": "I'm 25 years old, 180 cm tall, weigh 75 kg, and work out 4 times a week. How many calories should I eat?",
            }
        ],
        user_query="I'm 25 years old, 180 cm tall, weigh 75 kg, and work out 4 times a week. How many calories should I eat?",
    )
    config = {}

    result = await agent.process(state, config)

    # Verify the agent has tools available
    tools = await agent.get_domain_tools()
    tool_names = [tool.name for tool in tools]
    assert "calculate_daily_calories" in tool_names

    # Verify the result is processed correctly
    assert "response" in result
    assert len(result["response"]) > 0


@patch("athlea_langgraph.tools.nutrition.calculate_macro_targets")
async def test_nutrition_agent_calls_macro_calculator_tool(
    mock_macro_calc: AsyncMock, nutrition_agent_fixture: NutritionAgent
):
    """Verify the agent has the macro calculator tool available."""
    agent = nutrition_agent_fixture
    mock_response = {
        "daily_macros": {
            "protein_grams": 150,
            "carbs_grams": 250,
            "fat_grams": 85,
            "fiber_grams": 35,
        },
        "macro_percentages": {"protein": 24, "carbs": 40, "fat": 36},
        "meal_timing": {
            "pre_workout": {"carbs": 30, "protein": 20},
            "post_workout": {"carbs": 40, "protein": 35},
        },
        "recommendations": [
            "Focus on complete proteins at each meal",
            "Time carbohydrates around workouts",
            "Include healthy fats throughout the day",
        ],
    }
    mock_macro_calc.return_value = json.dumps(mock_response)

    state = AgentState(
        messages=[
            {
                "role": "user",
                "content": "Based on my 2500 calorie goal, what should my macronutrient breakdown be? I'm focused on muscle gain.",
            }
        ],
        user_query="Based on my 2500 calorie goal, what should my macronutrient breakdown be? I'm focused on muscle gain.",
    )
    config = {}

    result = await agent.process(state, config)

    # Verify the agent has tools available
    tools = await agent.get_domain_tools()
    tool_names = [tool.name for tool in tools]
    assert "calculate_macro_targets" in tool_names

    # Verify the result is processed correctly
    assert "response" in result
    assert len(result["response"]) > 0


@patch("athlea_langgraph.tools.nutrition.generate_meal_plan")
async def test_nutrition_agent_calls_meal_plan_tool(
    mock_meal_plan: AsyncMock, nutrition_agent_fixture: NutritionAgent
):
    """Verify the agent can correctly call the meal plan generation tool."""
    agent = nutrition_agent_fixture
    mock_response = {
        "meal_plan": {
            "breakfast": {
                "name": "Greek Yogurt Bowl",
                "calories": 350,
                "protein": 25,
                "carbs": 35,
                "fat": 8,
            },
            "lunch": {
                "name": "Grilled Chicken Salad",
                "calories": 450,
                "protein": 35,
                "carbs": 25,
                "fat": 15,
            },
            "dinner": {
                "name": "Salmon with Quinoa",
                "calories": 500,
                "protein": 30,
                "carbs": 45,
                "fat": 18,
            },
        },
        "total_calories": 1300,
        "total_protein": 90,
        "recommendations": [
            "Add healthy snacks to reach daily calorie target",
            "Include variety in vegetable choices",
        ],
    }

    # Handle case where generate_meal_plan might not be available
    if hasattr(nutrition_agent_fixture, "generate_meal_plan"):
        mock_meal_plan.return_value = json.dumps(mock_response)

        state = AgentState(
            messages=[
                {
                    "role": "user",
                    "content": "Can you create a daily meal plan for me? I want 1800 calories with high protein.",
                }
            ],
            user_query="Can you create a daily meal plan for me? I want 1800 calories with high protein.",
        )
        config = {}

        with patch.object(agent, "get_domain_tools") as mock_tools:
            # Mock that generate_meal_plan is available
            from unittest.mock import MagicMock

            mock_tool = MagicMock()
            mock_tool.name = "generate_meal_plan"
            mock_tools.return_value = [mock_tool]

            result = await agent.process(state, config)

            assert (
                "Greek Yogurt" in result["response"]
                or "meal plan" in result["response"]
            )
            assert "protein" in result["response"]


@patch("athlea_langgraph.tools.nutrition.search_recipes")
async def test_nutrition_agent_calls_recipe_search_tool(
    mock_recipes: AsyncMock, nutrition_agent_fixture: NutritionAgent
):
    """Verify the agent can correctly call the recipe search tool."""
    agent = nutrition_agent_fixture
    mock_response = {
        "recipes": [
            {
                "name": "High-Protein Smoothie",
                "calories": 320,
                "protein": 28,
                "prep_time": "5 minutes",
                "ingredients": ["Greek yogurt", "protein powder", "banana", "spinach"],
            },
            {
                "name": "Quinoa Power Bowl",
                "calories": 450,
                "protein": 18,
                "prep_time": "15 minutes",
                "ingredients": ["quinoa", "black beans", "avocado", "vegetables"],
            },
        ],
        "total_results": 2,
        "search_criteria": "high protein, quick prep",
    }

    # Handle case where search_recipes might not be available
    if hasattr(nutrition_agent_fixture, "search_recipes"):
        mock_recipes.return_value = json.dumps(mock_response)

        state = AgentState(
            messages=[
                {
                    "role": "user",
                    "content": "I need high-protein recipes that are quick to prepare. Can you help me find some?",
                }
            ],
            user_query="I need high-protein recipes that are quick to prepare. Can you help me find some?",
        )
        config = {}

        with patch.object(agent, "get_domain_tools") as mock_tools:
            from unittest.mock import MagicMock

            mock_tool = MagicMock()
            mock_tool.name = "search_recipes"
            mock_tools.return_value = [mock_tool]

            result = await agent.process(state, config)

            assert "protein" in result["response"]
            assert "Smoothie" in result["response"] or "recipe" in result["response"]


async def test_nutrition_agent_tool_loading_efficiency(
    nutrition_agent_fixture: NutritionAgent,
):
    """Test that tools are loaded efficiently and cached properly."""
    agent = nutrition_agent_fixture

    # First call should load tools
    tools_first = await agent.get_domain_tools()
    assert len(tools_first) >= 2  # At least 2 core tools should be available
    assert agent._tools_loaded

    # Second call should return cached tools without reloading
    tools_second = await agent.get_domain_tools()
    assert tools_second is agent.tools  # Should be the exact same object
    assert len(tools_second) >= 2


async def test_nutrition_agent_prompt_loading(nutrition_agent_fixture: NutritionAgent):
    """Test that the system prompt is loaded correctly."""
    agent = nutrition_agent_fixture

    # Initially should use fallback prompt
    assert agent.system_prompt == agent.fallback_prompt
    assert not agent._prompt_loaded

    # Loading prompt should update the system prompt
    await agent._load_system_prompt()
    assert agent._prompt_loaded
    # The prompt should contain tool usage guidance
    assert "calculate_daily_calories" in agent.system_prompt
    assert "calculate_macro_targets" in agent.system_prompt
    assert "nutrition" in agent.system_prompt.lower()


async def test_nutrition_agent_process_method(nutrition_agent_fixture: NutritionAgent):
    """Test that the process method correctly loads tools and prompt before processing."""
    agent = nutrition_agent_fixture

    state = AgentState(
        messages=[{"role": "user", "content": "Hello nutrition coach"}],
        user_query="Hello nutrition coach",
    )

    # Mock the parent process method to avoid actual LLM calls
    with patch.object(
        agent.__class__.__bases__[0], "process", new_callable=AsyncMock
    ) as mock_process:
        mock_process.return_value = {
            "response": "Hello! I'm your nutrition coach ready to help with your dietary goals.",
            "specialist_completed": True,
        }

        result = await agent.process(state, {})

        # Verify tools and prompt were loaded
        assert agent.tools  # Tools should be loaded
        assert agent._tools_loaded
        assert agent._prompt_loaded

        # Verify parent process was called
        mock_process.assert_called_once_with(state, {})

        # Verify result
        assert (
            result["response"]
            == "Hello! I'm your nutrition coach ready to help with your dietary goals."
        )


async def test_nutrition_agent_handles_optional_tools(
    nutrition_agent_fixture: NutritionAgent,
):
    """Test that the agent gracefully handles cases where some tools might not be available."""
    agent = nutrition_agent_fixture

    # Mock scenario where some tools are None (not available)
    with (
        patch("athlea_langgraph.tools.nutrition.generate_meal_plan", None),
        patch("athlea_langgraph.tools.nutrition.search_recipes", None),
    ):

        tools = await agent.get_domain_tools()

        # Should still load available tools
        assert len(tools) >= 2  # At least calorie and macro calculators
        tool_names = {tool.name for tool in tools}
        assert "calculate_daily_calories" in tool_names
        assert "calculate_macro_targets" in tool_names
