#!/usr/bin/env python3
"""
Test Tool Integration

Tests the integration between agents, direct tools, and MCP servers.
Validates that each agent gets the correct tools and can use them properly.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the athlea_langgraph module to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from athlea_langgraph.tools.mcp_client_manager import get_mcp_client_manager
from athlea_langgraph.tools.modular_agent_tools import get_modular_tools_manager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_mcp_connection():
    """Test MCP server connection."""
    print("\n" + "=" * 60)
    print("🔌 TESTING MCP SERVER CONNECTION")
    print("=" * 60)

    try:
        mcp_manager = await get_mcp_client_manager()

        if not mcp_manager.is_available():
            print(
                "❌ MCP not available - check if external MCP server is running on port 8006"
            )
            return False

        # Test external tools
        external_tools = await mcp_manager.get_external_tools()
        print(f"✅ External tools loaded: {len(external_tools)}")
        for tool in external_tools[:3]:  # Show first 3
            print(f"   - {tool.name}: {tool.description[:60]}...")

        # Test location tools
        location_tools = await mcp_manager.get_location_tools()
        print(f"✅ Location tools loaded: {len(location_tools)}")
        for tool in location_tools:
            print(f"   - {tool.name}")

        # Test data tools
        data_tools = await mcp_manager.get_data_tools()
        print(f"✅ Data tools loaded: {len(data_tools)}")
        for tool in data_tools:
            print(f"   - {tool.name}")

        return True

    except Exception as e:
        print(f"❌ MCP connection failed: {e}")
        return False


async def test_agent_tool_distribution():
    """Test that each agent gets the correct tools."""
    print("\n" + "=" * 60)
    print("🎯 TESTING AGENT TOOL DISTRIBUTION")
    print("=" * 60)

    try:
        tools_manager = await get_modular_tools_manager()

        agents = [
            ("strength", tools_manager.get_strength_agent_tools),
            ("nutrition", tools_manager.get_nutrition_agent_tools),
            ("cardio", tools_manager.get_cardio_agent_tools),
            ("recovery", tools_manager.get_recovery_agent_tools),
            ("mental", tools_manager.get_mental_agent_tools),
        ]

        for agent_name, get_tools_func in agents:
            print(f"\n🤖 {agent_name.upper()} AGENT TOOLS:")
            tools = get_tools_func()

            direct_tools = []
            mcp_tools = []

            for tool in tools:
                # Categorize tools based on their origin
                if hasattr(tool, "metadata") and tool.metadata.get("source") == "mcp":
                    mcp_tools.append(tool.name)
                else:
                    direct_tools.append(tool.name)

            print(f"   📋 Total tools: {len(tools)}")
            print(f"   🔧 Direct tools: {len(direct_tools)}")
            if direct_tools:
                for tool_name in direct_tools:
                    print(f"      - {tool_name}")

            print(f"   🔌 MCP tools: {len(mcp_tools)}")
            if mcp_tools:
                for tool_name in mcp_tools:
                    print(f"      - {tool_name}")

        return True

    except Exception as e:
        print(f"❌ Agent tool distribution test failed: {e}")
        return False


async def test_tool_functionality():
    """Test that tools can be invoked correctly."""
    print("\n" + "=" * 60)
    print("⚙️ TESTING TOOL FUNCTIONALITY")
    print("=" * 60)

    try:
        tools_manager = await get_modular_tools_manager()

        # Test a strength agent tool
        strength_tools = tools_manager.get_strength_agent_tools()
        assessment_tool = None

        for tool in strength_tools:
            if tool.name == "strength_assessment":
                assessment_tool = tool
                break

        if assessment_tool:
            print("🔧 Testing strength assessment tool...")
            test_data = '{"current_exercises": ["squats", "deadlifts"], "training_frequency": "3x per week"}'
            result = assessment_tool.invoke(test_data)
            print(f"✅ Strength assessment result: {result[:100]}...")
        else:
            print("❌ Strength assessment tool not found")

        # Test MCP tool if available
        mcp_manager = await get_mcp_client_manager()
        if mcp_manager.is_available():
            print("🔌 Testing MCP location tool...")
            location_tools = await mcp_manager.get_location_tools()

            for tool in location_tools:
                if tool.name == "search_locations":
                    # This would normally invoke the tool, but we'll just validate it exists
                    print(f"✅ MCP tool available: {tool.name}")
                    break
        else:
            print("⚠️ MCP tools not available for functionality testing")

        return True

    except Exception as e:
        print(f"❌ Tool functionality test failed: {e}")
        return False


async def test_tool_contract_compliance():
    """Test that tools comply with their contracts."""
    print("\n" + "=" * 60)
    print("📋 TESTING TOOL CONTRACT COMPLIANCE")
    print("=" * 60)

    try:
        tools_manager = await get_modular_tools_manager()

        # Check that each agent has required tool categories
        required_tool_patterns = {
            "strength": ["assessment", "search", "generation"],
            "nutrition": ["calculator", "search", "generation"],
            "cardio": ["assessment", "maps", "generation"],
            "recovery": ["assessment", "search"],
            "mental": ["assessment", "search"],
        }

        for agent_name, required_patterns in required_tool_patterns.items():
            get_tools_func = getattr(tools_manager, f"get_{agent_name}_agent_tools")
            tools = get_tools_func()
            tool_names = [tool.name for tool in tools]

            print(f"\n🤖 {agent_name.upper()} agent contract check:")

            for pattern in required_patterns:
                matching_tools = [name for name in tool_names if pattern in name]
                if matching_tools:
                    print(f"   ✅ {pattern} tools: {matching_tools}")
                else:
                    print(f"   ⚠️ No {pattern} tools found")

        return True

    except Exception as e:
        print(f"❌ Contract compliance test failed: {e}")
        return False


async def main():
    """Run all tool integration tests."""
    print("🚀 TOOL INTEGRATION TEST SUITE")
    print("Testing hybrid direct + MCP tool integration")

    tests = [
        ("MCP Connection", test_mcp_connection),
        ("Agent Tool Distribution", test_agent_tool_distribution),
        ("Tool Functionality", test_tool_functionality),
        ("Contract Compliance", test_tool_contract_compliance),
    ]

    results = []

    for test_name, test_func in tests:
        try:
            success = await test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)

    passed = sum(1 for _, success in results if success)
    total = len(results)

    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status} - {test_name}")

    print(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        print("\n🎉 All tests passed! Tool integration is working correctly.")
        return 0
    else:
        print(f"\n⚠️ {total-passed} test(s) failed. Check the output above.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
