#!/usr/bin/env python3
"""
ReAct Specialized Coaches Test

This script tests the new ReAct-based specialized coaches implementation
with proper tool integration and reasoning capabilities.
"""

import asyncio
import logging
from typing import Any, Dict, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test scenarios for ReAct coaches
REACT_TEST_SCENARIOS = [
    {
        "coach": "strength_coach",
        "query": "What is RPE and how should I use it in my strength training?",
        "expected_tools": ["azure_cognitive_search"],
        "description": "Should search for RPE information first, then provide comprehensive explanation",
        "test_type": "informational",
    },
    {
        "coach": "strength_coach",
        "query": "Find me existing deadlift workouts in your database",
        "expected_tools": ["airtable_mcp"],
        "description": "Should search Airtable for existing deadlift workouts",
        "test_type": "lookup",
    },
    {
        "coach": "strength_coach",
        "query": "Create a new hypertrophy workout for chest and triceps",
        "expected_tools": ["session_generation"],
        "description": "Should generate a new workout session",
        "test_type": "generation",
    },
    {
        "coach": "nutrition_coach",
        "query": "Explain the role of electrolytes in athletic performance",
        "expected_tools": ["azure_cognitive_search"],
        "description": "Should search for electrolyte research first",
        "test_type": "informational",
    },
    {
        "coach": "nutrition_coach",
        "query": "Create a high-protein meal plan for muscle building",
        "expected_tools": ["session_generation"],
        "description": "Should generate a nutrition plan",
        "test_type": "generation",
    },
    {
        "coach": "cardio_coach",
        "query": "What are the benefits of HIIT training compared to steady-state cardio?",
        "expected_tools": ["azure_cognitive_search"],
        "description": "Should search for HIIT research and comparison studies",
        "test_type": "informational",
    },
    {
        "coach": "cardio_coach",
        "query": "Plan a running route from Central Park to Brooklyn Bridge with elevation data",
        "expected_tools": ["google_maps_elevation", "azure_maps"],
        "description": "Should use mapping tools for route planning",
        "test_type": "route_planning",
    },
    {
        "coach": "cycling_coach",
        "query": "Explain power-based training zones for cycling",
        "expected_tools": ["azure_cognitive_search"],
        "description": "Should search for cycling power zone information",
        "test_type": "informational",
    },
    {
        "coach": "recovery_coach",
        "query": "What does research say about sleep and athletic recovery?",
        "expected_tools": ["azure_cognitive_search"],
        "description": "Should search for sleep and recovery research",
        "test_type": "informational",
    },
    {
        "coach": "mental_coach",
        "query": "How can I build mental resilience for competition?",
        "expected_tools": ["azure_cognitive_search"],
        "description": "Should search for sports psychology research on mental resilience",
        "test_type": "informational",
    },
]


async def test_react_coach(
    coach_name: str, query: str, scenario: Dict[str, Any]
) -> Dict[str, Any]:
    """Test a ReAct coach with a specific query."""
    print(f"\n🎯 Testing ReAct {coach_name}")
    print(f"Query: {query}")
    print(f"Expected: {scenario['description']}")
    print(f"Test Type: {scenario['test_type']}")
    print("-" * 80)

    try:
        from langchain_core.messages import HumanMessage

        from athlea_langgraph.agents.specialized_coaches_react import (
            get_specialized_coach,
        )
        from athlea_langgraph.state import AgentState

        # Get the ReAct coach
        coach = await get_specialized_coach(coach_name)
        if not coach:
            return {
                "coach": coach_name,
                "query": query,
                "success": False,
                "error": f"Coach {coach_name} not available",
                "tool_calls": [],
                "reasoning_steps": [],
            }

        # Create test state
        test_state = AgentState(
            messages=[HumanMessage(content=query)],
            user_query=query,
            user_profile={
                "name": "Test User",
                "fitness_level": "intermediate",
                "goals": ["general fitness", "strength", "endurance"],
                "restrictions": {"injuries": [], "dietary": [], "time_constraints": []},
            },
        )

        print(f"🤖 Invoking {coach_name} with ReAct pattern...")

        # Execute the ReAct coach
        result = await coach.invoke(test_state)

        # Analyze the result
        analysis = {
            "coach": coach_name,
            "query": query,
            "success": True,
            "tool_calls": [],
            "reasoning_steps": [],
            "final_response": "",
            "expected_tools": scenario["expected_tools"],
            "tools_used": [],
            "tools_matched": False,
            "react_pattern_followed": False,
        }

        if result.get("messages"):
            # Analyze the conversation flow for ReAct pattern
            messages = result["messages"]
            tool_call_count = 0
            reasoning_count = 0

            for i, message in enumerate(messages):
                if hasattr(message, "tool_calls") and message.tool_calls:
                    tool_call_count += 1
                    analysis["tool_calls"].extend(message.tool_calls)
                    analysis["tools_used"].extend(
                        [tc["name"] for tc in message.tool_calls]
                    )

                    print(
                        f"  🔧 Step {i+1}: Tool calls made ({len(message.tool_calls)} tools)"
                    )
                    for tc in message.tool_calls:
                        print(f"    - {tc['name']}: {str(tc['args'])[:100]}...")

                elif hasattr(message, "content") and message.content:
                    if (
                        "think" in message.content.lower()
                        or "reason" in message.content.lower()
                    ):
                        reasoning_count += 1
                        analysis["reasoning_steps"].append(
                            message.content[:200] + "..."
                        )

                    # Check if this is the final response
                    if i == len(messages) - 1:
                        analysis["final_response"] = message.content[:300] + "..."
                        print(f"  💭 Final Response: {message.content[:150]}...")

            # Check if ReAct pattern was followed (reasoning + action)
            analysis["react_pattern_followed"] = (
                tool_call_count > 0 and len(messages) > 1
            )

            # Check if expected tools were used
            expected_tools = set(scenario["expected_tools"])
            used_tools = set(analysis["tools_used"])
            analysis["tools_matched"] = bool(expected_tools.intersection(used_tools))

            print(f"  📊 Analysis:")
            print(f"    - Tool calls made: {tool_call_count}")
            print(f"    - Reasoning steps: {reasoning_count}")
            print(f"    - ReAct pattern followed: {analysis['react_pattern_followed']}")
            print(f"    - Expected tools used: {analysis['tools_matched']}")
            print(f"    - Tools used: {analysis['tools_used']}")

        return analysis

    except Exception as e:
        print(f"❌ Error testing {coach_name}: {e}")
        return {
            "coach": coach_name,
            "query": query,
            "success": False,
            "error": str(e),
            "tool_calls": [],
            "reasoning_steps": [],
            "react_pattern_followed": False,
        }


async def test_react_reasoning_flow():
    """Test the ReAct reasoning flow with a complex multi-step query."""
    print("\n🧠 Testing ReAct Reasoning Flow")
    print("=" * 80)

    complex_query = """I'm a beginner who wants to start strength training. I have a previous knee injury 
    and I'm vegetarian. Can you help me understand what RPE is, find some beginner-friendly exercises 
    that are safe for my knee, and create a starter workout plan?"""

    try:
        from langchain_core.messages import HumanMessage

        from athlea_langgraph.agents.specialized_coaches_react import (
            get_specialized_coach,
        )
        from athlea_langgraph.state import AgentState

        coach = await get_specialized_coach("strength_coach")
        if not coach:
            print("❌ Strength coach not available")
            return

        test_state = AgentState(
            messages=[HumanMessage(content=complex_query)],
            user_query=complex_query,
            user_profile={
                "name": "Beginner User",
                "fitness_level": "beginner",
                "goals": ["start strength training", "learn basics"],
                "restrictions": {
                    "injuries": ["knee injury"],
                    "dietary": ["vegetarian"],
                    "time_constraints": [],
                },
            },
        )

        print(f"🎯 Complex Query: {complex_query}")
        print("\n🤖 Executing ReAct flow...")

        result = await coach.invoke(test_state)

        if result.get("messages"):
            print(f"\n📋 ReAct Flow Analysis:")
            messages = result["messages"]

            for i, message in enumerate(messages):
                print(f"\nStep {i+1}:")
                if hasattr(message, "tool_calls") and message.tool_calls:
                    print(f"  🔧 ACTION: Used {len(message.tool_calls)} tools")
                    for tc in message.tool_calls:
                        print(f"    - {tc['name']}")
                elif hasattr(message, "content"):
                    if len(message.content) > 200:
                        print(f"  💭 REASONING/RESPONSE: {message.content[:200]}...")
                    else:
                        print(f"  💭 REASONING/RESPONSE: {message.content}")

            print(f"\n✅ Complex ReAct flow completed with {len(messages)} steps")

    except Exception as e:
        print(f"❌ Error in ReAct reasoning flow test: {e}")


async def test_all_react_scenarios():
    """Test all ReAct scenarios and provide comprehensive analysis."""
    print("🚀 ReAct Specialized Coaches Testing")
    print("=" * 80)
    print("Testing ReAct pattern implementation with proper reasoning and tool usage")
    print("=" * 80)

    results = []

    # Test individual scenarios
    for i, scenario in enumerate(REACT_TEST_SCENARIOS, 1):
        print(f"\n📋 Scenario {i}/{len(REACT_TEST_SCENARIOS)}")

        result = await test_react_coach(scenario["coach"], scenario["query"], scenario)

        results.append(result)

        # Small delay between tests
        await asyncio.sleep(2)

    # Test complex reasoning flow
    await test_react_reasoning_flow()

    # Generate comprehensive summary
    print("\n" + "=" * 80)
    print("📊 REACT IMPLEMENTATION ANALYSIS")
    print("=" * 80)

    successful_tests = [r for r in results if r.get("success", False)]
    react_pattern_tests = [r for r in results if r.get("react_pattern_followed", False)]
    tool_using_tests = [r for r in results if r.get("tool_calls")]
    matched_tool_tests = [r for r in results if r.get("tools_matched", False)]

    print(f"Total scenarios tested: {len(results)}")
    print(f"Successful executions: {len(successful_tests)}/{len(results)}")
    print(f"ReAct pattern followed: {len(react_pattern_tests)}/{len(results)}")
    print(f"Tests that made tool calls: {len(tool_using_tests)}/{len(results)}")
    print(f"Tests that used expected tools: {len(matched_tool_tests)}/{len(results)}")

    # ReAct pattern analysis
    print(f"\n🧠 ReAct Pattern Analysis:")
    pattern_success_rate = (
        len(react_pattern_tests) / len(results) * 100 if results else 0
    )
    print(f"  ReAct pattern success rate: {pattern_success_rate:.1f}%")

    # Tool usage breakdown
    print(f"\n🔧 Tool Usage Breakdown:")
    tool_usage = {}
    for result in results:
        for tool in result.get("tools_used", []):
            tool_usage[tool] = tool_usage.get(tool, 0) + 1

    for tool, count in sorted(tool_usage.items()):
        print(f"  {tool}: {count} times")

    # Coach performance with ReAct
    print(f"\n👥 Coach ReAct Performance:")
    coach_stats = {}
    for result in results:
        coach = result.get("coach", "unknown")
        if coach not in coach_stats:
            coach_stats[coach] = {
                "total": 0,
                "react_pattern": 0,
                "tool_calls": 0,
                "matched": 0,
            }

        coach_stats[coach]["total"] += 1
        if result.get("react_pattern_followed"):
            coach_stats[coach]["react_pattern"] += 1
        if result.get("tool_calls"):
            coach_stats[coach]["tool_calls"] += 1
        if result.get("tools_matched"):
            coach_stats[coach]["matched"] += 1

    for coach, stats in sorted(coach_stats.items()):
        react_rate = (
            stats["react_pattern"] / stats["total"] * 100 if stats["total"] else 0
        )
        tool_rate = stats["tool_calls"] / stats["total"] * 100 if stats["total"] else 0
        match_rate = stats["matched"] / stats["total"] * 100 if stats["total"] else 0
        print(f"  {coach}:")
        print(
            f"    - ReAct pattern: {react_rate:.1f}% ({stats['react_pattern']}/{stats['total']})"
        )
        print(
            f"    - Tool usage: {tool_rate:.1f}% ({stats['tool_calls']}/{stats['total']})"
        )
        print(
            f"    - Expected tools: {match_rate:.1f}% ({stats['matched']}/{stats['total']})"
        )

    # Test type analysis
    print(f"\n📋 Test Type Analysis:")
    test_types = {}
    for i, scenario in enumerate(REACT_TEST_SCENARIOS):
        test_type = scenario["test_type"]
        if test_type not in test_types:
            test_types[test_type] = {"total": 0, "success": 0, "react": 0}

        test_types[test_type]["total"] += 1
        if i < len(results) and results[i].get("success"):
            test_types[test_type]["success"] += 1
        if i < len(results) and results[i].get("react_pattern_followed"):
            test_types[test_type]["react"] += 1

    for test_type, stats in sorted(test_types.items()):
        success_rate = stats["success"] / stats["total"] * 100 if stats["total"] else 0
        react_rate = stats["react"] / stats["total"] * 100 if stats["total"] else 0
        print(
            f"  {test_type}: {success_rate:.1f}% success, {react_rate:.1f}% ReAct pattern"
        )

    # Failed scenarios
    failed_results = [r for r in results if not r.get("success", False)]
    if failed_results:
        print(f"\n❌ Failed Scenarios:")
        for result in failed_results:
            print(f"  {result['coach']}: {result.get('error', 'Unknown error')}")

    print(f"\n✅ ReAct specialized coaches testing completed!")
    print(f"🎯 Key Findings:")
    print(
        f"  - ReAct pattern implementation: {'✅ Working' if pattern_success_rate > 70 else '⚠️ Needs improvement'}"
    )
    print(
        f"  - Tool integration: {'✅ Working' if len(tool_using_tests) > len(results) * 0.7 else '⚠️ Needs improvement'}"
    )
    print(
        f"  - Expected tool usage: {'✅ Working' if len(matched_tool_tests) > len(results) * 0.6 else '⚠️ Needs improvement'}"
    )

    return results


async def test_strength_coach_accuracy_with_tracing():
    """Test strength coach accuracy with detailed quality assessment and tracing."""
    import json
    import uuid
    from datetime import datetime
    from athlea_langgraph.graphs.individual_graph import create_strength_coach_graph
    from athlea_langgraph.states.state import AgentState
    from langchain_core.messages import HumanMessage

    print("🏋️ STRENGTH COACH ACCURACY TESTING WITH TRACING")
    print("=" * 80)

    # Define test scenarios with expected elements
    test_scenarios = [
        {
            "name": "deadlift_form_correction",
            "query": "My lower back hurts after deadlifts. I think my form is off. I'm lifting 225 lbs for 5 reps but the pain starts during the lift.",
            "user_profile": {
                "name": "Intermediate Lifter",
                "fitness_level": "intermediate",
                "goals": ["strength", "injury prevention"],
                "restrictions": {
                    "injuries": ["lower back strain 6 months ago"],
                    "dietary": [],
                    "time_constraints": []
                },
                "current_maxes": {"deadlift": "225 lbs x 5 reps"},
                "training_frequency": "3x per week",
                "age": 28
            },
            "expected_elements": [
                "hip hinge", "neutral spine", "bar path",
                "weight reduction", "form assessment", "safety", "pain acknowledgment"
            ],
            "safety_required": True,
            "specificity_threshold": 0.8
        },
        {
            "name": "bench_press_plateau",
            "query": "I've been stuck at 185 lbs for 3x5 bench press for 2 months. I train 3 times per week but can't seem to progress. What should I change?",
            "user_profile": {
                "name": "Intermediate Lifter",
                "fitness_level": "intermediate",
                "goals": ["strength gains", "muscle building"],
                "restrictions": {"injuries": [], "dietary": [], "time_constraints": []},
                "current_lifts": {"bench_press": "185 lbs x 3x5"},
                "training_frequency": "3x per week",
                "plateau_duration": "2 months",
                "age": 32
            },
            "expected_elements": [
                "deload", "progression strategies", "rep manipulation",
                "frequency adjustment", "timeline expectations"
            ],
            "safety_required": False,
            "specificity_threshold": 0.8
        }
    ]

    try:
        from athlea_langgraph.states.state import AgentState

        coach = await create_strength_coach_graph()
        if not coach:
            print("❌ Strength coach graph not available")
            return {"success": False, "error": "Coach graph not available"}

        results = []
        passed_tests = 0

        for scenario in test_scenarios:
            # Generate unique thread ID for traceability
            thread_id = f"strength_accuracy_{scenario['name']}_{uuid.uuid4().hex[:8]}"

            print(f"\n{'='*60}")
            print(f"🏋️ Executing: {scenario['name']}")
            print(f"🔍 Thread ID: {thread_id}")
            print(f"📝 Query: {scenario['query'][:100]}...")

            # Create test state
            test_state = AgentState(
                messages=[HumanMessage(content=scenario['query'])],
                user_query=scenario['query'],
                user_profile=scenario['user_profile'],
                thread_id=thread_id,
                coach_type="strength_coach"
            )

            try:
                # Execute coach graph
                start_time = datetime.now()
                config = {"configurable": {"thread_id": thread_id}}
                result = await coach.ainvoke(test_state, config=config)
                end_time = datetime.now()

                execution_duration = (end_time - start_time).total_seconds()

                # Extract final response with comprehensive debugging
                print(f"🔍 DEBUG: Graph result type: {type(result)}")
                print(f"🔍 DEBUG: Graph result attributes: {[attr for attr in dir(result) if not attr.startswith('_')]}")

                final_response = ""
                message_count = 0

                # Method 1: Check result.messages (original approach)
                if hasattr(result, 'messages') and result.messages:
                    message_count = len(result.messages)
                    print(f"🔍 DEBUG: Found {message_count} messages in result.messages")
                    for i, msg in enumerate(result.messages):
                        print(f"🔍 DEBUG: Message {i}: type={type(msg)}, attributes={[attr for attr in dir(msg) if not attr.startswith('_')]}")
                        if hasattr(msg, 'content'):
                            content = str(msg.content)
                            print(f"🔍 DEBUG: Message {i} content: {content[:100]}...")
                            final_response += content + " "

                # Method 2: Check for direct response field
                elif hasattr(result, 'response'):
                    print(f"🔍 DEBUG: Found result.response: {str(result.response)[:100]}...")
                    final_response = str(result.response)

                # Method 3: Check for output field
                elif hasattr(result, 'output'):
                    print(f"🔍 DEBUG: Found result.output: {str(result.output)[:100]}...")
                    final_response = str(result.output)

                # Method 4: Check if result is a dictionary (PRIORITY: final_response field)
                elif isinstance(result, dict):
                    print(f"🔍 DEBUG: Result is dict with keys: {list(result.keys())}")
                    # Check final_response first (this is where LangGraph stores the response)
                    if 'final_response' in result and result['final_response']:
                        final_response = str(result['final_response'])
                        print(f"🔍 DEBUG: Found response in final_response: {final_response[:100]}...")
                    # Fallback to other fields
                    else:
                        final_response = result.get('response', result.get('output', result.get('content', '')))
                        if final_response:
                            print(f"🔍 DEBUG: Found response in fallback dict: {str(final_response)[:100]}...")

                # Method 5: Check for state-based response
                elif hasattr(result, '__dict__'):
                    print(f"🔍 DEBUG: Result dict contents: {list(result.__dict__.keys())}")
                    result_dict = result.__dict__
                    # Check final_response first
                    if 'final_response' in result_dict and result_dict['final_response']:
                        final_response = str(result_dict['final_response'])
                        print(f"🔍 DEBUG: Found response in final_response: {final_response[:100]}...")
                    else:
                        for key in ['response', 'output', 'content']:
                            if key in result_dict and result_dict[key]:
                                final_response = str(result_dict[key])
                                print(f"🔍 DEBUG: Found response in {key}: {final_response[:100]}...")
                                break

                final_response = final_response.strip()

                # Additional debugging
                print(f"🔍 DEBUG: Final extracted response length: {len(final_response)}")
                if not final_response:
                    print(f"🔍 DEBUG: No response found - investigating result structure...")
                    if hasattr(result, '__dict__'):
                        for key, value in result.__dict__.items():
                            if value and not key.startswith('_'):
                                print(f"🔍 DEBUG: {key}: {str(value)[:50]}...")
                    elif isinstance(result, dict):
                        for key, value in result.items():
                            if value:
                                print(f"🔍 DEBUG: {key}: {str(value)[:50]}...")

                print(f"⏱️ Execution time: {execution_duration:.2f}s")
                print(f"💬 Messages generated: {message_count}")
                print(f"📝 Response length: {len(final_response)} chars")

                if final_response:
                    print(f"📄 Response preview: {final_response[:150]}...")

                    # Analyze response quality
                    response_analysis = analyze_strength_response(
                        final_response,
                        scenario['query'],
                        scenario['user_profile'],
                        scenario['expected_elements'],
                        scenario['safety_required'],
                        scenario['specificity_threshold']
                    )

                    # Display detailed results
                    print(f"\n📊 DETAILED ANALYSIS:")
                    print(f"  - Specificity Score: {response_analysis['specificity_score']:.2f}")
                    print(f"  - Safety Score: {response_analysis['safety_score']:.2f}")
                    print(f"  - Expected Elements Found: {response_analysis['found_elements_count']}/{len(scenario['expected_elements'])}")
                    print(f"  - Critical Issues: {response_analysis['critical_issues']}")

                    # Pass/Fail determination
                    passes_criteria = (
                        response_analysis['specificity_score'] >= scenario['specificity_threshold'] and
                        response_analysis['safety_score'] >= (0.9 if scenario['safety_required'] else 0.5) and
                        len(response_analysis['critical_issues']) == 0 and
                        response_analysis['elements_coverage'] >= 0.6
                    )

                    status = "✅ PASS" if passes_criteria else "❌ FAIL"
                    print(f"\n🎯 RESULT: {status}")

                    if not passes_criteria:
                        print(f"  📋 Failure reasons:")
                        if response_analysis['specificity_score'] < scenario['specificity_threshold']:
                            print(f"  - Specificity too low: {response_analysis['specificity_score']:.2f} < {scenario['specificity_threshold']}")
                        if response_analysis['safety_score'] < (0.9 if scenario['safety_required'] else 0.5):
                            print(f"  - Safety score too low: {response_analysis['safety_score']:.2f} < 0.9")
                        if response_analysis['critical_issues']:
                            print(f"  - Critical issues: {', '.join(response_analysis['critical_issues'])}")
                        if response_analysis['elements_coverage'] < 0.6:
                            print(f"  - Missing expected elements: {response_analysis['missing_elements']}")

                    if passes_criteria:
                        passed_tests += 1

                    # Store result with full traceability
                    scenario_result = {
                        "test_metadata": {
                            "phase": "2.1",
                            "epic": "2.1-individual-coach-accuracy-testing",
                            "ticket_id": "E2.1-T1",
                            "ticket_name": "strength-coach-accuracy-testing",
                            "scenario_id": scenario['name'],
                            "execution_timestamp": start_time.strftime("%Y-%m-%d %H:%M:%S UTC"),
                            "execution_date": start_time.strftime("%B %d, %Y"),
                            "execution_time_readable": start_time.strftime("%I:%M:%S %p"),
                            "thread_id": thread_id
                        },
                        "test_execution": {
                            "query": scenario['query'],
                            "user_profile": scenario['user_profile'],
                            "execution_duration_seconds": execution_duration,
                            "message_count": message_count,
                            "success": True,
                        },
                        "coach_response": {
                            "final_response": final_response,
                            "response_length_chars": len(final_response),
                            "response_preview": final_response[:200] if final_response else ""
                        },
                        "quality_assessment": response_analysis,
                        "test_outcome": {
                            "passes_criteria": passes_criteria,
                            "pass_status": "PASS" if passes_criteria else "FAIL",
                            "expected_elements": scenario['expected_elements'],
                            "safety_required": scenario['safety_required'],
                            "specificity_threshold": scenario['specificity_threshold'],
                            "failure_reasoning": response_analysis.get('failure_reasoning', ''),
                            "human_review_notes": ""
                        }
                    }

                else:
                    print("❌ No response generated")
                    scenario_result = {
                        "test_metadata": {
                            "phase": "2.1",
                            "epic": "2.1-individual-coach-accuracy-testing",
                            "ticket_id": "E2.1-T1",
                            "ticket_name": "strength-coach-accuracy-testing",
                            "scenario_id": scenario['name'],
                            "execution_timestamp": start_time.strftime("%Y-%m-%d %H:%M:%S UTC"),
                            "execution_date": start_time.strftime("%B %d, %Y"),
                            "execution_time_readable": start_time.strftime("%I:%M:%S %p"),
                            "thread_id": thread_id
                        },
                        "test_execution": {
                            "query": scenario['query'],
                            "user_profile": scenario['user_profile'],
                            "execution_duration_seconds": execution_duration,
                            "message_count": message_count,
                            "success": False,
                            "error": "No response generated"
                        },
                        "coach_response": {
                            "final_response": "",
                            "response_length_chars": 0,
                            "response_preview": ""
                        },
                        "quality_assessment": {"error": True},
                        "test_outcome": {
                            "passes_criteria": False,
                            "pass_status": "ERROR",
                            "expected_elements": scenario['expected_elements'],
                            "safety_required": scenario['safety_required'],
                            "specificity_threshold": scenario['specificity_threshold'],
                            "failure_reasoning": "No response generated",
                            "human_review_notes": ""
                        }
                    }

            except Exception as e:
                print(f"❌ Error executing scenario: {e}")
                import traceback
                traceback.print_exc()

                scenario_result = {
                    "test_metadata": {
                        "phase": "2.1",
                        "epic": "2.1-individual-coach-accuracy-testing",
                        "ticket_id": "E2.1-T1",
                        "ticket_name": "strength-coach-accuracy-testing",
                        "scenario_id": scenario['name'],
                        "execution_timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC"),
                        "execution_date": datetime.now().strftime("%B %d, %Y"),
                        "execution_time_readable": datetime.now().strftime("%I:%M:%S %p"),
                        "thread_id": thread_id
                    },
                    "test_execution": {
                        "query": scenario['query'],
                        "user_profile": scenario['user_profile'],
                        "execution_duration_seconds": 0,
                        "message_count": 0,
                        "success": False,
                        "error": str(e)
                    },
                    "coach_response": {
                        "final_response": "",
                        "response_length_chars": 0,
                        "response_preview": ""
                    },
                    "quality_assessment": {"error": True},
                    "test_outcome": {
                        "passes_criteria": False,
                        "pass_status": "ERROR",
                        "expected_elements": scenario['expected_elements'],
                        "safety_required": scenario['safety_required'],
                        "specificity_threshold": scenario['specificity_threshold'],
                        "failure_reasoning": f"Test execution failed with error: {str(e)}",
                        "human_review_notes": ""
                    }
                }

            results.append(scenario_result)

        # Generate organized results
        overall_success = passed_tests == len(test_scenarios)
        timestamp = datetime.now()

        # Calculate quality metrics summary
        valid_assessments = [r['quality_assessment'] for r in results
                           if not r['quality_assessment'].get('error', False)]

        if valid_assessments:
            avg_specificity = sum(qa.get('specificity_score', 0) for qa in valid_assessments) / len(valid_assessments)
            avg_safety = sum(qa.get('safety_score', 0) for qa in valid_assessments) / len(valid_assessments)
            avg_response_length = sum(qa.get('response_length', 0) for qa in valid_assessments) / len(valid_assessments)
            total_critical_issues = sum(len(qa.get('critical_issues', [])) for qa in valid_assessments)
        else:
            avg_specificity = avg_safety = avg_response_length = total_critical_issues = 0

        organized_results = {
            "test_suite_metadata": {
                "phase": "2.1",
                "epic": "2.1-individual-coach-accuracy-testing",
                "ticket_id": "E2.1-T1",
                "ticket_name": "strength-coach-accuracy-testing",
                "execution_summary": {
                    "execution_timestamp": timestamp.strftime("%Y-%m-%d %H:%M:%S UTC"),
                    "execution_date": timestamp.strftime("%B %d, %Y"),
                    "execution_time": timestamp.strftime("%I:%M:%S %p"),
                    "total_scenarios": len(test_scenarios),
                    "passed_scenarios": passed_tests,
                    "failed_scenarios": len(test_scenarios) - passed_tests,
                    "pass_rate_percentage": f"{(passed_tests/len(test_scenarios)*100):.1f}%",
                    "overall_status": "PASS" if overall_success else "REQUIRES_INVESTIGATION"
                }
            },
            "test_scenarios": {result['test_metadata']['scenario_id']: result for result in results},
            "thread_traceability": {
                "langsmith_urls": {
                    result['test_metadata']['scenario_id']:
                    f"https://smith.langchain.com/o/[org]/projects/p/[project]/r/{result['test_metadata']['thread_id']}"
                    for result in results
                },
                "thread_ids": [result['test_metadata']['thread_id'] for result in results]
            },
            "quality_metrics_summary": {
                "average_specificity_score": avg_specificity,
                "average_safety_score": avg_safety,
                "critical_issues_total": total_critical_issues,
                "response_length_average": avg_response_length
            }
        }

        # Save results to file
        filename = f"E2.1-T1_strength_coach_accuracy_{timestamp.strftime('%B_%d_%Y_at_%I_%M_%S_%p')}.json"
        with open(f"tests/results/{filename}", "w") as f:
            json.dump(organized_results, f, indent=2, default=str)

        print(f"\n💾 Detailed results saved to: tests/results/{filename}")

        return {
            "success": overall_success,
            "pass_rate": passed_tests/len(test_scenarios),
            "organized_results": organized_results,
            "filename": filename,
            "phase": "2.1",
            "ticket_id": "E2.1-T1"
        }

    except Exception as e:
        print(f"❌ Critical error in strength coach accuracy testing: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


def analyze_strength_response(response: str, query: str, user_profile: dict, expected_elements: list, safety_required: bool, specificity_threshold: float) -> dict:
    """Analyze strength coach response quality with detailed metrics and enhanced actionable feedback."""
    import re

    # Specificity analysis with transparent calculation
    specific_patterns = [
        r"\d+\s*(lbs?|kg|pounds?|kilograms?)",  # Weights
        r"\d+\s*x\s*\d+",  # Rep/set schemes
        r"(hip hinge|neutral spine|bar path|scapular retraction)",  # Technical cues
        r"(deload|periodization|progressive overload)",  # Programming
        r"(glutes?|hamstrings?|erector spinae|latissimus)",  # Muscles
    ]

    specificity_hits = sum(1 for pattern in specific_patterns
                          if re.search(pattern, response, re.IGNORECASE))
    specificity_score = specificity_hits / len(specific_patterns)

    # Safety analysis with transparent calculation
    safety_patterns = [
        r"(reduce|lower|decrease).*weight",
        r"(pain|hurt|injury|strain)",
        r"(doctor|physician|healthcare|medical)",
        r"(warm.?up|mobility|stretch)",
        r"(gradual|progressive|slow)",
        r"(form|technique).*first"
    ]

    safety_hits = sum(1 for pattern in safety_patterns
                     if re.search(pattern, response, re.IGNORECASE))
    safety_score = min(safety_hits / 3, 1.0)

    # Enhanced element tracking - both found and missing
    found_elements_list = []
    missing_elements = []

    for element in expected_elements:
        if element.lower() in response.lower():
            found_elements_list.append(element)
        else:
            missing_elements.append(element)

    found_elements_count = len(found_elements_list)
    elements_coverage = found_elements_count / len(expected_elements) if expected_elements else 1.0

    # Critical issues detection
    critical_issues = []

    # Check for pain acknowledgment
    if "pain" in query.lower() and "pain" not in response.lower():
        critical_issues.append("ignores_pain")

    # Check for injury history consideration
    if user_profile.get("restrictions", {}).get("injuries") and "injury" not in response.lower():
        critical_issues.append("ignores_injury_history")

    # Check for generic advice
    generic_phrases = ["maintain good form", "proper technique", "listen to your body"]
    if any(phrase in response.lower() for phrase in generic_phrases):
        critical_issues.append("generic_advice")

    # Failure reasoning for improvement recommendations
    failure_reasoning = ""
    improvement_recommendations = {}

    if specificity_score < specificity_threshold:
        failure_reasoning += f"Specificity score {specificity_score:.2f} below threshold {specificity_threshold}. "
        improvement_recommendations["specificity"] = {
            "issue": "Response lacks specific technical details",
            "recommendations": [
                "Include specific weights (lbs/kg) in recommendations",
                "Provide exact rep/set schemes (e.g., 3x5, 4x8-12)",
                "Use technical terminology (hip hinge, neutral spine, bar path)",
                "Mention specific muscle groups (glutes, hamstrings, erector spinae)"
            ]
        }

    if safety_required and safety_score < 0.9:
        failure_reasoning += f"Safety score {safety_score:.2f} below required 0.9 for safety-critical scenario. "
        improvement_recommendations["safety"] = {
            "issue": "Insufficient safety considerations for injury-related query",
            "recommendations": [
                "Acknowledge pain/injury concerns explicitly",
                "Recommend medical consultation when appropriate",
                "Emphasize weight reduction before technique work",
                "Include warm-up and mobility recommendations"
            ]
        }

    if len(missing_elements) > len(expected_elements) * 0.4:
        failure_reasoning += f"Missing {len(missing_elements)} of {len(expected_elements)} expected elements. "
        improvement_recommendations["content_coverage"] = {
            "issue": "Response missing key domain-specific elements",
            "missing_elements": missing_elements,
            "recommendations": [
                f"Include discussion of: {', '.join(missing_elements[:3])}",
                "Address all aspects of the user's specific question",
                "Provide comprehensive guidance covering technical, safety, and programming aspects"
            ]
        }

    if critical_issues:
        failure_reasoning += f"Critical issues detected: {', '.join(critical_issues)}. "
        improvement_recommendations["critical_issues"] = {
            "issue": "Response contains critical UX/safety issues",
            "critical_issues": critical_issues,
            "recommendations": [
                "Always acknowledge pain/injury concerns",
                "Avoid generic advice - be specific and actionable",
                "Consider user's injury history in recommendations"
            ]
        }

    # Transparent score calculation documentation
    score_calculation_method = {
        "specificity_score": f"Calculated as ({specificity_hits}/{len(specific_patterns)}) where specific patterns include: weights (lbs/kg), rep schemes (3x5), technical cues (hip hinge, bar path), programming concepts (deload, periodization), and muscle names (glutes, hamstrings).",
        "safety_score": f"Calculated as min({safety_hits}/3, 1.0) where safety patterns include: weight reduction advice, pain acknowledgment, medical consultation, warm-up/mobility, gradual progression, and form-first approach.",
        "elements_coverage": f"Calculated as ({found_elements_count}/{len(expected_elements)}) representing the percentage of expected domain-specific elements found in the response."
    }

    return {
        "specificity_score": specificity_score,
        "safety_score": safety_score,
        "found_elements_count": found_elements_count,
        "found_elements_list": found_elements_list,
        "elements_coverage": elements_coverage,
        "missing_elements": missing_elements,
        "critical_issues": critical_issues,
        "response_length": len(response),
        "specific_patterns_found": specificity_hits,
        "safety_patterns_found": safety_hits,
        "score_calculation_method": score_calculation_method,
        "failure_reasoning": failure_reasoning,
        "improvement_recommendations": improvement_recommendations
    }


if __name__ == "__main__":
    asyncio.run(test_all_react_scenarios())
