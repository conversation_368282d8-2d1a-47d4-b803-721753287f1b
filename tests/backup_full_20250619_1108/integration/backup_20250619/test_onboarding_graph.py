#!/usr/bin/env python3
"""
Test script for the onboarding graph implementation
"""

import asyncio
import logging

from langchain_core.messages import HumanMessage

from athlea_langgraph import (
    create_initial_onboarding_state,
    create_onboarding_graph_factory,
    get_graph_by_type,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_onboarding_basic_flow():
    """Test basic onboarding flow"""
    logger.info("=== Testing Basic Onboarding Flow ===")

    # Create onboarding graph
    graph = create_onboarding_graph_factory(checkpointer_type="memory")

    # Create initial state
    initial_state = create_initial_onboarding_state("test_user_001")
    initial_state["messages"] = [
        HumanMessage(content="Hi, I want to get started with fitness!")
    ]
    initial_state["user_input"] = "Hi, I want to get started with fitness!"

    logger.info(f"Initial state created for user: {initial_state['user_id']}")

    # Test initial conversation
    config = {"configurable": {"thread_id": "test_thread_001"}}

    try:
        result = await graph.ainvoke(initial_state, config)
        logger.info("=== Graph execution completed ===")
        logger.info(
            f"Final onboarding stage: {result.get('onboarding_stage', 'unknown')}"
        )
        logger.info(f"Has enough info: {result.get('has_enough_info', False)}")
        logger.info(f"Needs input: {result.get('needs_input', False)}")

        # Check messages
        messages = result.get("messages", [])
        if messages:
            last_message = messages[-1]
            logger.info(f"Last AI message: {last_message.content[:100]}...")

        # Check sidebar data
        sidebar_data = result.get("sidebar_data")
        if sidebar_data:
            logger.info(f"Current stage: {sidebar_data.current_stage}")
            logger.info(f"Goals extracted: {sidebar_data.goals.exists}")
            if sidebar_data.goals.exists:
                logger.info(f"Goals: {sidebar_data.goals.goal_list}")
            logger.info(f"Summary items: {len(sidebar_data.summary_items)}")

            # Check sport suggestions
            if sidebar_data.sport_suggestions:
                logger.info(
                    f"Sport suggestions provided: {len(sidebar_data.sport_suggestions)}"
                )

        return result

    except Exception as e:
        logger.error(f"Error during graph execution: {e}")
        raise


async def test_onboarding_with_sport_selection():
    """Test onboarding with sport selection"""
    logger.info("=== Testing Onboarding with Sport Selection ===")

    # Create onboarding graph
    graph = create_onboarding_graph_factory(checkpointer_type="memory")

    # Create initial state with sport selection
    initial_state = create_initial_onboarding_state("test_user_002")
    initial_state["messages"] = [
        HumanMessage(content="Hi, I want to get started with fitness!"),
        HumanMessage(content="Running"),  # Sport selection
    ]
    initial_state["user_input"] = "Running"

    logger.info("Testing sport selection: Running")

    config = {"configurable": {"thread_id": "test_thread_002"}}

    try:
        result = await graph.ainvoke(initial_state, config)
        logger.info("=== Sport selection test completed ===")

        # Check if sport was selected
        sidebar_data = result.get("sidebar_data")
        if sidebar_data:
            logger.info(f"Selected sport: {sidebar_data.selected_sport}")
            logger.info(f"Selected sports list: {sidebar_data.selected_sports}")

        return result

    except Exception as e:
        logger.error(f"Error during sport selection test: {e}")
        raise


async def test_graph_factory():
    """Test the graph factory functionality"""
    logger.info("=== Testing Graph Factory ===")

    # Test getting onboarding graph via factory
    onboarding_graph = get_graph_by_type("onboarding", checkpointer_type="memory")
    coaching_graph = get_graph_by_type("coaching", checkpointer_type="memory")

    logger.info(f"Onboarding graph type: {type(onboarding_graph)}")
    logger.info(f"Coaching graph type: {type(coaching_graph)}")

    # Test that they are different instances
    assert onboarding_graph != coaching_graph
    logger.info("✓ Graphs are different instances")

    # Test invalid graph type
    try:
        invalid_graph = get_graph_by_type("invalid_type")
        assert False, "Should have raised ValueError"
    except ValueError as e:
        logger.info(f"✓ Correctly rejected invalid graph type: {e}")


def test_state_creation():
    """Test state creation and validation"""
    logger.info("=== Testing State Creation ===")

    # Test initial state creation
    state = create_initial_onboarding_state("test_user_123")

    logger.info(f"User ID: {state['user_id']}")
    logger.info(f"Onboarding stage: {state['onboarding_stage']}")
    logger.info(f"Has enough info: {state['has_enough_info']}")
    logger.info(f"Needs input: {state['needs_input']}")

    # Verify default values
    assert state["user_id"] == "test_user_123"
    assert state["onboarding_stage"] == "initial"
    assert state["has_enough_info"] == False
    assert state["needs_input"] == False
    assert len(state["messages"]) == 0

    logger.info("✓ State creation validation passed")


async def main():
    """Run all tests"""
    logger.info("Starting onboarding graph tests...")

    # Test state creation
    test_state_creation()

    # Test graph factory
    await test_graph_factory()

    # Test basic onboarding flow
    await test_onboarding_basic_flow()

    # Test sport selection
    await test_onboarding_with_sport_selection()

    logger.info("All tests completed successfully! ✓")


if __name__ == "__main__":
    asyncio.run(main())
