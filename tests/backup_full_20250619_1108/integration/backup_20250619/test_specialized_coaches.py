"""
Test Script for Specialized Coaches with Memory-Enhanced Coaching

This script demonstrates how to test the specialized coaches functionality
integrated with the memory-enhanced coaching system.

Features tested:
1. Individual specialized coach nodes
2. Tool integration for each coach
3. Memory-enhanced coaching with specialist routing
4. Cross-session memory persistence
5. Personalized responses based on coach expertise
"""

import asyncio
import logging
import os
from datetime import datetime
from typing import Any, Dict, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class SpecializedCoachTester:
    """
    Comprehensive tester for specialized coaches functionality.
    """

    def __init__(self, mongodb_uri: str):
        self.mongodb_uri = mongodb_uri

    async def test_individual_coaches(self) -> None:
        """Test individual specialized coach nodes."""
        print("🏋️‍♂️ Testing Individual Specialized Coaches")
        print("=" * 60)

        # Import the specialized coaches
        from langchain_core.messages import HumanMessage

        from athlea_langgraph.agents.coach_manager import (
            cardio_coach_node,
            cycling_coach_node,
            get_tools_manager,
            mental_coach_node,
            nutrition_coach_node,
            recovery_coach_node,
            running_coach_node,
            strength_coach_node,
        )
        from athlea_langgraph.state import AgentState

        # Initialize tools manager
        tools_manager = await get_tools_manager()
        print(f"✓ Tools manager initialized")

        # Test cases for each coach
        test_cases = [
            {
                "coach": strength_coach_node,
                "name": "Strength Coach",
                "query": "I want to build muscle and get stronger. Can you help me create a strength training plan?",
                "expected_expertise": [
                    "strength training",
                    "muscle building",
                    "resistance",
                ],
            },
            {
                "coach": running_coach_node,
                "name": "Running Coach",
                "query": "I'm training for a 10K race. What running plan should I follow?",
                "expected_expertise": ["running", "10K", "training plan"],
            },
            {
                "coach": cardio_coach_node,
                "name": "Cardio Coach",
                "query": "I want to improve my cardiovascular fitness. What cardio exercises do you recommend?",
                "expected_expertise": ["cardiovascular", "cardio", "fitness"],
            },
            {
                "coach": cycling_coach_node,
                "name": "Cycling Coach",
                "query": "I'm new to cycling. Can you help me get started with road cycling?",
                "expected_expertise": ["cycling", "road cycling", "beginner"],
            },
            {
                "coach": nutrition_coach_node,
                "name": "Nutrition Coach",
                "query": "I'm vegetarian and want to optimize my nutrition for muscle building. What should I eat?",
                "expected_expertise": ["nutrition", "vegetarian", "muscle building"],
            },
            {
                "coach": recovery_coach_node,
                "name": "Recovery Coach",
                "query": "I'm feeling overtrained and need help with recovery. What should I do?",
                "expected_expertise": ["recovery", "overtraining", "rest"],
            },
            {
                "coach": mental_coach_node,
                "name": "Mental Coach",
                "query": "I struggle with motivation to exercise. Can you help me stay consistent?",
                "expected_expertise": ["motivation", "consistency", "mental"],
            },
        ]

        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. Testing {test_case['name']}")
            print("-" * 40)

            # Create test state
            test_state = {
                "messages": [HumanMessage(content=test_case["query"])],
                "user_query": test_case["query"],
                "user_profile": {
                    "name": "Test User",
                    "fitness_level": "intermediate",
                    "goals": ["general fitness"],
                },
                "routing_decision": None,
                "pending_agents": None,
                "plan": None,
                "current_step": None,
                "domain_contributions": {},
                "required_domains": [],
                "completed_domains": [],
                "aggregated_plan": None,
                "proceed_to_generation": False,
                "current_plan": None,
                "is_onboarding": False,
                # Initialize specialist responses
                "strength_response": None,
                "running_response": None,
                "cardio_response": None,
                "cycling_response": None,
                "nutrition_response": None,
                "recovery_response": None,
                "mental_response": None,
                # Initialize other outputs
                "reasoning_output": None,
                "clarification_output": None,
                "aggregated_response": None,
            }

            try:
                print(f"Query: {test_case['query']}")

                # Test the coach
                result = await test_case["coach"](test_state)

                # Check if we got a response
                if result and result.get("messages"):
                    response_message = result["messages"][-1]
                    response_content = (
                        response_message.content
                        if hasattr(response_message, "content")
                        else str(response_message)
                    )

                    print(f"✓ {test_case['name']} responded successfully")
                    print(f"Response preview: {response_content[:200]}...")

                    # Check for tool calls
                    if (
                        hasattr(response_message, "tool_calls")
                        and response_message.tool_calls
                    ):
                        print(f"🔧 Made {len(response_message.tool_calls)} tool calls")
                        for tool_call in response_message.tool_calls:
                            print(f"   • {tool_call.get('name', 'unknown tool')}")

                    # Basic content validation
                    response_lower = response_content.lower()
                    expertise_found = any(
                        keyword.lower() in response_lower
                        for keyword in test_case["expected_expertise"]
                    )

                    if expertise_found:
                        print(f"✓ Response shows domain expertise")
                    else:
                        print(f"⚠ Response may lack expected domain expertise")

                else:
                    print(f"❌ {test_case['name']} failed to respond")

            except Exception as e:
                print(f"❌ Error testing {test_case['name']}: {e}")
                logger.error(f"Error in {test_case['name']}: {e}")

            print()

    async def test_memory_enhanced_coaching_with_specialists(self) -> None:
        """Test memory-enhanced coaching with specialist routing."""
        print("🧠 Testing Memory-Enhanced Coaching with Specialists")
        print("=" * 60)

        from athlea_langgraph.coaching_graph_with_memory import (
            MemoryEnhancedCoachingGraph,
        )

        user_id = "specialist_test_user"
        thread_id = f"specialist_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Create coaching graph
        coaching_graph = MemoryEnhancedCoachingGraph(
            mongodb_uri=self.mongodb_uri, user_id=user_id
        )
        await coaching_graph.create_compiled_graph()

        # Setup user profile
        user_profile = {
            "name": "Sarah Wilson",
            "age": 32,
            "fitness_level": "intermediate",
            "goals": ["strength training", "weight loss", "better nutrition"],
            "preferences": {
                "workout_time": "evening",
                "workout_duration": "60 minutes",
                "preferred_activities": ["weight training", "yoga"],
            },
            "restrictions": {
                "injuries": ["lower back sensitivity"],
                "dietary": ["gluten-free"],
                "time_constraints": ["limited weekday availability"],
            },
            "created_at": datetime.now().isoformat(),
        }

        await coaching_graph.update_user_profile(user_profile)
        print(f"✓ User profile created for {user_id}")

        # Test conversations that should route to different specialists
        specialist_conversations = [
            {
                "message": "I want to start strength training but I have lower back issues. Can you help me design a safe program?",
                "expected_specialist": "strength_coach",
                "description": "Strength training query - should route to strength coach",
            },
            {
                "message": "What should I eat before and after my workouts? I'm gluten-free.",
                "expected_specialist": "nutrition_coach",
                "description": "Nutrition query - should route to nutrition coach",
            },
            {
                "message": "I'm feeling burned out from training. How can I recover better?",
                "expected_specialist": "recovery_coach",
                "description": "Recovery query - should route to recovery coach",
            },
            {
                "message": "I want to add some cardio to my routine. What do you recommend?",
                "expected_specialist": "cardio_coach",
                "description": "Cardio query - should route to cardio coach",
            },
            {
                "message": "I struggle to stay motivated. How can I build better exercise habits?",
                "expected_specialist": "mental_coach",
                "description": "Mental/motivation query - should route to mental coach",
            },
        ]

        for i, conversation in enumerate(specialist_conversations, 1):
            print(f"\n{i}. {conversation['description']}")
            print("-" * 50)
            print(f"User: {conversation['message']}")

            try:
                # Run coaching session
                result = await coaching_graph.run_coaching_session(
                    user_message=conversation["message"], thread_id=thread_id
                )

                # Display results
                print(f"🤖 Coach Response:")
                if result.get("aggregated_response"):
                    response = result["aggregated_response"]
                    print(f"{response[:300]}...")
                elif result.get("clarification_output"):
                    response = result["clarification_output"]
                    print(f"{response[:300]}...")
                else:
                    print("Processing your request...")

                # Check which specialists were involved
                specialist_responses = []
                for specialist in [
                    "strength",
                    "running",
                    "cardio",
                    "cycling",
                    "nutrition",
                    "recovery",
                    "mental",
                ]:
                    if result.get(f"{specialist}_response"):
                        specialist_responses.append(specialist)

                if specialist_responses:
                    print(
                        f"👥 Specialists consulted: {', '.join(specialist_responses)}"
                    )

                    # Check if expected specialist was involved
                    expected = conversation["expected_specialist"].replace("_coach", "")
                    if expected in specialist_responses:
                        print(f"✓ Expected specialist ({expected}) was consulted")
                    else:
                        print(f"⚠ Expected specialist ({expected}) was not consulted")
                else:
                    print("ℹ No specialist responses detected")

                # Show memory context
                if result.get("memory_context"):
                    memory_info = result["memory_context"]
                    print(
                        f"🧠 Memory: {memory_info.get('memory_count', 0)} relevant memories"
                    )

            except Exception as e:
                logger.error(f"Error in conversation {i}: {e}")
                print(f"❌ Error: {e}")

            print()

    async def test_tool_integration(self) -> None:
        """Test tool integration for specialized coaches."""
        print("🔧 Testing Tool Integration for Specialized Coaches")
        print("=" * 60)

        from athlea_langgraph.agents.coach_manager import get_tools_manager

        try:
            # Initialize tools manager
            tools_manager = await get_tools_manager()
            print("✓ Tools manager initialized")

            # Test each coach's tools
            coach_tools = {
                "Strength Coach": tools_manager.get_strength_coach_tools(),
                "Cardio Coach": tools_manager.get_cardio_coach_tools(),
                "Cycling Coach": tools_manager.get_cycling_coach_tools(),
                "Nutrition Coach": tools_manager.get_nutrition_coach_tools(),
                "Recovery Coach": tools_manager.get_recovery_coach_tools(),
                "Mental Coach": tools_manager.get_mental_coach_tools(),
            }

            for coach_name, tools in coach_tools.items():
                print(f"\n{coach_name}:")
                if tools:
                    print(f"  ✓ {len(tools)} tools available:")
                    for tool in tools:
                        tool_name = getattr(tool, "name", "unknown")
                        tool_desc = getattr(tool, "description", "No description")[:100]
                        print(f"    • {tool_name}: {tool_desc}...")
                else:
                    print(f"  ⚠ No tools available")

            # Test tool status
            print(f"\nTool Status:")
            if tools_manager._airtable_tools:
                print(f"  ✓ Airtable MCP: {len(tools_manager._airtable_tools)} tools")
            else:
                print(f"  ⚠ Airtable MCP: Not available")

            if tools_manager._google_maps_tool:
                print(f"  ✓ Google Maps Elevation: Available")
            else:
                print(f"  ⚠ Google Maps Elevation: Not available")

            if tools_manager._azure_maps_tool:
                print(f"  ✓ Azure Maps: Available")
            else:
                print(f"  ⚠ Azure Maps: Not available")

            if tools_manager._session_generation_tool:
                print(f"  ✓ Session Generation: Available")
            else:
                print(f"  ⚠ Session Generation: Not available")

            if tools_manager._azure_search_tool:
                print(f"  ✓ Azure Search: Available")
            else:
                print(f"  ⚠ Azure Search: Not available")

        except Exception as e:
            print(f"❌ Error testing tool integration: {e}")
            logger.error(f"Tool integration test failed: {e}")

    async def test_cross_session_specialist_memory(self) -> None:
        """Test cross-session memory with specialist interactions."""
        print("🔄 Testing Cross-Session Specialist Memory")
        print("=" * 60)

        from athlea_langgraph.coaching_graph_with_memory import (
            MemoryEnhancedCoachingGraph,
        )

        user_id = "cross_session_test_user"

        # Create coaching graph
        coaching_graph = MemoryEnhancedCoachingGraph(
            mongodb_uri=self.mongodb_uri, user_id=user_id
        )
        await coaching_graph.create_compiled_graph()

        # Session 1: Strength training consultation
        print("\n📅 Session 1: Initial Strength Training Consultation")
        session1_thread = f"session_1_{datetime.now().strftime('%H%M%S')}"

        result1 = await coaching_graph.run_coaching_session(
            user_message="I'm a beginner and want to start strength training. I can only workout 3 days per week.",
            thread_id=session1_thread,
        )

        print(
            f"User: I'm a beginner and want to start strength training. I can only workout 3 days per week."
        )
        if result1.get("aggregated_response"):
            print(f"Coach: {result1['aggregated_response'][:200]}...")
        else:
            print("Coach: Processing your request...")

        # Small delay to simulate time passing
        await asyncio.sleep(2)

        # Session 2: Follow-up with nutrition question
        print("\n📅 Session 2: Follow-up Nutrition Question")
        session2_thread = f"session_2_{datetime.now().strftime('%H%M%S')}"

        result2 = await coaching_graph.run_coaching_session(
            user_message="Based on my strength training plan, what should I eat to support muscle growth?",
            thread_id=session2_thread,
        )

        print(
            f"User: Based on my strength training plan, what should I eat to support muscle growth?"
        )
        if result2.get("aggregated_response"):
            print(f"Coach: {result2['aggregated_response'][:200]}...")
        else:
            print("Coach: Processing your request...")

        # Session 3: Recovery question referencing previous sessions
        print("\n📅 Session 3: Recovery Question")
        session3_thread = f"session_3_{datetime.now().strftime('%H%M%S')}"

        result3 = await coaching_graph.run_coaching_session(
            user_message="I've been following the strength plan we discussed. How should I handle recovery between sessions?",
            thread_id=session3_thread,
        )

        print(
            f"User: I've been following the strength plan we discussed. How should I handle recovery between sessions?"
        )
        if result3.get("aggregated_response"):
            print(f"Coach: {result3['aggregated_response'][:200]}...")
        else:
            print("Coach: Processing your request...")

        print("\n🧠 Cross-Session Memory Analysis:")
        print("- Session 2 should reference the strength training plan from Session 1")
        print("- Session 3 should reference both previous sessions")
        print(
            "- Each session should maintain context about user's beginner status and 3-day schedule"
        )
        print(
            "- Different specialists should be consulted while maintaining continuity"
        )


async def run_specialized_coach_tests():
    """
    Main function to run all specialized coach tests.
    """
    # Get MongoDB URI from environment
    mongodb_uri = os.getenv("MONGODB_URI")

    if not mongodb_uri:
        print("❌ Error: MONGODB_URI environment variable not set")
        print("Please set your MongoDB connection string:")
        print("export MONGODB_URI='your_mongodb_connection_string'")
        return

    try:
        tester = SpecializedCoachTester(mongodb_uri)

        print("🚀 Starting Specialized Coaches Testing Suite")
        print("This comprehensive test demonstrates specialized coach functionality")
        print("=" * 80)

        # Run all tests
        await tester.test_individual_coaches()
        await tester.test_tool_integration()
        await tester.test_memory_enhanced_coaching_with_specialists()
        await tester.test_cross_session_specialist_memory()

        print("\n✅ All tests completed!")
        print("\nKey Features Tested:")
        print("• Individual specialized coach nodes with domain expertise")
        print("• Tool integration for each coach type")
        print("• Memory-enhanced coaching with specialist routing")
        print("• Cross-session memory persistence with specialists")
        print("• Personalized responses based on coach expertise")
        print("• Tool availability and status monitoring")

    except Exception as e:
        logger.error(f"Test suite failed: {e}")
        print(f"❌ Test suite failed: {e}")


if __name__ == "__main__":
    # Run the tests
    asyncio.run(run_specialized_coach_tests())
