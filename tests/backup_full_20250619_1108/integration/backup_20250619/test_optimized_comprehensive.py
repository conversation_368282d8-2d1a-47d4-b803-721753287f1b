#!/usr/bin/env python3
"""
Comprehensive test for the optimized coaching graph.
Tests all routing scenarios and saves detailed outputs.
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime
from typing import Dict, Any

# Set up path
sys.path.append("/Users/<USER>/python-langgraph")

# Enable SSL bypass for development (fixes Azure Search SSL errors)
os.environ["AZURE_SEARCH_DISABLE_SSL_VERIFICATION"] = "true"

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

from langchain_core.messages import HumanMessage
from athlea_langgraph.graphs.coaching_graph import (
    create_optimized_test_graph,
)


class OptimizedGraphTester:
    """Comprehensive tester for the optimized coaching graph."""

    def __init__(self):
        self.graph = None
        self.test_results = {}
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    async def setup(self):
        """Initialize the optimized graph for testing."""
        logger.info("🚀 Setting up optimized coaching graph for testing...")
        self.graph = await create_optimized_test_graph(
            {
                "enable_memory": False,  # Disable memory for consistent testing
                "user_id": "test_user_optimized",
                "thread_id": f"test_thread_{self.timestamp}",
            }
        )
        logger.info("✅ Optimized graph setup complete")

    def save_test_results(self):
        """Save all test results to a file."""
        output_file = f"optimized_graph_test_results_{self.timestamp}.json"

        # Convert any non-serializable objects to strings
        serializable_results = {}
        for test_name, result in self.test_results.items():
            serializable_results[test_name] = self._make_serializable(result)

        with open(output_file, "w") as f:
            json.dump(
                {
                    "timestamp": self.timestamp,
                    "test_summary": {
                        "total_tests": len(self.test_results),
                        "test_names": list(self.test_results.keys()),
                    },
                    "detailed_results": serializable_results,
                },
                f,
                indent=2,
            )

        logger.info(f"📁 Test results saved to: {output_file}")
        return output_file

    def _make_serializable(self, obj):
        """Convert objects to JSON-serializable format."""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, "__dict__"):
            return str(obj)
        elif hasattr(obj, "content"):  # Handle message objects
            return f"Message: {obj.content}"
        else:
            return str(obj)

    async def test_direct_coach_request(self):
        """Test direct coach routing - should go: intelligence_hub → coach → END"""
        logger.info("\n🔍 TEST 1: Direct Coach Request (Strength)")

        test_input = {
            "user_query": "How do I do proper squats with good form?",
            "messages": [
                HumanMessage(content="How do I do proper squats with good form?")
            ],
            "user_profile": {
                "name": "TestUser",
                "experience": "beginner",
                "goals": ["strength"],
            },
        }

        config = {"configurable": {"thread_id": f"direct_coach_{self.timestamp}"}}

        try:
            result = await self.graph.ainvoke(test_input, config)

            self.test_results["direct_coach_request"] = {
                "input": test_input,
                "output": result,
                "expected_path": "reasoning → intelligence_hub → strength_coach → END",
                "execution_steps": result.get("execution_steps", []),
                "final_response_length": len(result.get("final_response", "")),
                "current_node": result.get("current_node"),
                "routing_decision": result.get("routing_decision"),
                "debug_info": result.get("debug_info", {}),
            }

            logger.info(f"✅ Direct coach test completed")
            logger.info(
                f"   - Execution path: {' → '.join(result.get('execution_steps', []))}"
            )
            logger.info(f"   - Final node: {result.get('current_node')}")
            logger.info(
                f"   - Response length: {len(result.get('final_response', ''))} chars"
            )

        except Exception as e:
            logger.error(f"❌ Direct coach test failed: {e}")
            self.test_results["direct_coach_request"] = {"error": str(e)}

    async def test_multi_coach_request(self):
        """Test multi-coach routing - should go: intelligence_hub → smart_executor → aggregation → END"""
        logger.info("\n🔍 TEST 2: Multi-Coach Request")

        test_input = {
            "user_query": "I'm training for a marathon. I need help with my running plan, nutrition strategy, and recovery protocol.",
            "messages": [
                HumanMessage(
                    content="I'm training for a marathon. I need help with my running plan, nutrition strategy, and recovery protocol."
                )
            ],
            "user_profile": {
                "name": "TestUser",
                "experience": "intermediate",
                "goals": ["endurance", "marathon"],
            },
        }

        config = {"configurable": {"thread_id": f"multi_coach_{self.timestamp}"}}

        try:
            result = await self.graph.ainvoke(test_input, config)

            self.test_results["multi_coach_request"] = {
                "input": test_input,
                "output": result,
                "expected_path": "reasoning → intelligence_hub → smart_executor → aggregation → END",
                "execution_steps": result.get("execution_steps", []),
                "final_response_length": len(result.get("final_response", "")),
                "current_node": result.get("current_node"),
                "routing_decision": result.get("routing_decision"),
                "coach_responses": result.get("coach_responses", {}),
                "debug_info": result.get("debug_info", {}),
            }

            logger.info(f"✅ Multi-coach test completed")
            logger.info(
                f"   - Execution path: {' → '.join(result.get('execution_steps', []))}"
            )
            logger.info(f"   - Final node: {result.get('current_node')}")
            logger.info(
                f"   - Coach responses: {len(result.get('coach_responses', {}))}"
            )
            logger.info(
                f"   - Response length: {len(result.get('final_response', ''))} chars"
            )

        except Exception as e:
            logger.error(f"❌ Multi-coach test failed: {e}")
            self.test_results["multi_coach_request"] = {"error": str(e)}

    async def test_graphrag_enhanced_request(self):
        """Test GraphRAG routing - should go: intelligence_hub → graphrag_retrieval → smart_executor → aggregation → END"""
        logger.info("\n🔍 TEST 3: GraphRAG Enhanced Request")

        test_input = {
            "user_query": "What are the latest scientific findings on periodization training for elite endurance athletes?",
            "messages": [
                HumanMessage(
                    content="What are the latest scientific findings on periodization training for elite endurance athletes?"
                )
            ],
            "user_profile": {
                "name": "TestUser",
                "experience": "advanced",
                "goals": ["performance", "research"],
            },
        }

        config = {"configurable": {"thread_id": f"graphrag_{self.timestamp}"}}

        try:
            result = await self.graph.ainvoke(test_input, config)

            self.test_results["graphrag_enhanced_request"] = {
                "input": test_input,
                "output": result,
                "expected_path": "reasoning → intelligence_hub → graphrag_retrieval → smart_executor → aggregation → END",
                "execution_steps": result.get("execution_steps", []),
                "final_response_length": len(result.get("final_response", "")),
                "current_node": result.get("current_node"),
                "routing_decision": result.get("routing_decision"),
                "knowledge_retrieval_needed": result.get("knowledge_retrieval_needed"),
                "debug_info": result.get("debug_info", {}),
            }

            logger.info(f"✅ GraphRAG test completed")
            logger.info(
                f"   - Execution path: {' → '.join(result.get('execution_steps', []))}"
            )
            logger.info(f"   - Final node: {result.get('current_node')}")
            logger.info(
                f"   - Knowledge retrieval: {result.get('knowledge_retrieval_needed')}"
            )
            logger.info(
                f"   - Response length: {len(result.get('final_response', ''))} chars"
            )

        except Exception as e:
            logger.error(f"❌ GraphRAG test failed: {e}")
            self.test_results["graphrag_enhanced_request"] = {"error": str(e)}

    async def test_simple_greeting(self):
        """Test greeting routing - should go: intelligence_hub → optimized_greeting → END"""
        logger.info("\n🔍 TEST 4: Simple Greeting")

        test_input = {
            "user_query": "Hi there! What can you help me with?",
            "messages": [HumanMessage(content="Hi there! What can you help me with?")],
            "user_profile": {"name": "TestUser"},
        }

        config = {"configurable": {"thread_id": f"greeting_{self.timestamp}"}}

        try:
            result = await self.graph.ainvoke(test_input, config)

            self.test_results["simple_greeting"] = {
                "input": test_input,
                "output": result,
                "expected_path": "reasoning → intelligence_hub → optimized_greeting → END",
                "execution_steps": result.get("execution_steps", []),
                "final_response_length": len(result.get("final_response", "")),
                "current_node": result.get("current_node"),
                "routing_decision": result.get("routing_decision"),
                "debug_info": result.get("debug_info", {}),
            }

            logger.info(f"✅ Greeting test completed")
            logger.info(
                f"   - Execution path: {' → '.join(result.get('execution_steps', []))}"
            )
            logger.info(f"   - Final node: {result.get('current_node')}")
            logger.info(
                f"   - Response length: {len(result.get('final_response', ''))} chars"
            )

        except Exception as e:
            logger.error(f"❌ Greeting test failed: {e}")
            self.test_results["simple_greeting"] = {"error": str(e)}

    async def test_clarification_request(self):
        """Test clarification routing - should go: intelligence_hub → clarification → END"""
        logger.info("\n🔍 TEST 5: Clarification Request")

        test_input = {
            "user_query": "I want to get better",
            "messages": [HumanMessage(content="I want to get better")],
            "user_profile": {"name": "TestUser"},
        }

        config = {"configurable": {"thread_id": f"clarification_{self.timestamp}"}}

        try:
            result = await self.graph.ainvoke(test_input, config)

            self.test_results["clarification_request"] = {
                "input": test_input,
                "output": result,
                "expected_path": "reasoning → intelligence_hub → clarification → END",
                "execution_steps": result.get("execution_steps", []),
                "final_response_length": len(result.get("final_response", "")),
                "current_node": result.get("current_node"),
                "routing_decision": result.get("routing_decision"),
                "debug_info": result.get("debug_info", {}),
            }

            logger.info(f"✅ Clarification test completed")
            logger.info(
                f"   - Execution path: {' → '.join(result.get('execution_steps', []))}"
            )
            logger.info(f"   - Final node: {result.get('current_node')}")
            logger.info(
                f"   - Response length: {len(result.get('final_response', ''))} chars"
            )

        except Exception as e:
            logger.error(f"❌ Clarification test failed: {e}")
            self.test_results["clarification_request"] = {"error": str(e)}

    async def test_nutrition_coach_direct(self):
        """Test direct nutrition coach routing"""
        logger.info("\n🔍 TEST 6: Direct Nutrition Coach")

        test_input = {
            "user_query": "What should I eat before and after my workouts?",
            "messages": [
                HumanMessage(content="What should I eat before and after my workouts?")
            ],
            "user_profile": {"name": "TestUser", "goals": ["muscle_gain"]},
        }

        config = {"configurable": {"thread_id": f"nutrition_{self.timestamp}"}}

        try:
            result = await self.graph.ainvoke(test_input, config)

            self.test_results["nutrition_coach_direct"] = {
                "input": test_input,
                "output": result,
                "expected_path": "reasoning → intelligence_hub → nutrition_coach → END",
                "execution_steps": result.get("execution_steps", []),
                "final_response_length": len(result.get("final_response", "")),
                "current_node": result.get("current_node"),
                "routing_decision": result.get("routing_decision"),
                "debug_info": result.get("debug_info", {}),
            }

            logger.info(f"✅ Nutrition coach test completed")
            logger.info(
                f"   - Execution path: {' → '.join(result.get('execution_steps', []))}"
            )
            logger.info(f"   - Final node: {result.get('current_node')}")
            logger.info(
                f"   - Response length: {len(result.get('final_response', ''))} chars"
            )

        except Exception as e:
            logger.error(f"❌ Nutrition coach test failed: {e}")
            self.test_results["nutrition_coach_direct"] = {"error": str(e)}

    async def run_all_tests(self):
        """Run all test scenarios."""
        logger.info("🧪 Starting comprehensive optimized graph testing...")

        await self.setup()

        # Run all test scenarios
        await self.test_direct_coach_request()
        await self.test_multi_coach_request()
        await self.test_graphrag_enhanced_request()
        await self.test_simple_greeting()
        await self.test_clarification_request()
        await self.test_nutrition_coach_direct()

        # Generate summary
        self.generate_test_summary()

        # Save results
        output_file = self.save_test_results()

        logger.info("\n✅ All tests completed!")
        logger.info(f"📁 Detailed results saved to: {output_file}")

        return self.test_results, output_file

    def generate_test_summary(self):
        """Generate a summary of all test results."""
        logger.info("\n📊 TEST SUMMARY:")

        successful_tests = 0
        failed_tests = 0

        for test_name, result in self.test_results.items():
            if "error" in result:
                logger.info(f"   ❌ {test_name}: FAILED - {result['error']}")
                failed_tests += 1
            else:
                logger.info(f"   ✅ {test_name}: SUCCESS")
                logger.info(
                    f"      - Path: {' → '.join(result.get('execution_steps', []))}"
                )
                logger.info(f"      - Final node: {result.get('current_node')}")
                successful_tests += 1

        logger.info(f"\n📈 RESULTS: {successful_tests} passed, {failed_tests} failed")

        # Performance analysis
        logger.info("\n⚡ PERFORMANCE ANALYSIS:")
        for test_name, result in self.test_results.items():
            if "error" not in result:
                steps = result.get("execution_steps", [])
                logger.info(f"   - {test_name}: {len(steps)} nodes executed")


async def main():
    """Main test execution."""
    tester = OptimizedGraphTester()
    results, output_file = await tester.run_all_tests()

    print(f"\n🎯 Test execution complete!")
    print(f"📁 Results saved to: {output_file}")
    print(f"🔍 Check the JSON file for detailed outputs from each portion of the graph")


if __name__ == "__main__":
    asyncio.run(main())
