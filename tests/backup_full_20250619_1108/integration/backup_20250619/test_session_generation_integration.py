"""
Integration Test for Session Generation with Real User Data

This test passes real user goals, information, and plan details to the session
generation engine to see actual generated sessions.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from pprint import pprint

from athlea_langgraph.states.onboarding_state import (
    create_initial_onboarding_state,
    PlanDetails,
    UserGoals,
    SidebarStateData,
)
from athlea_langgraph.engines.session_generation_engine import (
    SessionGenerationEngine,
    generate_first_week_from_onboarding,
)
from athlea_langgraph.engines.session_state_models import (
    create_fitness_profile_from_onboarding,
    FitnessProfile,
)


class TestSessionGenerationIntegration:
    """Integration tests for session generation with real data."""

    def create_realistic_user_data(self):
        """Create realistic user data for testing session generation."""

        # Create initial onboarding state
        state = create_initial_onboarding_state()

        # Add realistic user information
        state["user_input"] = (
            "I want to build strength and improve my running endurance. I can train 4 times per week for about 45-60 minutes per session. I have access to a gym and enjoy both weight training and running outdoors."
        )

        # Add user goals
        user_goals = UserGoals(
            exists=True,
            list=[
                "Build muscle strength and size",
                "Improve running endurance for 10K",
                "Lose 10 pounds of body fat",
                "Develop consistent training routine",
            ],
        )

        # Add realistic plan details
        plan_details = PlanDetails(
            plan_type="Strength & Endurance Hybrid",
            duration="12 weeks",
            frequency="4 sessions per week",
            disciplines=["Strength Training", "Running"],
            progression="Progressive overload with periodized running",
            summary="A balanced 12-week program combining strength training and running to build muscle, improve endurance, and enhance overall fitness.",
            key_features=[
                "3 strength sessions + 1 long run per week",
                "Progressive resistance training",
                "Base building to tempo runs",
                "Recovery-focused programming",
            ],
        )

        # Set the plan in the state
        state["generated_plan"] = plan_details

        # Add sidebar data
        state["sidebar_data"] = SidebarStateData(
            goals=user_goals,
            summary_items=[],
            selected_sports=["strength", "running"],
            current_stage="plan_generated",
            generated_plan=plan_details,
            weekly_plan=None,  # Will be populated by session generation
        )

        # Add user preferences and constraints
        state["user_preferences"] = {
            "time_constraints": {
                "sessions_per_week": 4,
                "daily_minutes": 50,
                "preferred_days": ["Monday", "Tuesday", "Thursday", "Saturday"],
            },
            "equipment_access": ["gym", "outdoors"],
            "fitness_level": "intermediate",
            "training_experience": "2 years",
            "injuries_limitations": "None currently",
            "preferred_workout_times": "morning",
        }

        return state

    @pytest.mark.asyncio
    async def test_generate_sessions_with_real_user_data(self):
        """Test session generation with comprehensive user data and show actual results."""

        print("\n" + "=" * 80)
        print("TESTING SESSION GENERATION WITH REAL USER DATA")
        print("=" * 80)

        # 1. Create realistic user data
        print("\n1. Creating realistic user data...")
        onboarding_state = self.create_realistic_user_data()

        print("User Goals:")
        for goal in onboarding_state["sidebar_data"].goals.list:
            print(f"  - {goal}")

        print(f"\nPlan Type: {onboarding_state['generated_plan'].plan_type}")
        print(f"Duration: {onboarding_state['generated_plan'].duration}")
        print(f"Frequency: {onboarding_state['generated_plan'].frequency}")
        print(
            f"Disciplines: {', '.join(onboarding_state['generated_plan'].disciplines)}"
        )

        # 2. Create fitness profile from user data
        print("\n2. Creating fitness profile from onboarding data...")
        fitness_profile = create_fitness_profile_from_onboarding(onboarding_state)

        print(f"Fitness Level: {fitness_profile.fitness_level}")
        print(f"Experience Level: {fitness_profile.experience_level}")
        print(
            f"Sessions per week: {fitness_profile.time_constraints.get('sessions_per_week', 'N/A')}"
        )
        print(
            f"Daily minutes: {fitness_profile.time_constraints.get('daily_minutes', 'N/A')}"
        )

        # 3. Initialize session generation engine
        print("\n3. Initializing session generation engine...")
        engine = SessionGenerationEngine()

        # 4. Generate first week sessions
        print("\n4. Generating first week sessions...")
        weekly_plan = await engine.generate_weekly_sessions(
            user_profile=fitness_profile,
            plan_details=onboarding_state["generated_plan"],
            week_number=1,
            previous_performance=[],
            target_start_date=datetime.now(),
        )

        # 5. Display generated sessions
        print("\n" + "=" * 80)
        print("GENERATED WEEKLY TRAINING PLAN")
        print("=" * 80)

        print(f"\nWeek {weekly_plan.week_number}: {weekly_plan.weekly_focus}")
        print(
            f"Dates: {weekly_plan.start_date.strftime('%Y-%m-%d')} to {weekly_plan.end_date.strftime('%Y-%m-%d')}"
        )
        print(f"Total Training Load: {weekly_plan.total_training_load} minutes")

        print(f"\nWeekly Volume by Domain:")
        for domain, minutes in weekly_plan.weekly_volume.items():
            print(f"  {domain.title()}: {minutes} minutes")

        print(f"\nRationale: {weekly_plan.rationale}")
        print(f"\nCoach Coordination: {weekly_plan.coach_coordination_notes}")

        print(f"\n" + "-" * 60)
        print("DAILY SESSIONS")
        print("-" * 60)

        days_of_week = {
            1: "Monday",
            2: "Tuesday",
            3: "Wednesday",
            4: "Thursday",
            5: "Friday",
            6: "Saturday",
            7: "Sunday",
        }

        for day_num in range(1, 8):
            day_key = f"day_{day_num}"
            day_name = days_of_week[day_num]

            if day_key in weekly_plan.sessions and weekly_plan.sessions[day_key]:
                sessions = weekly_plan.sessions[day_key]
                print(f"\n{day_name} (Day {day_num}):")

                for i, session in enumerate(sessions, 1):
                    print(
                        f"  Session {i}: {session.domain.title()} - {session.session_type}"
                    )
                    print(f"    Duration: {session.duration_minutes} minutes")
                    print(f"    Intensity: {session.intensity_level}")
                    print(f"    Difficulty: {session.difficulty_level}/10")
                    print(
                        f"    Equipment: {', '.join(session.equipment_needed) if session.equipment_needed else 'None'}"
                    )
                    print(f"    Location: {session.location_type}")
                    print(f"    Benefits: {session.expected_benefits}")
                    print(f"    Coach Notes: {session.coach_rationale}")

                    if session.session_data:
                        print(f"    Session Details:")
                        for key, value in session.session_data.items():
                            if isinstance(value, (list, dict)):
                                print(
                                    f"      {key}: {len(value)} items"
                                    if isinstance(value, list)
                                    else f"      {key}: {type(value).__name__}"
                                )
                            else:
                                print(f"      {key}: {value}")
            else:
                daily_focus = weekly_plan.daily_focuses.get(day_num, "Rest day")
                print(f"\n{day_name} (Day {day_num}): {daily_focus}")

        # 6. Test assertions
        print(f"\n" + "=" * 80)
        print("VALIDATION RESULTS")
        print("=" * 80)

        # Validate the generated plan
        assert weekly_plan.week_number == 1, "Should be week 1"
        assert weekly_plan.sessions, "Should have generated sessions"
        assert weekly_plan.weekly_volume, "Should have volume data"
        assert weekly_plan.rationale, "Should have rationale"

        # Count total sessions
        total_sessions = sum(
            len(sessions) for sessions in weekly_plan.sessions.values()
        )
        print(f"✓ Generated {total_sessions} total sessions")

        # Validate sessions per week target
        target_sessions = fitness_profile.time_constraints.get("sessions_per_week", 4)
        assert (
            total_sessions >= target_sessions - 1
        ), f"Should generate close to {target_sessions} sessions (got {total_sessions})"
        print(f"✓ Session count ({total_sessions}) meets target (~{target_sessions})")

        # Validate disciplines are included
        session_domains = set()
        for sessions in weekly_plan.sessions.values():
            for session in sessions:
                session_domains.add(session.domain)

        expected_domains = {"strength", "running"}
        assert session_domains.intersection(
            expected_domains
        ), f"Should include expected domains: {expected_domains}"
        print(f"✓ Includes expected domains: {session_domains}")

        # Validate time constraints
        daily_sessions = [
            sessions for sessions in weekly_plan.sessions.values() if sessions
        ]
        if daily_sessions:
            max_daily_duration = max(
                sum(s.duration_minutes for s in sessions) for sessions in daily_sessions
            )
            target_duration = fitness_profile.time_constraints.get("daily_minutes", 60)
            assert (
                max_daily_duration <= target_duration + 15
            ), f"Daily sessions should respect time constraints ({max_daily_duration} <= {target_duration + 15})"
            print(
                f"✓ Respects daily time constraints (max {max_daily_duration} minutes)"
            )

        print(f"\n✅ All validations passed!")

        return weekly_plan

    @pytest.mark.asyncio
    async def test_generate_multiple_weeks_progression(self):
        """Test generating multiple weeks to see progression."""

        print("\n" + "=" * 80)
        print("TESTING MULTIPLE WEEK PROGRESSION")
        print("=" * 80)

        # Create user data
        onboarding_state = self.create_realistic_user_data()
        fitness_profile = create_fitness_profile_from_onboarding(onboarding_state)
        engine = SessionGenerationEngine()

        # Generate first 3 weeks
        weeks = []
        for week_num in range(1, 4):
            print(f"\nGenerating Week {week_num}...")

            weekly_plan = await engine.generate_weekly_sessions(
                user_profile=fitness_profile,
                plan_details=onboarding_state["generated_plan"],
                week_number=week_num,
                previous_performance=[],
                target_start_date=datetime.now() + timedelta(weeks=week_num - 1),
            )
            weeks.append(weekly_plan)

            print(f"Week {week_num}: {weekly_plan.weekly_focus}")
            print(f"  Total Load: {weekly_plan.total_training_load} minutes")
            print(
                f"  Sessions: {sum(len(sessions) for sessions in weekly_plan.sessions.values())}"
            )

        # Analyze progression
        print(f"\n" + "-" * 60)
        print("PROGRESSION ANALYSIS")
        print("-" * 60)

        for i, week in enumerate(weeks, 1):
            print(f"\nWeek {i}:")
            print(f"  Theme: {week.weekly_focus}")
            print(f"  Load: {week.total_training_load} minutes")
            print(f"  Volume breakdown: {week.weekly_volume}")

        # Validate progression exists
        themes = [week.weekly_focus for week in weeks]
        print(f"\nProgression themes: {' → '.join(themes)}")

        assert len(set(themes)) >= 1, "Should show some variation in themes"
        print("✓ Progression validated")

        return weeks

    @pytest.mark.asyncio
    async def test_session_generation_with_constraints(self):
        """Test session generation with specific constraints and preferences."""

        print("\n" + "=" * 80)
        print("TESTING SESSION GENERATION WITH CONSTRAINTS")
        print("=" * 80)

        # Create constrained user data
        onboarding_state = self.create_realistic_user_data()

        # Modify constraints
        onboarding_state["user_preferences"]["time_constraints"] = {
            "sessions_per_week": 3,  # Reduced
            "daily_minutes": 30,  # Shorter
            "preferred_days": ["Monday", "Wednesday", "Friday"],
        }

        # Add limitations
        onboarding_state["user_preferences"][
            "injuries_limitations"
        ] = "Lower back sensitivity - avoid heavy deadlifts"
        onboarding_state["user_preferences"]["equipment_access"] = [
            "home",
            "bodyweight",
        ]  # Limited equipment

        print("Constraints:")
        print(
            f"  Sessions per week: {onboarding_state['user_preferences']['time_constraints']['sessions_per_week']}"
        )
        print(
            f"  Daily minutes: {onboarding_state['user_preferences']['time_constraints']['daily_minutes']}"
        )
        print(
            f"  Equipment: {onboarding_state['user_preferences']['equipment_access']}"
        )
        print(
            f"  Limitations: {onboarding_state['user_preferences']['injuries_limitations']}"
        )

        # Generate sessions
        fitness_profile = create_fitness_profile_from_onboarding(onboarding_state)
        engine = SessionGenerationEngine()

        weekly_plan = await engine.generate_weekly_sessions(
            user_profile=fitness_profile,
            plan_details=onboarding_state["generated_plan"],
            week_number=1,
            previous_performance=[],
        )

        # Analyze constraint adherence
        print(f"\n" + "-" * 60)
        print("CONSTRAINT ADHERENCE")
        print("-" * 60)

        total_sessions = sum(
            len(sessions) for sessions in weekly_plan.sessions.values()
        )
        target_sessions = onboarding_state["user_preferences"]["time_constraints"][
            "sessions_per_week"
        ]
        print(f"Sessions generated: {total_sessions} (target: {target_sessions})")

        # Check daily durations
        for day_key, sessions in weekly_plan.sessions.items():
            if sessions:
                daily_duration = sum(s.duration_minutes for s in sessions)
                target_duration = onboarding_state["user_preferences"][
                    "time_constraints"
                ]["daily_minutes"]
                print(
                    f"{day_key}: {daily_duration} minutes (target: {target_duration})"
                )

                # Should respect time constraints (with some flexibility)
                assert (
                    daily_duration <= target_duration + 10
                ), f"Daily duration should respect constraints"

        print("✓ Time constraints respected")

        return weekly_plan


if __name__ == "__main__":
    # Run the main test
    test_instance = TestSessionGenerationIntegration()

    # Run in asyncio event loop
    async def run_test():
        await test_instance.test_generate_sessions_with_real_user_data()

    asyncio.run(run_test())
