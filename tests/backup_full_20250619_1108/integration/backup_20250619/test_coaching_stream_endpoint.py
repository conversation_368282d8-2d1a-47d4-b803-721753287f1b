#!/usr/bin/env python3
"""
Test Coaching Stream Endpoint

Comprehensive test for the streaming coaching API endpoint to verify:
1. Tool input validation fixes work in streaming context
2. ReAct pattern execution works correctly
3. Schema transformation is applied properly
4. Error handling works as expected
"""

import asyncio
import json
import logging
from typing import AsyncGenerator

import httpx
from fastapi.testclient import TestClient

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_coaching_stream_endpoint():
    """Test the coaching stream endpoint with various scenarios."""
    print("🧪 Testing Coaching Stream Endpoint")
    print("=" * 50)

    # Import the FastAPI app
    from athlea_langgraph.api.coaching_stream import app

    # Create test client
    client = TestClient(app)

    # Test 1: Health Check
    print("\n🔍 Test 1: Health Check")
    print("-" * 30)

    response = client.get("/coaching/health")
    print(f"Health check status: {response.status_code}")
    if response.status_code == 200:
        health_data = response.json()
        print(f"✅ Service status: {health_data.get('status')}")
        print(f"✅ Tools initialized: {health_data.get('tools_initialized')}")
        print(f"✅ Available coaches: {health_data.get('coaches_available')}")
    else:
        print(f"❌ Health check failed: {response.text}")
        return

    # Test 2: Get Available Coaches
    print("\n🔍 Test 2: Available Coaches")
    print("-" * 30)

    response = client.get("/coaching/coaches")
    if response.status_code == 200:
        coaches_data = response.json()
        print(f"✅ Total coaches: {coaches_data.get('total_count')}")
        for coach_type, info in coaches_data.get("coaches", {}).items():
            print(f"  - {info['name']}: {info['description']}")
    else:
        print(f"❌ Failed to get coaches: {response.text}")

    # Test 3: Stream Strength Coaching Session (Tool Input Validation)
    print("\n🔍 Test 3: Strength Coaching Stream (Tool Validation)")
    print("-" * 30)

    strength_request = {
        "message": "I need a 60-minute strength training session for building muscle. I have dumbbells and a bench available.",
        "coach_type": "strength",
        "user_profile": {
            "experience_level": "intermediate",
            "goals": ["build muscle", "increase strength"],
            "available_equipment": ["dumbbells", "bench"],
        },
    }

    print(f"Request: {strength_request['message']}")
    print("Streaming response...")

    try:
        with client.stream(
            "POST", "/coaching/stream", json=strength_request
        ) as response:
            if response.status_code == 200:
                print("✅ Stream started successfully")

                # Parse streaming response
                events = []
                for line in response.iter_lines():
                    if line.startswith("data: "):
                        try:
                            data = json.loads(line[6:])  # Remove "data: " prefix
                            events.append(data)

                            event_type = data.get("event")
                            event_data = data.get("data", {})

                            if event_type == "status":
                                status_type = event_data.get("type")
                                message = event_data.get("message")
                                print(f"  📊 Status: {status_type} - {message}")

                            elif event_type == "response_start":
                                coach_type = event_data.get("coach_type")
                                total_length = event_data.get("total_length")
                                print(
                                    f"  🎯 Response from {coach_type} coach ({total_length} chars)"
                                )

                            elif event_type == "response_chunk":
                                chunk = event_data.get("chunk", "")
                                is_final = event_data.get("is_final", False)
                                print(
                                    f"  📝 Chunk: {chunk[:50]}{'...' if len(chunk) > 50 else ''}"
                                )
                                if is_final:
                                    print("  ✅ Final chunk received")

                            elif event_type == "error":
                                error_message = event_data.get("message")
                                print(f"  ❌ Error: {error_message}")

                        except json.JSONDecodeError as e:
                            print(f"  ⚠️ Failed to parse line: {line}")

                print(f"✅ Stream completed with {len(events)} events")

                # Analyze events
                status_events = [e for e in events if e.get("event") == "status"]
                response_events = [
                    e for e in events if e.get("event") == "response_chunk"
                ]
                error_events = [e for e in events if e.get("event") == "error"]

                print(f"  - Status events: {len(status_events)}")
                print(f"  - Response chunks: {len(response_events)}")
                print(f"  - Error events: {len(error_events)}")

                if error_events:
                    print("❌ Errors detected in stream:")
                    for error in error_events:
                        print(f"  - {error.get('data', {}).get('message')}")
                else:
                    print("✅ No errors in stream")

            else:
                print(
                    f"❌ Stream failed with status {response.status_code}: {response.text}"
                )

    except Exception as e:
        print(f"❌ Stream test failed: {e}")

    # Test 4: Test Invalid Coach Type
    print("\n🔍 Test 4: Invalid Coach Type")
    print("-" * 30)

    invalid_request = {
        "message": "Test message",
        "coach_type": "invalid_coach",
    }

    response = client.post("/coaching/stream", json=invalid_request)
    if response.status_code == 400:
        print("✅ Invalid coach type properly rejected")
        error_data = response.json()
        print(f"  Error: {error_data.get('detail')}")
    else:
        print(f"❌ Expected 400 error, got {response.status_code}")

    # Test 5: Test Different Coach Types
    print("\n🔍 Test 5: Multiple Coach Types")
    print("-" * 30)

    coach_tests = [
        {
            "coach_type": "cardio",
            "message": "I want to improve my 5K running time. What training plan do you recommend?",
        },
        {
            "coach_type": "nutrition",
            "message": "What should I eat before and after my workouts?",
        },
        {
            "coach_type": "recovery",
            "message": "I'm feeling sore after yesterday's workout. What recovery strategies do you recommend?",
        },
    ]

    for test_case in coach_tests:
        print(f"\n  Testing {test_case['coach_type']} coach...")

        try:
            with client.stream("POST", "/coaching/stream", json=test_case) as response:
                if response.status_code == 200:
                    # Just check that we get some response
                    event_count = 0
                    for line in response.iter_lines():
                        if line.startswith("data: "):
                            event_count += 1
                            if event_count >= 3:  # Just check first few events
                                break

                    print(
                        f"    ✅ {test_case['coach_type']} coach responded ({event_count} events)"
                    )
                else:
                    print(
                        f"    ❌ {test_case['coach_type']} coach failed: {response.status_code}"
                    )

        except Exception as e:
            print(f"    ❌ {test_case['coach_type']} coach error: {e}")

    print("\n🎉 Coaching Stream Endpoint Tests Completed!")


async def test_tool_input_validation_in_stream():
    """Specific test for tool input validation in streaming context."""
    print("\n🔧 Testing Tool Input Validation in Stream Context")
    print("=" * 50)

    # This test specifically targets the tool input validation fixes
    # by sending a request that would trigger session_generation tool usage

    from athlea_langgraph.api.coaching_stream import (
        CoachingRequest,
        StreamingCoachingResponse,
    )

    # Create a request that should trigger tool usage
    request = CoachingRequest(
        message="Create a 45-minute strength workout for chest and back using dumbbells and barbells. I'm intermediate level.",
        coach_type="strength",
        user_profile={
            "experience_level": "intermediate",
            "goals": ["muscle building", "strength"],
            "equipment": ["dumbbells", "barbells", "bench"],
        },
    )

    print(f"Test request: {request.message}")
    print(f"Coach type: {request.coach_type}")
    print(f"User profile: {request.user_profile}")

    try:
        # Create streaming handler
        handler = StreamingCoachingResponse("strength", request)

        # Initialize coach
        await handler.initialize_coach()
        print("✅ Coach initialized successfully")

        # Test the streaming response
        print("\nStreaming response events:")
        event_count = 0
        async for event in handler.stream_response():
            event_count += 1

            # Parse the event
            if event.startswith("data: "):
                try:
                    data = json.loads(event[6:])
                    event_type = data.get("event")
                    event_data = data.get("data", {})

                    print(f"  Event {event_count}: {event_type}")

                    if event_type == "error":
                        print(f"    ❌ Error: {event_data.get('message')}")
                    elif event_type == "status":
                        print(
                            f"    📊 Status: {event_data.get('type')} - {event_data.get('message', '')}"
                        )
                    elif event_type == "response_chunk":
                        chunk = event_data.get("chunk", "")
                        print(
                            f"    📝 Chunk: {chunk[:30]}{'...' if len(chunk) > 30 else ''}"
                        )

                    # Stop after reasonable number of events for testing
                    if event_count >= 20:
                        print("    (Stopping after 20 events for test)")
                        break

                except json.JSONDecodeError:
                    print(f"    ⚠️ Could not parse event: {event[:50]}...")

        print(f"✅ Streaming test completed with {event_count} events")

    except Exception as e:
        print(f"❌ Tool validation streaming test failed: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":

    async def run_all_tests():
        """Run all tests."""
        await test_coaching_stream_endpoint()
        await test_tool_input_validation_in_stream()

    asyncio.run(run_all_tests())
