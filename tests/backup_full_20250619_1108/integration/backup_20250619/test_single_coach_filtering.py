#!/usr/bin/env python3
"""
Test script to verify singleCoach filtering in the comprehensive coaching graph.

This test ensures that when a singleCoach is specified, the graph:
1. Bypasses normal routing logic
2. Goes directly to the specified coach
3. Filters out all other coaches
4. Doesn't invoke unnecessary nodes
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Set

from athlea_langgraph.graphs.archived.comprehensive_coaching_graph import (
    create_comprehensive_coaching_graph,
    ComprehensiveCoachingConfig,
)
from langchain_core.messages import HumanMessage

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SingleCoachTester:
    """Test harness for verifying single coach filtering."""

    def __init__(self):
        self.test_results = []

    async def test_single_coach_routing(
        self, coach_type: str, test_message: str
    ) -> Dict:
        """
        Test that a specific coach type is correctly routed to without invoking others.

        Args:
            coach_type: The coach type to test (e.g., 'strength_coach', 'nutrition_coach')
            test_message: The message to send to the coach

        Returns:
            Dict with test results including nodes visited and coaches invoked
        """
        print(f"\n🧪 Testing single coach routing: {coach_type}")
        print(f"📝 Test message: {test_message}")
        print("-" * 60)

        start_time = time.time()
        nodes_visited = []
        coaches_invoked = []
        execution_path = None

        try:
            # Create config for the comprehensive coaching graph
            config = ComprehensiveCoachingConfig(
                user_id="test-user-123",
                mongodb_uri="mongodb://localhost:27017",
                thread_id=f"test-thread-{coach_type}-{int(time.time())}",
                enable_memory=False,  # Disable for testing
                use_react_agents=True,
                max_iterations=3,
                enable_human_feedback=False,
                enable_reflexion=False,  # Disable to prevent extra complexity
                complexity_threshold=0.6,
            )

            # Create the comprehensive coaching graph
            graph = await create_comprehensive_coaching_graph(config)

            # Create the targeted message with coach override
            targeted_message = f"[COACH_OVERRIDE:{coach_type}] {test_message}"

            # Create initial state
            initial_state = {
                "user_query": test_message,
                "messages": [HumanMessage(content=test_message)],
                "user_id": "test-user-123",
                "user_profile": {"user_id": "test-user-123"},
                "thread_id": f"test-thread-{coach_type}",
                "enable_human_feedback": False,
            }

            print(f"🔗 Invoking graph with coach override: {targeted_message}")

            # Track execution by streaming events
            event_count = 0
            async for event in graph.astream_events(
                {"user_query": targeted_message, **initial_state},
                version="v1",
                config={"configurable": {"thread_id": f"test-thread-{coach_type}"}},
            ):
                event_count += 1
                event_type = event.get("event", "")
                event_name = event.get("name", "")

                # Track nodes visited
                if event_type == "on_chain_start" and event_name:
                    if event_name not in nodes_visited:
                        nodes_visited.append(event_name)
                        print(f"📍 Node visited: {event_name}")

                    # Check if this is a coach node
                    if (
                        event_name.endswith("_coach")
                        and event_name not in coaches_invoked
                    ):
                        coaches_invoked.append(event_name)
                        print(f"🎯 Coach invoked: {event_name}")

                # Track execution path decisions
                if event_type == "on_chain_end" and "execution_path" in event.get(
                    "data", {}
                ).get("output", {}):
                    execution_path = event["data"]["output"]["execution_path"]
                    print(f"🛤️  Execution path: {execution_path}")

                # Log coach override detection
                if "coach_override" in event.get("data", {}).get("output", {}):
                    coach_override = event["data"]["output"]["coach_override"]
                    print(f"✅ Coach override detected: {coach_override}")

                # Limit events to prevent overwhelming output
                if event_count > 100:
                    print("⚠️  Event limit reached, stopping...")
                    break

            duration = time.time() - start_time

            # Analyze results
            success = self._analyze_single_coach_test(
                coach_type, nodes_visited, coaches_invoked, execution_path
            )

            result = {
                "coach_type": coach_type,
                "test_message": test_message,
                "success": success,
                "duration": duration,
                "nodes_visited": nodes_visited,
                "coaches_invoked": coaches_invoked,
                "execution_path": execution_path,
                "event_count": event_count,
            }

            self.test_results.append(result)

            # Print summary
            print(f"\n📊 Test Results for {coach_type}:")
            print(f"   ✅ Success: {success}")
            print(f"   ⏱️  Duration: {duration:.2f}s")
            print(f"   📍 Nodes visited: {len(nodes_visited)}")
            print(f"   🎯 Coaches invoked: {coaches_invoked}")
            print(f"   🛤️  Execution path: {execution_path}")

            return result

        except Exception as e:
            error_result = {
                "coach_type": coach_type,
                "test_message": test_message,
                "success": False,
                "error": str(e),
                "duration": time.time() - start_time,
                "nodes_visited": nodes_visited,
                "coaches_invoked": coaches_invoked,
            }

            print(f"❌ Test failed for {coach_type}: {e}")
            self.test_results.append(error_result)
            return error_result

    def _analyze_single_coach_test(
        self,
        expected_coach: str,
        nodes_visited: List[str],
        coaches_invoked: List[str],
        execution_path: str,
    ) -> bool:
        """
        Analyze if the single coach test was successful.

        Success criteria:
        1. Only the expected coach was invoked
        2. No other coaches were invoked
        3. Execution path is 'coach_override'
        4. The routing bypassed normal planning
        """
        print(f"\n🔍 Analyzing test results for {expected_coach}:")

        # Check 1: Only expected coach invoked
        if len(coaches_invoked) == 1 and coaches_invoked[0] == expected_coach:
            print(f"   ✅ Only {expected_coach} was invoked")
            coach_check = True
        elif len(coaches_invoked) == 0:
            print(f"   ❌ No coaches were invoked (expected: {expected_coach})")
            coach_check = False
        else:
            print(
                f"   ❌ Multiple coaches invoked: {coaches_invoked} (expected only: {expected_coach})"
            )
            coach_check = False

        # Check 2: Execution path should be coach_override
        if execution_path == "coach_override":
            print(f"   ✅ Correct execution path: {execution_path}")
            path_check = True
        else:
            print(
                f"   ❌ Incorrect execution path: {execution_path} (expected: coach_override)"
            )
            path_check = False

        # Check 3: Should bypass complex planning
        complex_nodes = ["complex_planner", "complex_executor", "complex_router"]
        bypassed_complex = not any(node in nodes_visited for node in complex_nodes)
        if bypassed_complex:
            print(f"   ✅ Bypassed complex planning nodes")
        else:
            found_complex = [node for node in complex_nodes if node in nodes_visited]
            print(f"   ❌ Unexpectedly visited complex nodes: {found_complex}")

        # Check 4: Should have coach override detection
        has_override_detection = "coach_override_detection" in nodes_visited
        if has_override_detection:
            print(f"   ✅ Coach override detection was executed")
        else:
            print(f"   ❌ Coach override detection was not executed")

        overall_success = (
            coach_check and path_check and bypassed_complex and has_override_detection
        )

        if overall_success:
            print(f"   🎉 Overall result: SUCCESS")
        else:
            print(f"   💥 Overall result: FAILED")

        return overall_success

    async def run_comprehensive_test(self):
        """Run comprehensive tests for all coach types."""
        print("🚀 Starting Comprehensive Single Coach Filtering Tests")
        print("=" * 70)

        # Define test cases
        test_cases = [
            ("strength_coach", "How do I improve my deadlift form?"),
            ("cardio_coach", "What's the best running training plan?"),
            ("nutrition_coach", "How should I eat for muscle gain?"),
            ("cycling_coach", "How do I train for a century ride?"),
            ("recovery_coach", "How do I optimize my sleep for recovery?"),
            ("mental_coach", "How can I improve my motivation to exercise?"),
        ]

        results = []

        for coach_type, test_message in test_cases:
            result = await self.test_single_coach_routing(coach_type, test_message)
            results.append(result)

            # Small delay between tests
            await asyncio.sleep(1)

        # Print final summary
        self._print_final_summary(results)

        return results

    def _print_final_summary(self, results: List[Dict]):
        """Print a comprehensive summary of all test results."""
        print("\n" + "=" * 70)
        print("📋 FINAL TEST SUMMARY")
        print("=" * 70)

        total_tests = len(results)
        successful_tests = sum(1 for r in results if r.get("success", False))
        failed_tests = total_tests - successful_tests

        print(f"📊 Overall Results:")
        print(f"   Total tests: {total_tests}")
        print(f"   ✅ Successful: {successful_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   📈 Success rate: {(successful_tests / total_tests * 100):.1f}%")

        print(f"\n🔍 Detailed Results:")
        for result in results:
            status = "✅ PASS" if result.get("success", False) else "❌ FAIL"
            coach = result["coach_type"]
            duration = result.get("duration", 0)
            coaches = result.get("coaches_invoked", [])

            print(f"   {status} {coach:<15} ({duration:.2f}s) - Coaches: {coaches}")

            if not result.get("success", False) and "error" in result:
                print(f"       Error: {result['error']}")

        if successful_tests == total_tests:
            print(
                f"\n🎉 All tests passed! Single coach filtering is working correctly."
            )
        else:
            print(
                f"\n⚠️  {failed_tests} test(s) failed. Single coach filtering needs attention."
            )

        return successful_tests == total_tests


async def main():
    """Main test execution function."""
    tester = SingleCoachTester()

    try:
        success = await tester.run_comprehensive_test()

        if success:
            print(f"\n🎯 SUCCESS: All single coach filtering tests passed!")
            return 0
        else:
            print(f"\n💥 FAILURE: Some single coach filtering tests failed!")
            return 1

    except Exception as e:
        print(f"\n❌ Fatal error during testing: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
