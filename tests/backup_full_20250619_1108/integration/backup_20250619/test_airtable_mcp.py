"""
Airtable MCP Tool Integration Tests

Comprehensive tests for the Airtable MCP tool implementation.
Tests both the tool functionality and MCP integration patterns.
"""

import asyncio
import os
from typing import Any, Dict
from unittest.mock import AsyncMock, Mock, patch

import pytest

from athlea_langgraph.schemas.airtable_mcp_schemas import (
    AirtableMCPInput,
    AirtableMCPOutput,
)
from athlea_langgraph.tools.external.airtable_mcp import (
    AirtableMCPError,
    AirtableMCPTool,
    airtable_mcp_operation,
    get_airtable_mcp_tool,
)


class TestAirtableMCPTool:
    """Test suite for AirtableMCPTool class."""

    def setup_method(self):
        """Set up test fixtures."""
        # Mock API key for testing
        self.test_api_key = "test_api_key_12345"

        # Reset global instance
        import athlea_langgraph.tools.external.airtable_mcp as airtable_module

        airtable_module._airtable_mcp_tool = None

    @patch("athlea_langgraph.tools.external.airtable_mcp.Api")
    def test_tool_initialization_success(self, mock_api_class):
        """Test successful tool initialization."""
        mock_api_instance = Mock()
        mock_api_class.return_value = mock_api_instance

        tool = AirtableMCPTool(api_key=self.test_api_key)

        assert tool.api_key == self.test_api_key
        assert tool.api == mock_api_instance
        assert tool.circuit_breaker is not None
        mock_api_class.assert_called_once_with(self.test_api_key)

    def test_tool_initialization_no_api_key(self):
        """Test tool initialization fails without API key."""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError, match="Airtable API key is required"):
                AirtableMCPTool()

    @patch.dict(os.environ, {"AIRTABLE_API_KEY": "env_api_key"})
    @patch("athlea_langgraph.tools.external.airtable_mcp.Api")
    def test_tool_initialization_from_env(self, mock_api_class):
        """Test tool initialization from environment variable."""
        mock_api_instance = Mock()
        mock_api_class.return_value = mock_api_instance

        tool = AirtableMCPTool()

        assert tool.api_key == "env_api_key"
        mock_api_class.assert_called_once_with("env_api_key")

    @patch("athlea_langgraph.tools.external.airtable_mcp.Api")
    def test_tool_initialization_api_failure(self, mock_api_class):
        """Test tool initialization handles API client creation failure."""
        mock_api_class.side_effect = Exception("API initialization failed")

        with pytest.raises(AirtableMCPError, match="Failed to initialize Airtable API"):
            AirtableMCPTool(api_key=self.test_api_key)

    @pytest.mark.asyncio
    @patch("athlea_langgraph.tools.external.airtable_mcp.Api")
    async def test_list_bases_operation(self, mock_api_class):
        """Test list_bases operation."""
        # Setup mocks
        mock_base1 = Mock()
        mock_base1.id = "base1"
        mock_base1.name = "Test Base 1"
        mock_base1.permission_level = "create"

        mock_base2 = Mock()
        mock_base2.id = "base2"
        mock_base2.name = "Test Base 2"
        mock_base2.permission_level = "read"

        mock_api_instance = Mock()
        mock_api_instance.bases.return_value = [mock_base1, mock_base2]
        mock_api_class.return_value = mock_api_instance

        # Create tool and test
        tool = AirtableMCPTool(api_key=self.test_api_key)

        input_data = AirtableMCPInput(operation="list_bases")
        result = await tool.execute(input_data)

        assert result.success is True
        assert result.operation == "list_bases"
        assert result.data_count == 2
        assert len(result.data) == 2
        assert result.data[0]["id"] == "base1"
        assert result.data[0]["name"] == "Test Base 1"
        assert result.data[1]["id"] == "base2"
        assert result.data[1]["name"] == "Test Base 2"

    @pytest.mark.asyncio
    @patch("athlea_langgraph.tools.external.airtable_mcp.Api")
    async def test_list_tables_operation(self, mock_api_class):
        """Test list_tables operation."""
        # Setup mocks
        mock_table1 = Mock()
        mock_table1.table_id = "table1"
        mock_table1.table_name = "Test Table 1"
        mock_table1.primary_field_id = "field1"

        mock_table2 = Mock()
        mock_table2.table_id = "table2"
        mock_table2.table_name = "Test Table 2"
        mock_table2.primary_field_id = "field2"

        mock_base = Mock()
        mock_base.tables.return_value = [mock_table1, mock_table2]

        mock_api_instance = Mock()
        mock_api_instance.base.return_value = mock_base
        mock_api_class.return_value = mock_api_instance

        # Create tool and test
        tool = AirtableMCPTool(api_key=self.test_api_key)

        input_data = AirtableMCPInput(operation="list_tables", baseId="test_base")
        result = await tool.execute(input_data)

        assert result.success is True
        assert result.operation == "list_tables"
        assert result.base_id == "test_base"
        assert result.data_count == 2
        assert len(result.data) == 2
        assert result.data[0]["id"] == "table1"
        assert result.data[0]["name"] == "Test Table 1"

    @pytest.mark.asyncio
    @patch("athlea_langgraph.tools.external.airtable_mcp.Api")
    async def test_list_records_operation(self, mock_api_class):
        """Test list_records operation."""
        # Setup mocks
        mock_records = [
            {
                "id": "rec1",
                "fields": {"Name": "Record 1", "Value": 100},
                "createdTime": "2024-01-01T00:00:00.000Z",
            },
            {
                "id": "rec2",
                "fields": {"Name": "Record 2", "Value": 200},
                "createdTime": "2024-01-02T00:00:00.000Z",
            },
        ]

        mock_table = Mock()
        mock_table.all.return_value = mock_records

        mock_api_instance = Mock()
        mock_api_instance.table.return_value = mock_table
        mock_api_class.return_value = mock_api_instance

        # Create tool and test
        tool = AirtableMCPTool(api_key=self.test_api_key)

        input_data = AirtableMCPInput(
            operation="list_records",
            baseId="test_base",
            tableId="test_table",
            maxRecords=10,
        )
        result = await tool.execute(input_data)

        assert result.success is True
        assert result.operation == "list_records"
        assert result.base_id == "test_base"
        assert result.table_id == "test_table"
        assert result.data_count == 2
        assert len(result.data) == 2
        assert result.data[0]["id"] == "rec1"
        assert result.data[0]["fields"]["Name"] == "Record 1"

    @pytest.mark.asyncio
    @patch("athlea_langgraph.tools.external.airtable_mcp.Api")
    async def test_search_records_operation(self, mock_api_class):
        """Test search_records operation."""
        # Setup mocks
        mock_records = [
            {
                "id": "rec1",
                "fields": {"Name": "High Value", "Value": 500},
                "createdTime": "2024-01-01T00:00:00.000Z",
            }
        ]

        mock_table = Mock()
        mock_table.all.return_value = mock_records

        mock_api_instance = Mock()
        mock_api_instance.table.return_value = mock_table
        mock_api_class.return_value = mock_api_instance

        # Create tool and test
        tool = AirtableMCPTool(api_key=self.test_api_key)

        input_data = AirtableMCPInput(
            operation="search_records",
            baseId="test_base",
            tableId="test_table",
            filterByFormula="{Value} > 400",
            maxRecords=5,
        )
        result = await tool.execute(input_data)

        assert result.success is True
        assert result.operation == "search_records"
        assert result.base_id == "test_base"
        assert result.table_id == "test_table"
        assert result.filter_formula == "{Value} > 400"
        assert result.data_count == 1
        assert result.data[0]["fields"]["Value"] == 500

    @pytest.mark.asyncio
    @patch("athlea_langgraph.tools.external.airtable_mcp.Api")
    async def test_circuit_breaker_functionality(self, mock_api_class):
        """Test circuit breaker functionality."""
        mock_api_instance = Mock()
        mock_api_instance.bases.side_effect = Exception("API Error")
        mock_api_class.return_value = mock_api_instance

        # Create tool with low failure threshold
        tool = AirtableMCPTool(
            api_key=self.test_api_key,
            circuit_breaker_failure_threshold=2,
            circuit_breaker_recovery_timeout=1,
        )

        input_data = AirtableMCPInput(operation="list_bases")

        # First failure
        result1 = await tool.execute(input_data)
        assert result1.success is False
        assert result1.error_type == "AirtableMCPError"

        # Second failure - should trigger circuit breaker
        result2 = await tool.execute(input_data)
        assert result2.success is False

        # Third call - circuit breaker should be open
        result3 = await tool.execute(input_data)
        assert result3.success is False
        assert "Circuit breaker is open" in result3.message

    @pytest.mark.asyncio
    @patch("athlea_langgraph.tools.external.airtable_mcp.Api")
    async def test_unknown_operation(self, mock_api_class):
        """Test handling of unknown operation."""
        mock_api_instance = Mock()
        mock_api_class.return_value = mock_api_instance

        tool = AirtableMCPTool(api_key=self.test_api_key)

        # Create input with valid operation first, then modify it to test unknown operation handling
        input_data = AirtableMCPInput(operation="list_bases")
        input_data.operation = "unknown_operation"

        result = await tool.execute(input_data)

        assert result.success is False
        assert result.error_type == "AirtableMCPError"
        assert "Unknown operation" in result.message

    @patch("athlea_langgraph.tools.external.airtable_mcp.Api")
    def test_get_status(self, mock_api_class):
        """Test get_status method."""
        mock_api_instance = Mock()
        mock_api_class.return_value = mock_api_instance

        tool = AirtableMCPTool(api_key=self.test_api_key)
        status = tool.get_status()

        assert "circuit_breaker_state" in status
        assert "failure_count" in status
        assert "api_key_configured" in status
        assert status["api_key_configured"] is True
        assert status["tool_name"] == "AirtableMCPTool"


class TestAirtableMCPIntegration:
    """Test suite for MCP integration functions."""

    def setup_method(self):
        """Set up test fixtures."""
        # Reset global instance
        import athlea_langgraph.tools.external.airtable_mcp as airtable_module

        airtable_module._airtable_mcp_tool = None

    @pytest.mark.asyncio
    @patch("athlea_langgraph.tools.external.airtable_mcp.Api")
    async def test_airtable_mcp_operation_success(self, mock_api_class):
        """Test successful airtable_mcp_operation call."""
        # Setup mocks
        mock_base = Mock()
        mock_base.id = "base1"
        mock_base.name = "Test Base"
        mock_base.permission_level = "create"

        mock_api_instance = Mock()
        mock_api_instance.bases.return_value = [mock_base]
        mock_api_class.return_value = mock_api_instance

        # Test operation
        tool_input = {"operation": "list_bases"}

        with patch.dict(os.environ, {"AIRTABLE_API_KEY": "test_key"}):
            result = await airtable_mcp_operation(tool_input)

        assert result["success"] is True
        assert result["operation"] == "list_bases"
        assert result["data_count"] == 1
        assert len(result["data"]) == 1

    @pytest.mark.asyncio
    async def test_airtable_mcp_operation_validation_error(self):
        """Test airtable_mcp_operation with validation error."""
        # Invalid input - missing required fields
        tool_input = {"operation": "list_tables"}  # Missing baseId

        result = await airtable_mcp_operation(tool_input)

        assert result["success"] is False
        assert result["error_type"] == "ValidationError"
        assert "Input validation failed" in result["message"]

    @pytest.mark.asyncio
    @patch("athlea_langgraph.tools.external.airtable_mcp.Api")
    async def test_airtable_mcp_operation_unexpected_error(self, mock_api_class):
        """Test airtable_mcp_operation with unexpected error."""
        mock_api_class.side_effect = Exception("Unexpected error")

        tool_input = {"operation": "list_bases"}

        with patch.dict(os.environ, {"AIRTABLE_API_KEY": "test_key"}):
            result = await airtable_mcp_operation(tool_input)

        assert result["success"] is False
        assert "Unexpected error" in result["message"]

    @patch("athlea_langgraph.tools.external.airtable_mcp.Api")
    def test_get_airtable_mcp_tool_singleton(self, mock_api_class):
        """Test that get_airtable_mcp_tool returns singleton instance."""
        mock_api_instance = Mock()
        mock_api_class.return_value = mock_api_instance

        with patch.dict(os.environ, {"AIRTABLE_API_KEY": "test_key"}):
            tool1 = get_airtable_mcp_tool()
            tool2 = get_airtable_mcp_tool()

        assert tool1 is tool2  # Same instance


class TestAirtableMCPSchemaValidation:
    """Test suite for schema validation."""

    def test_list_bases_input_validation(self):
        """Test list_bases input validation."""
        # Valid input
        input_data = AirtableMCPInput(operation="list_bases")
        assert input_data.operation == "list_bases"

    def test_list_tables_input_validation(self):
        """Test list_tables input validation."""
        # Valid input
        input_data = AirtableMCPInput(operation="list_tables", baseId="test_base")
        assert input_data.operation == "list_tables"
        assert input_data.baseId == "test_base"

        # Invalid input - missing baseId
        with pytest.raises(ValueError):
            AirtableMCPInput(operation="list_tables")

    def test_list_records_input_validation(self):
        """Test list_records input validation."""
        # Valid input
        input_data = AirtableMCPInput(
            operation="list_records",
            baseId="test_base",
            tableId="test_table",
            maxRecords=10,
        )
        assert input_data.operation == "list_records"
        assert input_data.baseId == "test_base"
        assert input_data.tableId == "test_table"
        assert input_data.maxRecords == 10

        # Invalid input - missing required fields
        with pytest.raises(ValueError):
            AirtableMCPInput(operation="list_records", baseId="test_base")

    def test_search_records_input_validation(self):
        """Test search_records input validation."""
        # Valid input
        input_data = AirtableMCPInput(
            operation="search_records",
            baseId="test_base",
            tableId="test_table",
            filterByFormula="{Value} > 100",
            maxRecords=5,
        )
        assert input_data.operation == "search_records"
        assert input_data.baseId == "test_base"
        assert input_data.tableId == "test_table"
        assert input_data.filterByFormula == "{Value} > 100"
        assert input_data.maxRecords == 5

        # Invalid input - missing filterByFormula
        with pytest.raises(ValueError):
            AirtableMCPInput(
                operation="search_records", baseId="test_base", tableId="test_table"
            )


if __name__ == "__main__":
    # Run basic functionality test
    async def main():
        print("Testing Airtable MCP Tool...")

        # Test schema validation
        try:
            input_data = AirtableMCPInput(operation="list_bases")
            print(f"✅ Schema validation passed: {input_data.operation}")
        except Exception as e:
            print(f"❌ Schema validation failed: {e}")

        # Test tool initialization (without real API key)
        try:
            with patch.dict(os.environ, {"AIRTABLE_API_KEY": "test_key"}):
                with patch("athlea_langgraph.tools.external.airtable_mcp.Api"):
                    tool = AirtableMCPTool()
                    status = tool.get_status()
                    print(f"✅ Tool initialization passed: {status['tool_name']}")
        except Exception as e:
            print(f"❌ Tool initialization failed: {e}")

        print("Basic tests completed!")

    asyncio.run(main())
