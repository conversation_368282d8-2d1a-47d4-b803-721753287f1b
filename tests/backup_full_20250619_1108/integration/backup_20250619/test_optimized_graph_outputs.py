"""
Test script to see actual outputs from the optimized comprehensive coaching graph.
This will show how the graph processes different types of queries and what outputs it generates.
"""

import asyncio
import json
import logging
from typing import Dict, Any
import pytest

# Configure logging to see the graph execution
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

from athlea_langgraph.graphs.coaching_graph import (
    create_optimized_test_graph,
)


async def run_graph_test(query: str, description: str):
    """Test the graph with a specific query and show the output."""
    print(f"\n{'='*60}")
    print(f"TEST: {description}")
    print(f"QUERY: {query}")
    print(f"{'='*60}")

    try:
        # Create the optimized graph
        graph = await create_optimized_test_graph()

        # Create test state
        test_state = {
            "user_query": query,
            "messages": [],
            "user_profile": {
                "experience_level": "intermediate",
                "goals": ["muscle_gain", "strength"],
                "age": 28,
                "gender": "male",
            },
            "execution_steps": [],
        }

        # Execute the graph
        config = {
            "configurable": {"thread_id": f"test_{description.replace(' ', '_')}"}
        }
        result = await graph.ainvoke(test_state, config=config)

        # Display the results
        print(f"\n📊 EXECUTION RESULTS:")
        print(f"Current Node: {result.get('current_node', 'unknown')}")
        print(f"Execution Steps: {result.get('execution_steps', [])}")

        if "routing_decision" in result:
            print(f"Routing Decision: {result['routing_decision']}")
        if "primary_coach" in result:
            print(f"Primary Coach: {result['primary_coach']}")
        if "required_coaches" in result:
            print(f"Required Coaches: {result['required_coaches']}")

        # Show final response
        if "final_response" in result:
            print(f"\n💬 FINAL RESPONSE:")
            print(f"{result['final_response']}")
        elif "aggregated_response" in result:
            print(f"\n💬 AGGREGATED RESPONSE:")
            print(f"{result['aggregated_response']}")

        # Show coach responses if available
        if "coach_responses" in result and result["coach_responses"]:
            print(f"\n🤖 INDIVIDUAL COACH RESPONSES:")
            for coach_name, response in result["coach_responses"].items():
                print(f"\n{coach_name.upper()}:")
                print(f"{response}")

        # Show debug info
        if "debug_info" in result:
            print(f"\n🔍 DEBUG INFO:")
            print(json.dumps(result["debug_info"], indent=2))

        print(f"\n✅ Test completed successfully!")

    except Exception as e:
        print(f"\n❌ Error during test: {e}")
        import traceback

        traceback.print_exc()


@pytest.mark.asyncio
async def test_routing_logic():
    """Automated test to verify the routing logic of the Intelligence Hub."""
    print(f"\n{'='*60}")
    print("AUTOMATED TEST: Verifying Intelligence Hub Routing")
    print(f"{'='*60}")

    graph = await create_optimized_test_graph()

    routing_test_cases = [
        ("hi there", "automated_greeting", "Simple Greeting"),
        ("What is the best way to squat?", "direct_coach", "Direct Strength Query"),
        (
            "I need a plan for my marathon, including nutrition and recovery",
            "multi_coach",
            "Multi-Coach Query",
        ),
        (
            "I want to improve",
            "clarification",
            "Vague Query requiring clarification",
        ),
        (
            "What should I eat before a workout?",
            "direct_coach",
            "Direct Nutrition Query",
        ),
    ]

    for query, expected_routing, description in routing_test_cases:
        print(f"\n--- Testing Case: {description} ---")
        print(f"Query: '{query}'")
        print(f"Expected Routing: '{expected_routing}'")

        test_state = {"user_query": query, "messages": []}
        config = {
            "configurable": {
                "thread_id": f"test_routing_{description.replace(' ', '_')}"
            }
        }

        result = await graph.ainvoke(test_state, config=config)

        actual_routing = result.get("routing_decision")
        print(f"Actual Routing: '{actual_routing}'")

        assert (
            actual_routing == expected_routing
        ), f"Failed routing for '{description}': Expected '{expected_routing}', got '{actual_routing}'"
        print(f"✅ PASSED: {description}")


@pytest.mark.asyncio
async def test_execution_flow_debug():
    """Debug test to understand why there are so many execution steps."""
    print(f"\n{'='*60}")
    print("DEBUG TEST: Checking Execution Flow")
    print(f"{'='*60}")

    graph = await create_optimized_test_graph()

    # Simple test query
    test_query = "hi there"

    # Configure thread
    config = {"configurable": {"thread_id": "debug_test"}}

    print(f"\n🧪 Testing query: '{test_query}'")
    print("Executing graph...")

    # Execute the graph
    result = await graph.ainvoke(
        {"user_query": test_query, "messages": [], "execution_steps": []}, config=config
    )

    # Log the final result
    final_steps = result.get("execution_steps", [])
    print(f"\n📊 EXECUTION SUMMARY:")
    print(f"  - Total steps: {len(final_steps)}")
    print(f"  - Steps: {final_steps}")
    print(f"  - Final response: {result.get('final_response', 'No response')[:100]}...")

    # Count step frequencies
    step_counts = {}
    for step in final_steps:
        step_counts[step] = step_counts.get(step, 0) + 1

    print(f"\n📈 STEP FREQUENCIES:")
    for step, count in step_counts.items():
        print(f"  - {step}: {count} times")
        if count > 1:
            print(f"    ⚠️ WARNING: {step} executed {count} times!")


@pytest.mark.asyncio
async def test_domain_agent_tool_calling():
    """Test that domain agents actually call their MCP tools when appropriate."""
    print(f"\n{'='*60}")
    print("TOOL CALLING TEST: Verifying Domain Agent Tool Usage")
    print(f"{'='*60}")

    graph = await create_optimized_test_graph()

    # Test cases designed to trigger specific MCP tools for each domain agent
    tool_test_cases = [
        {
            "query": "I'm 25 years old, 70kg, 175cm tall, moderately active. Calculate my daily calories for muscle building.",
            "expected_coach": "nutrition_coach",
            "expected_tools": ["calculate_daily_calories", "calculate_macro_targets"],
            "description": "Nutrition Tool Test - Should trigger calorie and macro calculations",
        },
        {
            "query": "I need a strength assessment and want to know the best squat progressions for a beginner.",
            "expected_coach": "strength_coach",
            "expected_tools": [
                "comprehensive_strength_assessment",
                "search_strength_exercises",
                "get_exercise_progression",
            ],
            "description": "Strength Tool Test - Should trigger strength assessment and exercise search",
        },
        {
            "query": "I want a cardiovascular fitness assessment and need my heart rate training zones calculated.",
            "expected_coach": "cardio_coach",
            "expected_tools": [
                "comprehensive_cardio_assessment",
                "calculate_heart_rate_zones",
            ],
            "description": "Cardio Tool Test - Should trigger cardio assessment and HR zone calculation",
        },
    ]

    for test_case in tool_test_cases:
        print(f"\n🧪 Testing: {test_case['description']}")
        print(f"Query: '{test_case['query']}'")
        print(f"Expected Coach: {test_case['expected_coach']}")
        print(f"Expected Tools: {test_case['expected_tools']}")

        # Configure thread
        config = {
            "configurable": {"thread_id": f"tool_test_{test_case['expected_coach']}"}
        }

        try:
            # Execute the graph and track tool usage
            result = await graph.ainvoke(
                {
                    "user_query": test_case["query"],
                    "messages": [],
                    "execution_steps": [],
                },
                config=config,
            )

            # Check routing decision
            routing_decision = result.get("routing_decision", "unknown")
            primary_coach = result.get("primary_coach", "unknown")
            final_response = result.get("final_response", "")

            print(f"✅ Routing Decision: {routing_decision}")
            print(f"✅ Primary Coach: {primary_coach}")
            print(f"✅ Final Response Length: {len(final_response)} chars")

            # Verify correct coach was selected
            if primary_coach == test_case["expected_coach"]:
                print(f"✅ ROUTING CORRECT: {primary_coach}")
            else:
                print(
                    f"❌ ROUTING INCORRECT: Expected {test_case['expected_coach']}, got {primary_coach}"
                )

            # Analyze response for tool usage indicators
            response_lower = final_response.lower()
            tool_indicators_found = []

            # Look for tool-specific indicators in the response
            tool_indicators = {
                "calculate_daily_calories": [
                    "calories",
                    "caloric needs",
                    "daily energy",
                    "bmr",
                    "tdee",
                ],
                "calculate_macro_targets": [
                    "protein",
                    "carbohydrates",
                    "macros",
                    "macro targets",
                    "macronutrients",
                ],
                "comprehensive_strength_assessment": [
                    "strength assessment",
                    "strength profile",
                    "movement quality",
                    "strength evaluation",
                ],
                "search_strength_exercises": [
                    "exercise database",
                    "exercise search",
                    "recommended exercises",
                ],
                "get_exercise_progression": [
                    "progression",
                    "beginner to advanced",
                    "exercise variations",
                ],
                "comprehensive_cardio_assessment": [
                    "cardiovascular assessment",
                    "vo2max",
                    "cardio fitness",
                    "fitness level",
                ],
                "calculate_heart_rate_zones": [
                    "heart rate zones",
                    "training zones",
                    "hr zones",
                    "zone 1",
                    "zone 2",
                ],
            }

            for expected_tool in test_case["expected_tools"]:
                indicators = tool_indicators.get(expected_tool, [])
                found_indicators = [ind for ind in indicators if ind in response_lower]
                if found_indicators:
                    tool_indicators_found.append(f"{expected_tool}: {found_indicators}")
                    print(f"✅ TOOL EVIDENCE for {expected_tool}: {found_indicators}")
                else:
                    print(f"❌ NO EVIDENCE for {expected_tool} in response")

            # Check if response shows any calculation/assessment activity
            calc_indicators = [
                "calculated",
                "assessment",
                "analysis",
                "based on your",
                "determined",
                "evaluated",
            ]
            calc_evidence = [ind for ind in calc_indicators if ind in response_lower]

            if calc_evidence:
                print(f"✅ CALCULATION ACTIVITY: {calc_evidence}")
            else:
                print(f"⚠️ NO CALCULATION ACTIVITY detected")

            # Show first 200 chars of response for manual verification
            print(f"📝 Response Preview: {final_response[:200]}...")

        except Exception as e:
            print(f"❌ Test failed with error: {e}")

        print("-" * 60)


@pytest.mark.asyncio
async def test_direct_tools_execution():
    """Test that domain agents actually execute their direct tools in the optimized graph."""
    print(f"\n{'='*60}")
    print("DIRECT TOOLS TEST: Verifying Tool Execution in Optimized Graph")
    print(f"{'='*60}")

    graph = await create_optimized_test_graph()

    # Test cases specifically designed to trigger direct tools
    tool_execution_test_cases = [
        {
            "query": "I'm 25 years old, weigh 70kg, 175cm tall, moderately active male. Calculate my daily calories for muscle building.",
            "expected_coach": "nutrition_coach",
            "expected_tools": ["calculate_daily_calories"],
            "description": "Nutrition Calorie Calculation",
            "should_trigger_tools": True,
        },
        {
            "query": "I want to squat better. Show me squat exercises for beginners.",
            "expected_coach": "strength_coach",
            "expected_tools": ["search_strength_exercises"],
            "description": "Strength Exercise Search",
            "should_trigger_tools": True,
        },
        {
            "query": "I'm a 30-year-old runner. Calculate my heart rate zones.",
            "expected_coach": "cardio_coach",
            "expected_tools": ["calculate_heart_rate_zones"],
            "description": "Cardio Heart Rate Zones",
            "should_trigger_tools": True,
        },
        {
            "query": "I'm feeling stressed about my workout routine. Can you assess my mental state?",
            "expected_coach": "mental_coach",
            "expected_tools": ["mental_state_assessment"],
            "description": "Mental State Assessment",
            "should_trigger_tools": True,
        },
        {
            "query": "My shoulders are tight. Generate a mobility routine for me.",
            "expected_coach": "recovery_coach",
            "expected_tools": ["generate_mobility_protocol"],
            "description": "Recovery Mobility Protocol",
            "should_trigger_tools": True,
        },
    ]

    results = []

    for i, test_case in enumerate(tool_execution_test_cases, 1):
        print(
            f"\n🧪 Test {i}/{len(tool_execution_test_cases)}: {test_case['description']}"
        )
        print(f"Query: '{test_case['query']}'")
        print(f"Expected Coach: {test_case['expected_coach']}")
        print(f"Expected Tools: {test_case['expected_tools']}")

        # Configure thread
        config = {"configurable": {"thread_id": f"tool_test_{i}"}}

        try:
            # Execute the graph
            result = await graph.ainvoke(
                {
                    "user_query": test_case["query"],
                    "messages": [],
                    "execution_steps": [],
                },
                config=config,
            )

            # Analyze results
            final_response = result.get("final_response", "")
            debug_info = result.get("debug_info", {})
            current_node = result.get("current_node", "")
            routing_decision = result.get("routing_decision", "")
            tool_count = debug_info.get("tool_count", 0) if debug_info else 0

            # Check if correct coach was selected
            coach_correct = current_node == test_case["expected_coach"]

            # Check for tool execution indicators in the response
            tools_mentioned = any(
                tool in final_response.lower() for tool in test_case["expected_tools"]
            )

            # Log detailed results
            print(f"✅ Routing Result:")
            print(f"  - Coach Selected: {current_node}")
            print(f"  - Routing Decision: {routing_decision}")
            print(f"  - Coach Correct: {coach_correct}")
            print(f"  - Tool Count Available: {tool_count}")
            print(f"  - Tools Mentioned in Response: {tools_mentioned}")
            print(f"  - Response Length: {len(final_response)} chars")

            # Check for actual tool execution patterns
            tool_execution_indicators = [
                "calculated" in final_response.lower(),
                "assessment" in final_response.lower(),
                "exercise" in final_response.lower(),
                "protocol" in final_response.lower(),
                "zone" in final_response.lower(),
                any(
                    tool.replace("_", " ") in final_response.lower()
                    for tool in test_case["expected_tools"]
                ),
            ]

            tools_likely_executed = any(tool_execution_indicators)

            print(f"  - Tools Likely Executed: {tools_likely_executed}")
            print(f"  - Response Preview: {final_response[:100]}...")

            # Store results for summary
            test_result = {
                "test_case": test_case["description"],
                "query": test_case["query"],
                "expected_coach": test_case["expected_coach"],
                "actual_coach": current_node,
                "coach_correct": coach_correct,
                "tool_count": tool_count,
                "tools_mentioned": tools_mentioned,
                "tools_likely_executed": tools_likely_executed,
                "response_length": len(final_response),
                "routing_decision": routing_decision,
            }
            results.append(test_result)

        except Exception as e:
            print(f"❌ Test {i} failed with error: {e}")
            results.append(
                {
                    "test_case": test_case["description"],
                    "error": str(e),
                    "coach_correct": False,
                    "tool_count": 0,
                    "tools_likely_executed": False,
                }
            )

    # Print comprehensive summary
    print(f"\n{'='*60}")
    print("DIRECT TOOLS TEST SUMMARY")
    print(f"{'='*60}")

    total_tests = len(results)
    correct_routing = sum(1 for r in results if r.get("coach_correct", False))
    tools_executed = sum(1 for r in results if r.get("tools_likely_executed", False))
    total_tool_count = sum(r.get("tool_count", 0) for r in results)

    print(f"📊 Overall Results:")
    print(f"  - Total Tests: {total_tests}")
    print(
        f"  - Correct Routing: {correct_routing}/{total_tests} ({correct_routing/total_tests*100:.1f}%)"
    )
    print(
        f"  - Tools Likely Executed: {tools_executed}/{total_tests} ({tools_executed/total_tests*100:.1f}%)"
    )
    print(f"  - Total Tools Available: {total_tool_count}")
    print(f"  - Average Tools per Agent: {total_tool_count/total_tests:.1f}")

    print(f"\n📋 Individual Test Results:")
    for r in results:
        status = (
            "✅" if r.get("coach_correct") and r.get("tools_likely_executed") else "❌"
        )
        print(
            f"  {status} {r['test_case']}: "
            f"Routing={r.get('coach_correct', False)}, "
            f"Tools={r.get('tool_count', 0)}, "
            f"Executed={r.get('tools_likely_executed', False)}"
        )

    # Assert that we have reasonable results
    assert (
        correct_routing >= total_tests * 0.8
    ), f"Routing accuracy too low: {correct_routing}/{total_tests}"
    assert total_tool_count > 0, "No tools were loaded for any agents"

    print(f"\n🎉 Direct Tools Test Completed!")
    print(
        f"The optimized graph is properly routing to domain agents with direct tools."
    )

    return results


@pytest.mark.asyncio
async def test_all_domain_agent_tools():
    """Test that all domain agents have their complete toolset including shared tools."""
    print(f"\n{'='*60}")
    print("COMPREHENSIVE TOOLS TEST: Verifying All Domain Agent Tools")
    print(f"{'='*60}")

    graph = await create_optimized_test_graph()

    # Expected tools for each domain agent
    expected_tools = {
        "nutrition_coach": {
            "domain_tools": [
                "calculate_daily_calories",
                "calculate_macro_targets",
                "generate_meal_plan",
                "search_recipes",
                "get_recipe_recommendations",
            ],
            "shared_tools": [
                "graphrag_search",
                "session_generation",
                "azure_search_retriever",
                "web_search",
            ],
            "min_total": 8,  # 5 domain + 4 shared - some may fail to load
        },
        "strength_coach": {
            "domain_tools": [
                "search_strength_exercises",
                "get_exercise_progression",
                "comprehensive_strength_assessment",
            ],
            "shared_tools": [
                "graphrag_search",
                "session_generation",
                "azure_search_retriever",
                "web_search",
            ],
            "min_total": 6,  # 3 domain + 4 shared - some may fail to load
        },
        "cardio_coach": {
            "domain_tools": [
                "comprehensive_cardio_assessment",
                "calculate_training_zones",
                "calculate_heart_rate_zones",
            ],
            "shared_tools": [
                "graphrag_search",
                "session_generation",
                "azure_search_retriever",
                "web_search",
            ],
            "min_total": 6,  # 3 domain + 4 shared - some may fail to load
        },
        "recovery_coach": {
            "domain_tools": [
                "generate_mobility_protocol",
                "optimize_sleep",
                "assess_wellness",
            ],
            "shared_tools": [
                "graphrag_search",
                "session_generation",
                "azure_search_retriever",
                "web_search",
            ],
            "min_total": 6,  # 3 domain + 4 shared - some may fail to load
        },
        "mental_coach": {
            "domain_tools": [
                "mental_state_assessment",
                "stress_level_tracker",
                "mood_pattern_analyzer",
                "goal_tracker",
            ],
            "shared_tools": [
                "graphrag_search",
                "session_generation",
                "azure_search_retriever",
                "web_search",
            ],
            "min_total": 7,  # 4 domain + 4 shared - some may fail to load
        },
    }

    # Test cases that should trigger different tools
    comprehensive_test_cases = [
        {
            "query": "I'm 25 years old, 70kg, moderately active. Calculate my daily calories and create a meal plan.",
            "expected_coach": "nutrition_coach",
            "expected_tools": ["calculate_daily_calories", "generate_meal_plan"],
            "description": "Nutrition Calorie + Meal Plan",
        },
        {
            "query": "Show me squat exercises and create a strength training session.",
            "expected_coach": "strength_coach",
            "expected_tools": ["search_strength_exercises", "session_generation"],
            "description": "Strength Exercises + Session Generation",
        },
        {
            "query": "I'm a runner. Calculate my heart rate zones and research optimal cardio training.",
            "expected_coach": "cardio_coach",
            "expected_tools": ["calculate_heart_rate_zones", "graphrag_search"],
            "description": "Cardio Zones + Research",
        },
        {
            "query": "My shoulders are tight. Generate a mobility routine and find recent recovery research.",
            "expected_coach": "recovery_coach",
            "expected_tools": ["generate_mobility_protocol", "web_search"],
            "description": "Recovery Protocol + Web Research",
        },
        {
            "query": "I'm stressed about training. Assess my mental state and research stress management techniques.",
            "expected_coach": "mental_coach",
            "expected_tools": ["mental_state_assessment", "azure_search_retriever"],
            "description": "Mental Assessment + Azure Search",
        },
    ]

    results = []

    for i, test_case in enumerate(comprehensive_test_cases, 1):
        print(
            f"\n🧪 Test {i}/{len(comprehensive_test_cases)}: {test_case['description']}"
        )
        print(f"Query: '{test_case['query']}'")
        print(f"Expected Coach: {test_case['expected_coach']}")
        print(f"Expected Tools: {test_case['expected_tools']}")

        # Configure thread
        config = {"configurable": {"thread_id": f"comprehensive_test_{i}"}}

        try:
            # Execute the graph
            result = await graph.ainvoke(
                {
                    "user_query": test_case["query"],
                    "messages": [],
                    "execution_steps": [],
                },
                config=config,
            )

            # Analyze results
            final_response = result.get("final_response", "")
            debug_info = result.get("debug_info", {})
            current_node = result.get("current_node", "")
            routing_decision = result.get("routing_decision", "")
            tool_count = debug_info.get("tool_count", 0) if debug_info else 0

            # Check if correct coach was selected
            coach_correct = current_node == test_case["expected_coach"]

            # Get expected tool counts for this coach
            if current_node in expected_tools:
                expected_domain = expected_tools[current_node]["domain_tools"]
                expected_shared = expected_tools[current_node]["shared_tools"]
                min_expected_total = expected_tools[current_node]["min_total"]

                # Check tool availability
                tools_adequate = tool_count >= min_expected_total

                print(f"✅ Tool Analysis:")
                print(f"  - Coach Selected: {current_node}")
                print(f"  - Routing Correct: {coach_correct}")
                print(f"  - Tools Available: {tool_count}")
                print(f"  - Min Expected: {min_expected_total}")
                print(f"  - Tools Adequate: {tools_adequate}")
                print(f"  - Expected Domain Tools: {expected_domain}")
                print(f"  - Expected Shared Tools: {expected_shared}")

                # Check for tool execution indicators
                tool_execution_indicators = []
                for tool in test_case["expected_tools"]:
                    if any(
                        keyword in final_response.lower()
                        for keyword in [
                            tool.replace("_", " "),
                            "calculated",
                            "generated",
                            "assessed",
                            "searched",
                            "found",
                            "analyzed",
                            "protocol",
                            "routine",
                        ]
                    ):
                        tool_execution_indicators.append(tool)

                tools_likely_executed = len(tool_execution_indicators) > 0

                print(f"  - Tools Likely Executed: {tools_likely_executed}")
                print(f"  - Execution Indicators: {tool_execution_indicators}")
                print(f"  - Response Length: {len(final_response)} chars")
                print(f"  - Response Preview: {final_response[:150]}...")

                # Store results
                test_result = {
                    "test_case": test_case["description"],
                    "coach_correct": coach_correct,
                    "tool_count": tool_count,
                    "min_expected": min_expected_total,
                    "tools_adequate": tools_adequate,
                    "tools_likely_executed": tools_likely_executed,
                    "execution_indicators": tool_execution_indicators,
                    "response_length": len(final_response),
                    "routing_decision": routing_decision,
                }
                results.append(test_result)
            else:
                print(f"❌ Unknown coach: {current_node}")
                results.append(
                    {
                        "test_case": test_case["description"],
                        "coach_correct": False,
                        "tool_count": 0,
                        "tools_adequate": False,
                        "tools_likely_executed": False,
                    }
                )

        except Exception as e:
            print(f"❌ Test {i} failed with error: {e}")
            results.append(
                {
                    "test_case": test_case["description"],
                    "error": str(e),
                    "coach_correct": False,
                    "tool_count": 0,
                    "tools_adequate": False,
                    "tools_likely_executed": False,
                }
            )

    # Print comprehensive summary
    print(f"\n{'='*60}")
    print("COMPREHENSIVE TOOLS TEST SUMMARY")
    print(f"{'='*60}")

    total_tests = len(results)
    correct_routing = sum(1 for r in results if r.get("coach_correct", False))
    adequate_tools = sum(1 for r in results if r.get("tools_adequate", False))
    tools_executed = sum(1 for r in results if r.get("tools_likely_executed", False))
    total_tool_count = sum(r.get("tool_count", 0) for r in results)

    print(f"📊 Overall Results:")
    print(f"  - Total Tests: {total_tests}")
    print(
        f"  - Correct Routing: {correct_routing}/{total_tests} ({correct_routing/total_tests*100:.1f}%)"
    )
    print(
        f"  - Adequate Tool Count: {adequate_tools}/{total_tests} ({adequate_tools/total_tests*100:.1f}%)"
    )
    print(
        f"  - Tools Executed: {tools_executed}/{total_tests} ({tools_executed/total_tests*100:.1f}%)"
    )
    print(f"  - Total Tools Available: {total_tool_count}")
    print(f"  - Average Tools per Agent: {total_tool_count/total_tests:.1f}")

    print(f"\n📋 Individual Test Results:")
    for r in results:
        routing_status = "✅" if r.get("coach_correct") else "❌"
        tools_status = "✅" if r.get("tools_adequate") else "❌"
        execution_status = "✅" if r.get("tools_likely_executed") else "❌"

        print(
            f"  {routing_status}{tools_status}{execution_status} {r['test_case']}: "
            f"Routing={r.get('coach_correct', False)}, "
            f"Tools={r.get('tool_count', 0)}/{r.get('min_expected', 0)}, "
            f"Executed={r.get('tools_likely_executed', False)}"
        )

    print(f"\n🔧 Expected Tool Breakdown by Agent:")
    for agent, tools_info in expected_tools.items():
        domain_count = len(tools_info["domain_tools"])
        shared_count = len(tools_info["shared_tools"])
        total_expected = domain_count + shared_count
        print(
            f"  - {agent}: {domain_count} domain + {shared_count} shared = {total_expected} total (min {tools_info['min_total']})"
        )

    # Assert that we have reasonable results
    assert (
        correct_routing >= total_tests * 0.8
    ), f"Routing accuracy too low: {correct_routing}/{total_tests}"
    assert (
        adequate_tools >= total_tests * 0.8
    ), f"Tool availability too low: {adequate_tools}/{total_tests}"
    assert (
        total_tool_count > 30
    ), f"Total tools too low: {total_tool_count} (expected >30 across all agents)"

    print(f"\n🎉 Comprehensive Tools Test Completed!")
    print(
        f"All domain agents have adequate tools including both domain-specific and shared tools."
    )

    return results


async def main():
    """Run various test scenarios to see graph outputs."""

    print("🚀 Testing Optimized Comprehensive Coaching Graph Outputs")
    print("This will show you actual outputs from different types of queries.")

    test_scenarios = [
        {
            "query": "Hello, I'm new to fitness and looking for guidance.",
            "description": "Simple Greeting",
        },
        {
            "query": "I want to build muscle and get stronger. Can you help me create a strength training plan?",
            "description": "Direct Strength Request",
        },
        {
            "query": "I need help with both strength training and nutrition for muscle gain.",
            "description": "Multi-Coach Request",
        },
        {
            "query": "What does the latest research say about optimal protein intake for muscle building?",
            "description": "Research Query (GraphRAG)",
        },
        {
            "query": "My goal is unclear, I want to get fit but don't know where to start.",
            "description": "Clarification Needed",
        },
    ]

    for scenario in test_scenarios:
        await run_graph_test(scenario["query"], scenario["description"])

        # Add a pause between tests
        print(f"\n{'⏸️ '*20}")
        await asyncio.sleep(1)


if __name__ == "__main__":
    # To run the automated test: pytest test_optimized_graph_outputs.py
    # To run the manual inspection: python test_optimized_graph_outputs.py
    asyncio.run(main())
