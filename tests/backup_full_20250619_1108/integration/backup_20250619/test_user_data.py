#!/usr/bin/env python3
"""
Test script to verify user data fetching functionality.

This script tests the MongoDB connection and user data retrieval
to ensure the Python backend can properly fetch user profiles and plans.
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# Add the project root to Python path
sys.path.insert(0, ".")

from athlea_langgraph.utils.user_data_utils import (
    fetch_user_data_for_coaching,
    get_user_data_manager,
    format_training_profile_for_prompt,
    format_current_plan_for_prompt,
    UserDataManager,
)

# Load environment variables
load_dotenv()


async def test_user_data_connection():
    """Test MongoDB connection and basic operations."""
    print("🔧 Testing MongoDB Connection...")

    mongodb_uri = os.getenv("MONGODB_URI")
    if not mongodb_uri:
        print("❌ MONGODB_URI environment variable not set")
        return False

    print(f"✅ MONGODB_URI found: {mongodb_uri[:20]}...")

    try:
        # Test basic connection
        manager = UserDataManager(mongodb_uri)

        # Try to connect (this will happen when we make our first query)
        print("🔌 Testing database connection...")

        # Use a test user ID - you can replace this with a real user ID from your database
        test_user_id = "test_user_123"  # Replace with actual user ID if you have one

        training_profile, current_plan = await manager.fetch_user_data(test_user_id)

        if training_profile:
            print(f"✅ Successfully fetched training profile for user {test_user_id}")
            print(f"   Profile domains: {list(training_profile.to_dict().keys())}")
        else:
            print(
                f"ℹ️  No training profile found for user {test_user_id} (this is normal for test user)"
            )

        if current_plan:
            print(f"✅ Successfully fetched current plan for user {test_user_id}")
            print(f"   Plan name: {current_plan.plan_name}")
        else:
            print(
                f"ℹ️  No current plan found for user {test_user_id} (this is normal for test user)"
            )

        # Close the connection
        manager.close()
        print("✅ Database connection test completed successfully")
        return True

    except Exception as e:
        print(f"❌ Database connection test failed: {e}")
        return False


async def test_user_data_fetching():
    """Test the main user data fetching function."""
    print("\n🎯 Testing User Data Fetching Function...")

    mongodb_uri = os.getenv("MONGODB_URI")
    test_user_id = "test_user_123"  # Replace with actual user ID if you have one

    try:
        user_data = await fetch_user_data_for_coaching(test_user_id, mongodb_uri)

        print(f"✅ fetch_user_data_for_coaching completed for user {test_user_id}")
        print(f"   Keys returned: {list(user_data.keys())}")

        # Test formatting functions
        if user_data.get("training_profile_formatted"):
            print(
                f"   Training profile formatted: {len(user_data['training_profile_formatted'])} characters"
            )

        if user_data.get("current_plan_formatted"):
            print(
                f"   Current plan formatted: {len(user_data['current_plan_formatted'])} characters"
            )

        return True

    except Exception as e:
        print(f"❌ User data fetching test failed: {e}")
        return False


async def test_with_real_user():
    """Test with a real user ID if provided via environment variable."""
    real_user_id = os.getenv("TEST_USER_ID")

    if not real_user_id:
        print("\nℹ️  To test with a real user, set TEST_USER_ID environment variable")
        return True

    print(f"\n🔍 Testing with real user ID: {real_user_id}")

    mongodb_uri = os.getenv("MONGODB_URI")

    try:
        user_data = await fetch_user_data_for_coaching(real_user_id, mongodb_uri)

        print(f"✅ Successfully fetched data for real user {real_user_id}")

        if user_data.get("training_profile"):
            profile_data = user_data["training_profile"]
            domains = (
                list(profile_data.keys()) if isinstance(profile_data, dict) else []
            )
            print(f"   Training profile domains: {domains}")

        if user_data.get("current_plan"):
            plan_data = user_data["current_plan"]
            plan_name = (
                plan_data.get("plan_name", "Unknown")
                if isinstance(plan_data, dict)
                else "Unknown"
            )
            print(f"   Current plan: {plan_name}")

        # Show formatted versions
        if user_data.get("training_profile_formatted"):
            formatted_preview = (
                user_data["training_profile_formatted"][:200] + "..."
                if len(user_data["training_profile_formatted"]) > 200
                else user_data["training_profile_formatted"]
            )
            print(f"   Formatted profile preview: {formatted_preview}")

        return True

    except Exception as e:
        print(f"❌ Real user test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Starting User Data Testing Suite\n")

    # Test 1: Basic connection
    connection_ok = await test_user_data_connection()

    if not connection_ok:
        print(
            "\n❌ Basic connection test failed. Please check your MongoDB configuration."
        )
        return

    # Test 2: User data fetching function
    fetching_ok = await test_user_data_fetching()

    # Test 3: Real user (if provided)
    real_user_ok = await test_with_real_user()

    # Summary
    print("\n📊 Test Summary:")
    print(f"   Connection Test: {'✅ PASS' if connection_ok else '❌ FAIL'}")
    print(f"   Fetching Test: {'✅ PASS' if fetching_ok else '❌ FAIL'}")
    print(f"   Real User Test: {'✅ PASS' if real_user_ok else '❌ FAIL'}")

    if connection_ok and fetching_ok and real_user_ok:
        print("\n🎉 All tests passed! User data fetching is working correctly.")
        print("\n💡 Next steps:")
        print("   1. Set TEST_USER_ID environment variable to test with a real user")
        print("   2. Start the Python backend server: python start_backend.py")
        print("   3. Test the API endpoints with real user data")
    else:
        print("\n⚠️  Some tests failed. Please check the error messages above.")


if __name__ == "__main__":
    asyncio.run(main())
