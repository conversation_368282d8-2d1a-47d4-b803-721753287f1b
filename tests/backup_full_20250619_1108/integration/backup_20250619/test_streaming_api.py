#!/usr/bin/env python3
"""
Test Streaming Coaching API

This script tests the new streaming coaching endpoint to ensure it properly
streams ReAct coaching responses to the frontend.
"""

import asyncio
import json
import logging
from typing import AsyncGenerator

import httpx

from athlea_langgraph.api.coaching_stream import CoachingRequest, app

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_streaming_endpoint_direct():
    """Test the streaming endpoint directly without HTTP server."""
    print("🚀 Testing Streaming Coaching API (Direct)")
    print("=" * 60)

    try:
        # Import the streaming handler directly
        from athlea_langgraph.api.coaching_stream import StreamingCoachingResponse

        # Test request
        test_request = CoachingRequest(
            message="I want to build muscle and get stronger. Can you create a strength training plan for me?",
            coach_type="strength",
            user_profile={
                "name": "Test User",
                "fitness_level": "beginner",
                "goals": ["build muscle", "get stronger"],
                "available_days": 3,
            },
        )

        # Create streaming handler
        streaming_handler = StreamingCoachingResponse("strength", test_request)

        print(f"✓ Created streaming handler for {test_request.coach_type} coach")

        # Test streaming response
        print("\n🔄 Streaming response:")
        print("-" * 40)

        message_count = 0
        async for message in streaming_handler.stream_response():
            message_count += 1

            # Parse the streamed message
            if message.startswith("data: "):
                try:
                    data = json.loads(message[6:])  # Remove "data: " prefix
                    event_type = data.get("event")
                    event_data = data.get("data", {})

                    if event_type == "status":
                        print(f"📊 Status: {event_data.get('message', 'Unknown')}")
                    elif event_type == "response_start":
                        print(
                            f"🎯 Response starting (length: {event_data.get('total_length', 0)})"
                        )
                    elif event_type == "response_chunk":
                        chunk = event_data.get("chunk", "")
                        is_final = event_data.get("is_final", False)
                        print(f"📝 Chunk: {chunk}", end="")
                        if is_final:
                            print("\n✅ Response complete")
                    elif event_type == "error":
                        print(f"❌ Error: {event_data.get('message', 'Unknown error')}")

                except json.JSONDecodeError as e:
                    print(f"⚠️ Failed to parse message: {e}")

            # Limit output for testing
            if message_count > 50:
                print("\n... (truncated for testing)")
                break

        print(f"\n✅ Streaming test completed ({message_count} messages)")

    except Exception as e:
        print(f"❌ Error testing streaming endpoint: {e}")
        logger.exception("Error in streaming endpoint test")


async def test_available_coaches():
    """Test the coaches endpoint."""
    print("\n👥 Testing Available Coaches Endpoint")
    print("=" * 60)

    try:
        # Import the endpoint function directly
        from athlea_langgraph.api.coaching_stream import get_available_coaches

        coaches_data = await get_available_coaches()

        print(f"✓ Found {coaches_data['total_count']} available coaches:")

        for coach_type, coach_info in coaches_data["coaches"].items():
            print(f"\n🏃‍♂️ {coach_info['name']}")
            print(f"   Description: {coach_info['description']}")
            print(f"   Specialties: {', '.join(coach_info['specialties'])}")

    except Exception as e:
        print(f"❌ Error testing coaches endpoint: {e}")
        logger.exception("Error in coaches endpoint test")


async def test_health_check():
    """Test the health check endpoint."""
    print("\n🏥 Testing Health Check Endpoint")
    print("=" * 60)

    try:
        # Import the health check function directly
        from athlea_langgraph.api.coaching_stream import health_check

        health_data = await health_check()

        print(f"✓ Health Status: {health_data['status']}")
        print(f"✓ Service: {health_data['service']}")
        print(f"✓ Coaches Available: {health_data['coaches_available']}")
        print(f"✓ Tools Initialized: {health_data['tools_initialized']}")

    except Exception as e:
        print(f"❌ Error testing health check: {e}")
        logger.exception("Error in health check test")


async def test_request_validation():
    """Test request validation."""
    print("\n✅ Testing Request Validation")
    print("=" * 60)

    try:
        # Test valid request
        valid_request = CoachingRequest(
            message="Test message",
            coach_type="strength",
            user_profile={"test": "data"},
        )
        print("✓ Valid request created successfully")
        print(f"  Message: {valid_request.message}")
        print(f"  Coach Type: {valid_request.coach_type}")
        print(f"  Has Profile: {valid_request.user_profile is not None}")

        # Test invalid coach type
        try:
            invalid_request = CoachingRequest(
                message="Test message",
                coach_type="invalid_coach",
            )
            print("⚠️ Invalid coach type was accepted (this might be expected)")
        except Exception as e:
            print(f"✓ Invalid coach type properly rejected: {e}")

    except Exception as e:
        print(f"❌ Error testing request validation: {e}")
        logger.exception("Error in request validation test")


async def test_multiple_coach_types():
    """Test streaming with different coach types."""
    print("\n🎯 Testing Multiple Coach Types")
    print("=" * 60)

    coach_tests = [
        {
            "type": "nutrition",
            "message": "I'm vegetarian and want to optimize my nutrition for muscle building.",
        },
        {
            "type": "recovery",
            "message": "I'm feeling tired after workouts. How can I improve my recovery?",
        },
        {
            "type": "mental",
            "message": "I struggle with motivation to exercise. Can you help?",
        },
    ]

    for test_case in coach_tests:
        try:
            print(f"\n🏃‍♂️ Testing {test_case['type']} coach...")

            from athlea_langgraph.api.coaching_stream import StreamingCoachingResponse

            request = CoachingRequest(
                message=test_case["message"],
                coach_type=test_case["type"],
                user_profile={"fitness_level": "intermediate"},
            )

            handler = StreamingCoachingResponse(test_case["type"], request)

            # Test just initialization and first few messages
            message_count = 0
            async for message in handler.stream_response():
                message_count += 1
                if message_count >= 3:  # Just test initialization
                    break

            print(
                f"✓ {test_case['type']} coach streaming works ({message_count} messages)"
            )

        except Exception as e:
            print(f"❌ Error testing {test_case['type']} coach: {e}")


async def main():
    """Run all streaming API tests."""
    print("🚀 Testing Streaming Coaching API")
    print("=" * 80)

    # Test individual components
    await test_health_check()
    await test_available_coaches()
    await test_request_validation()

    # Test streaming functionality
    await test_streaming_endpoint_direct()
    await test_multiple_coach_types()

    print("\n" + "=" * 80)
    print("✅ Streaming API Testing Complete!")
    print("\nKey Features Tested:")
    print("• Health check and service status")
    print("• Available coaches endpoint")
    print("• Request validation and error handling")
    print("• Direct streaming response generation")
    print("• Multiple coach type support")
    print("• ReAct integration with streaming")


if __name__ == "__main__":
    asyncio.run(main())
