#!/usr/bin/env python3
"""
Azure Search Retriever Integration Test

Tests the Azure Search Retriever tool against the actual Azure Search service.
This validates that the tool works end-to-end with real Azure credentials.
"""

import asyncio
import os
import re

from dotenv import load_dotenv

from athlea_langgraph.tools.external.azure_search_retriever import (
    AzureSearchRetrieverTool,
)


def extract_deployment_id_from_url(url):
    """Extract deployment ID from Azure OpenAI embedding endpoint URL.

    Matches the JavaScript implementation pattern.
    """
    if not url:
        return None

    # Match pattern: deployments/DEPLOYMENT_ID/embeddings
    match = re.search(r"/deployments/([^/]+)/embeddings", url)

    if match and match.group(1):
        return match.group(1)

    return None


async def test_azure_search_integration():
    """Test Azure Search Retriever against the real service."""
    # Load environment variables
    load_dotenv(".env.local")

    print("🔍 Azure Search Retriever Integration Test")
    print("=" * 50)

    # Check if credentials are available
    service_name = os.getenv("AZURE_SEARCH_SERVICE_NAME")
    api_key = os.getenv("AZURE_SEARCH_API_KEY")
    index_name = os.getenv("AZURE_SEARCH_INDEX_NAME", "docs-chunks-index-v2")

    if not service_name or not api_key:
        print("❌ Missing Azure Search credentials!")
        print(
            "   Please set AZURE_SEARCH_SERVICE_NAME and AZURE_SEARCH_API_KEY in .env.local"
        )
        print("   Optional: AZURE_SEARCH_INDEX_NAME (defaults to docs-chunks-index-v2)")
        return

    print(f"📡 Service: {service_name}")
    print(f"📚 Index: {index_name}")

    # Check embedding credentials (using JS pattern)
    embedding_api_key = os.getenv("EMBEDDING_API_KEY")
    embedding_endpoint = os.getenv("EMBEDDING_ENDPOINT")

    print(f"🧠 Embedding API Key: {'✅ Set' if embedding_api_key else '❌ Not set'}")
    print(
        f"🔗 Embedding Endpoint: {embedding_endpoint if embedding_endpoint else '❌ Not set'}"
    )

    if embedding_endpoint:
        deployment_id = extract_deployment_id_from_url(embedding_endpoint)
        print(f"🎯 Extracted Deployment ID: {deployment_id}")

    # Initialize the tool with the correct embedding configuration
    tool = AzureSearchRetrieverTool(
        azure_search_service_name=service_name,
        azure_search_api_key=api_key,
        default_index_name=index_name,
        azure_openai_api_key=embedding_api_key,  # Use EMBEDDING_API_KEY
        azure_openai_endpoint=embedding_endpoint,  # Use EMBEDDING_ENDPOINT
        timeout=30,
    )

    if not tool.is_initialized:
        print("❌ Tool failed to initialize!")
        return

    print("✅ Tool initialized successfully")

    # Test cases for different search types
    test_cases = [
        {
            "name": "🔍 Keyword Search - Strength Training",
            "input": {
                "query": "strength training benefits muscle",
                "top_k": 3,
                "vector_search": False,
            },
        },
        {
            "name": "🔍 Keyword Search - Running Performance",
            "input": {
                "query": "running performance endurance",
                "top_k": 3,
                "vector_search": False,
            },
        },
    ]

    # Test vector search if embedding credentials are available
    if embedding_api_key and embedding_endpoint:
        test_cases.extend(
            [
                {
                    "name": "🧠 Vector Search - Cardiovascular Health",
                    "input": {
                        "query": "cardiovascular health and running performance",
                        "top_k": 3,
                        "vector_search": True,
                        "hybrid_search": False,
                    },
                },
                {
                    "name": "🔀 Hybrid Search - HIIT Training",
                    "input": {
                        "query": "HIIT high intensity interval training",
                        "top_k": 3,
                        "vector_search": True,
                        "hybrid_search": True,
                    },
                },
            ]
        )
    else:
        print("⚠️  Vector/Hybrid search tests skipped - missing embedding credentials")
        print("   Set EMBEDDING_API_KEY and EMBEDDING_ENDPOINT for full testing")

    print(f"\n🧪 Running {len(test_cases)} test cases...")

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 40)

        try:
            result = await tool.invoke(test_case["input"])

            if result.success:
                print(f"✅ Success! Query: '{result.query}'")
                print(f"📊 Found: {result.result_count} results")
                print(f"⏱️  Time: {result.execution_time_ms}ms")
                print(f"🆔 Request ID: {result.request_id}")

                # Show first result details
                if result.results:
                    first_result = result.results[0]
                    print(f"\n📄 Top Result:")
                    print(f"   Title: {first_result.title or 'N/A'}")
                    print(f"   Score: {first_result.score}")
                    print(f"   Source: {first_result.source or 'N/A'}")
                    print(f"   Content: {first_result.content[:150]}...")

                    if first_result.highlights:
                        print(f"   Highlights: {list(first_result.highlights.keys())}")
                else:
                    print("   No results returned")

            else:
                print(f"❌ Failed: {result.error_type}")
                print(f"   Message: {result.message}")
                print(f"   Request ID: {result.request_id}")

        except Exception as e:
            print(f"💥 Exception: {e}")

    # Test error handling with invalid query
    print(f"\n{len(test_cases) + 1}. 🚫 Error Handling Test")
    print("-" * 40)

    try:
        error_result = await tool.invoke(
            {
                "query": "test query",
                "top_k": 150,
            }  # top_k above max of 100 should fail validation
        )

        if not error_result.success:
            print(f"✅ Error handling works: {error_result.error_type}")
            print(f"   Message: {error_result.message}")
        else:
            print("⚠️  Expected validation error but got success")

    except Exception as e:
        print(f"✅ Exception caught as expected: {e}")

    # Clean up
    await tool.close()
    print(f"\n🎉 Integration test completed!")
    print("   Tool successfully connects to and queries Azure Search")


async def test_tool_without_credentials():
    """Test tool behavior without credentials."""
    print(f"\n🔒 Testing tool without credentials...")

    tool = AzureSearchRetrieverTool(
        azure_search_service_name=None,
        azure_search_api_key=None,
    )

    if not tool.is_initialized:
        print("✅ Tool correctly reports as not initialized")

        result = await tool.invoke({"query": "test query", "top_k": 5})

        if not result.success and result.error_type == "configuration_error":
            print("✅ Tool correctly returns configuration error")
        else:
            print(f"⚠️  Unexpected result: {result.error_type}")
    else:
        print("⚠️  Tool reports as initialized without credentials")


if __name__ == "__main__":
    print("🧪 Azure Search Retriever - Real Service Integration Test")
    print("This test validates the tool against the actual Azure Search service")
    print("Make sure you have valid Azure Search credentials in .env.local\n")

    asyncio.run(test_azure_search_integration())
    asyncio.run(test_tool_without_credentials())
