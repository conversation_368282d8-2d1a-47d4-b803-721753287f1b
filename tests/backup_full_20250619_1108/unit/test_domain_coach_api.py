"""
Test script for the domain coach API endpoint.

This tests the /api/domain-coach endpoint that matches the Next.js frontend expectations.
"""

import asyncio
import json
import logging
import os
from datetime import datetime

import httpx

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_domain_coach_api():
    """Test the domain coach API endpoint."""
    print("🧪 Testing Domain Coach API Endpoint")
    print("=" * 40)

    # Test data matching frontend format
    test_messages = [
        {"id": "1", "role": "user", "content": "I want to start strength training"}
    ]

    # Test the API endpoint
    try:
        async with httpx.AsyncClient() as client:
            print("📤 Sending request to /api/domain-coach...")

            response = await client.post(
                "http://localhost:8000/api/domain-coach",
                json={"messages": test_messages},
                timeout=30.0,
            )

            if response.status_code == 200:
                print("✅ API endpoint responded successfully")
                print("📥 Streaming response:")
                print("-" * 30)

                # Process the streaming response
                content_chunks = []
                async for chunk in response.aiter_text():
                    if chunk.strip():
                        print(chunk.strip())

                        # Extract content from data lines
                        if chunk.startswith("data: "):
                            data = chunk[6:].strip()
                            if data == "[DONE]":
                                print("\n✅ Stream completed with [DONE] signal")
                                break
                            else:
                                content_chunks.append(data)

                print(f"\n📊 Received {len(content_chunks)} content chunks")
                if content_chunks:
                    full_response = "".join(content_chunks)
                    print(f"📝 Full response length: {len(full_response)} characters")
                    print(f"🎯 Response preview: {full_response[:100]}...")

                return True

            else:
                print(f"❌ API endpoint returned status {response.status_code}")
                print(f"Response: {response.text}")
                return False

    except httpx.ConnectError:
        print("⚠️ Could not connect to API server")
        print(
            "💡 Make sure to start the server with: poetry run python -m athlea_langgraph.api_streaming"
        )
        return False
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False


async def test_multiple_domains():
    """Test different domain queries."""
    print("\n🎯 Testing Multiple Domain Queries")
    print("=" * 40)

    test_cases = [
        {"query": "I want to improve my nutrition", "expected_domain": "nutrition"},
        {"query": "How can I build muscle strength?", "expected_domain": "strength"},
        {"query": "What's the best cardio workout?", "expected_domain": "cardio"},
    ]

    results = []

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}: {test_case['query']}")
        print("-" * 30)

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "http://localhost:8000/api/domain-coach",
                    json={
                        "messages": [
                            {
                                "id": str(i),
                                "role": "user",
                                "content": test_case["query"],
                            }
                        ]
                    },
                    timeout=30.0,
                )

                if response.status_code == 200:
                    content_chunks = []
                    async for chunk in response.aiter_text():
                        if chunk.startswith("data: "):
                            data = chunk[6:].strip()
                            if data == "[DONE]":
                                break
                            else:
                                content_chunks.append(data)

                    full_response = "".join(content_chunks)

                    # Simple domain detection (like frontend does)
                    detected_domain = "general"
                    if (
                        "strength" in full_response.lower()
                        or "muscle" in full_response.lower()
                    ):
                        detected_domain = "strength"
                    elif (
                        "cardio" in full_response.lower()
                        or "running" in full_response.lower()
                    ):
                        detected_domain = "cardio"
                    elif (
                        "nutrition" in full_response.lower()
                        or "diet" in full_response.lower()
                    ):
                        detected_domain = "nutrition"

                    print(f"✅ Response received ({len(full_response)} chars)")
                    print(f"🎯 Detected domain: {detected_domain}")
                    print(f"📝 Preview: {full_response[:100]}...")

                    results.append(
                        {
                            "query": test_case["query"],
                            "expected": test_case["expected_domain"],
                            "detected": detected_domain,
                            "success": True,
                        }
                    )

                else:
                    print(f"❌ Failed with status {response.status_code}")
                    results.append(
                        {
                            "query": test_case["query"],
                            "expected": test_case["expected_domain"],
                            "detected": None,
                            "success": False,
                        }
                    )

        except Exception as e:
            print(f"❌ Error: {e}")
            results.append(
                {
                    "query": test_case["query"],
                    "expected": test_case["expected_domain"],
                    "detected": None,
                    "success": False,
                }
            )

    # Summary
    print("\n📊 Domain Detection Summary")
    print("=" * 40)

    successful = 0
    for result in results:
        status = "✅" if result["success"] else "❌"
        domain_match = "🎯" if result["detected"] == result["expected"] else "⚠️"
        print(
            f"{status} {domain_match} {result['query'][:30]}... → {result['detected']}"
        )
        if result["success"]:
            successful += 1

    print(f"\nTotal: {successful}/{len(results)} tests successful")
    return successful == len(results)


async def main():
    """Run all domain coach API tests."""
    print("🚀 Domain Coach API Test Suite")
    print("=" * 50)

    # Check if MongoDB URI is set
    mongodb_uri = os.getenv("MONGODB_URI")
    if not mongodb_uri:
        print("❌ MONGODB_URI environment variable not set")
        return

    print(f"🔗 MongoDB URI: {mongodb_uri[:50]}...")

    # Run tests
    test1_result = await test_domain_coach_api()
    test2_result = await test_multiple_domains()

    # Final summary
    print("\n🏁 Final Results")
    print("=" * 30)

    if test1_result and test2_result:
        print("🎉 All tests passed! The domain coach API is working correctly.")
        print("\n💡 Next steps:")
        print(
            "1. Start the API server: poetry run python -m athlea_langgraph.api_streaming"
        )
        print("2. Update the Next.js frontend to use the correct endpoint")
        print("3. Test the full integration")
    else:
        print("⚠️ Some tests failed. Check the logs above for details.")


if __name__ == "__main__":
    asyncio.run(main())
