#!/usr/bin/env python3
"""
Simple Test for Comprehensive Coaching Graph

Debug the graph step by step to identify issues.
"""

import asyncio
import logging

from langchain_core.messages import HumanMessage

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

from athlea_langgraph.graphs.archived.comprehensive_coaching_graph import (
    create_comprehensive_coaching_graph,
)


async def test_simple_strength_request():
    """Test a simple strength training request."""
    print("🧪 Testing Simple Strength Request")
    print("=" * 50)

    # Create graph with minimal config
    config = {
        "user_id": "test_user",
        "enable_memory": False,
        "use_react_agents": True,
        "max_iterations": 2,
        "enable_human_feedback": False,
    }

    try:
        print("1. Creating graph...")
        graph = await create_comprehensive_coaching_graph(config)
        print(f"✅ Graph created with {len(graph.nodes)} nodes")

        print("\n2. Testing simple input...")
        initial_state = {
            "messages": [HumanMessage(content="I want to build muscle")],
            "user_query": "I want to build muscle",
            "execution_steps": [],
            "active_coaches": [],
            "coach_responses": {},
            "tool_calls_made": [],
        }

        print("3. Running graph...")
        result = await graph.ainvoke(initial_state)

        print("\n4. Results:")
        print(f"   Execution steps: {result.get('execution_steps', [])}")
        print(f"   Active coaches: {result.get('active_coaches', [])}")
        print(f"   Current node: {result.get('current_node', 'None')}")
        print(f"   Routing decision: {result.get('routing_decision', 'None')}")

        # Check if we have any coach responses
        coach_responses = result.get("coach_responses", {})
        if coach_responses:
            print(f"   Coach responses: {list(coach_responses.keys())}")
            for coach, response in coach_responses.items():
                print(f"     {coach}: {response[:100]}...")
        else:
            print("   No coach responses found")

        print("\n✅ Test completed successfully!")
        return True

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


async def test_graph_nodes():
    """Test individual graph nodes."""
    print("\n🔍 Testing Individual Graph Nodes")
    print("=" * 50)

    config = {
        "user_id": "test_user",
        "enable_memory": False,
        "use_react_agents": True,
        "max_iterations": 1,
        "enable_human_feedback": False,
    }

    try:
        graph = await create_comprehensive_coaching_graph(config)

        # Test just the reasoning and planning nodes
        print("Testing reasoning -> planning flow...")

        initial_state = {
            "messages": [HumanMessage(content="I want strength training help")],
            "user_query": "I want strength training help",
            "execution_steps": [],
            "active_coaches": [],
            "coach_responses": {},
            "tool_calls_made": [],
        }

        # Use astream to see step-by-step execution
        print("\nStep-by-step execution:")
        step_count = 0
        async for step in graph.astream(initial_state):
            step_count += 1
            print(f"\nStep {step_count}:")
            for node_name, state_update in step.items():
                print(f"  Node: {node_name}")
                if isinstance(state_update, dict):
                    current_node = state_update.get("current_node", "unknown")
                    execution_steps = state_update.get("execution_steps", [])
                    print(f"    Current node: {current_node}")
                    print(f"    Execution steps: {execution_steps}")

                    # Check for errors
                    if "error" in state_update:
                        print(f"    ❌ Error: {state_update['error']}")

                    # Check for routing decision
                    if "routing_decision" in state_update:
                        print(
                            f"    Routing decision: {state_update['routing_decision']}"
                        )

            if step_count > 10:  # Safety break
                print("⚠️ Breaking after 10 steps to prevent infinite loop")
                break

        print(f"\n✅ Completed {step_count} steps")
        return True

    except Exception as e:
        print(f"\n❌ Node testing failed: {e}")
        import traceback

        traceback.print_exc()
        return False


async def main():
    """Run simple tests."""
    print("🚀 Simple Comprehensive Graph Testing")
    print("=" * 60)

    # Test 1: Basic graph creation and execution
    success1 = await test_simple_strength_request()

    # Test 2: Step-by-step node execution
    success2 = await test_graph_nodes()

    print("\n" + "=" * 60)
    print("📊 SIMPLE TEST SUMMARY")
    print("=" * 60)
    print(f"Basic execution test: {'✅ PASSED' if success1 else '❌ FAILED'}")
    print(f"Node-by-node test: {'✅ PASSED' if success2 else '❌ FAILED'}")

    if success1 and success2:
        print("\n🎉 All simple tests passed! Graph is working.")
    else:
        print("\n⚠️ Some tests failed. Need to debug further.")


if __name__ == "__main__":
    asyncio.run(main())
