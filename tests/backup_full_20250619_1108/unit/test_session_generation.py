"""
Tests for Session Generation Tool

Comprehensive test suite covering all session types, validation,
error handling, circuit breaker functionality, and integration scenarios.
"""

import asyncio
from datetime import datetime
from unittest.mock import MagicMock, patch

import pytest

from athlea_langgraph.schemas.session_generation_schemas import (
    CyclingSession,
    RecoverySession,
    RunningSession,
    SessionGenerationInput,
    SessionGenerationOutput,
    StrengthSession,
)
from athlea_langgraph.tools.external.session_generation import (
    CircuitBreaker,
    SessionGenerationTool,
)


class TestSessionGenerationTool:
    """Test suite for SessionGenerationTool."""

    @pytest.fixture
    def tool(self):
        """Create a SessionGenerationTool instance for testing."""
        return SessionGenerationTool(timeout=5, max_retries=2, base_delay=0.1)

    @pytest.fixture
    def strength_input(self):
        """Sample strength session input."""
        return {
            "command": "strength",
            "date": "2024-01-15",
            "intensity": "high",
            "duration": 60,
            "skill_category": "General Strength",
            "training_category": "Full Body",
        }

    @pytest.fixture
    def running_input(self):
        """Sample running session input."""
        return {
            "command": "running",
            "date": "2024-01-15",
            "distance": 5.0,
            "intensity": "moderate",
            "run_category": "General Aerobic",
        }

    @pytest.fixture
    def cycling_input(self):
        """Sample cycling session input."""
        return {
            "command": "cycling",
            "date": "2024-01-15",
            "duration": 45,
            "intensity": "moderate",
            "cycling_category": ["Road"],
        }

    @pytest.fixture
    def recovery_input(self):
        """Sample recovery session input."""
        return {
            "command": "recovery",
            "date": "2024-01-15",
            "duration": 30,
            "recovery_focus": "Active Recovery",
        }

    @pytest.mark.asyncio
    async def test_tool_initialization(self):
        """Test tool initialization with various parameters."""
        tool = SessionGenerationTool(timeout=10, max_retries=5, base_delay=2.0)

        assert tool.timeout == 10
        assert tool.max_retries == 5
        assert tool.base_delay == 2.0
        assert isinstance(tool.circuit_breaker, CircuitBreaker)

    @pytest.mark.asyncio
    async def test_strength_session_generation(self, tool, strength_input):
        """Test successful strength session generation."""
        result = await tool.invoke(strength_input)

        assert result.success is True
        assert result.command == "strength"
        assert result.strength_session is not None
        assert result.running_session is None
        assert result.cycling_session is None
        assert result.recovery_session is None

        session = result.strength_session
        assert isinstance(session, StrengthSession)
        assert session.duration == 60
        assert session.intensity_level == 8  # high intensity
        assert len(session.segments) == 5
        assert "S&C-" in session.id

    @pytest.mark.asyncio
    async def test_running_session_generation(self, tool, running_input):
        """Test successful running session generation."""
        result = await tool.invoke(running_input)

        assert result.success is True
        assert result.command == "running"
        assert result.running_session is not None
        assert result.strength_session is None

        session = result.running_session
        assert isinstance(session, RunningSession)
        assert session.distance == 5.0
        assert session.intensity_level == "3"  # moderate intensity
        assert len(session.segments) == 3  # warmup, main, cooldown
        assert session.session_type == "run"

    @pytest.mark.asyncio
    async def test_cycling_session_generation(self, tool, cycling_input):
        """Test successful cycling session generation."""
        result = await tool.invoke(cycling_input)

        assert result.success is True
        assert result.command == "cycling"
        assert result.cycling_session is not None
        assert result.strength_session is None

        session = result.cycling_session
        assert isinstance(session, CyclingSession)
        assert session.duration == "45"
        assert session.intensity == "moderate"
        assert len(session.segments) == 3  # warmup, main, cooldown
        assert session.session_type == "bike"

    @pytest.mark.asyncio
    async def test_recovery_session_generation(self, tool, recovery_input):
        """Test successful recovery session generation."""
        result = await tool.invoke(recovery_input)

        assert result.success is True
        assert result.command == "recovery"
        assert result.recovery_session is not None
        assert result.strength_session is None

        session = result.recovery_session
        assert isinstance(session, RecoverySession)
        assert session.duration == 30
        assert session.recovery_focus == "Active Recovery"
        assert len(session.activities) == 3  # for 30+ minute sessions
        assert session.session_type == "recovery"

    @pytest.mark.asyncio
    async def test_input_validation_errors(self, tool):
        """Test input validation error handling."""
        # Invalid date format
        invalid_input = {
            "command": "strength",
            "date": "2024/01/15",  # Wrong format
        }

        result = await tool.invoke(invalid_input)

        assert result.success is False
        assert result.error_type == "validation_error"
        assert "Date must be in YYYY-MM-DD format" in result.message

    @pytest.mark.asyncio
    async def test_invalid_command_validation(self, tool):
        """Test validation of invalid command types."""
        invalid_input = {
            "command": "invalid_command",
            "date": "2024-01-15",
        }

        result = await tool.invoke(invalid_input)

        assert result.success is False
        assert result.error_type == "validation_error"

    @pytest.mark.asyncio
    async def test_intensity_validation(self, tool):
        """Test intensity validation across different session types."""
        # Valid intensities
        for intensity in ["low", "moderate", "high"]:
            input_data = {
                "command": "strength",
                "date": "2024-01-15",
                "intensity": intensity,
            }
            result = await tool.invoke(input_data)
            assert result.success is True

        # Invalid intensity
        invalid_input = {
            "command": "strength",
            "date": "2024-01-15",
            "intensity": "invalid",
        }
        result = await tool.invoke(invalid_input)
        assert result.success is False
        assert result.error_type == "validation_error"

    @pytest.mark.asyncio
    async def test_distance_validation_running(self, tool):
        """Test distance validation for running sessions."""
        # Valid distance
        valid_input = {
            "command": "running",
            "date": "2024-01-15",
            "distance": 10.0,
        }
        result = await tool.invoke(valid_input)
        assert result.success is True
        assert result.running_session.distance == 10.0

        # Invalid distance (negative)
        invalid_input = {
            "command": "running",
            "date": "2024-01-15",
            "distance": -5.0,
        }
        result = await tool.invoke(invalid_input)
        assert result.success is False
        assert result.error_type == "validation_error"

    @pytest.mark.asyncio
    async def test_duration_validation(self, tool):
        """Test duration validation for various session types."""
        # Valid duration
        valid_input = {
            "command": "strength",
            "date": "2024-01-15",
            "duration": 90,
        }
        result = await tool.invoke(valid_input)
        assert result.success is True
        assert result.strength_session.duration == 90

        # Invalid duration (too high)
        invalid_input = {
            "command": "strength",
            "date": "2024-01-15",
            "duration": 500,  # Over max limit
        }
        result = await tool.invoke(invalid_input)
        assert result.success is False
        assert result.error_type == "validation_error"

    @pytest.mark.asyncio
    async def test_session_defaults(self, tool):
        """Test that default values are properly applied."""
        # Minimal strength input - should get defaults
        minimal_input = {
            "command": "strength",
            "date": "2024-01-15",
        }

        result = await tool.invoke(minimal_input)

        assert result.success is True
        session = result.strength_session
        assert session.duration == 60  # default
        assert session.skill_category == "General Strength"  # default
        assert session.training_category == "Full Body"  # default
        assert session.intensity_level == 8  # default high intensity

    @pytest.mark.asyncio
    async def test_running_pace_calculation(self, tool):
        """Test running session pace and duration calculations."""
        # Test different intensities affect duration
        for intensity, expected_pace in [("low", 9), ("moderate", 7), ("high", 5)]:
            input_data = {
                "command": "running",
                "date": "2024-01-15",
                "distance": 5.0,
                "intensity": intensity,
            }

            result = await tool.invoke(input_data)
            assert result.success is True

            # Check that duration reflects expected pace
            expected_minutes = int(5.0 * expected_pace)
            expected_duration = (
                f"{expected_minutes // 60:02d}:{expected_minutes % 60:02d}:00"
            )
            assert result.running_session.duration == expected_duration

    @pytest.mark.asyncio
    async def test_cycling_power_zones(self, tool):
        """Test cycling session power zone assignments."""
        for intensity in ["low", "moderate", "high"]:
            input_data = {
                "command": "cycling",
                "date": "2024-01-15",
                "intensity": intensity,
            }

            result = await tool.invoke(input_data)
            assert result.success is True

            segments = result.cycling_session.segments
            assert len(segments) == 3

            # Check power progression
            warmup_power = segments[0].intensity
            main_power = segments[1].intensity
            cooldown_power = segments[2].intensity

            assert "100-120W" in warmup_power
            assert "80-100W" in cooldown_power

            # Main power should vary by intensity
            if intensity == "high":
                assert "220-250W" in main_power
            elif intensity == "moderate":
                assert "180-210W" in main_power
            else:  # low
                assert "140-170W" in main_power

    @pytest.mark.asyncio
    async def test_recovery_activity_scaling(self, tool):
        """Test recovery session activity scaling based on duration."""
        # Short recovery session
        short_input = {
            "command": "recovery",
            "date": "2024-01-15",
            "duration": 15,
        }

        result = await tool.invoke(short_input)
        assert result.success is True
        assert (
            len(result.recovery_session.activities) == 1
        )  # Single activity for short sessions

        # Long recovery session
        long_input = {
            "command": "recovery",
            "date": "2024-01-15",
            "duration": 60,
        }

        result = await tool.invoke(long_input)
        assert result.success is True
        assert (
            len(result.recovery_session.activities) == 3
        )  # Multiple activities for longer sessions

    @pytest.mark.asyncio
    async def test_session_id_generation(self, tool):
        """Test unique session ID generation."""
        input_data = {
            "command": "strength",
            "date": "2024-01-15",
        }

        # Generate multiple sessions
        results = []
        for _ in range(3):
            result = await tool.invoke(input_data)
            assert result.success is True
            results.append(result.strength_session.id)

        # All IDs should be unique
        assert len(set(results)) == 3

        # All should follow expected format
        for session_id in results:
            assert session_id.startswith("S&C-20240115-")

    @pytest.mark.asyncio
    async def test_segment_ordering(self, tool):
        """Test that session segments are properly ordered."""
        input_data = {
            "command": "strength",
            "date": "2024-01-15",
        }

        result = await tool.invoke(input_data)
        assert result.success is True

        segments = result.strength_session.segments
        for i, segment in enumerate(segments):
            assert segment.segment_order == i + 1
            assert segment.session_id == result.strength_session.id

    @pytest.mark.asyncio
    async def test_execution_time_tracking(self, tool):
        """Test that execution time is properly tracked."""
        input_data = {
            "command": "strength",
            "date": "2024-01-15",
        }

        result = await tool.invoke(input_data)

        assert result.success is True
        assert result.execution_time_ms > 0
        assert isinstance(result.execution_time_ms, int)

    @pytest.mark.asyncio
    async def test_request_id_generation(self, tool):
        """Test that unique request IDs are generated."""
        input_data = {
            "command": "strength",
            "date": "2024-01-15",
        }

        # Generate multiple requests
        results = []
        for _ in range(3):
            result = await tool.invoke(input_data)
            results.append(result.request_id)

        # All request IDs should be unique
        assert len(set(results)) == 3

        # All should be 8-character strings
        for request_id in results:
            assert len(request_id) == 8
            assert isinstance(request_id, str)


class TestCircuitBreaker:
    """Test suite for CircuitBreaker functionality."""

    def test_circuit_breaker_initialization(self):
        """Test circuit breaker initialization."""
        cb = CircuitBreaker(failure_threshold=3, recovery_timeout=30)

        assert cb.failure_threshold == 3
        assert cb.recovery_timeout == 30
        assert cb.failure_count == 0
        assert cb.state == "CLOSED"
        assert cb.can_execute() is True

    def test_circuit_breaker_closed_state(self):
        """Test circuit breaker in CLOSED state."""
        cb = CircuitBreaker()

        # Initially closed, should allow execution
        assert cb.state == "CLOSED"
        assert cb.can_execute() is True

        # Record some failures (under threshold)
        for _ in range(4):  # threshold is 5
            cb.record_failure()

        assert cb.state == "CLOSED"
        assert cb.can_execute() is True

    def test_circuit_breaker_open_state(self):
        """Test circuit breaker opening after threshold failures."""
        cb = CircuitBreaker(failure_threshold=3, recovery_timeout=60)

        # Record failures to trigger opening
        for _ in range(3):
            cb.record_failure()

        assert cb.state == "OPEN"
        assert cb.can_execute() is False

    def test_circuit_breaker_recovery(self):
        """Test circuit breaker recovery to HALF_OPEN state."""
        cb = CircuitBreaker(failure_threshold=3, recovery_timeout=0.1)

        # Open the circuit
        for _ in range(3):
            cb.record_failure()

        assert cb.state == "OPEN"
        assert cb.can_execute() is False

        # Wait for recovery timeout
        import time

        time.sleep(0.2)

        # Should transition to HALF_OPEN
        assert cb.can_execute() is True

    def test_circuit_breaker_success_reset(self):
        """Test circuit breaker reset after successful execution."""
        cb = CircuitBreaker(failure_threshold=3)

        # Record some failures
        for _ in range(2):
            cb.record_failure()

        assert cb.failure_count == 2

        # Record success
        cb.record_success()

        assert cb.failure_count == 0
        assert cb.state == "CLOSED"

    @pytest.mark.asyncio
    async def test_circuit_breaker_integration(self):
        """Test circuit breaker integration with session generation."""
        tool = SessionGenerationTool(timeout=1, max_retries=0)

        # Force circuit breaker to open by triggering failures
        for _ in range(6):  # Above default threshold of 5
            tool.circuit_breaker.record_failure()

        input_data = {
            "command": "strength",
            "date": "2024-01-15",
        }

        result = await tool.invoke(input_data)

        assert result.success is False
        assert result.error_type == "circuit_breaker_open"
        assert result.fallback_response is not None
        assert result.fallback_response["session_type"] == "strength"


class TestSessionGenerationEdgeCases:
    """Test edge cases and error scenarios."""

    @pytest.mark.asyncio
    async def test_missing_required_fields(self):
        """Test handling of missing required fields."""
        tool = SessionGenerationTool()

        # Missing command
        invalid_input = {
            "date": "2024-01-15",
        }

        result = await tool.invoke(invalid_input)
        assert result.success is False
        assert result.error_type == "validation_error"

    @pytest.mark.asyncio
    async def test_empty_input(self):
        """Test handling of completely empty input."""
        tool = SessionGenerationTool()

        result = await tool.invoke({})

        assert result.success is False
        assert result.error_type == "validation_error"

    @pytest.mark.asyncio
    async def test_future_date_handling(self):
        """Test handling of future dates."""
        tool = SessionGenerationTool()

        # Future date should be valid
        future_input = {
            "command": "strength",
            "date": "2030-12-31",
        }

        result = await tool.invoke(future_input)
        assert result.success is True

    @pytest.mark.asyncio
    async def test_boundary_values(self):
        """Test boundary values for various parameters."""
        tool = SessionGenerationTool()

        # Minimum duration
        min_input = {
            "command": "strength",
            "date": "2024-01-15",
            "duration": 1,
        }

        result = await tool.invoke(min_input)
        assert result.success is True
        assert result.strength_session.duration == 1

        # Maximum duration (should be within limit)
        max_input = {
            "command": "strength",
            "date": "2024-01-15",
            "duration": 300,
        }

        result = await tool.invoke(max_input)
        assert result.success is True
        assert result.strength_session.duration == 300

    @pytest.mark.asyncio
    async def test_cycling_category_handling(self):
        """Test cycling category list handling."""
        tool = SessionGenerationTool()

        # Multiple categories
        input_data = {
            "command": "cycling",
            "date": "2024-01-15",
            "cycling_category": ["Road", "MTB", "Criterium"],
        }

        result = await tool.invoke(input_data)
        assert result.success is True
        assert "Road, MTB, Criterium" in result.cycling_session.cycling_category

    @pytest.mark.asyncio
    async def test_fallback_response_structure(self):
        """Test structure of fallback responses."""
        tool = SessionGenerationTool()

        # Force circuit breaker open
        for _ in range(6):
            tool.circuit_breaker.record_failure()

        input_data = {
            "command": "running",
            "date": "2024-01-15",
            "distance": 10.0,
        }

        result = await tool.invoke(input_data)

        assert result.success is False
        assert result.fallback_response is not None

        fallback = result.fallback_response
        assert fallback["session_type"] == "running"
        assert fallback["date"] == "2024-01-15"
        assert "segments" in fallback
        assert len(fallback["segments"]) == 1


if __name__ == "__main__":
    pytest.main([__file__])
