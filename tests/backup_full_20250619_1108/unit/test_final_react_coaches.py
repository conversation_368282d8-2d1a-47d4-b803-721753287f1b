#!/usr/bin/env python3
"""
Test Final ReAct Specialized Coaches

This script tests the final corrected ReAct implementation that properly handles
the Azure OpenAI GPT-4.1 model response format and tool call ID matching.
"""

import asyncio
import logging
from typing import Any, Dict, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_final_react_coaches():
    """Test the final ReAct specialized coaches implementation."""
    print("🧪 Testing Final ReAct Specialized Coaches")
    print("=" * 60)

    try:
        # Import the final implementation
        from langchain_core.messages import HumanMessage

        from athlea_langgraph.agents.specialized_coaches_react_final import (
            get_final_specialized_coach,
            list_final_available_coaches,
        )
        from athlea_langgraph.state import AgentState

        # Test scenarios
        test_scenarios = [
            {
                "coach": "strength_coach",
                "query": "What is RPE and how should I use it in my strength training?",
                "description": "Should search for RPE information first, then provide explanation",
            },
            {
                "coach": "strength_coach",
                "query": "Find me deadlift exercises in your database",
                "description": "Should search Airtable for deadlift exercises",
            },
            {
                "coach": "strength_coach",
                "query": "Create a beginner strength training workout for me",
                "description": "Should generate a new strength training session",
            },
        ]

        # List available coaches
        coaches = await list_final_available_coaches()
        print(f"📋 Available coaches: {coaches}")
        print()

        # Test each scenario
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"📋 Scenario {i}/{len(test_scenarios)}")
            print(f"🎯 Testing Final ReAct {scenario['coach']}")
            print(f"Query: {scenario['query']}")
            print(f"Expected: {scenario['description']}")
            print("-" * 80)

            try:
                # Get the coach
                coach = await get_final_specialized_coach(scenario["coach"])
                if not coach:
                    print(f"❌ Coach {scenario['coach']} not available")
                    continue

                # Create state with user query
                state = AgentState(
                    messages=[HumanMessage(content=scenario["query"])],
                    user_query=scenario["query"],
                )

                # Invoke the coach
                print("🚀 Invoking final ReAct coach...")
                result = await coach.invoke(state)

                # Display results
                messages = result.get("messages", [])
                print(f"✅ Completed - got {len(messages)} messages")

                # Show the conversation flow
                for j, msg in enumerate(messages):
                    msg_type = type(msg).__name__
                    content_preview = (
                        str(msg.content)[:150] + "..."
                        if len(str(msg.content)) > 150
                        else str(msg.content)
                    )

                    if hasattr(msg, "tool_calls") and msg.tool_calls:
                        print(
                            f"  Message {j+1}: {msg_type} - Tool calls: {[tc['name'] for tc in msg.tool_calls]}"
                        )
                    elif hasattr(msg, "tool_call_id"):
                        print(
                            f"  Message {j+1}: {msg_type} - Tool response (ID: {msg.tool_call_id})"
                        )
                    else:
                        print(f"  Message {j+1}: {msg_type} - {content_preview}")

                # Show final response
                final_message = messages[-1] if messages else None
                if (
                    final_message
                    and hasattr(final_message, "content")
                    and final_message.content
                ):
                    print(f"\n💬 Final Response:")
                    print(f"   {final_message.content[:300]}...")

                print("✅ Test completed successfully!")

            except Exception as e:
                print(f"❌ Error in scenario {i}: {e}")
                import traceback

                traceback.print_exc()

            print("\n" + "=" * 80 + "\n")

        print("🎉 Final ReAct testing completed!")

    except Exception as e:
        print(f"❌ Failed to test final ReAct coaches: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_final_react_coaches())
