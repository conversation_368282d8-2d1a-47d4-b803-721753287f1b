#!/usr/bin/env python3
"""
Test ReAct Implementation

This script tests the new ReAct pattern implementation for specialized coaches
to verify that tool execution works correctly with the Thought→Action→Observation cycle.
"""

import asyncio
import logging

from langchain_core.messages import HumanMessage

from athlea_langgraph.agents.react_coach_node import create_react_coach_node
from athlea_langgraph.agents.coach_manager import (
    NUTRITION_COACH_PROMPT,
    STRENGTH_COACH_PROMPT,
    get_tools_manager,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_react_strength_coach():
    """Test ReAct pattern with strength coach."""
    print("🏋️‍♂️ Testing ReAct Strength Coach")
    print("=" * 60)

    try:
        # Get tools manager
        tools_manager = await get_tools_manager()
        strength_tools = tools_manager.get_strength_coach_tools()

        print(f"✓ Strength coach has {len(strength_tools)} tools available")

        # Create ReAct-enabled strength coach
        strength_coach_node = await create_react_coach_node(
            coach_name="strength_coach",
            coach_prompt=STRENGTH_COACH_PROMPT,
            tools=strength_tools,
            max_iterations=5,  # Limit iterations for testing
        )

        # Test state
        test_state = {
            "messages": [
                HumanMessage(
                    content="I want to build muscle and get stronger. Can you create a strength training plan for me? I'm a beginner and can only workout 3 days per week."
                )
            ],
            "user_profile": {
                "name": "Test User",
                "fitness_level": "beginner",
                "goals": ["build muscle", "get stronger"],
                "available_days": 3,
            },
        }

        # Execute ReAct coach
        print("\n🤖 Executing ReAct Strength Coach...")
        result = await strength_coach_node(test_state)

        # Display results
        if "messages" in result and result["messages"]:
            response = result["messages"][0]
            print(f"\n✅ Coach Response:")
            print(f"Coach: {response.content}")
            print(f"Response length: {len(response.content)} characters")

            # Check if response shows evidence of tool usage
            if len(response.content) > 500:  # Detailed response suggests tool usage
                print("✅ Response appears comprehensive (likely used tools)")
            else:
                print("⚠️ Response seems brief (may not have used tools)")

        else:
            print("❌ No response received")

    except Exception as e:
        print(f"❌ Error testing ReAct strength coach: {e}")
        logger.exception("Error in ReAct strength coach test")


async def test_react_nutrition_coach():
    """Test ReAct pattern with nutrition coach."""
    print("\n🥗 Testing ReAct Nutrition Coach")
    print("=" * 60)

    try:
        # Get tools manager
        tools_manager = await get_tools_manager()
        nutrition_tools = tools_manager.get_nutrition_coach_tools()

        print(f"✓ Nutrition coach has {len(nutrition_tools)} tools available")

        # Create ReAct-enabled nutrition coach
        nutrition_coach_node = await create_react_coach_node(
            coach_name="nutrition_coach",
            coach_prompt=NUTRITION_COACH_PROMPT,
            tools=nutrition_tools,
            max_iterations=5,
        )

        # Test state
        test_state = {
            "messages": [
                HumanMessage(
                    content="I'm vegetarian and want to optimize my nutrition for muscle building. What should I eat before and after workouts?"
                )
            ],
            "user_profile": {
                "name": "Test User",
                "dietary_restrictions": ["vegetarian"],
                "goals": ["muscle building"],
                "fitness_level": "intermediate",
            },
        }

        # Execute ReAct coach
        print("\n🤖 Executing ReAct Nutrition Coach...")
        result = await nutrition_coach_node(test_state)

        # Display results
        if "messages" in result and result["messages"]:
            response = result["messages"][0]
            print(f"\n✅ Coach Response:")
            print(f"Coach: {response.content}")
            print(f"Response length: {len(response.content)} characters")

            # Check for nutrition-specific content
            nutrition_keywords = [
                "protein",
                "carbohydrate",
                "vegetarian",
                "muscle",
                "workout",
                "meal",
            ]
            found_keywords = [
                kw
                for kw in nutrition_keywords
                if kw.lower() in response.content.lower()
            ]
            print(f"✅ Found nutrition keywords: {found_keywords}")

        else:
            print("❌ No response received")

    except Exception as e:
        print(f"❌ Error testing ReAct nutrition coach: {e}")
        logger.exception("Error in ReAct nutrition coach test")


async def test_react_pattern_structure():
    """Test that ReAct pattern follows the correct structure."""
    print("\n🔍 Testing ReAct Pattern Structure")
    print("=" * 60)

    try:
        # Get tools manager
        tools_manager = await get_tools_manager()
        strength_tools = tools_manager.get_strength_coach_tools()

        # Create ReAct executor directly for detailed testing
        from athlea_langgraph.agents.react_coach_node import ReActCoachExecutor

        executor = ReActCoachExecutor(
            coach_name="test_coach",
            coach_prompt=STRENGTH_COACH_PROMPT,
            tools=strength_tools,
            max_iterations=3,  # Limited for testing
        )

        # Test prompt creation
        react_prompt = executor._create_react_prompt(
            user_profile={"fitness_level": "beginner"}
        )

        print("✅ ReAct prompt structure:")
        print("- Contains 'Thought:' instruction")
        print("- Contains 'Action:' instruction")
        print("- Contains 'Observation:' instruction")
        print("- Contains 'Final Answer:' instruction")
        print("- Lists available tools")

        # Verify prompt contains key elements
        required_elements = [
            "Thought:",
            "Action:",
            "Action Input:",
            "Observation:",
            "Final Answer:",
            "AVAILABLE TOOLS:",
        ]

        for element in required_elements:
            if element in react_prompt:
                print(f"  ✅ {element}")
            else:
                print(f"  ❌ Missing: {element}")

        # Test action parsing
        test_response = """
        Thought: I need to create a strength training plan for this user.
        Action: session_generation
        Action Input: {"command": "strength", "duration_minutes": 60, "experience_level": "beginner"}
        """

        action_result = executor._parse_action(test_response)
        if action_result:
            action_name, action_input = action_result
            print(f"\n✅ Action parsing works:")
            print(f"  Action: {action_name}")
            print(f"  Input: {action_input}")
        else:
            print("\n❌ Action parsing failed")

        # Test final answer detection
        final_response = """
        Thought: I now have all the information needed to provide a comprehensive answer
        Final Answer: Here's your personalized strength training plan...
        """

        if executor._is_final_answer(final_response):
            final_answer = executor._extract_final_answer(final_response)
            print(f"\n✅ Final answer detection works:")
            print(f"  Answer: {final_answer[:50]}...")
        else:
            print("\n❌ Final answer detection failed")

    except Exception as e:
        print(f"❌ Error testing ReAct pattern structure: {e}")
        logger.exception("Error in ReAct pattern structure test")


async def main():
    """Run all ReAct implementation tests."""
    print("🚀 Testing ReAct Implementation for Specialized Coaches")
    print("=" * 80)

    # Test ReAct pattern structure
    await test_react_pattern_structure()

    # Test individual coaches with ReAct
    await test_react_strength_coach()
    await test_react_nutrition_coach()

    print("\n" + "=" * 80)
    print("✅ ReAct Implementation Testing Complete!")
    print("\nKey Features Tested:")
    print("• ReAct prompt structure with Thought→Action→Observation format")
    print("• Action parsing and tool execution")
    print("• Final answer detection and extraction")
    print("• Integration with specialized coach tools")
    print("• Proper error handling and iteration limits")


if __name__ == "__main__":
    asyncio.run(main())
