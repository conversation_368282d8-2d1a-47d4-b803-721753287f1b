#!/usr/bin/env python3
"""
Test script for the new athlea-redis-standard cache
"""
import asyncio
import sys

sys.path.append(".")
from athlea_langgraph.utils.redis_client import get_redis_client


async def test_redis_comprehensive():
    print("🧪 Testing athlea-redis-standard cache...")

    client = await get_redis_client()

    try:
        # Health check
        health = await client.health_check()
        print(f"\n📊 Redis Health Check:")
        print(f'   Status: {health["status"]}')
        print(f'   Ping Latency: {health.get("ping_latency_ms", 0):.2f}ms')
        print(f'   Connected: {health.get("connected", False)}')

        # Test basic operations
        print(f"\n🧪 Testing basic operations...")

        # Set/Get test
        await client.set("test_key", "Hello from athlea-redis-standard! 🎉", ex=300)
        value = await client.get("test_key")
        print(
            f'   Set/Get test: {"✅ SUCCESS" if value else "❌ FAILED"} - Value: {value}'
        )

        # TTL test
        ttl = await client.ttl("test_key")
        print(f'   TTL test: {"✅ SUCCESS" if ttl > 0 else "❌ FAILED"} - TTL: {ttl}s')

        # Multiple operations test
        test_data = {
            "user:123": "John Doe",
            "cache:test": "performance data",
            "session:abc": "active",
        }

        result = await client.mset(test_data)
        print(f'   Multi-set test: {"✅ SUCCESS" if result else "❌ FAILED"}')

        retrieved = await client.mget(list(test_data.keys()))
        print(f'   Multi-get test: {"✅ SUCCESS" if all(retrieved) else "❌ FAILED"}')

        # Cleanup
        deleted = await client.delete(*test_data.keys(), "test_key")
        print(
            f'   Cleanup: {"✅ SUCCESS" if deleted > 0 else "❌ FAILED"} - Deleted {deleted} keys'
        )

        # Get stats
        stats = await client.get_stats()
        print(f"\n📈 Redis Statistics:")
        print(f'   Successful operations: {stats["successful_operations"]}')
        print(f'   Failed operations: {stats["failed_operations"]}')
        print(f'   Error rate: {stats["error_rate"]:.2%}')
        print(f'   Host: {stats["config"]["host"]}')

        print(f"\n🎉 All tests completed successfully!")

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
    finally:
        await client.close()


if __name__ == "__main__":
    asyncio.run(test_redis_comprehensive())
