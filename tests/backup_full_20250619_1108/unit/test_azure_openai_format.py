#!/usr/bin/env python3
"""
Test Azure OpenAI GPT-4.1 Response Format

This script tests the exact format of Azure OpenAI GPT-4.1 model responses
to understand how tool calls are structured in the response.
"""

import asyncio
import json

from langchain_core.messages import HumanMessage, SystemMessage

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.tools.external.airtable_mcp import get_airtable_langchain_tool


async def test_azure_openai_format():
    """Test the exact format of Azure OpenAI responses."""
    print("🔍 Testing Azure OpenAI GPT-4.1 Response Format")
    print("=" * 60)

    try:
        # Create LLM with tools
        llm = create_azure_chat_openai(temperature=0.7, max_tokens=1000)

        # Get a tool to bind
        airtable_tool = get_airtable_langchain_tool()
        llm_with_tools = llm.bind_tools([airtable_tool])

        # Test message that should trigger a tool call
        messages = [
            SystemMessage(content="You are a helpful assistant with access to tools."),
            HumanMessage(content="Search for deadlift exercises in the database"),
        ]

        print("🚀 Calling Azure OpenAI with tool binding...")
        response = await llm_with_tools.ainvoke(messages)

        print(f"✅ Response received: {type(response)}")
        print(f"📝 Response content: {response.content}")

        # Examine the response structure
        print("\n🔍 Response Structure Analysis:")
        print(f"  - Type: {type(response)}")
        print(f"  - Has content: {hasattr(response, 'content')}")
        print(f"  - Has tool_calls: {hasattr(response, 'tool_calls')}")
        print(f"  - Has additional_kwargs: {hasattr(response, 'additional_kwargs')}")

        if hasattr(response, "tool_calls"):
            print(f"  - tool_calls value: {response.tool_calls}")
            print(f"  - tool_calls type: {type(response.tool_calls)}")

        if hasattr(response, "additional_kwargs"):
            print(f"  - additional_kwargs: {response.additional_kwargs}")
            if "tool_calls" in response.additional_kwargs:
                print(
                    f"  - additional_kwargs.tool_calls: {response.additional_kwargs['tool_calls']}"
                )

        # Check all attributes
        print("\n📋 All Response Attributes:")
        for attr in dir(response):
            if not attr.startswith("_"):
                try:
                    value = getattr(response, attr)
                    if not callable(value):
                        print(f"  - {attr}: {type(value)} = {str(value)[:100]}...")
                except:
                    print(f"  - {attr}: <unable to access>")

        # Test without tools to compare
        print("\n🔄 Testing without tools for comparison...")
        llm_no_tools = create_azure_chat_openai(temperature=0.7, max_tokens=1000)
        response_no_tools = await llm_no_tools.ainvoke(messages)

        print(f"✅ No-tools response: {type(response_no_tools)}")
        print(f"📝 No-tools content: {response_no_tools.content[:200]}...")

        # Compare structures
        print("\n🔍 Structure Comparison:")
        print(
            f"  With tools - tool_calls: {hasattr(response, 'tool_calls') and response.tool_calls}"
        )
        print(
            f"  Without tools - tool_calls: {hasattr(response_no_tools, 'tool_calls') and response_no_tools.tool_calls}"
        )

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_azure_openai_format())
