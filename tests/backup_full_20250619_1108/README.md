# Athlea LangGraph Test Suite

This directory contains the comprehensive test suite for the Athlea LangGraph coaching system.

## 📁 **Test Structure**

```
tests/
├── README.md                    # This file
├── conftest.py                  # Pytest configuration and fixtures
├── __init__.py                  # Test package initialization
├── unit/                        # Unit tests for individual components
├── integration/                 # Integration tests for system workflows
├── e2e/                        # End-to-end tests for complete user journeys
├── tools/                      # Tool-specific tests
├── agents/                     # Agent-specific tests
├── routing/                    # Routing logic tests
├── streaming/                  # Streaming API tests
├── graphrag/                   # GraphRAG integration tests
├── n8n_integration/            # N8N workflow tests
├── hallucination_mitigation/   # Hallucination prevention tests
├── prompts/                    # Prompt system tests
├── synthetic/                  # Synthetic data generation tests
├── fixtures/                   # Test data and fixtures
└── runners/                    # Test execution utilities
```

## 🧪 **Test Categories**

### **Unit Tests** (`unit/`)
Tests for individual components in isolation:
- Individual coach implementations
- Tool functionality
- State management
- Configuration handling
- Redis caching
- Architecture components

### **Integration Tests** (`integration/`)
Tests for component interactions and workflows:
- Multi-coach coordination
- Memory integration
- API endpoints
- Streaming functionality
- Tool integration
- Graph optimization
- Frontend format compatibility

### **End-to-End Tests** (`e2e/`)
Complete user journey tests:
- Full coaching conversations
- Onboarding workflows
- Multi-session interactions

### **Specialized Tests**
- **Tools**: Individual tool testing and validation
- **Agents**: Agent behavior and ReAct implementation
- **Routing**: Intelligence Hub and routing logic
- **Streaming**: Real-time API functionality
- **GraphRAG**: Knowledge retrieval and research
- **N8N**: Workflow automation integration

### **🎯 Automated Optimization Testing** (`integration/test_react_coaches.py`)
Advanced prompt optimization and quality assessment:
- **Function**: `test_strength_coach_accuracy_with_tracing()`
- **Metrics**: Specificity (≥0.75), Safety (≥0.90), Coverage (≥0.80)
- **Integration**: LangSmith tracing and quality assessment
- **Results**: Stored in `tests/results/` and root directory JSON files

## 🚀 **Running Tests**

### **Quick Test Run**
```bash
# Run all tests
pytest

# Run specific category
pytest tests/unit/
pytest tests/integration/
pytest tests/e2e/

# Run with coverage
pytest --cov=athlea_langgraph
```

### **Using Test Runners**
```bash
# General test runner
python tests/runners/run_tests.py

# Convenience runner for common tests
python tests/runners/run_tests_convenience.py

# Single query testing
python tests/runners/run_test_query.py
```

### **🎯 Automated Optimization Testing**
```bash
# Run complete optimization test with quality assessment
python -c "
import asyncio
from tests.integration.test_react_coaches import test_strength_coach_accuracy_with_tracing
result = asyncio.run(test_strength_coach_accuracy_with_tracing())
print(f'✅ Pass Rate: {result.get(\"pass_rate\", 0):.2f}')
print(f'📊 Composite Score: {result.get(\"organized_results\", {}).get(\"composite_score\", 0):.3f}')
"

# Check latest test results
ls -la tests/results/ | tail -1

# View baseline metrics (if available)
python -c "
import json
try:
    with open('baseline_results.json', 'r') as f:
        data = json.load(f)
    print(f'Baseline Composite Score: {data[\"metrics\"][\"composite_score\"]:.3f}')
    print(f'Optimization Priorities: {data[\"optimization_priorities\"]}')
except FileNotFoundError:
    print('No baseline results - run optimization test first')
"
```

### **Environment Setup**
Ensure you have the required environment variables:
```bash
# Required
OPENAI_API_KEY=your_key
AZURE_OPENAI_API_KEY=your_key
AZURE_OPENAI_ENDPOINT=your_endpoint

# Optional (for specific tests)
MONGODB_URI=mongodb://localhost:27017
REDIS_URL=redis://localhost:6379
PINECONE_API_KEY=your_key
LANGCHAIN_API_KEY=your_key

# Automated Optimization Testing (Additional)
LANGSMITH_API_KEY=your_langsmith_api_key
LANGSMITH_TRACING=true
LANGCHAIN_TRACING_V2=true
LANGCHAIN_PROJECT=athlea-coaching-dev
```

## 📊 **Test Configuration**

### **Pytest Configuration** (`conftest.py`)
- Async test support
- Common fixtures
- Test environment setup
- Mock configurations

### **Test Fixtures** (`fixtures/`)
- User profiles for testing
- Sample conversation data
- Mock responses
- Test configurations

## 🔧 **Writing New Tests**

### **Unit Test Example**
```python
import pytest
from athlea_langgraph.agents.strength_agent import StrengthAgent

@pytest.mark.asyncio
async def test_strength_agent_initialization():
    agent = StrengthAgent()
    assert agent.name == "strength_coach"
    assert len(agent.tools) > 0
```

### **Integration Test Example**
```python
import pytest
from athlea_langgraph.graphs.optimized_comprehensive_coaching_graph import create_optimized_coaching_graph

@pytest.mark.asyncio
async def test_coaching_workflow():
    graph = await create_optimized_coaching_graph()
    result = await graph.ainvoke({
        "user_query": "How do I do squats?",
        "messages": []
    })
    assert result["final_response"]
    assert "squat" in result["final_response"].lower()
```

## 📋 **Test Standards**

### **Naming Convention**
- Test files: `test_*.py`
- Test functions: `test_*`
- Test classes: `Test*`

### **Test Structure**
- **Arrange**: Set up test data and conditions
- **Act**: Execute the functionality being tested
- **Assert**: Verify the expected outcomes

### **Async Testing**
Use `@pytest.mark.asyncio` for async tests:
```python
@pytest.mark.asyncio
async def test_async_function():
    result = await some_async_function()
    assert result is not None
```

## 🚨 **Common Issues**

### **Environment Variables**
- Ensure all required API keys are set
- Use `.env.local` for local development
- Check `conftest.py` for test-specific configurations

### **Async Tests**
- Always use `@pytest.mark.asyncio` for async tests
- Ensure proper cleanup of async resources
- Use `pytest-asyncio` plugin

### **Memory Tests**
- Some tests require MongoDB or Redis
- Use mocks when external services aren't available
- Clean up test data after tests

## 📈 **Test Metrics**

### **Coverage Goals**
- Unit tests: >90% coverage
- Integration tests: >80% coverage
- Critical paths: 100% coverage

### **Performance Benchmarks**
- Unit tests: <1s per test
- Integration tests: <10s per test
- E2E tests: <30s per test

## 🔄 **Continuous Integration**

Tests are automatically run on:
- Pull requests
- Main branch commits
- Release candidates

### **CI Configuration**
- GitHub Actions workflow
- Multiple Python versions
- Different environment configurations
- Coverage reporting

## � **Documentation Links**

- **[General Testing Guide](../docs/04_developer_guides/03_testing_guide.md)** - Unit, integration, E2E testing
- **[Automated Optimization Guide](../docs/04_developer_guides/04_automated_optimization_guide.md)** - Specialized optimization workflows
- **[LangSmith Portal](https://smith.langchain.com/)** - Trace monitoring (use `athlea-coaching-dev` project, "Last 6 hours" filter)

## 🎯 **Key Files for Optimization Testing**

| File | Purpose | Location |
|------|---------|----------|
| `test_react_coaches.py` | Core optimization testing | `tests/integration/` |
| `baseline_results.json` | Latest baseline metrics | Root directory |
| `optimization_demonstration_results.json` | Optimization analysis | Root directory |
| Test result files | Historical test data | `tests/results/` |

## �📞 **Support**

- **Issues**: Create GitHub issues for test failures
- **Questions**: Use team communication channels
- **Contributions**: Follow the test standards above
- **Optimization Issues**: See [Automated Optimization Guide](../docs/04_developer_guides/04_automated_optimization_guide.md) troubleshooting section

---

**Last Updated**: June 19, 2025
**Test Framework**: pytest + pytest-asyncio
**Coverage Tool**: pytest-cov
**Optimization System**: Automated Evidence-Based Deployment v1.0