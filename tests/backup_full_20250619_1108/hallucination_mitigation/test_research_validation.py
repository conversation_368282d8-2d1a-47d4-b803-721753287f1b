"""
Comprehensive tests for hallucination mitigation and research validation.

Tests the complete pipeline from detection to knowledge graph integration.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from langchain_core.messages import HumanMessage, AIMessage

from athlea_langgraph.services.research_validation_service import (
    ResearchValidationService,
    ResearchClaim,
    ValidationResult
)
from athlea_langgraph.agents.research_validation_node import research_validation_node


class TestResearchValidationService:
    """Test the research validation service detection capabilities."""
    
    def setup_method(self):
        """Setup test environment."""
        self.service = ResearchValidationService()
    
    def test_detect_fake_citations(self):
        """Test detection of fake citations and references."""
        fake_response = """
        Studies show that consuming protein within 30 minutes post-workout maximizes 
        muscle protein synthesis (<PERSON> et al., 2023). According to research by 
        Harvard University, this effect is enhanced by 40% when combined with carbohydrates.
        """
        
        claims = self.service.detect_research_claims(fake_response)
        
        assert len(claims) >= 2
        assert any("Studies show" in claim.claim_text for claim in claims)
        assert any("According to research" in claim.claim_text for claim in claims)
    
    def test_detect_high_risk_hallucination_patterns(self):
        """Test detection of high-risk hallucination patterns."""
        high_risk_response = """
        For more details, click here to read the full study. 
        See reference #5 in the appendix for complete methodology.
        Download the complete research paper for detailed results.
        """
        
        claims = self.service.detect_research_claims(high_risk_response)
        
        # Should detect high-risk patterns
        high_risk_claims = [c for c in claims if c.claim_type == "high_risk_hallucination"]
        assert len(high_risk_claims) >= 2
    
    def test_statistical_claims_detection(self):
        """Test detection of statistical claims."""
        stats_response = """
        Research indicates a 35% improvement in performance.
        Athletes showed statistically significant gains in VO2 max.
        There's a strong correlation between sleep quality and recovery.
        """
        
        claims = self.service.detect_research_claims(stats_response)
        
        assert len(claims) >= 3
        assert any("35%" in claim.claim_text for claim in claims)
        assert any("statistically significant" in claim.claim_text for claim in claims)
    
    def test_safe_response_no_detection(self):
        """Test that safe responses don't trigger false positives."""
        safe_response = """
        Here's a practical approach to improving your nutrition:
        
        1. Focus on whole foods
        2. Eat adequate protein (aim for 0.8-1g per kg bodyweight)
        3. Stay hydrated throughout the day
        4. Time your meals around your workouts
        
        These are established principles that can help you reach your goals.
        """
        
        claims = self.service.detect_research_claims(safe_response)
        assert len(claims) == 0
    
    def test_validation_result_risk_assessment(self):
        """Test risk level assessment in validation results."""
        # High risk response with fake links
        high_risk = "Click here for the full study: www.fake-study.com"
        result = self.service.validate_response(high_risk, "What does research say?")
        
        assert result.has_research_claims
        assert result.risk_level == "high"
        assert result.should_query_knowledge_graph
        
        # Medium risk response with multiple claims
        medium_risk = "Studies show A. Research indicates B. According to experts C."
        result = self.service.validate_response(medium_risk, "Tell me about nutrition")
        
        assert result.has_research_claims
        assert result.risk_level == "medium"
        assert result.should_query_knowledge_graph
        
        # Low risk response
        low_risk = "Research suggests that protein is important for muscle building."
        result = self.service.validate_response(low_risk, "Help with protein")
        
        assert result.has_research_claims
        assert result.risk_level == "low"


class TestResearchValidationNode:
    """Test the graph node implementation."""
    
    @pytest.mark.asyncio
    async def test_node_passes_through_safe_responses(self):
        """Test that safe responses pass through unchanged."""
        safe_state = {
            "messages": [
                HumanMessage(content="How do I build muscle?"),
                AIMessage(content="Focus on compound exercises, progressive overload, and adequate protein.")
            ]
        }
        
        result = await research_validation_node(safe_state)
        
        # Should pass through unchanged
        assert len(result["messages"]) == 2
        assert result["research_validation"]["claims_detected"] == 0
        assert not result["research_validation"]["knowledge_graph_triggered"]
    
    @pytest.mark.asyncio
    @patch('athlea_langgraph.agents.research_validation_node.create_graphrag_tool')
    async def test_node_corrects_high_risk_responses(self, mock_graphrag_tool):
        """Test that high-risk responses are corrected with knowledge graph data."""
        
        # Mock knowledge graph response
        mock_tool = AsyncMock()
        mock_result = MagicMock()
        mock_result.response = "Based on verified research, here are the key findings..."
        mock_result.acs_chunks = ["chunk1", "chunk2"]
        mock_tool._arun.return_value = mock_result
        mock_graphrag_tool.return_value = mock_tool
        
        high_risk_state = {
            "messages": [
                HumanMessage(content="What does research say about protein timing?"),
                AIMessage(content="Studies show protein timing is crucial (Smith et al., 2023). Click here for more details.")
            ]
        }
        
        result = await research_validation_node(high_risk_state)
        
        # Should replace the response
        assert len(result["messages"]) == 2
        assert result["research_validation"]["claims_detected"] > 0
        assert result["research_validation"]["knowledge_graph_triggered"]
        assert result["research_validation"]["action_taken"] == "response_corrected"
        
        # Verify the response was replaced with knowledge graph data
        corrected_response = result["messages"][-1].content
        assert "verified research" in corrected_response.lower()
        assert "Smith et al., 2023" not in corrected_response  # Fake citation removed
    
    @pytest.mark.asyncio
    @patch('athlea_langgraph.agents.research_validation_node.create_graphrag_tool')
    async def test_node_enhances_medium_risk_responses(self, mock_graphrag_tool):
        """Test that medium-risk responses are enhanced with knowledge graph data."""
        
        # Mock knowledge graph response
        mock_tool = AsyncMock()
        mock_result = MagicMock()
        mock_result.response = "Additional research context from knowledge graph..."
        mock_tool._arun.return_value = mock_result
        mock_graphrag_tool.return_value = mock_tool
        
        medium_risk_state = {
            "messages": [
                HumanMessage(content="Tell me about nutrition for athletes"),
                AIMessage(content="Research suggests that timing matters. Studies indicate protein is important. According to experts, carbs help too.")
            ]
        }
        
        result = await research_validation_node(medium_risk_state)
        
        # Should enhance the response
        assert result["research_validation"]["knowledge_graph_triggered"]
        assert result["research_validation"]["action_taken"] == "response_enhanced"
        
        # Verify the response was enhanced
        enhanced_response = result["messages"][-1].content
        assert "Additional Research Context" in enhanced_response
    
    @pytest.mark.asyncio
    async def test_node_handles_no_messages(self):
        """Test node behavior with empty messages."""
        empty_state = {"messages": []}
        
        result = await research_validation_node(empty_state)
        
        # Should return state unchanged
        assert result == empty_state
    
    @pytest.mark.asyncio
    async def test_node_skips_human_messages(self):
        """Test that node skips when latest message is not from AI."""
        human_state = {
            "messages": [
                HumanMessage(content="How do I build muscle?"),
                HumanMessage(content="Follow-up question")
            ]
        }
        
        result = await research_validation_node(human_state)
        
        # Should return state unchanged (no validation data added)
        assert result == human_state


class TestHallucinationMitigationIntegration:
    """Integration tests for the complete hallucination mitigation pipeline."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_fake_citation_prevention(self):
        """Test end-to-end prevention of fake citations."""
        
        # Simulate a user asking for research
        user_query = "What does research say about HIIT for fat loss?"
        
        # Simulate a coach response with fake citations
        fake_coach_response = """
        Recent studies show that HIIT can increase fat loss by 40% compared to steady-state cardio 
        (Johnson et al., 2024). A meta-analysis published in the Journal of Sports Science found 
        that participants following HIIT protocols experienced significant improvements 
        (DOI: 10.1234/fake.study.2024).
        
        For more details, you can read the full study here: www.fake-research-site.com/hiit-study
        """
        
        state = {
            "messages": [
                HumanMessage(content=user_query),
                AIMessage(content=fake_coach_response)
            ]
        }
        
        # Mock knowledge graph to return verified data
        with patch('athlea_langgraph.agents.research_validation_node.create_graphrag_tool') as mock_tool:
            mock_instance = AsyncMock()
            mock_result = MagicMock()
            mock_result.response = "Based on verified research from our database: HIIT training shows benefits for fat loss when compared to moderate-intensity continuous training. Key studies include [verified DOIs and citations]."
            mock_result.acs_chunks = ["verified_chunk_1", "verified_chunk_2"]
            mock_instance._arun.return_value = mock_result
            mock_tool.return_value = mock_instance
            
            # Run validation
            result = await research_validation_node(state)
            
            # Verify fake citations were caught and replaced
            final_response = result["messages"][-1].content
            
            # Should NOT contain fake elements
            assert "Johnson et al., 2024" not in final_response
            assert "fake-research-site.com" not in final_response
            assert "10.1234/fake.study.2024" not in final_response
            
            # Should contain verified research language
            assert "verified research" in final_response.lower()
            assert "research database" in final_response.lower()
            
            # Should log that intervention occurred
            assert result["research_validation"]["claims_detected"] > 0
            assert result["research_validation"]["knowledge_graph_triggered"]
    
    @pytest.mark.asyncio
    async def test_knowledge_graph_fallback_behavior(self):
        """Test behavior when knowledge graph has no data."""
        
        state = {
            "messages": [
                HumanMessage(content="Tell me about the latest research on X"),
                AIMessage(content="Studies show that X is important (Fake et al., 2024).")
            ]
        }
        
        # Mock knowledge graph to return no results
        with patch('athlea_langgraph.agents.research_validation_node.create_graphrag_tool') as mock_tool:
            mock_instance = AsyncMock()
            mock_instance._arun.return_value = None  # No results
            mock_tool.return_value = mock_instance
            
            result = await research_validation_node(state)
            
            final_response = result["messages"][-1].content
            
            # Should provide safe fallback
            assert "couldn't find specific studies" in final_response
            assert "established training principles" in final_response
            assert "Fake et al., 2024" not in final_response
    
    def test_research_claim_pattern_coverage(self):
        """Test that our patterns cover various hallucination formats."""
        service = ResearchValidationService()
        
        test_cases = [
            # Common fake citation formats
            "According to Smith et al. (2023), protein timing matters.",
            "A study published in Nature found that...",
            "Research from Harvard shows 30% improvement.",
            "Meta-analysis reveals significant benefits (p<0.05).",
            "Clinical trials demonstrate efficacy (Jones & Brown, 2024).",
            
            # Fake URLs and DOIs
            "Read more at: www.fake-study.com",
            "DOI: 10.1234/fake.journal.2024",
            "Available at: https://research.fake-site.org",
            
            # High-risk link patterns
            "Click here for the full study",
            "Download the complete research paper",
            "See reference #5 for details",
            "As shown in Table 1 of the study",
            
            # Statistics without sources
            "85% of athletes showed improvement",
            "Statistically significant results (p<0.001)",
            "Strong correlation was observed (r=0.85)"
        ]
        
        for test_case in test_cases:
            claims = service.detect_research_claims(test_case)
            assert len(claims) > 0, f"Failed to detect research claim in: {test_case}"


# Benchmark tests for performance
class TestPerformanceBenchmarks:
    """Performance tests to ensure validation doesn't slow down responses."""
    
    @pytest.mark.asyncio
    async def test_validation_performance(self):
        """Test that validation completes quickly."""
        import time
        
        service = ResearchValidationService()
        
        # Test with moderately complex response
        test_response = """
        Here's what research tells us about strength training:
        
        Studies show that compound exercises are effective for building muscle.
        Research indicates that progressive overload is key for adaptation.
        According to sports science, rest periods between sets matter.
        
        For optimal results, focus on:
        1. Proper form and technique
        2. Gradual progression in weight or reps
        3. Adequate recovery between sessions
        """
        
        start_time = time.time()
        result = service.validate_response(test_response, "Tell me about strength training")
        end_time = time.time()
        
        # Should complete in under 100ms
        assert (end_time - start_time) < 0.1
        assert result.has_research_claims
        assert len(result.claims) > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 