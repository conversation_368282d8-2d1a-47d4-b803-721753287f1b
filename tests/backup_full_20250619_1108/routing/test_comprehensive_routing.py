#!/usr/bin/env python3
"""
Comprehensive Routing Test (Direct Invocation)

Tests that the routing fix works across all specialist coaches by directly
invoking the graph, bypassing any potential HTTP/networking issues.
This verifies that the enhanced_planning_node parsing fix resolves
routing issues for all domains.
"""

import asyncio
import os
import sys
from typing import Dict, List, Any

from langchain_core.messages import HumanMessage

# Ensure the script can find the root package
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from athlea_langgraph.graphs.archived.comprehensive_coaching_graph import create_comprehensive_coaching_graph, ComprehensiveCoachingConfig

class RoutingTestRunner:
    """
    Runs routing tests by directly creating and invoking the comprehensive coaching graph.
    """

    def __init__(self):
        self.test_cases = [
            # Recovery/Sleep Coach
            ("I'm looking to understand the relationship between my sleep patterns and my recovery from activities", "recovery_coach", "recovery-1"),
            ("How can I improve my sleep for better recovery?", "recovery_coach", "recovery-2"),
            
            # Strength Coach
            ("How do I increase my bench press max?", "strength_coach", "strength-1"),
            ("I want to build muscle and get stronger", "strength_coach", "strength-2"),
            
            # Running Coach
            ("I want to train for a marathon", "running_coach", "running-1"),
            ("How can I improve my 5K time?", "running_coach", "running-2"),
            
            # Cycling Coach
            ("How can I improve my cycling training?", "cycling_coach", "cycling-1"),
            ("I want to get better at road cycling", "cycling_coach", "cycling-2"),
            
            # Nutrition Coach
            ("I need a meal plan for muscle building", "nutrition_coach", "nutrition-1"),
            ("How much protein should I eat daily?", "nutrition_coach", "nutrition-2"),
            
            # Mental/Psychology Coach
            ("I get nervous before competitions", "mental_coach", "mental-1"),
            ("How can I stay motivated to train?", "mental_coach", "mental-2"),
        ]

    async def run_single_test(self, question: str, expected_coach: str, test_id: str) -> Dict[str, Any]:
        """Runs a single test case."""
        print(f"\n🔍 Testing: '{question}'")
        print(f"   Expected Route: {expected_coach}")
        print("-" * 60)

        user_id = f"test-user-{test_id}"
        thread_id = f"test-thread-{test_id}"

        graph_config = ComprehensiveCoachingConfig(
            user_id=user_id,
            thread_id=thread_id,
        )

        app = await create_comprehensive_coaching_graph(graph_config)
        
        inputs = {"messages": [HumanMessage(content=question)]}
        run_config = {"configurable": {"thread_id": thread_id, "user_id": user_id}}

        final_state = None
        activated_nodes = set()
        run_id = None

        try:
            async for event in app.astream_events(inputs, config=run_config, version="v2"):
                event_name = event['name']
                event_data = event['data']
                
                # Track every node that starts
                if event['event'] == 'on_chain_start':
                    if event_name == "ComprehensiveCoachingGraph":
                        run_id = event['run_id'] # Capture the main run_id
                    if event_name not in activated_nodes:
                        print(f"  ...Node Start: {event_name}")
                        activated_nodes.add(event_name)

                # Capture final state
                if event["event"] == "on_chain_end" and event["name"] == "ComprehensiveCoachingGraph":
                    final_state = event_data.get("output", {})

        except Exception as e:
            print(f"  ❌ ERROR during graph execution: {e}")
            return {"success": False, "reason": "Exception", "activated_nodes": list(activated_nodes), "expected": expected_coach}

        # Analyze the results
        success = expected_coach in activated_nodes
        
        trace_url = f"https://smith.langchain.com/o/athlea/runs/{run_id}" if run_id else "Not available"
        
        result = {
            "success": success,
            "reason": "Routing successful" if success else "Incorrect or no routing",
            "expected": expected_coach,
            "activated_nodes": list(activated_nodes),
            "trace_url": trace_url
        }
        
        if success:
            print(f"  ✅ SUCCESS: Correctly routed to '{expected_coach}'")
        else:
            print(f"  ❌ FAILED: Did not route to '{expected_coach}'.")
            print(f"     Activated nodes: {list(activated_nodes)}")
            
        return result

    async def run_all_tests(self):
        """Runs all defined test cases and prints a summary."""
        print("🧪 COMPREHENSIVE ROUTING TEST (DIRECT INVOCATION)")
        print("=" * 80)

        results = []
        for question, expected_coach, test_id in self.test_cases:
            res = await self.run_single_test(question, expected_coach, test_id)
            results.append(res)
            if res.get("trace_url"):
                print(f"     Trace URL: {res['trace_url']}")
            await asyncio.sleep(0.5) # Small delay between tests

        # Final summary
        successful_tests = sum(1 for r in results if r["success"])
        total_tests = len(results)

        print("\n" + "=" * 80)
        print("🏆 TEST SUMMARY")
        print("=" * 80)
        print(f"Total tests: {total_tests}")
        print(f"  - Passed: {successful_tests}")
        print(f"  - Failed: {total_tests - successful_tests}")
        success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        print(f"Success Rate: {success_rate:.1f}%")

        if total_tests - successful_tests > 0:
            print("\n📋 Failed Tests:")
            for i, r in enumerate(results):
                if not r['success']:
                    print(f"  - Test {i+1} ('{self.test_cases[i][0][:30]}...'):")
                    print(f"    - Expected: {r['expected']}")
                    print(f"    - Activated: {r['activated_nodes']}")
        
        print("=" * 80)
        if successful_tests == total_tests:
            print("🎉 ALL ROUTING TESTS PASSED!")
        else:
            print("⚠️ Some routing tests failed. Please review the output.")


async def main():
    """Main function to run the test suite."""
    runner = RoutingTestRunner()
    await runner.run_all_tests()

if __name__ == "__main__":
    # Note: Ensure all necessary environment variables are set (e.g., in a .env file)
    from dotenv import load_dotenv
    load_dotenv()
    asyncio.run(main()) 