#!/usr/bin/env python3
"""
Cycling Routing Debug Test

This test specifically debugs why cycling questions are being routed to strength
instead of cycling coach.
"""

import asyncio
import json
import time

import aiohttp


async def test_cycling_routing():
    """Test cycling routing specifically."""

    print("🚴 Cycling Routing Debug Test")
    print("=" * 70)

    url = "http://localhost:8000/api/coaching"

    # Test different cycling-related questions
    cycling_questions = [
        "How can I improve my cycling training?",
        "I want to start cycling",
        "Help me with bike training",
        "What's the best cycling workout?",
        "How do I cycle better?",
    ]

    for i, question in enumerate(cycling_questions, 1):
        print(f"\n🔍 Test {i}: '{question}'")
        print("-" * 50)

        params = {
            "message": question,
            "threadId": f"cycling-test-{i}-{int(time.time())}",
            "userId": "cycling_routing_test",
        }

        agents_seen = []
        routing_events = []

        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                if response.status != 200:
                    print(f"❌ Error: HTTP {response.status}")
                    continue

                event_type = None

                async for line in response.content:
                    line_text = line.decode("utf-8").strip()

                    if not line_text:
                        continue

                    # Parse SSE format
                    if line_text.startswith("event: "):
                        event_type = line_text.split("event: ")[1].strip()
                        continue

                    if line_text.startswith("data: "):
                        try:
                            data = json.loads(line_text[6:])

                            if event_type == "agent_start":
                                agent = data.get("agent")
                                if agent:
                                    agents_seen.append(agent)
                                    routing_events.append(f"Agent Start: {agent}")
                                    print(f"  🚀 Agent Start: {agent}")

                            elif event_type == "token":
                                agent = data.get("agent")
                                content = data.get("content", "")

                                # Look for routing-related content
                                if any(
                                    word in content.lower()
                                    for word in ["routing", "decision", "coach", "plan"]
                                ):
                                    routing_events.append(
                                        f"Token from {agent}: {content}"
                                    )

                            elif event_type == "complete":
                                break

                        except json.JSONDecodeError:
                            continue

        # Analyze results
        print(f"  📊 Agents activated: {agents_seen}")

        # Check if cycling coach was used
        if "cycling" in agents_seen:
            print(f"  ✅ SUCCESS: Cycling coach was activated")
        else:
            print(f"  ❌ ISSUE: Cycling coach NOT activated")
            if "strength" in agents_seen:
                print(f"  ⚠️  Strength coach was used instead")

        # Show routing events
        if routing_events:
            print(f"  🔄 Routing events:")
            for event in routing_events:
                print(f"    - {event}")

        # Small delay between tests
        await asyncio.sleep(1)

    print("\n" + "=" * 70)
    print("🏆 CYCLING ROUTING ANALYSIS COMPLETE")
    print("=" * 70)


if __name__ == "__main__":
    asyncio.run(test_cycling_routing())
