"""
Test comparing ReAct executor vs direct tool calling approaches.
This will help validate whether removing ReAct executor improves reliability.
"""

import asyncio
import logging
from athlea_langgraph.agents.nutrition_agent import NutritionAgent
from athlea_langgraph.states import AgentState
from langchain_core.messages import HumanMessage
from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_direct_tool_calling():
    """Test direct tool calling without ReAct executor."""
    print("\n" + "=" * 60)
    print("TESTING DIRECT TOOL CALLING (NO REACT)")
    print("=" * 60)

    # Create nutrition agent and load tools
    nutrition_agent = NutritionAgent()
    tools = await nutrition_agent.get_domain_tools()

    # Get the calculate_daily_calories tool
    calc_tool = next((t for t in tools if t.name == "calculate_daily_calories"), None)
    if not calc_tool:
        print("❌ calculate_daily_calories tool not found")
        return False

    print(f"✅ Found tool: {calc_tool.name}")

    # Create LLM with tools bound directly
    llm = create_azure_chat_openai(temperature=0.2)
    llm_with_tools = llm.bind_tools([calc_tool])

    # Test message - include explicit goal instruction
    messages = [
        HumanMessage(
            content="I am a 30 year old male, 180cm tall, weigh 80kg, moderately active. My goal is to maintain my weight. Please calculate my daily calories using the calculate_daily_calories tool."
        )
    ]

    print("\n1. Testing direct tool calling...")
    try:
        # Call LLM with tools
        response = await llm_with_tools.ainvoke(messages)

        print(f"   📝 LLM Response: {response.content}")
        print(
            f"   🔧 Tool Calls: {len(response.tool_calls) if response.tool_calls else 0}"
        )

        if response.tool_calls:
            for tool_call in response.tool_calls:
                print(f"   🎯 Tool Called: {tool_call['name']}")
                print(f"   📊 Arguments: {tool_call['args']}")

                # Execute the tool call
                tool_result = calc_tool.invoke(tool_call["args"])
                print(f"   ✅ Tool Result: {tool_result}")

                return True
        else:
            print("   ❌ No tools called")
            return False

    except Exception as e:
        print(f"   ❌ Direct tool calling failed: {e}")
        return False


async def test_react_executor():
    """Test with ReAct executor for comparison."""
    print("\n" + "=" * 60)
    print("TESTING REACT EXECUTOR")
    print("=" * 60)

    # Create nutrition agent
    nutrition_agent = NutritionAgent()

    # Test with ReAct executor
    state = AgentState(
        user_query="I am a 30 year old male, 180cm tall, weigh 80kg, moderately active. Calculate my daily calories using the calculate_daily_calories tool.",
        messages=[],
        execution_steps=[],
        config={},
    )

    print("\n1. Testing ReAct executor...")
    try:
        result = await nutrition_agent.process(state)
        response = result.get("response", "No response")

        print(f"   📝 Agent Response: {response[:200]}...")

        # Check if tool was actually used
        if (
            "calculate_daily_calories" in response
            or "2759" in response
            or "kcal" in response
        ):
            print("   ✅ Tool appears to have been used")
            return True
        else:
            print("   ❌ Tool not used - manual calculation detected")
            return False

    except Exception as e:
        print(f"   ❌ ReAct executor failed: {e}")
        return False


async def main():
    """Compare both approaches."""
    logger.info("🚀 Starting Direct Tool Calling vs ReAct Comparison")

    # Test direct tool calling
    direct_success = await test_direct_tool_calling()

    # Test ReAct executor
    react_success = await test_react_executor()

    print("\n" + "=" * 60)
    print("COMPARISON RESULTS")
    print("=" * 60)
    print(f"Direct Tool Calling: {'✅ SUCCESS' if direct_success else '❌ FAILED'}")
    print(f"ReAct Executor:      {'✅ SUCCESS' if react_success else '❌ FAILED'}")

    if direct_success and not react_success:
        print("\n🎯 CONCLUSION: Direct tool calling is more reliable!")
        print("💡 RECOMMENDATION: Remove ReAct executor and use direct tool calling")
    elif react_success and not direct_success:
        print("\n🎯 CONCLUSION: ReAct executor is more reliable!")
        print("💡 RECOMMENDATION: Keep ReAct executor but fix the prompting")
    elif direct_success and react_success:
        print("\n🎯 CONCLUSION: Both approaches work!")
        print("💡 RECOMMENDATION: Use direct tool calling for simplicity")
    else:
        print("\n🎯 CONCLUSION: Both approaches have issues!")
        print("💡 RECOMMENDATION: Debug the fundamental tool configuration")


if __name__ == "__main__":
    asyncio.run(main())
