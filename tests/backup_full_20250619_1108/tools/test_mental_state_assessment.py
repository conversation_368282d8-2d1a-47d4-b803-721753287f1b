"""
Tests for Mental State Assessment Tool

Comprehensive test suite covering mental state evaluation functionality,
edge cases, and validation scenarios.
"""

import pytest
from datetime import datetime

from athlea_langgraph.tools.mental.mental_state_assessment import (
    MentalStateAssessmentTool,
)
from athlea_langgraph.tools.schemas.mental_schemas import (
    MentalStateAssessmentInput,
    MentalStateAssessmentOutput,
)


class TestMentalStateAssessmentTool:
    """Test suite for Mental State Assessment Tool."""

    @pytest.fixture
    def assessment_tool(self):
        """Create assessment tool instance."""
        return MentalStateAssessmentTool()

    @pytest.fixture
    def normal_assessment_input(self):
        """Normal assessment input for testing."""
        return MentalStateAssessmentInput(
            user_id="user_123",
            mood_rating=6,
            stress_level=5,
            anxiety_level=4,
            energy_level=7,
            focus_level=6,
            motivation_level=7,
            confidence_level=6,
            sleep_hours=7.5,
            sleep_quality=7,
            current_stressors=["work deadlines", "family obligations"],
            positive_factors=["supportive friends", "regular exercise"],
            recent_activities=["gym workout", "team meeting", "lunch with friend"],
        )

    @pytest.fixture
    def concerning_assessment_input(self):
        """Concerning assessment input for testing."""
        return MentalStateAssessmentInput(
            user_id="user_456",
            mood_rating=2,
            stress_level=9,
            anxiety_level=8,
            energy_level=2,
            focus_level=3,
            motivation_level=2,
            confidence_level=3,
            sleep_hours=4.0,
            sleep_quality=3,
            current_stressors=[
                "job loss",
                "health issues",
                "financial stress",
                "relationship problems",
            ],
            positive_factors=[],
            recent_activities=["staying in bed"],
            specific_concerns=["persistent sadness", "thoughts of hopelessness"],
        )

    @pytest.fixture
    def excellent_assessment_input(self):
        """Excellent assessment input for testing."""
        return MentalStateAssessmentInput(
            user_id="user_789",
            mood_rating=9,
            stress_level=2,
            anxiety_level=2,
            energy_level=9,
            focus_level=8,
            motivation_level=9,
            confidence_level=8,
            sleep_hours=8.0,
            sleep_quality=9,
            current_stressors=[],
            positive_factors=[
                "great job",
                "loving family",
                "good health",
                "fulfilling hobbies",
            ],
            recent_activities=[
                "morning run",
                "productive work",
                "quality time with family",
                "meditation",
            ],
        )

    @pytest.mark.asyncio
    async def test_normal_assessment(self, assessment_tool, normal_assessment_input):
        """Test normal mental state assessment."""
        result = await assessment_tool.assess_mental_state(normal_assessment_input)

        assert isinstance(result, MentalStateAssessmentOutput)
        assert 40 <= result.overall_mental_state_score <= 80
        assert len(result.primary_concerns) >= 0
        assert len(result.strengths) >= 0
        assert len(result.recommendations) > 0
        assert len(result.immediate_actions) > 0
        assert result.follow_up_timeline
        assert isinstance(result.professional_referral_needed, bool)

    @pytest.mark.asyncio
    async def test_concerning_assessment(
        self, assessment_tool, concerning_assessment_input
    ):
        """Test concerning mental state assessment."""
        result = await assessment_tool.assess_mental_state(concerning_assessment_input)

        assert isinstance(result, MentalStateAssessmentOutput)
        assert result.overall_mental_state_score <= 50  # Should be low
        assert len(result.primary_concerns) >= 3  # Should identify multiple concerns
        assert result.professional_referral_needed  # Should recommend referral
        assert (
            "24-48 hours" in result.follow_up_timeline
            or "urgent" in result.follow_up_timeline.lower()
        )
        assert len(result.risk_factors) > 0

    @pytest.mark.asyncio
    async def test_excellent_assessment(
        self, assessment_tool, excellent_assessment_input
    ):
        """Test excellent mental state assessment."""
        result = await assessment_tool.assess_mental_state(excellent_assessment_input)

        assert isinstance(result, MentalStateAssessmentOutput)
        assert result.overall_mental_state_score >= 70  # Should be high
        assert len(result.strengths) >= 3  # Should identify multiple strengths
        assert not result.professional_referral_needed  # Should not need referral
        assert len(result.protective_factors) > 0

    def test_calculate_overall_score(self, assessment_tool, normal_assessment_input):
        """Test overall score calculation."""
        score = assessment_tool._calculate_overall_score(normal_assessment_input)

        assert isinstance(score, int)
        assert 1 <= score <= 100

    def test_identify_concerns(self, assessment_tool, concerning_assessment_input):
        """Test concern identification."""
        concerns = assessment_tool._identify_concerns(concerning_assessment_input)

        assert isinstance(concerns, list)
        assert len(concerns) > 0
        assert any("mood" in concern.lower() for concern in concerns)
        assert any("stress" in concern.lower() for concern in concerns)

    def test_identify_strengths(self, assessment_tool, excellent_assessment_input):
        """Test strength identification."""
        strengths = assessment_tool._identify_strengths(excellent_assessment_input)

        assert isinstance(strengths, list)
        assert len(strengths) > 0
        assert any(
            "mood" in strength.lower() or "positive" in strength.lower()
            for strength in strengths
        )

    def test_analyze_risk_factors(self, assessment_tool, concerning_assessment_input):
        """Test risk factor analysis."""
        risk_factors = assessment_tool._analyze_risk_factors(
            concerning_assessment_input
        )

        assert isinstance(risk_factors, list)
        assert len(risk_factors) > 0

    def test_analyze_protective_factors(
        self, assessment_tool, excellent_assessment_input
    ):
        """Test protective factor analysis."""
        protective_factors = assessment_tool._analyze_protective_factors(
            excellent_assessment_input
        )

        assert isinstance(protective_factors, list)
        assert len(protective_factors) > 0

    def test_generate_recommendations(self, assessment_tool, normal_assessment_input):
        """Test recommendation generation."""
        recommendations = assessment_tool._generate_recommendations(
            normal_assessment_input, ["moderate stress", "low energy"]
        )

        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
        assert len(recommendations) <= 8  # Should be limited

    def test_generate_immediate_actions(
        self, assessment_tool, concerning_assessment_input
    ):
        """Test immediate action generation."""
        concerns = ["high stress", "low mood", "poor sleep"]
        immediate_actions = assessment_tool._generate_immediate_actions(
            concerning_assessment_input, concerns
        )

        assert isinstance(immediate_actions, list)
        assert len(immediate_actions) > 0
        assert len(immediate_actions) <= 5  # Should be limited

    def test_determine_follow_up_timeline(self, assessment_tool):
        """Test follow-up timeline determination."""
        # Test urgent scenario
        urgent_timeline = assessment_tool._determine_follow_up_timeline(
            25, ["high-priority concern: persistent sadness"]
        )
        assert "24-48 hours" in urgent_timeline or "urgent" in urgent_timeline.lower()

        # Test normal scenario
        normal_timeline = assessment_tool._determine_follow_up_timeline(
            65, ["mild stress"]
        )
        assert "week" in normal_timeline.lower()

    def test_assess_referral_need(self, assessment_tool, concerning_assessment_input):
        """Test professional referral assessment."""
        # Should need referral for concerning assessment
        needs_referral = assessment_tool._assess_referral_need(
            concerning_assessment_input, 25, ["multiple severe concerns"]
        )
        assert needs_referral

        # Should not need referral for normal assessment
        normal_input = MentalStateAssessmentInput(
            user_id="user_123",
            mood_rating=7,
            stress_level=4,
            anxiety_level=3,
            energy_level=7,
            focus_level=6,
            motivation_level=7,
            confidence_level=6,
            sleep_hours=8.0,
            sleep_quality=7,
        )
        no_referral = assessment_tool._assess_referral_need(
            normal_input, 75, ["good mood"]
        )
        assert not no_referral

    @pytest.mark.asyncio
    async def test_edge_case_minimal_input(self, assessment_tool):
        """Test with minimal required input."""
        minimal_input = MentalStateAssessmentInput(
            user_id="user_minimal",
            mood_rating=5,
            stress_level=5,
            anxiety_level=5,
            energy_level=5,
            focus_level=5,
            motivation_level=5,
            confidence_level=5,
            sleep_hours=7.0,
            sleep_quality=5,
        )

        result = await assessment_tool.assess_mental_state(minimal_input)
        assert isinstance(result, MentalStateAssessmentOutput)
        assert result.overall_mental_state_score > 0

    @pytest.mark.asyncio
    async def test_edge_case_extreme_values(self, assessment_tool):
        """Test with extreme input values."""
        extreme_input = MentalStateAssessmentInput(
            user_id="user_extreme",
            mood_rating=1,
            stress_level=10,
            anxiety_level=10,
            energy_level=1,
            focus_level=1,
            motivation_level=1,
            confidence_level=1,
            sleep_hours=2.0,
            sleep_quality=1,
            current_stressors=["extreme stress"] * 10,
            positive_factors=[],
            recent_activities=[],
        )

        result = await assessment_tool.assess_mental_state(extreme_input)
        assert isinstance(result, MentalStateAssessmentOutput)
        assert result.overall_mental_state_score <= 30
        assert result.professional_referral_needed

    def test_validation_constraints(self):
        """Test input validation constraints."""
        # Test valid input
        valid_input = MentalStateAssessmentInput(
            user_id="test",
            mood_rating=5,
            stress_level=5,
            anxiety_level=5,
            energy_level=5,
            focus_level=5,
            motivation_level=5,
            confidence_level=5,
            sleep_hours=7.0,
            sleep_quality=5,
        )
        assert valid_input.mood_rating == 5

        # Test invalid ratings (should raise validation error)
        with pytest.raises(ValueError):
            MentalStateAssessmentInput(
                user_id="test",
                mood_rating=11,  # Invalid: > 10
                stress_level=5,
                anxiety_level=5,
                energy_level=5,
                focus_level=5,
                motivation_level=5,
                confidence_level=5,
                sleep_hours=7.0,
                sleep_quality=5,
            )

    def test_tool_properties(self, assessment_tool):
        """Test tool properties and configuration."""
        assert assessment_tool.domain == "mental_training"
        assert assessment_tool.name == "mental_state_assessment"
        assert assessment_tool.description
        assert hasattr(assessment_tool, "score_thresholds")
        assert hasattr(assessment_tool, "high_risk_indicators")
        assert hasattr(assessment_tool, "recommendation_library")
