"""
Tests for the NutritionMealPlanTool.
"""

from datetime import datetime

import pytest

from athlea_langgraph.tools.nutrition.meal_plan_tool import (
    DailyPlan,
    Macronutrients,
    Meal,
    MealIngredient,
    MealItem,
    MealPlanInput,
    NutritionMealPlanTool,
    NutritionPlan,
)


@pytest.fixture
def meal_plan_tool() -> NutritionMealPlanTool:
    """Fixture to provide an instance of the NutritionMealPlanTool."""
    return NutritionMealPlanTool()


@pytest.mark.parametrize(
    "diet_type, expected_macros",
    [
        ("balanced", {"carbs": 0.45, "protein": 0.25, "fat": 0.3}),
        ("high-protein", {"carbs": 0.3, "protein": 0.4, "fat": 0.3}),
        ("low-carb", {"carbs": 0.2, "protein": 0.4, "fat": 0.4}),
        (
            "High-Protein",
            {"carbs": 0.3, "protein": 0.4, "fat": 0.3},
        ),  # Case insensitive
        ("unknown", {"carbs": 0.45, "protein": 0.25, "fat": 0.3}),  # Default
    ],
)
def test_get_macro_percentages(
    meal_plan_tool: NutritionMealPlanTool, diet_type: str, expected_macros: dict
):
    """Test the _get_macro_percentages method."""
    result = meal_plan_tool._get_macro_percentages(diet_type)
    assert result == expected_macros


def test_generate_meals(meal_plan_tool: NutritionMealPlanTool):
    """Test the _generate_meals method."""
    # Standard calorie breakdown
    breakfast_cal = 395
    morning_snack_cal = 165
    lunch_cal = 380
    afternoon_snack_cal = 185
    dinner_cal = 585
    total_cal = 1710

    daily_plan = meal_plan_tool._generate_meals(
        breakfast_cal,
        morning_snack_cal,
        lunch_cal,
        afternoon_snack_cal,
        dinner_cal,
        total_cal,
    )

    # Test structure
    assert isinstance(daily_plan, DailyPlan)
    assert daily_plan.daily_total_calories == total_cal

    # Test individual meals
    assert daily_plan.breakfast.meal_calories == breakfast_cal
    assert daily_plan.morning_snack.meal_calories == morning_snack_cal
    assert daily_plan.lunch.meal_calories == lunch_cal
    assert daily_plan.afternoon_snack.meal_calories == afternoon_snack_cal
    assert daily_plan.dinner.meal_calories == dinner_cal

    # Test that each meal has items
    assert len(daily_plan.breakfast.items) > 0
    assert len(daily_plan.morning_snack.items) > 0
    assert len(daily_plan.lunch.items) > 0
    assert len(daily_plan.afternoon_snack.items) > 0
    assert len(daily_plan.dinner.items) > 0

    # Test meal item structure
    breakfast_item = daily_plan.breakfast.items[0]
    assert isinstance(breakfast_item, MealItem)
    assert isinstance(breakfast_item.description, str)
    assert len(breakfast_item.ingredients) > 0
    assert breakfast_item.total_calories == breakfast_cal

    # Test ingredient structure
    ingredient = breakfast_item.ingredients[0]
    assert isinstance(ingredient, MealIngredient)
    assert isinstance(ingredient.name, str)
    assert isinstance(ingredient.quantity, str)
    assert isinstance(ingredient.calories, int)


@pytest.mark.parametrize(
    "diet_type, calorie_target, expected_carbs, expected_protein, expected_fat",
    [
        ("balanced", 2000, 225, 125, 67),  # 45% carbs, 25% protein, 30% fat
        ("high-protein", 2000, 150, 200, 67),  # 30% carbs, 40% protein, 30% fat
        ("low-carb", 2000, 100, 200, 89),  # 20% carbs, 40% protein, 40% fat
        ("balanced", 1800, 203, 113, 60),  # Lower calorie balanced
    ],
)
def test_generate_rationale(
    meal_plan_tool: NutritionMealPlanTool,
    diet_type: str,
    calorie_target: int,
    expected_carbs: int,
    expected_protein: int,
    expected_fat: int,
):
    """Test the _generate_rationale method."""
    macro_percentages = meal_plan_tool._get_macro_percentages(diet_type)
    rationale = meal_plan_tool._generate_rationale(
        diet_type, calorie_target, macro_percentages
    )

    assert isinstance(rationale, str)
    assert len(rationale) > 0
    assert diet_type in rationale
    assert str(calorie_target) in rationale
    assert f"{macro_percentages['carbs'] * 100:.0f}%" in rationale
    assert f"{macro_percentages['protein'] * 100:.0f}%" in rationale
    assert f"{macro_percentages['fat'] * 100:.0f}%" in rationale


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_id, input_data, expected_calories, expected_diet_type",
    [
        (
            "balanced_default",
            MealPlanInput(date="2024-01-15", calorie_target=2000, diet_type="balanced"),
            2000,
            "balanced",
        ),
        (
            "high_protein_1800",
            MealPlanInput(
                date="2024-01-16", calorie_target=1800, diet_type="high-protein"
            ),
            1800,
            "high-protein",
        ),
        (
            "low_carb_2200",
            MealPlanInput(date="2024-01-17", calorie_target=2200, diet_type="low-carb"),
            2200,
            "low-carb",
        ),
        (
            "default_calories",
            MealPlanInput(date="2024-01-18", diet_type="balanced"),  # No calorie_target
            2000,  # Should default to 2000
            "balanced",
        ),
    ],
)
async def test_generate_meal_plan(
    meal_plan_tool: NutritionMealPlanTool,
    test_id: str,
    input_data: MealPlanInput,
    expected_calories: int,
    expected_diet_type: str,
):
    """Test the main generate_meal_plan method."""
    result = await meal_plan_tool.generate_meal_plan(input_data)

    # Test basic structure
    assert isinstance(result, NutritionPlan)
    assert result.day == input_data.date
    assert result.session_type == "nutrition"
    assert result.caloric_intake == str(expected_calories)

    # Test macronutrients structure
    assert isinstance(result.macronutrients, Macronutrients)
    assert result.macronutrients.protein.endswith("g")
    assert result.macronutrients.carbohydrates.endswith("g")
    assert result.macronutrients.fat.endswith("g")

    # Test meals structure
    assert isinstance(result.meals, DailyPlan)

    # Test rationale
    assert isinstance(result.rationale, str)
    assert len(result.rationale) > 0
    assert expected_diet_type in result.rationale.lower()


@pytest.mark.asyncio
async def test_generate_meal_plan_macro_calculations(
    meal_plan_tool: NutritionMealPlanTool,
):
    """Test that macro calculations are correct."""
    input_data = MealPlanInput(
        date="2024-01-15", calorie_target=2000, diet_type="balanced"
    )
    result = await meal_plan_tool.generate_meal_plan(input_data)

    # Extract macro values
    protein_grams = int(result.macronutrients.protein.rstrip("g"))
    carbs_grams = int(result.macronutrients.carbohydrates.rstrip("g"))
    fat_grams = int(result.macronutrients.fat.rstrip("g"))

    # For balanced diet (45% carbs, 25% protein, 30% fat) at 2000 calories:
    # Carbs: 2000 * 0.45 / 4 = 225g
    # Protein: 2000 * 0.25 / 4 = 125g
    # Fat: 2000 * 0.30 / 9 = 67g (rounded)

    assert carbs_grams == 225
    assert protein_grams == 125
    assert fat_grams == 67


@pytest.mark.asyncio
async def test_plan_meals_legacy_method(meal_plan_tool: NutritionMealPlanTool):
    """Test the legacy plan_meals method for backward compatibility."""
    # Test with dict input
    input_dict = {
        "date": "2024-01-15",
        "calorie_target": 1800,
        "diet_type": "high-protein",
    }

    result = await meal_plan_tool.plan_meals(input_dict)

    # Test structure of legacy output
    assert isinstance(result, dict)
    assert "meal_plan" in result
    assert "recipes" in result
    assert "calorie_target" in result
    assert "macronutrients" in result
    assert "full_plan" in result

    # Test values
    assert "nutrition plan for 2024-01-15" in result["meal_plan"]
    assert result["calorie_target"] == "1800"
    assert isinstance(result["recipes"], list)
    assert len(result["recipes"]) == 3  # breakfast, lunch, dinner

    # Test with MealPlanInput object
    input_obj = MealPlanInput(
        date="2024-01-16", calorie_target=2200, diet_type="low-carb"
    )
    result2 = await meal_plan_tool.plan_meals(input_obj)

    assert result2["calorie_target"] == "2200"


@pytest.mark.asyncio
async def test_plan_meals_default_values(meal_plan_tool: NutritionMealPlanTool):
    """Test plan_meals with default values."""
    # Test with minimal dict input
    input_dict = {}

    result = await meal_plan_tool.plan_meals(input_dict)

    # Should use defaults
    assert result["calorie_target"] == "2000"  # Default calorie target
    # Date should be today's date
    today = datetime.now().strftime("%Y-%m-%d")
    assert today in result["meal_plan"]


@pytest.mark.asyncio
async def test_generate_meal_plan_error_handling(meal_plan_tool: NutritionMealPlanTool):
    """Test error handling in generate_meal_plan."""
    # This is hard to test directly without mocking internal methods
    # For now, we test that valid inputs don't raise exceptions
    input_data = MealPlanInput(
        date="2024-01-15", calorie_target=2000, diet_type="balanced"
    )

    try:
        result = await meal_plan_tool.generate_meal_plan(input_data)
        assert isinstance(result, NutritionPlan)
    except Exception as e:
        pytest.fail(f"generate_meal_plan raised an unexpected exception: {e}")


def test_meal_plan_input_validation():
    """Test MealPlanInput validation."""
    # Valid input
    valid_input = MealPlanInput(
        date="2024-01-15", calorie_target=2000, diet_type="balanced"
    )
    assert valid_input.date == "2024-01-15"
    assert valid_input.calorie_target == 2000
    assert valid_input.diet_type == "balanced"

    # Test defaults
    minimal_input = MealPlanInput(date="2024-01-15")
    assert minimal_input.calorie_target == 2000  # Default
    assert minimal_input.diet_type == "balanced"  # Default


def test_model_structures():
    """Test the Pydantic model structures."""
    # Test MealIngredient
    ingredient = MealIngredient(name="Oats", quantity="50", unit="g", calories=180)
    assert ingredient.name == "Oats"
    assert ingredient.quantity == "50"
    assert ingredient.unit == "g"
    assert ingredient.calories == 180

    # Test MealItem
    meal_item = MealItem(
        description="Oatmeal",
        ingredients=[ingredient],
        preparation="Cook with water",
        total_calories=180,
    )
    assert meal_item.description == "Oatmeal"
    assert len(meal_item.ingredients) == 1
    assert meal_item.preparation == "Cook with water"
    assert meal_item.total_calories == 180

    # Test Meal
    meal = Meal(items=[meal_item], meal_calories=180)
    assert len(meal.items) == 1
    assert meal.meal_calories == 180

    # Test Macronutrients
    macros = Macronutrients(protein="125g", carbohydrates="225g", fat="67g")
    assert macros.protein == "125g"
    assert macros.carbohydrates == "225g"
    assert macros.fat == "67g"
