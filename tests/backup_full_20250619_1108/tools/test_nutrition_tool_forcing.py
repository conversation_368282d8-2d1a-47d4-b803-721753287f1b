"""
Test to verify that the nutrition agent uses tools instead of calculating manually.
This is a focused test to understand why the agent is bypassing tools.
"""

import asyncio
import logging
from athlea_langgraph.agents.nutrition_agent import NutritionAgent
from athlea_langgraph.states import AgentState

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_nutrition_tool_forcing():
    """Test if nutrition agent will use tools when prompted correctly."""
    print("\n" + "=" * 60)
    print("TESTING NUTRITION AGENT TOOL FORCING")
    print("=" * 60)

    # Create nutrition agent
    nutrition_agent = NutritionAgent()

    # Load tools
    print("1. Loading tools...")
    tools = await nutrition_agent.get_domain_tools()
    print(f"   ✅ Loaded {len(tools)} tools: {[t.name for t in tools]}")

    # Load system prompt
    print("2. Loading system prompt...")
    prompt = await nutrition_agent._load_system_prompt()
    print(f"   ✅ Prompt loaded ({len(prompt)} chars)")

    # Check if calculate_daily_calories is available
    calc_tool = next((t for t in tools if t.name == "calculate_daily_calories"), None)
    if calc_tool:
        print(f"   ✅ calculate_daily_calories tool available")
        print(f"   📝 Tool description: {calc_tool.description[:100]}...")
    else:
        print("   ❌ calculate_daily_calories tool NOT FOUND")
        return False

    # Test direct tool call first
    print("\n3. Testing direct tool call...")
    try:
        test_input = {
            "age": 30,
            "height": 180,
            "weight": 80,
            "gender": "male",
            "activity_level": "moderately_active",
            "goal": "maintenance",
        }
        result = calc_tool.invoke(test_input)
        print(f"   ✅ Direct tool call successful: {result}")
    except Exception as e:
        print(f"   ❌ Direct tool call failed: {e}")
        return False

    # Test ReAct executor forcing tool usage
    print("\n4. Testing ReAct executor with explicit tool forcing...")

    # Create a state that forces tool usage
    state = AgentState(
        user_query="I am a 30 year old male, 180cm tall, weigh 80kg, moderately active. Calculate my daily calories using the calculate_daily_calories tool.",
        messages=[],
        execution_steps=[],
        config={},
    )

    # Process with nutrition agent
    try:
        result = await nutrition_agent.process(state)
        response = result.get("response", "No response")

        print(f"   📝 Agent Response: {response[:200]}...")

        # Check if tool was actually used
        if "calculate_daily_calories" in response:
            print("   ✅ Tool mentioned in response")
        else:
            print("   ❌ Tool NOT mentioned in response")

        # Check for manual calculation patterns
        manual_patterns = ["BMR =", "1785", "Mifflin-St Jeor", "manually calculated"]
        found_manual = any(pattern in response for pattern in manual_patterns)

        if found_manual:
            print("   ❌ PROBLEM: Agent calculated manually instead of using tool")
            print("   🔍 This indicates the ReAct executor is not enforcing tool usage")
        else:
            print("   ✅ No manual calculation detected")

        return not found_manual

    except Exception as e:
        print(f"   ❌ Agent processing failed: {e}")
        return False


async def main():
    """Run the nutrition tool forcing test."""
    logger.info("🧪 Starting Nutrition Tool Forcing Test")
    success = await test_nutrition_tool_forcing()

    if success:
        print("\n🎉 SUCCESS: Nutrition agent uses tools correctly")
    else:
        print("\n❌ FAILURE: Nutrition agent bypasses tools")
        print("\n🔧 RECOMMENDED FIXES:")
        print("1. Strengthen ReAct executor tool enforcement")
        print("2. Update prompt to be more explicit about tool usage")
        print("3. Add tool usage validation in the agent")


if __name__ == "__main__":
    asyncio.run(main())
