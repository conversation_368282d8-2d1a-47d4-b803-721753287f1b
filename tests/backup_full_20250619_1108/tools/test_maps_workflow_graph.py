"""
Test suite for Maps Workflow Graph

Tests the graph compilation, workflow execution, and integration
with underlying Azure Maps and Google Maps Elevation tools.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch

from athlea_langgraph.graphs.tools.maps_workflow_graph import (
    create_maps_workflow_graph,
    create_location_analysis_graph,
    create_route_planning_graph,
    create_area_exploration_graph,
    create_weather_check_graph,
    MapsWorkflowGraphWrapper,
)


class TestMapsWorkflowGraphCompilation:
    """Test graph compilation and initialization."""

    @pytest.mark.asyncio
    async def test_maps_workflow_graph_compilation(self):
        """Test that the maps workflow graph compiles successfully."""
        config = {
            "azure_maps_key": "test_azure_key",
            "google_maps_key": "test_google_key",
        }

        graph = await create_maps_workflow_graph(config)

        assert graph is not None
        assert hasattr(graph, "invoke")
        assert hasattr(graph, "ainvoke")

    @pytest.mark.asyncio
    async def test_location_analysis_graph_compilation(self):
        """Test location analysis specific graph compilation."""
        config = {"azure_maps_key": "test_key"}

        graph = await create_location_analysis_graph(config)

        assert graph is not None

    @pytest.mark.asyncio
    async def test_route_planning_graph_compilation(self):
        """Test route planning specific graph compilation."""
        config = {"google_maps_key": "test_key"}

        graph = await create_route_planning_graph(config)

        assert graph is not None

    @pytest.mark.asyncio
    async def test_area_exploration_graph_compilation(self):
        """Test area exploration specific graph compilation."""
        config = {"azure_maps_key": "test_key"}

        graph = await create_area_exploration_graph(config)

        assert graph is not None

    @pytest.mark.asyncio
    async def test_weather_check_graph_compilation(self):
        """Test weather check specific graph compilation."""
        config = {"azure_maps_key": "test_key"}

        graph = await create_weather_check_graph(config)

        assert graph is not None

    @pytest.mark.asyncio
    async def test_graph_compilation_without_keys(self):
        """Test graph compiles even without API keys (for testing)."""
        graph = await create_maps_workflow_graph()

        assert graph is not None


class TestMapsWorkflowGraphWrapper:
    """Test the Maps Workflow Graph Wrapper."""

    def setup_method(self):
        """Set up test fixtures."""
        self.wrapper = MapsWorkflowGraphWrapper()

    @pytest.mark.asyncio
    async def test_wrapper_initialization(self):
        """Test wrapper initializes properly."""
        assert self.wrapper is not None
        assert hasattr(self.wrapper, "graph")


class TestMapsWorkflowGraphIntegration:
    """Test integration with actual workflow components."""

    @pytest.mark.asyncio
    async def test_graph_invoke_structure(self):
        """Test that graph invoke returns proper structure."""
        config = {"test_mode": True}
        graph = await create_maps_workflow_graph(config)

        test_input = {
            "workflow_type": "location_analysis",
            "address": "Seattle, WA",
            "include_elevation": False,
            "include_weather": False,
            "include_nearby": False,
        }

        # Test that invoke works (even if tools are mocked/unavailable)
        try:
            result = await graph.ainvoke(test_input)
            assert result is not None
            assert "result" in result or "errors" in result
        except Exception as e:
            # Expected if tools aren't configured, but graph structure should be valid
            error_str = str(e).lower()
            # Accept initialization or API key errors, but not structural errors
            assert any(
                term in error_str for term in ["key", "tool", "api", "auth", "init"]
            )

    @pytest.mark.asyncio
    async def test_different_workflow_types(self):
        """Test graph handles different workflow types."""
        graph = await create_maps_workflow_graph()

        workflow_types = [
            "location_analysis",
            "route_planning",
            "area_exploration",
            "weather_check",
        ]

        for workflow_type in workflow_types:
            test_input = {
                "workflow_type": workflow_type,
                "lat": 47.6062,
                "lon": -122.3321,
            }

            try:
                result = await graph.ainvoke(test_input)
                # If it doesn't throw a structure error, the graph is properly built
                assert result is not None
            except Exception as e:
                # Expected errors should be about missing API keys, not graph structure
                error_str = str(e).lower()
                assert any(
                    term in error_str for term in ["key", "tool", "api", "auth", "init"]
                )


@pytest.mark.asyncio
async def test_create_all_graph_variants():
    """Test that all graph creation functions work."""
    config = {"test_mode": True}

    graph_creators = [
        create_maps_workflow_graph,
        create_location_analysis_graph,
        create_route_planning_graph,
        create_area_exploration_graph,
        create_weather_check_graph,
    ]

    for creator in graph_creators:
        graph = await creator(config)
        assert graph is not None
        assert hasattr(graph, "ainvoke")


@pytest.mark.asyncio
async def test_graph_state_handling():
    """Test that graph properly handles state transitions."""
    graph = await create_maps_workflow_graph()

    test_input = {
        "workflow_type": "location_analysis",
        "address": "Test Address",
        "include_elevation": True,
        "include_weather": True,
        "include_nearby": True,
        "search_radius": 5000,
    }

    try:
        result = await graph.ainvoke(test_input)

        # Check that result has proper structure
        assert isinstance(result, dict)

        # Should have either result data or error handling
        assert any(key in result for key in ["result", "errors", "current_node"])

    except Exception as e:
        # Accept tool/API errors but not structural ones
        error_str = str(e).lower()
        assert any(term in error_str for term in ["key", "tool", "api", "auth", "init"])


if __name__ == "__main__":
    # Run a simple test to verify the test file works
    async def run_basic_test():
        """Run a basic test to verify functionality."""
        print("🧪 Testing Maps Workflow Graph compilation...")

        try:
            graph = await create_maps_workflow_graph()
            print("✅ Maps Workflow Graph compiled successfully")

            wrapper = MapsWorkflowGraphWrapper()
            print("✅ Maps Workflow Graph Wrapper initialized successfully")

            print("🎉 All basic tests passed!")

        except Exception as e:
            print(f"❌ Test failed: {e}")

    asyncio.run(run_basic_test())
