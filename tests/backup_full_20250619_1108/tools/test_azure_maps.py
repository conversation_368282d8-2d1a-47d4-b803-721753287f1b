"""
Comprehensive tests for Azure Maps Tool.

Tests cover:
- Input validation for all commands
- Output validation
- API request handling
- Error scenarios
- Circuit breaker functionality
- Fallback responses
- Command-specific functionality
"""

import asyncio
from typing import Any, Dict
from unittest.mock import AsyncMock, Mock, patch

import aiohttp
import pytest

from athlea_langgraph.tools import AzureMapsTool
from athlea_langgraph.tools.base_tool import ToolError, ToolErrorType
from athlea_langgraph.tools.circuit_breaker import CircuitState
from athlea_langgraph.tools.schemas.azure_maps_schemas import (
    Address,
    AzureMapsInput,
    AzureMapsOutput,
    Position,
)


class TestAzureMapsTool:
    """Test suite for Azure Maps Tool."""

    @pytest.fixture
    def tool(self):
        """Create tool instance with mock subscription key."""
        return AzureMapsTool(subscription_key="test_subscription_key")

    @pytest.fixture
    def mock_geocode_response(self):
        """Mock successful Azure Maps geocoding response."""
        return {
            "results": [
                {
                    "position": {"lat": 40.7128, "lon": -74.0060},
                    "address": {
                        "freeformAddress": "New York, NY, USA",
                        "municipality": "New York",
                        "countryCode": "US",
                        "country": "United States",
                    },
                    "confidence": "High",
                }
            ]
        }

    @pytest.fixture
    def mock_weather_response(self):
        """Mock successful Azure Maps weather response."""
        return {
            "results": [
                {
                    "temperature": {"value": 22.5, "unit": "C"},
                    "relativeHumidity": 65,
                    "wind": {"speed": {"value": 15.2, "unit": "km/h"}},
                    "precipitation": {"value": 0.0, "unit": "mm"},
                    "phrase": "Partly Cloudy",
                    "realFeelTemperature": {"value": 24.1, "unit": "C"},
                }
            ]
        }

    @pytest.fixture
    def mock_nearby_response(self):
        """Mock successful Azure Maps nearby search response."""
        return {
            "results": [
                {
                    "id": "poi123",
                    "type": "POI",
                    "score": 8.5,
                    "dist": 0.3,
                    "position": {"lat": 40.7130, "lon": -74.0065},
                    "address": {
                        "freeformAddress": "Central Park, New York, NY",
                        "municipality": "New York",
                    },
                    "poi": {
                        "name": "Central Park",
                        "categories": ["park", "recreation"],
                        "categorySet": [{"id": 9362}],
                    },
                }
            ]
        }

    # ==================== Initialization Tests ====================

    def test_tool_initialization_with_subscription_key(self):
        """Test tool initializes correctly with subscription key."""
        tool = AzureMapsTool(subscription_key="test_key")
        assert tool.subscription_key == "test_key"
        assert tool.name == "azure_maps"
        assert tool.circuit_breaker is not None

    def test_tool_initialization_without_subscription_key(self):
        """Test tool initialization fails without subscription key."""
        with patch.dict("os.environ", {}, clear=True):
            with pytest.raises(
                ValueError, match="Azure Maps subscription key required"
            ):
                AzureMapsTool()

    # ==================== Input Validation Tests ====================

    @pytest.mark.asyncio
    async def test_valid_geocode_input(self, tool):
        """Test validation of valid geocode input."""
        input_data = {"command": "geocode", "address": "New York, NY"}

        with patch.object(tool, "_execute_tool") as mock_execute:
            mock_execute.return_value = {
                "command": "geocode",
                "success": True,
                "api_status": "OK",
            }

            result = await tool.invoke(input_data)

            mock_execute.assert_called_once()
            validated_input = mock_execute.call_args[0][0]
            assert isinstance(validated_input, AzureMapsInput)
            assert validated_input.command == "geocode"
            assert validated_input.address == "New York, NY"

    @pytest.mark.asyncio
    async def test_geocode_missing_address(self, tool):
        """Test geocode command requires address."""
        input_data = {
            "command": "geocode"
            # Missing address
        }

        result = await tool.invoke(input_data)

        assert not result.success
        assert "validation" in result.error_type.lower()

    @pytest.mark.asyncio
    async def test_valid_weather_input(self, tool):
        """Test validation of valid weather input."""
        input_data = {"command": "weather", "lat": 40.7128, "lon": -74.0060}

        with patch.object(tool, "_execute_tool") as mock_execute:
            mock_execute.return_value = {
                "command": "weather",
                "success": True,
                "api_status": "OK",
            }

            result = await tool.invoke(input_data)

            mock_execute.assert_called_once()
            validated_input = mock_execute.call_args[0][0]
            assert validated_input.command == "weather"
            assert validated_input.lat == 40.7128
            assert validated_input.lon == -74.0060

    @pytest.mark.asyncio
    async def test_weather_missing_coordinates(self, tool):
        """Test weather command requires coordinates."""
        input_data = {
            "command": "weather",
            "lat": 40.7128,
            # Missing lon
        }

        result = await tool.invoke(input_data)

        assert not result.success
        assert "validation" in result.error_type.lower()

    @pytest.mark.asyncio
    async def test_valid_nearby_input(self, tool):
        """Test validation of valid nearby input."""
        input_data = {
            "command": "nearby",
            "lat": 40.7128,
            "lon": -74.0060,
            "query": "restaurants",
            "radius": 1000,
        }

        with patch.object(tool, "_execute_tool") as mock_execute:
            mock_execute.return_value = {
                "command": "nearby",
                "success": True,
                "api_status": "OK",
            }

            result = await tool.invoke(input_data)

            mock_execute.assert_called_once()
            validated_input = mock_execute.call_args[0][0]
            assert validated_input.command == "nearby"
            assert validated_input.query == "restaurants"
            assert validated_input.radius == 1000

    @pytest.mark.asyncio
    async def test_nearby_missing_query(self, tool):
        """Test nearby command requires query."""
        input_data = {
            "command": "nearby",
            "lat": 40.7128,
            "lon": -74.0060,
            # Missing query
        }

        result = await tool.invoke(input_data)

        assert not result.success
        assert "validation" in result.error_type.lower()

    @pytest.mark.asyncio
    async def test_invalid_coordinates(self, tool):
        """Test validation rejects invalid coordinates."""
        input_data = {
            "command": "weather",
            "lat": 91.0,  # Invalid latitude
            "lon": -74.0060,
        }

        result = await tool.invoke(input_data)

        assert not result.success
        assert "validation" in result.error_type.lower()

    # ==================== API Request Tests ====================

    @pytest.mark.asyncio
    async def test_geocode_success(self, tool, mock_geocode_response):
        """Test successful geocoding."""
        input_data = {"command": "geocode", "address": "New York, NY"}

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_geocode_response)
            mock_get.return_value.__aenter__.return_value = mock_response

            result = await tool.invoke(input_data)

            assert result.success
            assert result.data.command == "geocode"
            assert result.data.geocode_result is not None
            assert result.data.geocode_result.position.lat == 40.7128
            assert result.data.geocode_result.position.lon == -74.0060

    @pytest.mark.asyncio
    async def test_weather_success(self, tool, mock_weather_response):
        """Test successful weather lookup."""
        input_data = {"command": "weather", "lat": 40.7128, "lon": -74.0060}

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_weather_response)
            mock_get.return_value.__aenter__.return_value = mock_response

            result = await tool.invoke(input_data)

            assert result.success
            assert result.data.command == "weather"
            assert result.data.weather_result is not None
            assert result.data.weather_result.temperature.value == 22.5
            assert result.data.weather_result.condition == "Partly Cloudy"

    @pytest.mark.asyncio
    async def test_nearby_search_success(self, tool, mock_nearby_response):
        """Test successful nearby search."""
        input_data = {
            "command": "nearby",
            "lat": 40.7128,
            "lon": -74.0060,
            "query": "parks",
            "radius": 1000,
        }

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_nearby_response)
            mock_get.return_value.__aenter__.return_value = mock_response

            result = await tool.invoke(input_data)

            assert result.success
            assert result.data.command == "nearby"
            assert result.data.location_count == 1
            assert len(result.data.locations) == 1
            assert result.data.locations[0].poi.name == "Central Park"

    @pytest.mark.asyncio
    async def test_routes_search_success(self, tool, mock_nearby_response):
        """Test successful routes search."""
        input_data = {
            "command": "routes",
            "lat": 40.7128,
            "lon": -74.0060,
            "radius": 5000,
        }

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_nearby_response)
            mock_get.return_value.__aenter__.return_value = mock_response

            result = await tool.invoke(input_data)

            assert result.success
            assert result.data.command == "routes"
            assert result.data.location_count >= 0  # May find routes or not
            assert result.data.search_query == "running routes and trails"

    # ==================== Error Handling Tests ====================

    @pytest.mark.asyncio
    async def test_api_rate_limit_error(self, tool):
        """Test handling of API rate limit errors."""
        input_data = {"command": "geocode", "address": "New York, NY"}

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 429
            mock_response.headers = {"Retry-After": "60"}
            mock_get.return_value.__aenter__.return_value = mock_response

            result = await tool.invoke(input_data)

            assert not result.success
            assert "rate" in result.error_type.lower()
            assert result.retry_after == 60

    @pytest.mark.asyncio
    async def test_api_authentication_error(self, tool):
        """Test handling of authentication errors."""
        input_data = {"command": "weather", "lat": 40.7128, "lon": -74.0060}

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 401
            mock_get.return_value.__aenter__.return_value = mock_response

            result = await tool.invoke(input_data)

            assert not result.success
            assert "authentication" in result.error_type.lower()

    @pytest.mark.asyncio
    async def test_api_forbidden_error(self, tool):
        """Test handling of forbidden access errors."""
        input_data = {
            "command": "nearby",
            "lat": 40.7128,
            "lon": -74.0060,
            "query": "restaurants",
        }

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 403
            mock_get.return_value.__aenter__.return_value = mock_response

            result = await tool.invoke(input_data)

            assert not result.success
            assert "authentication" in result.error_type.lower()

    @pytest.mark.asyncio
    async def test_no_results_found(self, tool):
        """Test handling when no results are found."""
        input_data = {"command": "geocode", "address": "Nonexistent Address 12345"}

        empty_response = {"results": []}

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=empty_response)
            mock_get.return_value.__aenter__.return_value = mock_response

            result = await tool.invoke(input_data)

            assert not result.success
            assert "external_service" in result.error_type.lower()

    @pytest.mark.asyncio
    async def test_network_timeout_error(self, tool):
        """Test handling of network timeout errors."""
        input_data = {"command": "weather", "lat": 40.7128, "lon": -74.0060}

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_get.side_effect = asyncio.TimeoutError("Request timed out")

            result = await tool.invoke(input_data)

            assert not result.success
            assert "timeout" in result.error_type.lower()

    # ==================== Circuit Breaker Tests ====================

    @pytest.mark.asyncio
    async def test_circuit_breaker_blocks_requests_when_open(self, tool):
        """Test circuit breaker blocks requests when open."""
        # Manually open the circuit breaker
        tool.circuit_breaker.state = CircuitState.OPEN
        tool.circuit_breaker.next_attempt_time = asyncio.get_event_loop().time() + 60

        input_data = {"command": "geocode", "address": "New York, NY"}

        result = await tool.invoke(input_data)

        assert not result.success
        assert "circuit" in result.error_type.lower()

    # ==================== Fallback Response Tests ====================

    @pytest.mark.asyncio
    async def test_fallback_response_for_rate_limit(self, tool):
        """Test fallback response generation for rate limits."""
        error = ToolError(
            "Rate limited", ToolErrorType.RATE_LIMIT_ERROR, retry_after=300
        )

        fallback = await tool._generate_fallback(error)

        assert fallback is not None
        assert fallback["api_status"] == "RATE_LIMITED"
        assert "retry after 300 seconds" in fallback["fallback_message"]

    @pytest.mark.asyncio
    async def test_fallback_response_for_service_error(self, tool):
        """Test fallback response for general service errors."""
        error = ToolError("Service error", ToolErrorType.EXTERNAL_SERVICE_ERROR)

        fallback = await tool._generate_fallback(error)

        assert fallback is not None
        assert fallback["api_status"] == "SERVICE_UNAVAILABLE"
        assert "temporarily unavailable" in fallback["fallback_message"]

    # ==================== Command-Specific Tests ====================

    @pytest.mark.asyncio
    async def test_routes_deduplication(self, tool):
        """Test that routes search deduplicates results."""
        input_data = {
            "command": "routes",
            "lat": 40.7128,
            "lon": -74.0060,
            "radius": 1000,
        }

        # Mock response with duplicate location
        duplicate_response = {
            "results": [
                {
                    "id": "park1",
                    "type": "POI",
                    "score": 8.5,
                    "dist": 0.3,
                    "position": {"lat": 40.7130, "lon": -74.0065},
                    "address": {"freeformAddress": "Central Park"},
                    "poi": {"name": "Central Park", "categories": ["park"]},
                }
            ]
        }

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=duplicate_response)
            mock_get.return_value.__aenter__.return_value = mock_response

            result = await tool.invoke(input_data)

            assert result.success
            # Should deduplicate based on position
            unique_positions = set()
            for location in result.data.locations:
                pos_key = f"{location.position.lat:.6f},{location.position.lon:.6f}"
                assert pos_key not in unique_positions
                unique_positions.add(pos_key)

    # ==================== Integration Test ====================

    @pytest.mark.asyncio
    async def test_full_integration_with_mock_api(self, tool, mock_geocode_response):
        """Test full integration flow with mocked API."""
        input_data = {"command": "geocode", "address": "New York, NY"}

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_geocode_response)
            mock_get.return_value.__aenter__.return_value = mock_response

            result = await tool.invoke(input_data)

            # Verify the complete flow
            assert result.success
            assert hasattr(result, "data")
            assert hasattr(result, "execution_time_ms")
            assert hasattr(result, "tool_name")
            assert hasattr(result, "request_id")

            # Verify data structure
            assert result.data.command == "geocode"
            assert result.data.api_status == "OK"
            assert result.data.geocode_result is not None

            # Verify API was called correctly
            mock_get.assert_called_once()
            call_args = mock_get.call_args
            params = call_args[1]["params"]
            assert "subscription-key" in params
            assert params["query"] == "New York, NY"


# ==================== Fixtures for Integration Testing ====================


@pytest.fixture
def integration_tool():
    """Create tool for integration testing with real subscription key if available."""
    import os

    subscription_key = os.getenv("AZURE_MAPS_SUBSCRIPTION_KEY")
    if subscription_key:
        return AzureMapsTool(subscription_key=subscription_key)
    else:
        pytest.skip(
            "No AZURE_MAPS_SUBSCRIPTION_KEY environment variable set for integration tests"
        )


class TestAzureMapsIntegration:
    """Integration tests that require a real subscription key."""

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_api_geocode(self, integration_tool):
        """Test with real Azure Maps API - geocoding."""
        input_data = {
            "command": "geocode",
            "address": "1600 Pennsylvania Avenue NW, Washington, DC",
        }

        result = await integration_tool.invoke(input_data)

        assert result.success
        assert result.data.geocode_result is not None
        assert result.data.geocode_result.position.lat is not None
        assert result.data.geocode_result.position.lon is not None

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_api_weather(self, integration_tool):
        """Test with real Azure Maps API - weather."""
        input_data = {
            "command": "weather",
            "lat": 40.7128,  # New York City
            "lon": -74.0060,
        }

        result = await integration_tool.invoke(input_data)

        assert result.success
        assert result.data.weather_result is not None
        assert result.data.weather_result.temperature is not None
        assert result.data.weather_result.condition is not None


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
