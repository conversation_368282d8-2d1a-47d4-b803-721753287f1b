"""
Tests for the SleepOptimizationTool.
"""

from datetime import datetime, timedelta

import pytest
import pytest_asyncio

from athlea_langgraph.tools.recovery.sleep_optimization_tool import (
    SleepOptimizationTool,
)
from athlea_langgraph.tools.schemas.recovery_schemas import (
    SleepAssessmentInput,
    SleepOptimizationOutput,
)


@pytest.fixture
def tool() -> SleepOptimizationTool:
    """Fixture to provide an instance of the SleepOptimizationTool."""
    return SleepOptimizationTool()


# Tests for _calculate_sleep_duration
@pytest.mark.parametrize(
    "bedtime, wake_time, expected_duration",
    [
        ("22:00", "06:00", 8.0),
        ("01:00", "09:00", 8.0),
        ("23:30", "07:30", 8.0),
        ("00:00", "08:00", 8.0),
        ("22:00", "22:00", 0.0),
    ],
)
def test_calculate_sleep_duration(
    tool: SleepOptimizationTool, bedtime: str, wake_time: str, expected_duration: float
):
    """Test the _calculate_sleep_duration method."""
    duration = tool._calculate_sleep_duration(bedtime, wake_time)
    assert duration == pytest.approx(expected_duration)


def test_calculate_sleep_duration_invalid_format(tool: SleepOptimizationTool):
    """Test _calculate_sleep_duration with invalid time format, expects default."""
    duration = tool._calculate_sleep_duration("invalid", "07:00")
    assert duration == 8.0


# Tests for _calculate_sleep_efficiency
@pytest.mark.parametrize(
    "bedtime, wake_time, time_to_fall_asleep, night_awakenings, morning_alertness, expected_efficiency",
    [
        ("22:00", "06:00", 15, 0, 7, 96.875),  # 8h TIB, 7.75h TST
        ("23:00", "07:00", 30, 2, 6, 87.5),  # 8h TIB, 7h TST (2*15min for awakenings)
        ("00:00", "08:00", 0, 0, 8, 100.0),  # 8h TIB, 8h TST
        ("22:00", "06:00", 60, 4, 5, 75.0),  # 8h TIB, 6h TST
        (
            "22:00",
            "06:00",
            120,
            0,
            3,
            75.0,
        ),  # 8h TIB, 6h TST (time_to_fall_asleep is 2h)
        (
            "07:00",
            "08:00",
            60,
            0,
            3,
            0.0,
        ),  # 1h TIB, time_to_fall_asleep is 1h -> 0% efficiency
    ],
)
def test_calculate_sleep_efficiency(
    tool: SleepOptimizationTool,
    bedtime: str,
    wake_time: str,
    time_to_fall_asleep: int,
    night_awakenings: int,
    morning_alertness: int,
    expected_efficiency: float,
):
    """Test the _calculate_sleep_efficiency method."""
    assessment_input = SleepAssessmentInput(
        bedtime=bedtime,
        wake_time=wake_time,
        sleep_quality_rating=8,
        time_to_fall_asleep=time_to_fall_asleep,
        night_awakenings=night_awakenings,
        morning_alertness=morning_alertness,
        caffeine_intake_time="14:00",
        alcohol_intake_last_24h="none",
        stress_level_rating=5,
        recent_exercise_intensity="moderate",
        recent_exercise_time="17:00",
        current_medications=None,
        user_goals=None,
    )
    efficiency = tool._calculate_sleep_efficiency(assessment_input)
    assert efficiency == pytest.approx(expected_efficiency, abs=0.01)


# Tests for _assess_chronotype
@pytest.mark.parametrize(
    "bedtime, wake_time, expected_chronotype",
    [
        ("21:00", "05:00", "definite_morning"),  # Midpoint 1.0
        (
            "21:30",
            "06:00",
            "definite_morning",
        ),  # Midpoint 1.75 (Corrected from moderate_morning)
        (
            "22:45",
            "07:15",
            "moderate_morning",
        ),  # Midpoint 3.0  (Corrected from neutral)
        (
            "23:30",
            "08:30",
            "neutral",
        ),  # Midpoint 4.0  (Corrected from moderate_evening)
        ("00:00", "08:00", "neutral"),  # Midpoint 4.0 (Was correct)
        (
            "01:00",
            "09:30",
            "moderate_evening",
        ),  # Midpoint 5.25 (Corrected from definite_evening)
    ],
)
def test_assess_chronotype(
    tool: SleepOptimizationTool, bedtime: str, wake_time: str, expected_chronotype: str
):
    """Test the _assess_chronotype method."""
    chronotype = tool._assess_chronotype(bedtime, wake_time, sleep_quality=8)
    assert chronotype == expected_chronotype


# Tests for _screen_sleep_disorders
@pytest.mark.parametrize(
    "assessment_params, expected_risks",
    [
        # No significant risks
        (
            {
                "bedtime": "22:00",
                "wake_time": "06:00",
                "time_to_fall_asleep": 15,
                "night_awakenings": 0,
                "sleep_quality_rating": 8,
                "morning_alertness": 7,
            },
            [],
        ),
        # Sleep apnea risk indications
        (
            {
                "bedtime": "23:00",
                "wake_time": "07:00",
                "time_to_fall_asleep": 3,
                "night_awakenings": 1,
                "sleep_quality_rating": 4,
                "morning_alertness": 3,
            },
            ["Consider sleep apnea evaluation - rapid sleep onset with poor quality"],
        ),
        # Insomnia symptoms
        (
            {
                "bedtime": "00:00",
                "wake_time": "08:00",
                "time_to_fall_asleep": 45,
                "night_awakenings": 1,
                "sleep_quality_rating": 6,
                "morning_alertness": 6,
            },
            ["Insomnia symptoms present - consider CBT-I techniques"],
        ),
        (
            {
                "bedtime": "22:00",
                "wake_time": "06:00",
                "time_to_fall_asleep": 10,
                "night_awakenings": 3,
                "sleep_quality_rating": 5,
                "morning_alertness": 5,
            },
            ["Insomnia symptoms present - consider CBT-I techniques"],
        ),
        # Delayed sleep phase pattern
        (
            {
                "bedtime": "02:00",
                "wake_time": "10:00",
                "time_to_fall_asleep": 20,
                "night_awakenings": 0,
                "sleep_quality_rating": 7,
                "morning_alertness": 7,
            },
            ["Delayed sleep phase pattern - consider chronotherapy"],
        ),
        # Multiple risks: Apnea + Insomnia (due to night_awakenings) + Delayed Phase
        (
            {
                "bedtime": "03:00",
                "wake_time": "11:00",
                "time_to_fall_asleep": 4,
                "night_awakenings": 3,
                "sleep_quality_rating": 3,
                "morning_alertness": 2,
            },
            [
                "Consider sleep apnea evaluation - rapid sleep onset with poor quality",
                "Insomnia symptoms present - consider CBT-I techniques",
                "Delayed sleep phase pattern - consider chronotherapy",
            ],
        ),
    ],
)
def test_screen_sleep_disorders(
    tool: SleepOptimizationTool, assessment_params: dict, expected_risks: list
):
    """Test the _screen_sleep_disorders method."""
    # Populate default values for fields not directly tested for disorder screening if not in params
    full_assessment_params = {
        "caffeine_intake_time": "14:00",
        "alcohol_intake_last_24h": "none",
        "stress_level_rating": 5,
        "recent_exercise_intensity": "moderate",
        "recent_exercise_time": "17:00",
        **assessment_params,  # Overwrite with specific test params
    }
    assessment = SleepAssessmentInput(**full_assessment_params)
    risks = tool._screen_sleep_disorders(assessment)
    assert sorted(risks) == sorted(
        expected_risks
    )  # Sort for comparison as order doesn't matter


# Test for the main optimize_sleep method (basic)
@pytest.mark.asyncio
async def test_optimize_sleep_basic_flow(tool: SleepOptimizationTool):
    """Test the main optimize_sleep method for basic input and output structure."""
    sample_input = SleepAssessmentInput(
        bedtime="23:00",  # Midpoint 3.0 -> moderate_morning
        wake_time="07:00",
        sleep_quality_rating=7,
        time_to_fall_asleep=20,
        night_awakenings=1,
        morning_alertness=6,
        caffeine_intake_time="15:00",
        alcohol_intake_last_24h="none",
        stress_level_rating=6,
        recent_exercise_intensity="light",
        recent_exercise_time="19:00",
    )

    result = await tool.optimize_sleep(sample_input)

    assert isinstance(result, SleepOptimizationOutput)
    assert result.sleep_efficiency is not None
    assert result.sleep_debt is not None
    assert isinstance(result.optimal_bedtime, str)
    assert isinstance(result.optimal_wake_time, str)
    assert len(result.sleep_hygiene_recommendations) > 0
    assert len(result.environmental_optimizations) > 0
    assert len(result.pre_sleep_routine) > 0
    assert len(result.lifestyle_adjustments) > 0

    # Check for presence of chronotype-specific advice fragments
    chronotype_keywords_found = False
    expected_keywords = [
        "light",
        "schedule",
        "wind down",
        "caffeine",
        "dim lighting",
        "routine",
    ]
    all_recommendations = (
        result.sleep_hygiene_recommendations
        + result.lifestyle_adjustments
        + result.pre_sleep_routine
    )

    # Get the expected chronotype profile for "moderate_morning"
    moderate_morning_profile = tool.chronotype_profiles["moderate_morning"][
        "recommendations"
    ]
    keywords_from_profile = [
        kw.lower() for rec in moderate_morning_profile for kw in rec.split()[:3]
    ]  # check first few words

    combined_search_terms = expected_keywords + keywords_from_profile

    for rec in all_recommendations:
        if any(keyword in rec.lower() for keyword in combined_search_terms):
            chronotype_keywords_found = True
            break
    assert (
        chronotype_keywords_found
    ), "Expected chronotype-specific keywords not found in recommendations"


# TODO: Add tests for _generate_cbt_i_recommendations
# TODO: Add tests for _get_environmental_data (might need mocking if it makes external calls in future)
