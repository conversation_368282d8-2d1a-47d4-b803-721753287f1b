"""
Tests for Mood Pattern Analyzer Tool

Comprehensive test suite covering mood tracking functionality,
pattern analysis, and correlation insights.
"""

import pytest
from datetime import datetime

from athlea_langgraph.tools.mental.mood_pattern_analyzer import MoodPatternAnalyzer
from athlea_langgraph.tools.schemas.mental_schemas import (
    MoodEntryInput,
    MoodPatternAnalysisInput,
    MoodPatternAnalysisOutput,
    MoodState,
)


class TestMoodPatternAnalyzer:
    """Test suite for Mood Pattern Analyzer Tool."""

    @pytest.fixture
    def mood_analyzer(self):
        """Create mood analyzer instance."""
        return MoodPatternAnalyzer()

    @pytest.fixture
    def positive_mood_entry(self):
        """Positive mood entry for testing."""
        return MoodEntryInput(
            user_id="user_123",
            mood_rating=8,
            mood_descriptors=["happy", "energetic", "confident"],
            activities_before=["morning run", "good breakfast", "team meeting"],
            social_context="with supportive colleagues",
            physical_state="energetic and healthy",
            time_of_day="10:00",
            notes="Great start to the day",
        )

    @pytest.fixture
    def negative_mood_entry(self):
        """Negative mood entry for testing."""
        return MoodEntryInput(
            user_id="user_456",
            mood_rating=3,
            mood_descriptors=["sad", "overwhelmed", "tired"],
            activities_before=["stressful meeting", "bad news", "skip lunch"],
            social_context="alone and isolated",
            physical_state="exhausted and tense",
            time_of_day="15:30",
            notes="Difficult afternoon",
        )

    @pytest.fixture
    def neutral_mood_entry(self):
        """Neutral mood entry for testing."""
        return MoodEntryInput(
            user_id="user_789",
            mood_rating=5,
            mood_descriptors=["okay", "neutral"],
            activities_before=["routine work", "normal commute"],
            social_context="with familiar people",
            physical_state="normal energy",
            time_of_day="12:00",
        )

    @pytest.fixture
    def weekly_analysis_input(self):
        """Weekly pattern analysis input."""
        return MoodPatternAnalysisInput(
            user_id="user_123",
            analysis_period_days=7,
            include_correlations=True,
            focus_areas=["work_stress", "social_interactions"],
        )

    @pytest.fixture
    def monthly_analysis_input(self):
        """Monthly pattern analysis input."""
        return MoodPatternAnalysisInput(
            user_id="user_456",
            analysis_period_days=30,
            include_correlations=True,
            focus_areas=["sleep", "exercise", "mood_triggers"],
        )

    @pytest.mark.asyncio
    async def test_log_positive_mood_entry(self, mood_analyzer, positive_mood_entry):
        """Test logging positive mood entry."""
        result = await mood_analyzer.log_mood_entry(positive_mood_entry)

        assert result["status"] == "logged"
        assert "positive" in result["mood_category"]
        assert "positive" in result["descriptor_sentiment"]
        assert len(result["immediate_insights"]) > 0
        assert result["timestamp"]

    @pytest.mark.asyncio
    async def test_log_negative_mood_entry(self, mood_analyzer, negative_mood_entry):
        """Test logging negative mood entry."""
        result = await mood_analyzer.log_mood_entry(negative_mood_entry)

        assert result["status"] == "logged"
        assert "negative" in result["mood_category"]
        assert "negative" in result["descriptor_sentiment"]
        assert any(
            "self-care" in insight
            for insight in result["immediate_insights"].split("; ")
        )

    @pytest.mark.asyncio
    async def test_log_neutral_mood_entry(self, mood_analyzer, neutral_mood_entry):
        """Test logging neutral mood entry."""
        result = await mood_analyzer.log_mood_entry(neutral_mood_entry)

        assert result["status"] == "logged"
        assert "neutral" in result["mood_category"]

    @pytest.mark.asyncio
    async def test_weekly_pattern_analysis(self, mood_analyzer, weekly_analysis_input):
        """Test weekly mood pattern analysis."""
        result = await mood_analyzer.analyze_mood_patterns(weekly_analysis_input)

        assert isinstance(result, MoodPatternAnalysisOutput)
        assert 1.0 <= result.average_mood <= 10.0
        assert result.mood_trend
        assert result.mood_volatility
        assert len(result.mood_triggers_positive) > 0
        assert len(result.mood_triggers_negative) > 0
        assert len(result.recommendations) > 0
        assert len(result.insights) > 0

    @pytest.mark.asyncio
    async def test_monthly_pattern_analysis(
        self, mood_analyzer, monthly_analysis_input
    ):
        """Test monthly mood pattern analysis."""
        result = await mood_analyzer.analyze_mood_patterns(monthly_analysis_input)

        assert isinstance(result, MoodPatternAnalysisOutput)
        assert result.time_patterns
        assert (
            "activity_correlations" in result.activity_correlations
            or len(result.activity_correlations) > 0
        )

    def test_categorize_mood(self, mood_analyzer):
        """Test mood categorization."""
        assert mood_analyzer._categorize_mood(1) == MoodState.VERY_NEGATIVE
        assert mood_analyzer._categorize_mood(3) == MoodState.NEGATIVE
        assert mood_analyzer._categorize_mood(5) == MoodState.NEUTRAL
        assert mood_analyzer._categorize_mood(7) == MoodState.POSITIVE
        assert mood_analyzer._categorize_mood(9) == MoodState.VERY_POSITIVE

    def test_analyze_mood_descriptors(self, mood_analyzer):
        """Test mood descriptor analysis."""
        # Test positive descriptors
        positive_descriptors = ["happy", "joyful", "energetic"]
        sentiment = mood_analyzer._analyze_mood_descriptors(positive_descriptors)
        assert "positive" in sentiment

        # Test negative descriptors
        negative_descriptors = ["sad", "anxious", "depressed"]
        sentiment = mood_analyzer._analyze_mood_descriptors(negative_descriptors)
        assert "negative" in sentiment

        # Test mixed descriptors
        mixed_descriptors = ["happy", "worried"]
        sentiment = mood_analyzer._analyze_mood_descriptors(mixed_descriptors)
        assert "mixed" in sentiment or "neutral" in sentiment

        # Test empty descriptors
        empty_descriptors = []
        sentiment = mood_analyzer._analyze_mood_descriptors(empty_descriptors)
        assert sentiment == "neutral"

    def test_generate_immediate_insights(self, mood_analyzer, positive_mood_entry):
        """Test immediate insight generation."""
        mood_category = MoodState.POSITIVE
        insights = mood_analyzer._generate_immediate_insights(
            positive_mood_entry, mood_category
        )

        assert isinstance(insights, list)
        assert len(insights) <= 3  # Should be limited
        assert any("positive" in insight.lower() for insight in insights)

    def test_analyze_activity_mood_correlation(self, mood_analyzer):
        """Test activity-mood correlation analysis."""
        # Test positive mood with exercise
        positive_activities = ["morning run", "gym workout"]
        insights = mood_analyzer._analyze_activity_mood_correlation(
            positive_activities, 8
        )
        assert any("physical activity" in insight.lower() for insight in insights)

        # Test negative mood with work stress
        negative_activities = ["stressful meeting", "overtime work"]
        insights = mood_analyzer._analyze_activity_mood_correlation(
            negative_activities, 3
        )
        assert any("work" in insight.lower() for insight in insights)

    def test_analyze_time_patterns(self, mood_analyzer):
        """Test time-based pattern analysis."""
        # Test morning time with low mood
        morning_insights = mood_analyzer._analyze_time_patterns("08:00", 3)
        assert any("morning" in insight.lower() for insight in morning_insights)

        # Test evening time with high mood
        evening_insights = mood_analyzer._analyze_time_patterns("19:00", 8)
        assert any("evening" in insight.lower() for insight in evening_insights)

        # Test invalid time format
        invalid_insights = mood_analyzer._analyze_time_patterns("invalid", 5)
        assert isinstance(invalid_insights, list)  # Should handle gracefully

    def test_simulate_mood_pattern_analysis(self, mood_analyzer, weekly_analysis_input):
        """Test mood pattern analysis simulation."""
        result = mood_analyzer._simulate_mood_pattern_analysis(weekly_analysis_input)

        assert isinstance(result, MoodPatternAnalysisOutput)
        assert 1.0 <= result.average_mood <= 10.0
        assert result.mood_trend
        assert len(result.best_mood_days) > 0
        assert len(result.challenging_mood_days) > 0

    @pytest.mark.asyncio
    async def test_edge_case_extreme_positive_mood(self, mood_analyzer):
        """Test with extremely positive mood."""
        extreme_positive = MoodEntryInput(
            user_id="user_extreme_pos",
            mood_rating=10,
            mood_descriptors=["ecstatic", "amazing", "perfect"],
            activities_before=["life-changing news", "celebration"],
            social_context="surrounded by loved ones",
            time_of_day="14:00",
        )

        result = await mood_analyzer.log_mood_entry(extreme_positive)
        assert result["status"] == "logged"
        assert "very_positive" in result["mood_category"]

    @pytest.mark.asyncio
    async def test_edge_case_extreme_negative_mood(self, mood_analyzer):
        """Test with extremely negative mood."""
        extreme_negative = MoodEntryInput(
            user_id="user_extreme_neg",
            mood_rating=1,
            mood_descriptors=["hopeless", "devastated", "terrible"],
            activities_before=["bad news", "conflict"],
            social_context="alone",
            time_of_day="22:00",
        )

        result = await mood_analyzer.log_mood_entry(extreme_negative)
        assert result["status"] == "logged"
        assert "very_negative" in result["mood_category"]

    @pytest.mark.asyncio
    async def test_mood_entry_with_no_descriptors(self, mood_analyzer):
        """Test mood entry with no descriptors."""
        no_descriptors = MoodEntryInput(
            user_id="user_minimal",
            mood_rating=6,
            mood_descriptors=[],
            time_of_day="12:00",
        )

        result = await mood_analyzer.log_mood_entry(no_descriptors)
        assert result["status"] == "logged"
        assert result["descriptor_sentiment"] == "neutral"

    @pytest.mark.asyncio
    async def test_mood_entry_with_no_activities(self, mood_analyzer):
        """Test mood entry with no activities."""
        no_activities = MoodEntryInput(
            user_id="user_no_activities",
            mood_rating=5,
            mood_descriptors=["okay"],
            activities_before=[],
            time_of_day="15:00",
        )

        result = await mood_analyzer.log_mood_entry(no_activities)
        assert result["status"] == "logged"

    def test_long_term_analysis_period(self, mood_analyzer):
        """Test long-term pattern analysis."""
        long_term_input = MoodPatternAnalysisInput(
            user_id="user_longterm", analysis_period_days=90, include_correlations=True
        )

        result = mood_analyzer._simulate_mood_pattern_analysis(long_term_input)
        assert "extended period" in result.mood_trend.lower()
        assert result.average_mood > 6.0  # Should show stability over time

    def test_short_term_analysis_period(self, mood_analyzer):
        """Test short-term pattern analysis."""
        short_term_input = MoodPatternAnalysisInput(
            user_id="user_shortterm", analysis_period_days=3, include_correlations=False
        )

        result = mood_analyzer._simulate_mood_pattern_analysis(short_term_input)
        assert (
            "recent" in result.mood_trend.lower()
            or "baseline" in result.mood_trend.lower()
        )

    def test_correlation_analysis_inclusion(self, mood_analyzer):
        """Test correlation analysis when requested."""
        with_correlations = MoodPatternAnalysisInput(
            user_id="user_corr", analysis_period_days=14, include_correlations=True
        )

        result = mood_analyzer._simulate_mood_pattern_analysis(with_correlations)
        assert len(result.activity_correlations) > 0
        assert any("correlation" in insight.lower() for insight in result.insights)

    def test_validation_constraints(self):
        """Test input validation constraints."""
        # Test valid mood entry
        valid_entry = MoodEntryInput(
            user_id="test",
            mood_rating=5,
            mood_descriptors=["okay"],
            time_of_day="12:00",
        )
        assert valid_entry.mood_rating == 5

        # Test invalid mood rating (should raise validation error)
        with pytest.raises(ValueError):
            MoodEntryInput(
                user_id="test",
                mood_rating=11,  # Invalid: > 10
                mood_descriptors=["okay"],
                time_of_day="12:00",
            )

    def test_tool_properties(self, mood_analyzer):
        """Test tool properties and configuration."""
        assert mood_analyzer.domain == "mental_training"
        assert mood_analyzer.name == "mood_pattern_analyzer"
        assert mood_analyzer.description
        assert hasattr(mood_analyzer, "mood_categories")
        assert hasattr(mood_analyzer, "positive_mood_keywords")
        assert hasattr(mood_analyzer, "negative_mood_keywords")
        assert hasattr(mood_analyzer, "activity_categories")
        assert hasattr(mood_analyzer, "time_patterns")
        assert hasattr(mood_analyzer, "mood_strategies")
