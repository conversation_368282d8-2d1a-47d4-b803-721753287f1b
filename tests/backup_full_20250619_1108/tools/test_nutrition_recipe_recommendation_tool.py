"""
Tests for the NutritionRecipeRecommendationTool.
"""

from unittest.mock import AsyncMock, patch

import pytest

from athlea_langgraph.tools.nutrition.recipe_recommendation_tool import (
    NutritionRecipeRecommendationTool,
    NutritionRecommendationInput,
    Recipe,
    RecipeSearchInput,
    RecipeSearchOutput,
)


@pytest.fixture
def recipe_tool() -> NutritionRecipeRecommendationTool:
    """Fixture to provide an instance of the NutritionRecipeRecommendationTool."""
    return NutritionRecipeRecommendationTool()


def test_load_fallback_recipes(recipe_tool: NutritionRecipeRecommendationTool):
    """Test that fallback recipes are loaded properly."""
    fallback_recipes = recipe_tool.fallback_recipes
    assert isinstance(fallback_recipes, list)
    assert len(fallback_recipes) > 0

    # Check structure of first recipe
    first_recipe = fallback_recipes[0]
    assert "label" in first_recipe
    assert "calories_per_serving" in first_recipe
    assert "ingredients" in first_recipe
    assert isinstance(first_recipe["ingredients"], list)


@pytest.mark.parametrize(
    "query, expected_matches",
    [
        ("chicken", 1),  # Should match "Grilled Chicken Breast"
        ("oats", 1),  # Should match "Overnight Oats"
        ("quinoa", 1),  # Should match "Quinoa Buddha Bowl" - updated from "breakfast"
        (
            "protein",
            1,
        ),  # Should match high-protein recipes - updated from "high protein"
        ("nonexistent food", 0),  # Should match nothing
    ],
)
def test_search_fallback_database(
    recipe_tool: NutritionRecipeRecommendationTool, query: str, expected_matches: int
):
    """Test the fallback database search functionality."""
    search_input = RecipeSearchInput(query=query, max_results=10)
    result = recipe_tool._search_fallback_database(search_input)

    assert isinstance(result, RecipeSearchOutput)
    assert result.search_query == query
    assert len(result.recipes) == expected_matches
    assert result.total_found == expected_matches
    assert "fallback_database" in result.applied_filters.get("source", "")


def test_search_fallback_database_with_filters(
    recipe_tool: NutritionRecipeRecommendationTool,
):
    """Test fallback database search with filters."""
    # Test calorie filter
    search_input = RecipeSearchInput(
        query="chicken", max_calories=300  # Lower than chicken recipe calories (350)
    )
    result = recipe_tool._search_fallback_database(search_input)
    assert len(result.recipes) == 0  # Should be filtered out

    # Test health label filter
    search_input = RecipeSearchInput(
        query="chicken", health_labels=["gluten-free"]  # Chicken recipe has this label
    )
    result = recipe_tool._search_fallback_database(search_input)
    assert len(result.recipes) == 1


def test_search_fallback_database_max_results(
    recipe_tool: NutritionRecipeRecommendationTool,
):
    """Test that max_results is respected."""
    search_input = RecipeSearchInput(
        query="", max_results=1
    )  # Empty query should match all
    result = recipe_tool._search_fallback_database(search_input)
    assert len(result.recipes) <= 1


@pytest.mark.asyncio
async def test_search_recipes_fallback(recipe_tool: NutritionRecipeRecommendationTool):
    """Test search_recipes method using fallback database."""
    # Ensure we use fallback by clearing API credentials
    with (
        patch.object(recipe_tool, "app_id", ""),
        patch.object(recipe_tool, "app_key", ""),
    ):

        search_input = RecipeSearchInput(query="chicken", max_results=5)
        result = await recipe_tool.search_recipes(search_input)

        assert isinstance(result, RecipeSearchOutput)
        assert result.search_query == "chicken"
        assert len(result.recipes) >= 0


@pytest.mark.asyncio
async def test_search_recipes_api_error_fallback(
    recipe_tool: NutritionRecipeRecommendationTool,
):
    """Test that API errors fall back to database."""
    # Mock API method to raise an exception
    with patch.object(
        recipe_tool, "_search_edamam_api", side_effect=Exception("API Error")
    ):
        search_input = RecipeSearchInput(query="chicken", max_results=5)
        result = await recipe_tool.search_recipes(search_input)

        # Should still return results from fallback
        assert isinstance(result, RecipeSearchOutput)


def test_parse_edamam_response(recipe_tool: NutritionRecipeRecommendationTool):
    """Test parsing of Edamam API response."""
    # Mock Edamam response structure
    mock_response = {
        "hits": [
            {
                "recipe": {
                    "label": "Test Recipe",
                    "source": "Test Source",
                    "url": "https://example.com",
                    "yield": 2,
                    "calories": 400,
                    "totalTime": 30,
                    "cuisineType": ["American"],
                    "mealType": ["dinner"],
                    "dishType": ["main course"],
                    "ingredientLines": ["1 cup flour", "2 eggs"],
                    "healthLabels": ["vegetarian"],
                    "dietLabels": ["balanced"],
                    "image": "https://example.com/image.jpg",
                    "totalNutrients": {
                        "PROCNT": {"quantity": 20},
                        "CHOCDF": {"quantity": 40},
                        "FAT": {"quantity": 10},
                        "FIBTG": {"quantity": 5},
                    },
                }
            }
        ]
    }

    recipes = recipe_tool._parse_edamam_response(mock_response)

    assert len(recipes) == 1
    recipe = recipes[0]
    assert isinstance(recipe, Recipe)
    assert recipe.label == "Test Recipe"
    assert recipe.source == "Test Source"
    assert recipe.yield_servings == 2  # This should work correctly
    assert recipe.calories == 400
    assert recipe.calories_per_serving == 200  # 400 / 2
    assert recipe.protein_per_serving == 10  # 20 / 2
    assert recipe.carbs_per_serving == 20  # 40 / 2
    assert recipe.fat_per_serving == 5  # 10 / 2


def test_parse_edamam_response_missing_yield(
    recipe_tool: NutritionRecipeRecommendationTool,
):
    """Test parsing when yield is missing or zero."""
    mock_response = {
        "hits": [
            {
                "recipe": {
                    "label": "Test Recipe",
                    "source": "Test Source",
                    "url": "https://example.com",
                    "calories": 400,
                    # yield is missing
                    "totalNutrients": {"PROCNT": {"quantity": 20}},
                }
            }
        ]
    }

    recipes = recipe_tool._parse_edamam_response(mock_response)

    assert len(recipes) == 1
    recipe = recipes[0]
    assert recipe.yield_servings == 1  # Should default to 1
    assert recipe.calories_per_serving == 400  # 400 / 1


def test_parse_edamam_response_zero_yield(
    recipe_tool: NutritionRecipeRecommendationTool,
):
    """Test parsing when yield is zero."""
    mock_response = {
        "hits": [
            {
                "recipe": {
                    "label": "Test Recipe",
                    "source": "Test Source",
                    "url": "https://example.com",
                    "yield": 0,  # Zero yield
                    "calories": 400,
                    "totalNutrients": {"PROCNT": {"quantity": 20}},
                }
            }
        ]
    }

    recipes = recipe_tool._parse_edamam_response(mock_response)

    assert len(recipes) == 1
    recipe = recipes[0]
    # The tool converts 0 yield to 1 for division safety
    assert recipe.yield_servings == 1  # Tool sets servings = 1 when yield is 0
    assert recipe.calories_per_serving == 400  # 400 / 1


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "meal_type, calorie_target, expected_query_contains",
    [
        ("breakfast", 300, "breakfast"),
        ("lunch", 500, "lunch"),
        ("dinner", 600, "dinner"),
        ("snack", 200, "snack"),
    ],
)
async def test_get_personalized_recommendations(
    recipe_tool: NutritionRecipeRecommendationTool,
    meal_type: str,
    calorie_target: int,
    expected_query_contains: str,
):
    """Test personalized recipe recommendations."""
    # Use fallback database for testing
    with (
        patch.object(recipe_tool, "app_id", ""),
        patch.object(recipe_tool, "app_key", ""),
    ):

        recommendation_input = NutritionRecommendationInput(
            calorie_target=calorie_target,
            meal_type=meal_type,
            diet_preferences=["balanced"],
            health_restrictions=["gluten-free"],
        )

        result = await recipe_tool.get_personalized_recommendations(
            recommendation_input
        )

        assert isinstance(result, RecipeSearchOutput)
        assert expected_query_contains in result.search_query


@pytest.mark.asyncio
async def test_get_personalized_recommendations_with_cuisine(
    recipe_tool: NutritionRecipeRecommendationTool,
):
    """Test personalized recommendations with cuisine preference."""
    with (
        patch.object(recipe_tool, "app_id", ""),
        patch.object(recipe_tool, "app_key", ""),
    ):

        recommendation_input = NutritionRecommendationInput(
            calorie_target=400, meal_type="dinner", cuisine_preference="Italian"
        )

        result = await recipe_tool.get_personalized_recommendations(
            recommendation_input
        )

        # The search_query is built as "dinner Italian" but then replaced with personalized format
        assert "dinner" in result.search_query.lower()
        # Since personalized method overwrites search_query, check that it contains "personalized"
        assert "personalized" in result.search_query.lower()


@pytest.mark.asyncio
async def test_recommend_recipes_legacy_method(
    recipe_tool: NutritionRecipeRecommendationTool,
):
    """Test the legacy recommend_recipes method."""
    # Test with dict input for personalized recommendations
    input_dict = {
        "calorie_target": 350,
        "meal_type": "lunch",
        "diet_preferences": ["high-protein"],
    }

    # Use fallback for testing
    with (
        patch.object(recipe_tool, "app_id", ""),
        patch.object(recipe_tool, "app_key", ""),
    ):

        result = await recipe_tool.recommend_recipes(input_dict)

        # The legacy method now returns RecipeSearchOutput directly, not a dict
        assert isinstance(result, RecipeSearchOutput)
        assert "lunch" in result.search_query.lower()
        assert "personalized" in result.search_query.lower()


def test_recipe_model_structure():
    """Test the Recipe Pydantic model."""
    recipe_data = {
        "label": "Test Recipe",
        "source": "Test Source",
        "url": "https://example.com",
        "yield": 2,  # Using alias
        "calories": 400,
        "total_time": 30,
        "cuisine_type": ["American"],
        "meal_type": ["dinner"],
        "dish_type": ["main course"],
        "ingredients": ["ingredient 1", "ingredient 2"],
        "health_labels": ["vegetarian"],
        "diet_labels": ["balanced"],
        "image_url": "https://example.com/image.jpg",
        "calories_per_serving": 200,
        "protein_per_serving": 25,
        "carbs_per_serving": 30,
        "fat_per_serving": 8,
        "fiber_per_serving": 4,
    }

    recipe = Recipe(**recipe_data)

    assert recipe.label == "Test Recipe"
    assert recipe.source == "Test Source"
    assert recipe.url == "https://example.com"
    assert recipe.yield_servings == 2  # Field name after alias resolution
    assert recipe.calories == 400
    assert recipe.total_time == 30
    assert recipe.cuisine_type == ["American"]
    assert recipe.meal_type == ["dinner"]
    assert recipe.dish_type == ["main course"]
    assert recipe.ingredients == ["ingredient 1", "ingredient 2"]
    assert recipe.health_labels == ["vegetarian"]
    assert recipe.diet_labels == ["balanced"]
    assert recipe.image_url == "https://example.com/image.jpg"
    assert recipe.calories_per_serving == 200
    assert recipe.protein_per_serving == 25
    assert recipe.carbs_per_serving == 30
    assert recipe.fat_per_serving == 8
    assert recipe.fiber_per_serving == 4


def test_recipe_search_input_validation():
    """Test RecipeSearchInput validation."""
    # Valid input
    search_input = RecipeSearchInput(
        query="chicken breast",
        diet_type=["high-protein"],
        health_labels=["gluten-free"],
        cuisine_type=["American"],
        meal_type=["lunch"],
        max_calories=500,
        min_protein=30,
        max_results=10,
    )

    assert search_input.query == "chicken breast"
    assert search_input.diet_type == ["high-protein"]
    assert search_input.health_labels == ["gluten-free"]
    assert search_input.cuisine_type == ["American"]
    assert search_input.meal_type == ["lunch"]
    assert search_input.max_calories == 500
    assert search_input.min_protein == 30
    assert search_input.max_results == 10


def test_nutrition_recommendation_input_validation():
    """Test NutritionRecommendationInput validation."""
    recommendation_input = NutritionRecommendationInput(
        calorie_target=400,
        diet_preferences=["balanced"],
        health_restrictions=["dairy-free"],
        meal_type="lunch",
        cuisine_preference="Mediterranean",
    )

    assert recommendation_input.calorie_target == 400
    assert recommendation_input.diet_preferences == ["balanced"]
    assert recommendation_input.health_restrictions == ["dairy-free"]
    assert recommendation_input.meal_type == "lunch"
    assert recommendation_input.cuisine_preference == "Mediterranean"


@pytest.mark.asyncio
async def test_search_recipes_with_api_credentials(
    recipe_tool: NutritionRecipeRecommendationTool,
):
    """Test search_recipes with API credentials (mocked)."""
    # Mock the API call
    mock_response = {
        "hits": [
            {
                "recipe": {
                    "label": "API Recipe",
                    "source": "API Source",
                    "url": "https://api-example.com",
                    "yield": 1,
                    "calories": 300,
                    "totalNutrients": {},
                }
            }
        ]
    }

    with (
        patch.object(recipe_tool, "app_id", "test_id"),
        patch.object(recipe_tool, "app_key", "test_key"),
        patch("aiohttp.ClientSession.get") as mock_get,
    ):

        mock_response_obj = AsyncMock()
        mock_response_obj.status = 200
        mock_response_obj.json = AsyncMock(return_value=mock_response)
        mock_get.return_value.__aenter__.return_value = mock_response_obj

        search_input = RecipeSearchInput(query="test", max_results=5)
        result = await recipe_tool.search_recipes(search_input)

        assert isinstance(result, RecipeSearchOutput)
        assert len(result.recipes) == 1
        assert result.recipes[0].label == "API Recipe"
