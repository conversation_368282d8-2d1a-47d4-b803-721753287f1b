#!/usr/bin/env python3
"""
Test script to verify Python backend streaming functionality.
Mimics the behavior of the Next.js coaching-python route.
"""

import asyncio
import json
import sys
from datetime import datetime
from typing import AsyncGenerator

import aiohttp


class StreamingTester:
    """Test client for the Python backend streaming API."""

    def __init__(self, backend_url: str = "http://localhost:8000"):
        self.backend_url = backend_url

    async def test_streaming_response(
        self,
        message: str,
        thread_id: str,
        user_id: str,
        single_coach: str = None,
    ) -> None:
        """Test streaming response from the backend."""
        print(f"\n🚀 TEST: Starting streaming test")
        print(f"📊 TEST: Request parameters:")
        print(f"  - Message: {message}")
        print(f"  - Thread ID: {thread_id}")
        print(f"  - User ID: {user_id}")
        print(f"  - Single Coach: {single_coach or 'None'}")
        print(f"  - Backend URL: {self.backend_url}")
        print("-" * 80)

        # Construct URL with parameters (mimicking the route.ts behavior)
        params = {
            "message": message,
            "threadId": thread_id,
            "userId": user_id,
        }
        if single_coach:
            params["singleCoach"] = single_coach

        url = f"{self.backend_url}/api/coaching"

        try:
            async with aiohttp.ClientSession() as session:
                print(f"\n🔗 TEST: Connecting to {url}")
                print(f"📤 TEST: Query parameters: {params}")

                async with session.get(
                    url,
                    params=params,
                    headers={"Accept": "text/event-stream"},
                ) as response:
                    print(f"\n📡 TEST: Response status: {response.status}")
                    print(f"📡 TEST: Response headers:")
                    for key, value in response.headers.items():
                        print(f"  - {key}: {value}")
                    print("-" * 80)

                    if response.status != 200:
                        text = await response.text()
                        print(f"❌ TEST: Error response: {text}")
                        return

                    # Process the SSE stream
                    event_count = 0
                    agent_starts = []
                    tokens_by_agent = {}
                    tool_results = []

                    print("\n🌊 TEST: Starting to process SSE stream...\n")

                    async for line in response.content:
                        line = line.decode("utf-8").strip()

                        if line.startswith("event:"):
                            event_type = line[6:].strip()
                        elif line.startswith("data:") and line != "data:":
                            event_count += 1
                            data_str = line[5:].strip()

                            try:
                                data = json.loads(data_str)

                                # Process different event types
                                if event_type == "agent_start":
                                    agent = data.get("agent", "unknown")
                                    agent_starts.append(agent)
                                    print(
                                        f"🚀 EVENT [{event_count}]: Agent Start - {agent}"
                                    )

                                elif event_type == "token":
                                    agent = data.get("agent", "unknown")
                                    content = data.get("content", "")

                                    if agent not in tokens_by_agent:
                                        tokens_by_agent[agent] = []
                                    tokens_by_agent[agent].append(content)

                                    # Print first few tokens and then summarize
                                    if len(tokens_by_agent[agent]) <= 3:
                                        print(
                                            f"💬 EVENT [{event_count}]: Token from {agent} - '{content[:50]}{'...' if len(content) > 50 else ''}'"
                                        )
                                    elif len(tokens_by_agent[agent]) == 4:
                                        print(
                                            f"📝 EVENT [{event_count}]: {agent} - (continuing to stream...)"
                                        )

                                elif event_type == "tool_result":
                                    tool_name = data.get("toolName", "unknown")
                                    agent = data.get("agent", "unknown")
                                    tool_results.append(
                                        {
                                            "tool": tool_name,
                                            "agent": agent,
                                            "event_num": event_count,
                                        }
                                    )
                                    print(
                                        f"🔧 EVENT [{event_count}]: Tool Result - {tool_name} (called by {agent})"
                                    )

                                elif event_type == "complete":
                                    print(f"✅ EVENT [{event_count}]: Stream Complete")
                                    break

                                elif event_type == "error":
                                    error_msg = data.get("message", "Unknown error")
                                    print(
                                        f"❌ EVENT [{event_count}]: Error - {error_msg}"
                                    )
                                    break

                                else:
                                    print(
                                        f"❓ EVENT [{event_count}]: Unknown event type: {event_type}"
                                    )

                            except json.JSONDecodeError as e:
                                print(
                                    f"⚠️  EVENT [{event_count}]: Failed to parse JSON: {e}"
                                )
                                print(f"    Raw data: {data_str[:100]}...")

                    print("\n" + "=" * 80)
                    print("📊 SUMMARY:")
                    print(f"  - Total events processed: {event_count}")
                    print(f"  - Agents started: {agent_starts}")
                    print(
                        f"  - Agents that sent tokens: {list(tokens_by_agent.keys())}"
                    )
                    print(f"  - Tools used: {len(tool_results)}")

                    for agent, tokens in tokens_by_agent.items():
                        full_response = "".join(tokens)
                        print(
                            f"\n📝 {agent} Response ({len(tokens)} tokens, {len(full_response)} chars):"
                        )
                        print(
                            f"  {full_response[:200]}{'...' if len(full_response) > 200 else ''}"
                        )

                    if tool_results:
                        print(f"\n🔧 Tools Used:")
                        for tool in tool_results:
                            print(
                                f"  - {tool['tool']} (by {tool['agent']}, event #{tool['event_num']})"
                            )

        except Exception as e:
            print(f"\n❌ TEST: Exception occurred: {type(e).__name__}: {e}")
            import traceback

            traceback.print_exc()


async def main():
    """Run the streaming tests."""
    tester = StreamingTester()

    # Test cases
    test_cases = [
        {
            "name": "Simple Cycling Question",
            "message": "How can I start cycling training?",
            "thread_id": "test-thread-001",
            "user_id": "test-user-001",
            "single_coach": None,
        },
        {
            "name": "Direct Coach Request",
            "message": "What are the best strength exercises for beginners?",
            "thread_id": "test-thread-002",
            "user_id": "test-user-001",
            "single_coach": "strength",
        },
        {
            "name": "Complex Multi-Coach Query",
            "message": "I want to improve my cycling performance and need nutrition advice for long rides",
            "thread_id": "test-thread-003",
            "user_id": "test-user-001",
            "single_coach": None,
        },
    ]

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'#' * 80}")
        print(f"# TEST CASE {i}: {test_case['name']}")
        print(f"{'#' * 80}")

        await tester.test_streaming_response(
            message=test_case["message"],
            thread_id=test_case["thread_id"],
            user_id=test_case["user_id"],
            single_coach=test_case.get("single_coach"),
        )

        if i < len(test_cases):
            print(f"\n⏳ Waiting 2 seconds before next test...")
            await asyncio.sleep(2)

    print(f"\n{'=' * 80}")
    print("✅ All tests completed!")


if __name__ == "__main__":
    print("🧪 Python Backend Streaming Test")
    print("=" * 80)
    print(f"Started at: {datetime.now().isoformat()}")

    asyncio.run(main())
