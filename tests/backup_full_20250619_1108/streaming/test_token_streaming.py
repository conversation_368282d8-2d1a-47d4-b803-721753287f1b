#!/usr/bin/env python3
"""
Token Streaming Verification Test

This test specifically checks if we're getting true token-by-token streaming
by measuring the time between tokens and their individual sizes.
"""

import asyncio
import time
from datetime import datetime

import aiohttp


async def test_token_streaming():
    """Test that verifies true token-by-token streaming."""

    print("🧪 Token Streaming Verification Test")
    print("=" * 60)

    url = "http://localhost:8000/api/coaching"
    params = {
        "message": "Explain cycling training basics step by step",
        "threadId": f"test-{int(time.time())}",
        "userId": "test_user_token_streaming",
    }

    start_time = time.time()
    first_token_time = None
    token_times = []
    token_sizes = []
    token_contents = []

    async with aiohttp.ClientSession() as session:
        async with session.get(url, params=params) as response:
            if response.status != 200:
                print(f"❌ Error: HTTP {response.status}")
                return

            print(f"📡 Connected to streaming endpoint")
            print(f"⏳ Waiting for tokens...\n")

            async for line in response.content:
                line_text = line.decode("utf-8").strip()

                if not line_text:
                    continue

                # Parse SSE events
                if line_text.startswith("event: "):
                    event_type = line_text.split("event: ")[1].strip()
                    continue

                if line_text.startswith("data: "):
                    current_time = time.time()

                    try:
                        import json

                        data = json.loads(line_text[6:])  # Remove "data: " prefix

                        if (
                            event_type == "token"
                            and "content" in data
                            and "agent" in data
                        ):
                            token_content = data["content"]
                            agent = data["agent"]

                            if first_token_time is None:
                                first_token_time = current_time
                                print(
                                    f"🟢 First token received after {current_time - start_time:.2f}s"
                                )
                                print(f"📡 Agent: {agent}")
                                print("=" * 60)

                            # Record timing and size data
                            elapsed = current_time - start_time
                            token_times.append(elapsed)
                            token_sizes.append(len(token_content))
                            token_contents.append(token_content)

                            # Show individual tokens (first 20 for brevity)
                            if len(token_contents) <= 20:
                                print(
                                    f"Token {len(token_contents):3d}: '{token_content}' ({len(token_content)} chars, +{elapsed:.3f}s)"
                                )
                            elif len(token_contents) == 21:
                                print("... (showing only first 20 tokens)")

                        elif event_type == "complete":
                            print(f"\n✅ Streaming completed")
                            break

                    except json.JSONDecodeError:
                        continue

    total_time = time.time() - start_time

    print("=" * 60)
    print("📊 STREAMING ANALYSIS RESULTS")
    print("=" * 60)

    if token_times:
        print(f"🕐 Total time: {total_time:.2f}s")
        print(f"⚡ Time to first token: {first_token_time - start_time:.2f}s")
        print(f"🔢 Total tokens received: {len(token_times)}")
        print(f"📏 Average token size: {sum(token_sizes) / len(token_sizes):.1f} chars")
        print(
            f"🚀 Average time between tokens: {(token_times[-1] - token_times[0]) / (len(token_times) - 1):.3f}s"
        )

        # Check for true streaming characteristics
        small_tokens = sum(1 for size in token_sizes if size <= 10)
        print(
            f"🎯 Small tokens (≤10 chars): {small_tokens}/{len(token_sizes)} ({small_tokens/len(token_sizes)*100:.1f}%)"
        )

        # Check timing distribution
        if len(token_times) > 10:
            time_diffs = [
                token_times[i] - token_times[i - 1]
                for i in range(1, min(11, len(token_times)))
            ]
            avg_early_diff = sum(time_diffs) / len(time_diffs)
            print(f"⏱️  Average time between first 10 tokens: {avg_early_diff:.3f}s")

        # Final assessment
        if small_tokens / len(token_sizes) > 0.7 and avg_early_diff < 0.1:
            print("\n🎉 VERDICT: TRUE TOKEN STREAMING DETECTED!")
            print("   - Most tokens are small (good granularity)")
            print("   - Fast token delivery (good responsiveness)")
        elif len(token_sizes) > 100:
            print("\n✅ VERDICT: GOOD TOKEN STREAMING")
            print("   - High token count indicates proper streaming")
        else:
            print("\n⚠️  VERDICT: LIMITED STREAMING")
            print("   - May still be sending large chunks instead of individual tokens")

        print(f"\n📝 Sample tokens (first 10):")
        for i, content in enumerate(token_contents[:10]):
            print(f"   {i+1}: '{content}'")
    else:
        print("❌ No tokens received")


if __name__ == "__main__":
    asyncio.run(test_token_streaming())
