#!/usr/bin/env python3
"""
Session Generation Tool Demo

Demonstrates the Session Generation Tool with all four session types:
- Strength training
- Running
- Cycling
- Recovery
"""

import asyncio
import json

from athlea_langgraph.tools.external.session_generation import SessionGenerationTool


async def demo_session_generation():
    """Demonstrate session generation for all session types."""
    print("🏋️ Session Generation Tool Demo")
    print("=" * 50)

    tool = SessionGenerationTool()

    # Test cases for each session type
    test_cases = [
        {
            "name": "💪 High-Intensity Strength Session",
            "input": {
                "command": "strength",
                "date": "2024-01-15",
                "intensity": "high",
                "duration": 60,
                "skill_category": "General Strength",
                "training_category": "Full Body",
            },
        },
        {
            "name": "🏃 Moderate Running Session",
            "input": {
                "command": "running",
                "date": "2024-01-16",
                "distance": 8.0,
                "intensity": "moderate",
                "run_category": "General Aerobic",
            },
        },
        {
            "name": "🚴 High-Intensity Cycling Session",
            "input": {
                "command": "cycling",
                "date": "2024-01-17",
                "duration": 45,
                "intensity": "high",
                "cycling_category": ["Road"],
            },
        },
        {
            "name": "🧘 Active Recovery Session",
            "input": {
                "command": "recovery",
                "date": "2024-01-18",
                "duration": 30,
                "recovery_focus": "Active Recovery",
            },
        },
    ]

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 40)

        result = await tool.invoke(test_case["input"])

        if result.success:
            print(f"✅ Success! Generated {result.command} session")
            print(f"📊 Request ID: {result.request_id}")
            print(f"⏱️  Execution time: {result.execution_time_ms}ms")

            # Display session details based on type
            if result.strength_session:
                session = result.strength_session
                print(f"🏋️ Strength Session: {session.id}")
                print(f"   Duration: {session.duration} minutes")
                print(f"   Intensity: {session.intensity_level}/10")
                print(f"   Segments: {len(session.segments)}")
                for j, segment in enumerate(session.segments[:3], 1):  # Show first 3
                    print(f"     {j}. {segment.name} ({segment.duration}min)")

            elif result.running_session:
                session = result.running_session
                print(f"🏃 Running Session: {session.id}")
                print(f"   Distance: {session.distance}km")
                print(f"   Intensity: Zone {session.intensity_level}")
                print(f"   Segments: {len(session.segments)}")
                for j, segment in enumerate(session.segments, 1):
                    print(
                        f"     {j}. {segment.name} ({segment.duration}min, {segment.pace})"
                    )

            elif result.cycling_session:
                session = result.cycling_session
                print(f"🚴 Cycling Session: {session.id}")
                print(f"   Duration: {session.duration} minutes")
                print(f"   Intensity: {session.intensity}")
                print(f"   Segments: {len(session.segments)}")
                for j, segment in enumerate(session.segments, 1):
                    print(
                        f"     {j}. {segment.name} ({segment.duration}min, {segment.power_zone})"
                    )

            elif result.recovery_session:
                session = result.recovery_session
                print(f"🧘 Recovery Session: {session.id}")
                print(f"   Duration: {session.duration} minutes")
                print(f"   Focus: {session.recovery_focus}")
                print(f"   Activities: {len(session.activities)}")
                for j, activity in enumerate(session.activities, 1):
                    print(f"     {j}. {activity.name} ({activity.duration}min)")

        else:
            print(f"❌ Failed: {result.error_type}")
            print(f"   Message: {result.message}")

    print(f"\n🎉 Demo completed! All session types tested.")


if __name__ == "__main__":
    asyncio.run(demo_session_generation())
