#!/usr/bin/env python3
"""
API Test script to verify singleCoach parameter filtering through the HTTP endpoints.

This test verifies that the singleCoach parameter:
1. Routes to the correct specialized coach
2. Filters out other coaches
3. Returns appropriate responses
4. Works correctly through the API layer
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, List, Optional


class APISingleCoachTester:
    """Test harness for verifying single coach filtering through API endpoints."""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_results = []

    async def test_api_single_coach(
        self, coach_type: str, test_message: str, session: aiohttp.ClientSession
    ) -> Dict:
        """
        Test single coach routing through the API endpoint.

        Args:
            coach_type: The coach type to test (e.g., 'strength', 'nutrition')
            test_message: The message to send to the coach
            session: HTTP session for making requests

        Returns:
            Dict with test results
        """
        print(f"\n🧪 Testing API single coach: {coach_type}")
        print(f"📝 Test message: {test_message}")
        print("-" * 50)

        start_time = time.time()
        events_received = []
        agents_started = set()
        tokens_received = 0

        try:
            # Build the API URL with singleCoach parameter
            params = {
                "message": test_message,
                "threadId": f"test-api-{coach_type}-{int(time.time())}",
                "userId": "test-user-api",
                "singleCoach": coach_type,  # This is the key parameter we're testing
            }

            url = f"{self.base_url}/api/coaching"

            print(f"🔗 Making request to: {url}")
            print(f"📊 Parameters: {params}")

            # Make SSE request
            async with session.get(url, params=params) as response:
                print(f"📡 Response status: {response.status}")

                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(
                        f"API returned status {response.status}: {error_text}"
                    )

                # Process SSE stream
                async for line in response.content:
                    line_str = line.decode("utf-8").strip()

                    if not line_str:
                        continue

                    # Parse SSE events
                    if line_str.startswith("event:"):
                        event_type = line_str.split(":", 1)[1].strip()
                    elif line_str.startswith("data:"):
                        try:
                            data_json = line_str.split(":", 1)[1].strip()
                            data = json.loads(data_json)

                            event_info = {
                                "type": data.get(
                                    "type",
                                    (
                                        event_type
                                        if "event_type" in locals()
                                        else "unknown"
                                    ),
                                ),
                                "data": data,
                                "timestamp": time.time() - start_time,
                            }

                            events_received.append(event_info)

                            # Track specific event types
                            if data.get("type") == "agent_start":
                                agent = data.get("agent", "unknown")
                                agents_started.add(agent)
                                print(f"🚀 Agent started: {agent}")

                            elif data.get("type") == "token":
                                tokens_received += 1
                                agent = data.get("agent", "unknown")
                                content = data.get("content", "")

                                if tokens_received <= 5:  # Show first few tokens
                                    print(f"💬 Token from {agent}: {content[:30]}...")
                                elif tokens_received % 20 == 0:  # Show progress
                                    print(
                                        f"📊 Received {tokens_received} tokens from {agent}"
                                    )

                            elif data.get("type") == "complete":
                                print(f"✅ Stream completed")
                                break

                            elif data.get("type") == "error":
                                print(
                                    f"❌ Error received: {data.get('message', 'Unknown error')}"
                                )
                                break

                        except json.JSONDecodeError:
                            # Skip non-JSON data lines
                            continue

                    # Safety limit to prevent infinite loops
                    if len(events_received) > 200:
                        print("⚠️  Event limit reached, stopping...")
                        break

        except Exception as e:
            print(f"❌ API test failed: {e}")
            return {
                "coach_type": coach_type,
                "success": False,
                "error": str(e),
                "duration": time.time() - start_time,
                "events_received": len(events_received),
                "agents_started": list(agents_started),
                "tokens_received": tokens_received,
            }

        duration = time.time() - start_time

        # Analyze results
        success = self._analyze_api_test_results(
            coach_type, agents_started, tokens_received, events_received
        )

        result = {
            "coach_type": coach_type,
            "success": success,
            "duration": duration,
            "events_received": len(events_received),
            "agents_started": list(agents_started),
            "tokens_received": tokens_received,
            "test_message": test_message,
        }

        self.test_results.append(result)

        # Print test summary
        print(f"\n📊 API Test Results for {coach_type}:")
        print(f"   ✅ Success: {success}")
        print(f"   ⏱️  Duration: {duration:.2f}s")
        print(f"   📡 Events received: {len(events_received)}")
        print(f"   🚀 Agents started: {list(agents_started)}")
        print(f"   💬 Tokens received: {tokens_received}")

        return result

    def _analyze_api_test_results(
        self,
        expected_coach: str,
        agents_started: set,
        tokens_received: int,
        events_received: List[Dict],
    ) -> bool:
        """
        Analyze API test results for success criteria.

        Success criteria:
        1. Only one agent should start (the specialized coach or general coach)
        2. Tokens should be received from appropriate agent
        3. No multiple coaches should be invoked
        """
        print(f"\n🔍 Analyzing API results for {expected_coach}:")

        # Normalize expected coach name for comparison
        expected_agent_names = {
            "strength": ["strength", "Athlea"],
            "cardio": ["cardio", "running", "Athlea"],
            "nutrition": ["nutrition", "Athlea"],
            "cycling": ["cycling", "Athlea"],
            "recovery": ["recovery", "Athlea"],
            "mental": ["mental", "Athlea"],
        }

        expected_agents = expected_agent_names.get(expected_coach, ["Athlea"])

        # Check 1: Appropriate agent started
        if len(agents_started) == 1:
            started_agent = list(agents_started)[0]
            if started_agent in expected_agents:
                print(f"   ✅ Correct agent started: {started_agent}")
                agent_check = True
            else:
                print(
                    f"   ❌ Unexpected agent started: {started_agent} (expected one of: {expected_agents})"
                )
                agent_check = False
        elif len(agents_started) == 0:
            print(f"   ❌ No agents started")
            agent_check = False
        else:
            print(
                f"   ❌ Multiple agents started: {list(agents_started)} (expected only one)"
            )
            agent_check = False

        # Check 2: Tokens received
        if tokens_received > 0:
            print(f"   ✅ Received {tokens_received} tokens")
            token_check = True
        else:
            print(f"   ❌ No tokens received")
            token_check = False

        # Check 3: No errors in stream
        error_events = [
            e for e in events_received if e.get("data", {}).get("type") == "error"
        ]
        if not error_events:
            print(f"   ✅ No error events")
            error_check = True
        else:
            print(f"   ❌ {len(error_events)} error events received")
            error_check = False

        overall_success = agent_check and token_check and error_check

        if overall_success:
            print(f"   🎉 Overall API test: SUCCESS")
        else:
            print(f"   💥 Overall API test: FAILED")

        return overall_success

    async def run_api_tests(self):
        """Run comprehensive API tests for all coach types."""
        print("🚀 Starting API Single Coach Tests")
        print("=" * 60)

        # Define test cases
        test_cases = [
            ("strength", "How do I improve my deadlift form?"),
            ("cardio", "What's the best running training plan?"),
            ("nutrition", "How should I eat for muscle gain?"),
            ("cycling", "How do I train for a century ride?"),
            ("recovery", "How do I optimize my sleep for recovery?"),
            ("mental", "How can I improve my motivation to exercise?"),
        ]

        # Check if API is running
        print(f"🔍 Checking API availability at {self.base_url}...")

        async with aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30)
        ) as session:
            try:
                # Health check
                async with session.get(f"{self.base_url}/api/health") as response:
                    if response.status == 200:
                        health_data = await response.json()
                        print(
                            f"✅ API is running: {health_data.get('status', 'unknown')}"
                        )
                    else:
                        print(f"⚠️  API health check returned status {response.status}")
            except Exception as e:
                print(f"❌ Cannot reach API: {e}")
                print(f"💡 Make sure the Python backend is running on {self.base_url}")
                return []

            # Run test cases
            results = []
            for coach_type, test_message in test_cases:
                try:
                    result = await self.test_api_single_coach(
                        coach_type, test_message, session
                    )
                    results.append(result)

                    # Small delay between tests
                    await asyncio.sleep(2)

                except Exception as e:
                    print(f"❌ Test failed for {coach_type}: {e}")
                    results.append(
                        {
                            "coach_type": coach_type,
                            "success": False,
                            "error": str(e),
                        }
                    )

        # Print final summary
        self._print_api_summary(results)
        return results

    def _print_api_summary(self, results: List[Dict]):
        """Print comprehensive API test summary."""
        print("\n" + "=" * 60)
        print("📋 API TEST SUMMARY")
        print("=" * 60)

        total_tests = len(results)
        successful_tests = sum(1 for r in results if r.get("success", False))
        failed_tests = total_tests - successful_tests

        print(f"📊 Overall Results:")
        print(f"   Total tests: {total_tests}")
        print(f"   ✅ Successful: {successful_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   📈 Success rate: {(successful_tests / total_tests * 100):.1f}%")

        print(f"\n🔍 Detailed Results:")
        for result in results:
            status = "✅ PASS" if result.get("success", False) else "❌ FAIL"
            coach = result["coach_type"]
            duration = result.get("duration", 0)
            tokens = result.get("tokens_received", 0)
            agents = result.get("agents_started", [])

            print(
                f"   {status} {coach:<10} ({duration:.2f}s) - {tokens} tokens, agents: {agents}"
            )

            if not result.get("success", False) and "error" in result:
                print(f"       Error: {result['error']}")

        if successful_tests == total_tests:
            print(
                f"\n🎉 All API tests passed! Single coach filtering works correctly through the API."
            )
        else:
            print(
                f"\n⚠️  {failed_tests} API test(s) failed. Check the backend implementation."
            )


async def main():
    """Main test execution function."""
    import sys

    # Allow custom base URL
    base_url = "http://localhost:8000"
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
        print(f"📍 Using custom base URL: {base_url}")

    tester = APISingleCoachTester(base_url)

    try:
        results = await tester.run_api_tests()

        successful_tests = sum(1 for r in results if r.get("success", False))
        total_tests = len(results)

        if successful_tests == total_tests:
            print(f"\n🎯 SUCCESS: All API single coach tests passed!")
            return 0
        else:
            print(f"\n💥 FAILURE: {total_tests - successful_tests} API test(s) failed!")
            return 1

    except Exception as e:
        print(f"\n❌ Fatal error during API testing: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
