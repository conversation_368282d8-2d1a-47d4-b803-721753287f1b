#!/usr/bin/env python3
"""
Frontend Format Verification Test

This test verifies that the SSE events from the Python backend match exactly
what the Next.js frontend expects in terms of format, event types, and data structure.
"""

import asyncio
import json
import time

import aiohttp


async def test_frontend_format():
    """Test that the SSE format matches frontend expectations."""

    print("🎨 Frontend Format Verification Test")
    print("=" * 70)

    url = "http://localhost:8000/api/coaching"
    params = {
        "message": "How can I improve my cycling training?",
        "threadId": f"frontend-test-{int(time.time())}",
        "userId": "frontend_test_user",
    }

    # Track events by type
    events_by_type = {}
    agents_seen = set()
    first_token_by_agent = {}
    token_samples_by_agent = {}

    print(f"📡 Testing with cycling question to verify agent routing")
    print(f"🔍 Expected: cycling coach should be detected and stream tokens")
    print(f"⏳ Connecting...\n")

    async with aiohttp.ClientSession() as session:
        async with session.get(url, params=params) as response:
            if response.status != 200:
                print(f"❌ Error: HTTP {response.status}")
                return

            event_type = None
            event_count = 0

            async for line in response.content:
                line_text = line.decode("utf-8").strip()

                if not line_text:
                    continue

                # Parse SSE format
                if line_text.startswith("event: "):
                    event_type = line_text.split("event: ")[1].strip()
                    continue

                if line_text.startswith("data: "):
                    event_count += 1

                    try:
                        data = json.loads(line_text[6:])  # Remove "data: " prefix

                        # Track events by type
                        if event_type not in events_by_type:
                            events_by_type[event_type] = 0
                        events_by_type[event_type] += 1

                        # Analyze specific event types
                        if event_type == "agent_start":
                            agent = data.get("agent")
                            if agent:
                                agents_seen.add(agent)
                                print(f"🚀 Agent Start: {agent}")

                        elif event_type == "token":
                            agent = data.get("agent")
                            content = data.get("content")

                            if agent and content is not None:
                                # Track first token for each agent
                                if agent not in first_token_by_agent:
                                    first_token_by_agent[agent] = content
                                    print(f"🎯 First token from {agent}: '{content}'")

                                # Collect sample tokens
                                if agent not in token_samples_by_agent:
                                    token_samples_by_agent[agent] = []
                                if (
                                    len(token_samples_by_agent[agent]) < 5
                                ):  # First 5 tokens
                                    token_samples_by_agent[agent].append(content)

                        elif event_type == "complete":
                            print(f"✅ Stream completed")
                            break

                        elif event_type == "error":
                            print(f"❌ Error event: {data}")
                            break

                        # Show progress every 100 events
                        if event_count % 100 == 0:
                            print(f"📊 {event_count} events processed...")

                    except json.JSONDecodeError as e:
                        print(f"❌ JSON parse error: {e}")
                        print(f"   Raw data: {line_text}")
                        continue

    print("\n" + "=" * 70)
    print("📊 FRONTEND FORMAT ANALYSIS")
    print("=" * 70)

    # Event type analysis
    print("📋 Event Types Received:")
    for event_type, count in events_by_type.items():
        print(f"   • {event_type}: {count} events")

    # Agent analysis
    print(f"\n🤖 Agents Detected ({len(agents_seen)}):")
    expected_agents = [
        "reasoning",
        "cycling",
        "Athlea",
    ]  # Expected for cycling question
    for agent in sorted(agents_seen):
        status = "✅" if agent in expected_agents else "⚠️"
        print(f"   {status} {agent}")

    # Check for missing expected agents
    missing_agents = set(expected_agents) - agents_seen
    if missing_agents:
        print(f"   ❌ Missing expected agents: {missing_agents}")

    # Token format analysis
    print(f"\n💬 Token Format Analysis:")
    for agent, samples in token_samples_by_agent.items():
        print(f"   {agent}: {samples}")

    # Frontend compatibility check
    print(f"\n🎨 Frontend Compatibility:")

    # Check required event types
    required_events = ["agent_start", "token", "complete"]
    missing_events = set(required_events) - set(events_by_type.keys())
    if missing_events:
        print(f"   ❌ Missing required events: {missing_events}")
    else:
        print(f"   ✅ All required event types present")

    # Check token data structure
    token_format_ok = True
    for agent, samples in token_samples_by_agent.items():
        for sample in samples:
            if not isinstance(sample, str):
                print(f"   ❌ Token content not string for {agent}: {type(sample)}")
                token_format_ok = False
                break

    if token_format_ok:
        print(f"   ✅ Token content format correct (all strings)")

    # Check agent names
    frontend_agent_names = [
        "cycling",
        "strength",
        "nutrition",
        "recovery",
        "mental",
        "reasoning",
        "Athlea",
    ]
    unexpected_agents = agents_seen - set(frontend_agent_names)
    if unexpected_agents:
        print(f"   ⚠️  Unexpected agent names: {unexpected_agents}")
    else:
        print(f"   ✅ Agent names match frontend expectations")

    # Overall verdict
    print(f"\n🏆 OVERALL VERDICT:")
    if (
        not missing_events
        and token_format_ok
        and not missing_agents
        and events_by_type.get("token", 0) > 100
    ):
        print(f"   🎉 FRONTEND READY! Perfect streaming format detected")
        print(f"   • Event structure: ✅ Correct")
        print(f"   • Agent routing: ✅ Working")
        print(f"   • Token streaming: ✅ {events_by_type.get('token', 0)} tokens")
        print(f"   • Data format: ✅ Compatible")
    else:
        print(f"   ⚠️  Issues detected that may affect frontend:")
        if missing_events:
            print(f"      - Missing events: {missing_events}")
        if not token_format_ok:
            print(f"      - Token format issues")
        if missing_agents:
            print(f"      - Missing agents: {missing_agents}")
        if events_by_type.get("token", 0) <= 100:
            print(f"      - Low token count: {events_by_type.get('token', 0)}")


if __name__ == "__main__":
    asyncio.run(test_frontend_format())
