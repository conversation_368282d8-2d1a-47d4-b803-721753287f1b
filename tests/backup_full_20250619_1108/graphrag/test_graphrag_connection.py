#!/usr/bin/env python3
"""
Test script to verify GraphRAG connections and functionality.
"""

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from athlea_langgraph.services.graphrag_service import get_graphrag_service
from athlea_langgraph.config.graphrag_config import (
    get_graphrag_config,
    validate_graphrag_config,
)


async def test_graphrag_connections():
    """Test GraphRAG service connections and basic functionality."""
    print("🔍 Testing GraphRAG Configuration and Connections")
    print("=" * 60)

    try:
        # Test configuration
        print("1. Testing Configuration...")
        config = get_graphrag_config()
        print(f"   ✅ Azure Search Service: {config.acs_service_name}")
        print(f"   ✅ Azure Search Index: {config.acs_index_name}")
        print(f"   ✅ Cosmos DB Endpoint: {config.cosmos_endpoint}")
        print(f"   ✅ Cosmos DB Database: {config.cosmos_database}")
        print(f"   ✅ Cosmos DB Graph: {config.cosmos_graph}")

        # Validate required fields
        print("\n2. Validating Required Configuration...")
        try:
            validate_graphrag_config()
            print("   ✅ All required configuration present")
        except ValueError as e:
            print(f"   ❌ Configuration validation failed: {e}")
            return False

        # Test GraphRAG service initialization
        print("\n3. Testing GraphRAG Service Initialization...")
        graphrag_service = get_graphrag_service()
        print("   ✅ GraphRAG service initialized successfully")

        # Test a simple GraphRAG query
        print("\n4. Testing GraphRAG Query...")
        test_query = {
            "query": "FIFA 11+ injury prevention",
            "include_acs": True,
            "include_graph": True,
            "top_k": 5,
        }

        print(f"   Query: {test_query['query']}")
        result = await graphrag_service.execute_graphrag_query(test_query)

        if result.success:
            print("   ✅ GraphRAG query executed successfully!")
            print(f"   📊 Execution time: {result.execution_time_ms}ms")
            print(f"   📄 ACS chunks found: {result.acs_chunk_count}")
            print(f"   🔗 Graph entities found: {result.graph_entity_count}")
            print(f"   🕸️ Graph relationships found: {result.graph_relationship_count}")

            if result.synthesis:
                print(f"   💡 Synthesis: {result.synthesis[:100]}...")

            if result.recommendations:
                print(f"   🎯 Recommendations: {len(result.recommendations)} found")
                for i, rec in enumerate(result.recommendations[:3]):
                    print(f"      {i+1}. {rec}")
        else:
            print(f"   ❌ GraphRAG query failed: {result.message}")
            print(f"   ❌ Error type: {result.error_type}")
            return False

        # Test the futsal study specifically
        print("\n5. Testing Futsal Study Query...")
        futsal_result = await graphrag_service.query_futsal_study()

        if futsal_result.success:
            print("   ✅ Futsal study query successful!")
            print(f"   📊 Execution time: {futsal_result.execution_time_ms}ms")
            print(f"   📄 ACS chunks: {futsal_result.acs_chunk_count}")
            print(f"   🔗 Graph entities: {futsal_result.graph_entity_count}")
        else:
            print(f"   ⚠️ Futsal study query failed: {futsal_result.message}")

        # Cleanup
        print("\n6. Cleaning Up...")
        try:
            await graphrag_service.close()
            print("   ✅ GraphRAG service closed")

            # Give a moment for async cleanup to complete
            await asyncio.sleep(0.1)

        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")
            # Don't fail the test due to cleanup issues

        print("\n" + "=" * 60)
        print("🎉 GraphRAG Connection Test COMPLETED SUCCESSFULLY!")
        return True

    except Exception as e:
        print(f"\n❌ GraphRAG test failed with error: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(test_graphrag_connections())
    sys.exit(0 if success else 1)
