#!/usr/bin/env python3
"""
Fixed Gremlin Test - Remove enable_ssl parameter

The diagnostic revealed that 'enable_ssl' is not a valid parameter
for the current gremlinpython client. This test removes that parameter.
"""

import logging
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_gremlin_fixed():
    """Test Gremlin connection with corrected parameters."""
    print("🚀 Testing FIXED Gremlin Connection")
    print("=" * 50)

    try:
        from gremlin_python.driver import client

        # Connection parameters
        endpoint = "cosmos-gremlin-aml.documents.azure.com"
        port = 443
        username = "/dbs/graphragdb/colls/knowledge-graph"
        password = "****************************************************************************************"

        print(f"Connecting to: {endpoint}:{port}")
        print(f"Username: {username}")
        print(f"Password: [REDACTED - {len(password)} chars]")

        # Create client WITHOUT enable_ssl parameter
        gremlin_client = client.Client(
            f"wss://{endpoint}:{port}/gremlin",
            "g",
            username=username,
            password=password,
        )

        print("✓ Gremlin client created successfully (without enable_ssl)")

        # Try to submit a very simple query
        print("Attempting vertex count query...")

        result = gremlin_client.submit("g.V().count()")
        count = result.one()

        print(f"✅ SUCCESS! Connection works! Vertex count: {count}")

        # Try a few more queries to test functionality
        print("\nTesting additional queries...")

        # Test getting first 3 vertices
        result = gremlin_client.submit("g.V().limit(3)")
        vertices = result.all().result()
        print(f"✓ Retrieved {len(vertices)} sample vertices")

        # Test getting edge count
        result = gremlin_client.submit("g.E().count()")
        edge_count = result.one()
        print(f"✓ Edge count: {edge_count}")

        # Test getting specific entity types
        result = gremlin_client.submit("g.V().hasLabel('Paper').count()")
        paper_count = result.one()
        print(f"✓ Paper entities: {paper_count}")

        result = gremlin_client.submit("g.V().hasLabel('FIFA 11+').count()")
        fifa_count = result.one()
        print(f"✓ FIFA 11+ entities: {fifa_count}")

        print("\n🎉 ALL TESTS PASSED!")
        return True

    except Exception as e:
        print(f"✗ Connection failed: {e}")
        print(f"Error type: {type(e).__name__}")

        # Additional error details
        if hasattr(e, "status"):
            print(f"Status: {e.status}")
        if hasattr(e, "message"):
            print(f"Message: {e.message}")

        return False


def test_specific_document_queries():
    """Test queries for the specific test document."""
    print("\n🔍 Testing Queries for Test Document")
    print("=" * 50)

    try:
        from gremlin_python.driver import client

        # Connection parameters (corrected)
        gremlin_client = client.Client(
            "wss://cosmos-gremlin-aml.documents.azure.com:443/gremlin",
            "g",
            username="/dbs/graphragdb/colls/knowledge-graph",
            password="****************************************************************************************",
        )

        test_doc_id = "a8eb82eb-829e-5eb1-b0b9-65dbad8fa897"
        test_doi = "10.3390/healthcare12141387"

        print(f"Testing queries for document: {test_doc_id}")
        print(f"DOI: {test_doi}")

        # Query for paper with specific document ID
        result = gremlin_client.submit(
            f"g.V().has('document_id', '{test_doc_id}').count()"
        )
        doc_count = result.one()
        print(f"✓ Documents with ID {test_doc_id}: {doc_count}")

        # Query for paper with specific DOI
        result = gremlin_client.submit(f"g.V().has('doi', '{test_doi}').count()")
        doi_count = result.one()
        print(f"✓ Documents with DOI {test_doi}: {doi_count}")

        # Query for FIFA 11+ related entities
        result = gremlin_client.submit("g.V().hasLabel('FIFA 11+').count()")
        fifa_entities = result.one()
        print(f"✓ FIFA 11+ entities: {fifa_entities}")

        # Query for PREVENTS relationships
        result = gremlin_client.submit("g.E().hasLabel('PREVENTS').count()")
        prevents_edges = result.one()
        print(f"✓ PREVENTS relationships: {prevents_edges}")

        # Query for injury prevention relationships
        result = gremlin_client.submit(
            "g.V().hasLabel('FIFA 11+').outE('PREVENTS').inV().hasLabel('INJURY').count()"
        )
        fifa_injury_prevention = result.one()
        print(f"✓ FIFA 11+ → INJURY prevention relationships: {fifa_injury_prevention}")

        return True

    except Exception as e:
        print(f"✗ Document queries failed: {e}")
        return False


def main():
    """Run the fixed connection test."""
    print("🔧 FIXED GREMLIN CONNECTION TEST")
    print("=" * 60)
    print("Issue identified: 'enable_ssl' parameter not supported")
    print("Solution: Remove enable_ssl parameter from client creation")
    print("=" * 60)
    print()

    # Test basic connection
    success = test_gremlin_fixed()

    if success:
        # Test document-specific queries
        test_specific_document_queries()

        print("\n🎯 FINAL RESULT")
        print("=" * 60)
        print("✅ GREMLIN CONNECTION WORKING!")
        print("✅ GraphRAG integration is now ready!")
        print("\n📋 Next steps:")
        print("1. Update GraphRAG service to remove enable_ssl parameter")
        print("2. Set Azure Search credentials for full integration")
        print("3. Test complete GraphRAG workflow")
    else:
        print("\n❌ Connection still failing - need further investigation")


if __name__ == "__main__":
    main()
