"""
Synchronous Gremlin Test

Test Gremlin connectivity without async complications.
"""

import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_gremlin_sync():
    """Test Gremlin connection synchronously."""
    print("🚀 Testing Direct Gremlin Connection (Sync)")
    print("=" * 50)

    try:
        from gremlin_python.driver import client
        import urllib.parse

        # Connection parameters
        cosmos_endpoint = "https://cosmos-gremlin-aml.documents.azure.com:443/"
        cosmos_key = "****************************************************************************************"
        cosmos_database = "graphragdb"
        cosmos_graph = "knowledge-graph"

        # Parse endpoint
        parsed_url = urllib.parse.urlparse(cosmos_endpoint)
        hostname = parsed_url.hostname
        port = parsed_url.port or 443
        connection_string = f"wss://{hostname}:{port}/gremlin"

        print(f"Connecting to: {hostname}:{port}")
        print(f"Database: {cosmos_database}")
        print(f"Graph: {cosmos_graph}")

        # Create client with minimal configuration
        gremlin_client = client.Client(
            connection_string,
            "g",
            username=f"/dbs/{cosmos_database}/colls/{cosmos_graph}",
            password=cosmos_key,
            message_serializer=client.serializer.GraphSONSerializersV2d0(),
        )

        print("✓ Gremlin client created successfully")

        # Test vertex count
        print("\nTesting vertex count...")
        start_time = time.time()
        result_set = gremlin_client.submit("g.V().count()")
        results = list(result_set)
        execution_time = int((time.time() - start_time) * 1000)

        print(f"✓ Query executed in {execution_time}ms")
        print(f"  - Total vertices in graph: {results[0] if results else 'No results'}")

        # Test edge count
        print("\nTesting edge count...")
        result_set = gremlin_client.submit("g.E().count()")
        edge_results = list(result_set)
        print(f"  - Total edges in graph: {edge_results[0] if edge_results else 0}")

        # Test vertex labels
        print("\nTesting vertex labels...")
        result_set = gremlin_client.submit("g.V().label().dedup()")
        label_results = list(result_set)
        print(f"  - Vertex labels: {label_results if label_results else 'None'}")

        # Test edge labels
        print("\nTesting edge labels...")
        result_set = gremlin_client.submit("g.E().label().dedup()")
        edge_label_results = list(result_set)
        print(
            f"  - Edge labels: {edge_label_results if edge_label_results else 'None'}"
        )

        # Test specific queries
        test_doi = "10.3390/healthcare12141387"
        print(f"\nTesting specific document lookup (DOI: {test_doi})...")
        result_set = gremlin_client.submit(
            f"g.V().hasLabel('Paper').has('DOI', '{test_doi}').valueMap(true)"
        )
        doi_results = list(result_set)

        if doi_results:
            print(f"✓ Found paper with DOI: {test_doi}")
            paper_data = doi_results[0]
            print(f"  - Paper properties: {list(paper_data.keys())}")
        else:
            print(f"◐ Paper with DOI {test_doi} not found")

        # Test FIFA 11+ entity
        print("\nTesting FIFA 11+ entity lookup...")
        result_set = gremlin_client.submit(
            "g.V().has('name', 'FIFA 11+').valueMap(true)"
        )
        fifa_results = list(result_set)

        if fifa_results:
            print("✓ Found FIFA 11+ entity")
            entity_data = fifa_results[0]
            print(f"  - Entity properties: {list(entity_data.keys())}")
        else:
            print("◐ FIFA 11+ entity not found")

        # Test relationships
        print("\nTesting relationship patterns...")
        result_set = gremlin_client.submit(
            "g.E().hasLabel('PREVENTS').limit(5).valueMap(true)"
        )
        prevents_results = list(result_set)
        print(f"  - PREVENTS relationships found: {len(prevents_results)}")

        result_set = gremlin_client.submit(
            "g.E().hasLabel('IMPROVES').limit(5).valueMap(true)"
        )
        improves_results = list(result_set)
        print(f"  - IMPROVES relationships found: {len(improves_results)}")

        # Close connection
        gremlin_client.close()
        print("\n✓ Connection closed successfully")

        # Summary
        print("\n" + "=" * 50)
        print("📊 GREMLIN CONNECTION SUMMARY")
        print("=" * 50)

        vertex_count = results[0] if results else 0
        edge_count = edge_results[0] if edge_results else 0

        print(f"✓ Connection Status: SUCCESSFUL")
        print(f"✓ Database: {cosmos_database}")
        print(f"✓ Graph: {cosmos_graph}")
        print(f"✓ Total Vertices: {vertex_count}")
        print(f"✓ Total Edges: {edge_count}")
        print(f"✓ Vertex Labels: {len(label_results) if label_results else 0}")
        print(f"✓ Edge Labels: {len(edge_label_results) if edge_label_results else 0}")

        if vertex_count > 0:
            print(
                "\n🌟 SUCCESS: Your GraphRAG knowledge graph is populated and accessible!"
            )
            print("   The system can retrieve structured relationships and entities.")
        else:
            print(
                "\n📝 INFO: Graph exists but appears to be empty or not populated yet."
            )
            print(
                "   This is expected if data hasn't been ingested into the knowledge graph."
            )

        return True

    except Exception as e:
        print(f"\n✗ Gremlin connection failed: {e}")
        print(f"Error type: {type(e).__name__}")
        return False


if __name__ == "__main__":
    test_gremlin_sync()
