#!/usr/bin/env python3
"""
Test script to verify all migrated coach prompts are working correctly.
"""

import logging

from athlea_langgraph.utils import PromptLoader

# Set up logging
logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")
logger = logging.getLogger(__name__)


def test_all_coach_prompts():
    """Test loading and rendering all migrated coach prompts."""

    print("🤖 TESTING ALL MIGRATED COACH PROMPTS")
    print("=" * 60)

    # Initialize the prompt loader
    loader = PromptLoader("athlea_langgraph/prompts")
    loader.initialize()

    # List of expected coach prompts
    coach_prompts = [
        "strength_coach",
        "nutrition_coach",
        "cardio_coach",
        "cycling_coach",
        "recovery_coach",
        "mental_coach",
    ]

    for coach_name in coach_prompts:
        try:
            print(f"\n🔍 Testing {coach_name}...")

            # Load the prompt config
            config = loader.load_prompt(coach_name)

            # Display metadata
            print(f"   ✅ Name: {config.metadata.name}")
            print(f"   ✅ Version: {config.metadata.version}")
            print(f"   ✅ Type: {config.metadata.prompt_type.value}")
            print(f"   ✅ Tags: {', '.join(config.metadata.tags)}")
            print(f"   ✅ Created: {config.metadata.created_at}")

            # Validate the prompt
            is_valid = loader.validate_prompt(config)
            print(f"   ✅ Validation: {'PASSED' if is_valid else 'FAILED'}")

            # Test rendering
            rendered = loader.render_prompt(coach_name)
            print(f"   ✅ Rendered length: {len(rendered)} characters")

            # Show first line of the prompt
            first_line = rendered.split("\n")[0]
            print(f"   ✅ First line: {first_line}")

        except Exception as e:
            print(f"   ❌ ERROR loading {coach_name}: {e}")

    print(f"\n📊 SUMMARY")
    print("=" * 30)

    # Show all available prompts
    all_prompts = loader.list_prompts()
    print(f"Total prompts available: {len(all_prompts)}")

    # Group by category
    coaches = [p for p in all_prompts if "coach" in p]
    reasoning = [
        p
        for p in all_prompts
        if "template" in p or "extraction" in p or "completion" in p or "system" in p
    ]
    system = [p for p in all_prompts if p.startswith("goal") or p.startswith("sample")]

    print(f"Coach prompts: {len(coaches)} - {', '.join(coaches)}")
    print(f"Reasoning prompts: {len(reasoning)} - {', '.join(reasoning)}")
    print(f"System prompts: {len(system)} - {', '.join(system)}")

    # Test cache statistics
    cache_stats = loader.get_cache_stats()
    print(
        f"\nCache stats: {cache_stats['cached_prompts']} cached, {cache_stats['tracked_files']} files tracked"
    )

    print("\n🎉 MIGRATION TEST COMPLETE!")
    return True


if __name__ == "__main__":
    test_all_coach_prompts()
