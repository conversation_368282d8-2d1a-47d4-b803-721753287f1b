#!/usr/bin/env python3
"""
Simple n8n Integration Test

Test the n8n configuration and client without importing the full package.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


def test_n8n_config():
    """Test n8n configuration values."""
    print("🔧 Testing n8n Configuration")
    print("=" * 50)

    # Get environment variables
    api_key = os.getenv("N8N_API_KEY")
    api_url = os.getenv("N8N_API_URL", "http://localhost:5678/api/v1")
    webhook_base_url = os.getenv(
        "N8N_WEBHOOK_BASE_URL", "http://localhost:8000/api/webhooks/n8n"
    )
    environment = os.getenv("N8N_ENVIRONMENT", "development")
    timeout_seconds = int(os.getenv("N8N_TIMEOUT_SECONDS", "30"))
    max_retries = int(os.getenv("N8N_MAX_RETRIES", "3"))

    # Test configuration
    print(f"✅ API Key configured: {bool(api_key)}")
    if api_key:
        print(f"   API Key preview: {api_key[:20]}...")
    else:
        print("   ❌ No API key found!")

    print(f"✅ API URL: {api_url}")
    print(f"✅ Webhook Base URL: {webhook_base_url}")
    print(f"✅ Environment: {environment}")
    print(f"✅ Timeout: {timeout_seconds}s")
    print(f"✅ Max Retries: {max_retries}")

    # Derived endpoints
    workflows_endpoint = f"{api_url.rstrip('/')}/workflows"
    executions_endpoint = f"{api_url.rstrip('/')}/executions"

    print(f"📡 Workflows Endpoint: {workflows_endpoint}")
    print(f"📡 Executions Endpoint: {executions_endpoint}")

    # Check if enabled
    is_enabled = bool(api_key)
    print(f"🎯 n8n Integration Enabled: {is_enabled}")

    if is_enabled:
        print("\n🎉 n8n integration is ready!")
        print("\nNext steps:")
        print("1. Make sure your n8n instance is running")
        print("2. Test connectivity with the full integration")
        print("3. Create your first workflow")
    else:
        print("\n❌ n8n integration is disabled")
        print("Add N8N_API_KEY to your .env file to enable it")

    return is_enabled


def test_jwt_parsing():
    """Test JWT token parsing to validate the API key format."""
    print("\n🔍 Testing JWT Token Format")
    print("=" * 50)

    api_key = os.getenv("N8N_API_KEY")
    if not api_key:
        print("❌ No API key to test")
        return False

    try:
        # Basic JWT format check (3 parts separated by dots)
        parts = api_key.split(".")
        if len(parts) != 3:
            print(f"❌ Invalid JWT format - expected 3 parts, got {len(parts)}")
            return False

        print(f"✅ JWT format valid: {len(parts)} parts")
        print(f"   Header length: {len(parts[0])}")
        print(f"   Payload length: {len(parts[1])}")
        print(f"   Signature length: {len(parts[2])}")

        # Try to decode the payload (base64)
        import base64
        import json

        # Add padding if needed
        payload = parts[1]
        while len(payload) % 4:
            payload += "="

        try:
            decoded = base64.b64decode(payload)
            payload_data = json.loads(decoded)

            print("✅ JWT payload decoded successfully:")
            print(f"   Subject: {payload_data.get('sub', 'N/A')}")
            print(f"   Issuer: {payload_data.get('iss', 'N/A')}")
            print(f"   Audience: {payload_data.get('aud', 'N/A')}")

            # Check expiration
            import time

            exp = payload_data.get("exp")
            if exp:
                exp_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(exp))
                is_expired = exp < time.time()
                print(
                    f"   Expires: {exp_time} ({'EXPIRED' if is_expired else 'VALID'})"
                )

                if is_expired:
                    print("⚠️  WARNING: API key has expired!")
                    return False

            return True

        except Exception as e:
            print(f"❌ Failed to decode JWT payload: {e}")
            return False

    except Exception as e:
        print(f"❌ JWT validation failed: {e}")
        return False


if __name__ == "__main__":
    print("🚀 n8n Integration Simple Test")
    print("=" * 60)

    # Test configuration
    config_ok = test_n8n_config()

    # Test JWT if config is OK
    if config_ok:
        jwt_ok = test_jwt_parsing()

        if config_ok and jwt_ok:
            print("\n🎉 ALL TESTS PASSED - n8n integration is ready!")
        else:
            print("\n⚠️  Some tests failed - check the details above")
    else:
        print("\n❌ Configuration test failed")

    print("\n📋 To complete the setup:")
    print("1. Ensure your n8n instance is running")
    print("2. Run: python -m pytest tests/test_n8n_client.py -v")
    print("3. Create workflows in your n8n instance")
    print("4. Use the client in your agents!")
