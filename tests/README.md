# Athlea LangGraph Test Suite

This directory contains the comprehensive test suite for the Athlea LangGraph coaching system.

## 📁 **Test Structure**

```
tests/
├── README.md                    # This file
├── conftest.py                  # Pytest configuration and fixtures
├── __init__.py                  # Test package initialization
├── core/                        # 🎯 PRIMARY: Automated optimization testing
│   ├── test_automated_optimization.py  # Core optimization and quality assessment
│   ├── test_tool_integration.py        # Tool integration testing
│   └── test_streaming_api.py           # API streaming functionality
├── coaches/                     # Individual coach testing
│   └── test_head_coach.py              # Head coach routing logic
├── system/                      # System-level testing
│   ├── test_prompt_system.py           # Prompt loading and validation
│   ├── test_migrated_coaches.py        # Coach prompt migration
│   ├── test_config.py                  # Configuration validation
│   ├── test_redis.py                   # Redis integration
│   └── test_azure_openai_format.py     # Azure OpenAI formatting
├── tools/                       # Tool-specific testing (21 files)
├── external/                    # Third-party integrations
│   ├── n8n_integration/               # N8N workflow tests
│   ├── graphrag/                      # GraphRAG integration tests
│   └── hallucination_mitigation/     # Hallucination prevention tests
├── e2e/                        # End-to-end tests for complete user journeys
├── utilities/                   # Test utilities and fixtures
│   ├── fixtures/                      # Test data and fixtures
│   ├── runners/                       # Test execution utilities
│   └── synthetic/                     # Synthetic data generation tests
├── routing/                     # Routing logic tests
├── streaming/                   # Streaming functionality tests
├── unit/                        # Remaining unit tests
└── results/                     # Test results and historical data
```

## 🧪 **Test Categories**

### **🎯 Core Tests** (`core/`) - **PRIMARY TESTING WORKFLOW**
The main automated optimization and core functionality testing:
- **`test_automated_optimization.py`** - Automated prompt optimization with quality assessment
- **`test_tool_integration.py`** - Comprehensive tool integration testing
- **`test_streaming_api.py`** - API streaming functionality testing

### **🤖 Coach Tests** (`coaches/`)
Individual coach and routing logic testing:
- **`test_head_coach.py`** - Head coach routing and clarification logic

### **⚙️ System Tests** (`system/`)
System-level configuration and infrastructure testing:
- **`test_prompt_system.py`** - Prompt loading and validation
- **`test_migrated_coaches.py`** - Coach prompt migration testing
- **`test_config.py`** - Configuration validation
- **`test_redis.py`** - Redis integration testing
- **`test_azure_openai_format.py`** - Azure OpenAI formatting

### **🔧 Tool Tests** (`tools/`)
Tool-specific functionality testing (21 files):
- Individual tool validation and integration
- Tool parameter handling and responses
- Tool workflow testing

### **🌐 External Tests** (`external/`)
Third-party integration testing:
- **N8N Integration** - Workflow automation testing
- **GraphRAG** - Knowledge retrieval and research testing
- **Hallucination Mitigation** - Content validation testing

### **🔄 End-to-End Tests** (`e2e/`)
Complete user journey tests:
- Full coaching conversations
- Onboarding workflows
- Multi-session interactions

### **🛠️ Utilities** (`utilities/`)
Test support and infrastructure:
- **Fixtures** - Test data and mock configurations
- **Runners** - Test execution utilities
- **Synthetic** - Generated scenario testing

### **🎯 Automated Optimization Testing** (`core/test_automated_optimization.py`)
**PRIMARY TESTING FUNCTION** - Advanced prompt optimization and quality assessment:
- **Function**: `test_strength_coach_accuracy_with_tracing()`
- **Metrics**: Specificity (≥0.75), Safety (≥0.90), Coverage (≥0.80)
- **Integration**: LangSmith tracing and quality assessment
- **Results**: Stored in `tests/results/` and root directory JSON files

## 🚀 **Running Tests**

### **Quick Test Run**
```bash
# Run all tests
pytest

# Run specific categories
pytest tests/core/          # Core automated optimization tests
pytest tests/coaches/       # Coach-specific tests
pytest tests/system/        # System-level tests
pytest tests/tools/         # Tool-specific tests
pytest tests/e2e/          # End-to-end tests

# Run with coverage
pytest --cov=athlea_langgraph
```

### **Using Test Runners**
```bash
# General test runner
python tests/utilities/runners/run_tests.py
```

### **🎯 Automated Optimization Testing** - **PRIMARY WORKFLOW**
```bash
# Run complete optimization test with quality assessment
python -c "
import asyncio
from tests.core.test_automated_optimization import test_strength_coach_accuracy_with_tracing
result = asyncio.run(test_strength_coach_accuracy_with_tracing())
print(f'✅ Pass Rate: {result.get(\"pass_rate\", 0):.2f}')
print(f'📊 Composite Score: {result.get(\"organized_results\", {}).get(\"composite_score\", 0):.3f}')
"

# Run core test suite
pytest tests/core/ -v

# Run tool integration tests
pytest tests/core/test_tool_integration.py -v

# Run streaming API tests
pytest tests/core/test_streaming_api.py -v

# Check latest test results
ls -la tests/results/ | tail -1

# View baseline metrics (if available)
python -c "
import json
try:
    with open('baseline_results.json', 'r') as f:
        data = json.load(f)
    print(f'Baseline Composite Score: {data[\"metrics\"][\"composite_score\"]:.3f}')
    print(f'Optimization Priorities: {data[\"optimization_priorities\"]}')
except FileNotFoundError:
    print('No baseline results - run optimization test first')
"
```

### **Environment Setup**
Ensure you have the required environment variables:
```bash
# Required
OPENAI_API_KEY=your_key
AZURE_OPENAI_API_KEY=your_key
AZURE_OPENAI_ENDPOINT=your_endpoint

# Optional (for specific tests)
MONGODB_URI=mongodb://localhost:27017
REDIS_URL=redis://localhost:6379
PINECONE_API_KEY=your_key
LANGCHAIN_API_KEY=your_key

# Automated Optimization Testing (Additional)
LANGSMITH_API_KEY=your_langsmith_api_key
LANGSMITH_TRACING=true
LANGCHAIN_TRACING_V2=true
LANGCHAIN_PROJECT=athlea-coaching-dev
```

## 📊 **Test Configuration**

### **Pytest Configuration** (`conftest.py`)
- Async test support
- Common fixtures
- Test environment setup
- Mock configurations

### **Test Fixtures** (`utilities/fixtures/`)
- User profiles for testing
- Sample conversation data
- Mock responses
- Test configurations

## 🔧 **Writing New Tests**

### **Unit Test Example**
```python
import pytest
from athlea_langgraph.agents.strength_agent import StrengthAgent

@pytest.mark.asyncio
async def test_strength_agent_initialization():
    agent = StrengthAgent()
    assert agent.name == "strength_coach"
    assert len(agent.tools) > 0
```

### **Integration Test Example**
```python
import pytest
from athlea_langgraph.graphs.optimized_comprehensive_coaching_graph import create_optimized_coaching_graph

@pytest.mark.asyncio
async def test_coaching_workflow():
    graph = await create_optimized_coaching_graph()
    result = await graph.ainvoke({
        "user_query": "How do I do squats?",
        "messages": []
    })
    assert result["final_response"]
    assert "squat" in result["final_response"].lower()
```

## 📋 **Test Standards**

### **Naming Convention**
- Test files: `test_*.py`
- Test functions: `test_*`
- Test classes: `Test*`

### **Test Structure**
- **Arrange**: Set up test data and conditions
- **Act**: Execute the functionality being tested
- **Assert**: Verify the expected outcomes

### **Async Testing**
Use `@pytest.mark.asyncio` for async tests:
```python
@pytest.mark.asyncio
async def test_async_function():
    result = await some_async_function()
    assert result is not None
```

## 🚨 **Common Issues**

### **Environment Variables**
- Ensure all required API keys are set
- Use `.env.local` for local development
- Check `conftest.py` for test-specific configurations

### **Async Tests**
- Always use `@pytest.mark.asyncio` for async tests
- Ensure proper cleanup of async resources
- Use `pytest-asyncio` plugin

### **Memory Tests**
- Some tests require MongoDB or Redis
- Use mocks when external services aren't available
- Clean up test data after tests

## 📈 **Test Metrics**

### **Coverage Goals**
- Unit tests: >90% coverage
- Integration tests: >80% coverage
- Critical paths: 100% coverage

### **Performance Benchmarks**
- Unit tests: <1s per test
- Integration tests: <10s per test
- E2E tests: <30s per test

## 🔄 **Continuous Integration**

Tests are automatically run on:
- Pull requests
- Main branch commits
- Release candidates

### **CI Configuration**
- GitHub Actions workflow
- Multiple Python versions
- Different environment configurations
- Coverage reporting

## � **Documentation Links**

- **[General Testing Guide](../docs/04_developer_guides/03_testing_guide.md)** - Unit, integration, E2E testing
- **[Automated Optimization Guide](../docs/04_developer_guides/04_automated_optimization_guide.md)** - Specialized optimization workflows
- **[LangSmith Portal](https://smith.langchain.com/)** - Trace monitoring (use `athlea-coaching-dev` project, "Last 6 hours" filter)

## 🎯 **Key Files for Optimization Testing**

| File | Purpose | Location |
|------|---------|----------|
| `test_automated_optimization.py` | **PRIMARY**: Core optimization testing | `tests/core/` |
| `test_tool_integration.py` | Tool integration testing | `tests/core/` |
| `test_streaming_api.py` | API streaming testing | `tests/core/` |
| `baseline_results.json` | Latest baseline metrics | Root directory |
| `optimization_demonstration_results.json` | Optimization analysis | Root directory |
| Test result files | Historical test data | `tests/results/` |

## �📞 **Support**

- **Issues**: Create GitHub issues for test failures
- **Questions**: Use team communication channels
- **Contributions**: Follow the test standards above
- **Optimization Issues**: See [Automated Optimization Guide](../docs/04_developer_guides/04_automated_optimization_guide.md) troubleshooting section

---

**Last Updated**: June 19, 2025
**Test Framework**: pytest + pytest-asyncio
**Coverage Tool**: pytest-cov
**Optimization System**: Automated Evidence-Based Deployment v1.0