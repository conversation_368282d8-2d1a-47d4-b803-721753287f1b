#!/usr/bin/env python3
#!/usr/bin/env python3
"""
Lever-specific validation scenarios for UX optimization testing.

Each scenario is designed to test the impact of specific lever improvements.
"""

from typing import Dict, List, Any


# Lever 2: Coach System Prompt test scenarios
LEVER_2_SCENARIOS = [
    {
        "name": "specificity_test",
        "query": "How do I improve my bench press?",
        "baseline_expected": {
            "has_numbers": False,
            "has_technical_terms": False,
            "generic_advice": True
        },
        "improved_expected": {
            "has_numbers": True,  # Should include specific weights, reps, sets
            "has_technical_terms": True,  # Should use terms like "ROM", "tempo", "RPE"
            "generic_advice": False
        }
    },
    {
        "name": "safety_acknowledgment",
        "query": "My shoulder hurts when I do overhead press",
        "baseline_expected": {
            "acknowledges_pain": False,
            "recommends_caution": False,
            "suggests_alternatives": False
        },
        "improved_expected": {
            "acknowledges_pain": True,
            "recommends_caution": True,
            "suggests_alternatives": True
        }
    },
    {
        "name": "comprehensive_coverage",
        "query": "Design a leg day workout for muscle growth",
        "baseline_expected": {
            "covers_exercises": True,
            "covers_sets_reps": False,
            "covers_rest_periods": False,
            "covers_progression": False
        },
        "improved_expected": {
            "covers_exercises": True,
            "covers_sets_reps": True,
            "covers_rest_periods": True,
            "covers_progression": True
        }
    }
]

# Lever 3: Tool Scripts test scenarios
LEVER_3_SCENARIOS = [
    {
        "name": "tool_success",
        "tool": "search_strength_exercises",
        "params": {"muscle_groups": ["chest"], "equipment": ["barbell"]},
        "baseline_expected": {
            "success": True,
            "has_results": True,
            "has_fallback": False
        },
        "improved_expected": {
            "success": True,
            "has_results": True,
            "has_fallback": True  # Fallback ready even on success
        }
    },
    {
        "name": "tool_failure_recovery",
        "tool": "search_strength_exercises",
        "params": {"muscle_groups": ["invalid_muscle_xyz"], "equipment": None},
        "baseline_expected": {
            "success": False,
            "has_results": False,
            "has_fallback": False,
            "error_handled": False
        },
        "improved_expected": {
            "success": True,  # Should succeed with fallback
            "has_results": True,  # Should provide fallback results
            "has_fallback": True,
            "error_handled": True
        }
    },
    {
        "name": "tool_edge_case",
        "tool": "azure_search_retriever",
        "params": {"query": "", "top_k": -1},  # Invalid params
        "baseline_expected": {
            "success": False,
            "error_message": "Invalid parameters",
            "graceful_failure": False
        },
        "improved_expected": {
            "success": True,  # Should handle edge case
            "error_message": None,
            "graceful_failure": True,
            "default_behavior": True
        }
    }
]

# Lever 4: Reasoning Node test scenarios
LEVER_4_SCENARIOS = [
    {
        "name": "pain_detection",
        "queries": [
            "My back hurts during squats",
            "I feel pain in my knee when running",
            "Experiencing discomfort in shoulder during bench press"
        ],
        "baseline_expected": {
            "pain_detected_rate": 0.33,  # Only catches obvious "hurts"
            "safety_routing": False,
            "immediate_acknowledgment": False
        },
        "improved_expected": {
            "pain_detected_rate": 1.0,  # Catches all pain indicators
            "safety_routing": True,
            "immediate_acknowledgment": True
        }
    },
    {
        "name": "domain_routing",
        "queries": [
            "Need help with diet and cardio plan",
            "Stressed about competition next week",
            "Want to improve cycling power output"
        ],
        "baseline_expected": {
            "correct_coach_routing": 0.33,
            "multi_coach_detection": False,
            "confidence_scores": False
        },
        "improved_expected": {
            "correct_coach_routing": 1.0,
            "multi_coach_detection": True,
            "confidence_scores": True
        }
    },
    {
        "name": "ambiguity_detection",
        "queries": [
            "Help",
            "Workout?",
            "Need advice"
        ],
        "baseline_expected": {
            "ambiguity_detected": False,
            "clarification_requested": False,
            "helpful_prompts": False
        },
        "improved_expected": {
            "ambiguity_detected": True,
            "clarification_requested": True,
            "helpful_prompts": True
        }
    }
]

# Lever 5: Planning Node test scenarios
LEVER_5_SCENARIOS = [
    {
        "name": "clarification_handling",
        "query": "I need help",
        "baseline_expected": {
            "asks_clarification": False,
            "provides_options": False,
            "guides_user": False
        },
        "improved_expected": {
            "asks_clarification": True,
            "provides_options": True,
            "guides_user": True,
            "example_response": "I'd be happy to help! Are you looking for: 1) Workout planning, 2) Nutrition advice, 3) Technique guidance?"
        }
    },
    {
        "name": "multi_coach_coordination",
        "query": "I want to lose weight through diet and exercise",
        "baseline_expected": {
            "identifies_multiple_domains": False,
            "coordinates_coaches": False,
            "unified_response": False
        },
        "improved_expected": {
            "identifies_multiple_domains": True,
            "coordinates_coaches": True,
            "unified_response": True,
            "coaches_involved": ["nutrition_coach", "cardio_coach", "strength_coach"]
        }
    }
]

# A/B Testing scenarios comparing baseline vs improved
AB_TEST_SCENARIOS = [
    {
        "lever": 2,
        "name": "prompt_specificity_ab_test",
        "test_queries": [
            "How should I warm up before deadlifts?",
            "What's the best rep range for muscle growth?",
            "How do I know if my squat form is correct?"
        ],
        "metrics_to_compare": [
            "specificity_score",
            "technical_term_count",
            "number_usage",
            "actionable_advice_score"
        ],
        "expected_improvement": {
            "specificity_score": 0.25,  # Expected 25% improvement
            "technical_term_count": 2.0,  # Expected 2x more technical terms
            "number_usage": 3.0,  # Expected 3x more numbers
            "actionable_advice_score": 0.30
        }
    },
    {
        "lever": 3,
        "name": "tool_reliability_ab_test",
        "test_scenarios": [
            {"tool": "search_exercises", "fail_injection": True},
            {"tool": "get_progression", "fail_injection": True},
            {"tool": "azure_search", "fail_injection": True}
        ],
        "metrics_to_compare": [
            "success_rate",
            "fallback_activation_rate",
            "user_satisfaction_estimate",
            "response_completeness"
        ],
        "expected_improvement": {
            "success_rate": 0.80,  # From 0% to 80%
            "fallback_activation_rate": 1.0,  # 100% fallback coverage
            "user_satisfaction_estimate": 0.50,
            "response_completeness": 0.40
        }
    }
]


def get_lever_scenarios(lever: int) -> List[Dict[str, Any]]:
    """Get test scenarios for a specific lever."""
    lever_scenario_map = {
        2: LEVER_2_SCENARIOS,
        3: LEVER_3_SCENARIOS,
        4: LEVER_4_SCENARIOS,
        5: LEVER_5_SCENARIOS
    }
    
    return lever_scenario_map.get(lever, [])


def get_ab_test_scenarios(lever: int) -> List[Dict[str, Any]]:
    """Get A/B test scenarios for a specific lever."""
    return [s for s in AB_TEST_SCENARIOS if s["lever"] == lever]


def validate_scenario_improvement(
    baseline_result: Dict[str, Any],
    improved_result: Dict[str, Any],
    expected_improvement: Dict[str, float]
) -> Dict[str, Any]:
    """Validate that improvements meet expected thresholds."""
    validation_results = {
        "met_expectations": True,
        "metrics": {}
    }
    
    for metric, expected_gain in expected_improvement.items():
        baseline_value = baseline_result.get(metric, 0)
        improved_value = improved_result.get(metric, 0)
        
        if baseline_value > 0:
            actual_improvement = (improved_value - baseline_value) / baseline_value
        else:
            actual_improvement = improved_value
        
        meets_expectation = actual_improvement >= expected_gain
        
        validation_results["metrics"][metric] = {
            "baseline": baseline_value,
            "improved": improved_value,
            "expected_improvement": expected_gain,
            "actual_improvement": actual_improvement,
            "meets_expectation": meets_expectation
        }
        
        if not meets_expectation:
            validation_results["met_expectations"] = False
    
    return validation_results 