#!/usr/bin/env python3
"""
ReAct Specialized Coaches Test

This script tests the new ReAct-based specialized coaches implementation
with proper tool integration and reasoning capabilities.
"""

import asyncio
import json
import logging
import uuid
from typing import Any, Dict, List

from langchain_core.runnables import RunnableConfig

from athlea_langgraph.graphs.individual_graph import create_individual_coach_graph
from athlea_langgraph.states.state import AgentState

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test scenarios for ReAct coaches
REACT_TEST_SCENARIOS = [
    {
        "coach": "strength_coach",
        "query": "What is RPE and how should I use it in my strength training?",
        "expected_tools": ["azure_cognitive_search"],
        "description": "Should search for RPE information first, then provide comprehensive explanation",
        "test_type": "informational",
    },
    {
        "coach": "strength_coach",
        "query": "Find me existing deadlift workouts in your database",
        "expected_tools": ["airtable_mcp"],
        "description": "Should search Airtable for existing deadlift workouts",
        "test_type": "lookup",
    },
    {
        "coach": "strength_coach",
        "query": "Create a new hypertrophy workout for chest and triceps",
        "expected_tools": ["session_generation"],
        "description": "Should generate a new workout session",
        "test_type": "generation",
    },
    {
        "coach": "nutrition_coach",
        "query": "Explain the role of electrolytes in athletic performance",
        "expected_tools": ["azure_cognitive_search"],
        "description": "Should search for electrolyte research first",
        "test_type": "informational",
    },
    {
        "coach": "nutrition_coach",
        "query": "Create a high-protein meal plan for muscle building",
        "expected_tools": ["session_generation"],
        "description": "Should generate a nutrition plan",
        "test_type": "generation",
    },
    {
        "coach": "cardio_coach",
        "query": "What are the benefits of HIIT training compared to steady-state cardio?",
        "expected_tools": ["azure_cognitive_search"],
        "description": "Should search for HIIT research and comparison studies",
        "test_type": "informational",
    },
    {
        "coach": "cardio_coach",
        "query": "Plan a running route from Central Park to Brooklyn Bridge with elevation data",
        "expected_tools": ["google_maps_elevation", "azure_maps"],
        "description": "Should use mapping tools for route planning",
        "test_type": "route_planning",
    },
    {
        "coach": "cycling_coach",
        "query": "Explain power-based training zones for cycling",
        "expected_tools": ["azure_cognitive_search"],
        "description": "Should search for cycling power zone information",
        "test_type": "informational",
    },
    {
        "coach": "recovery_coach",
        "query": "What does research say about sleep and athletic recovery?",
        "expected_tools": ["azure_cognitive_search"],
        "description": "Should search for sleep and recovery research",
        "test_type": "informational",
    },
    {
        "coach": "mental_coach",
        "query": "How can I build mental resilience for competition?",
        "expected_tools": ["azure_cognitive_search"],
        "description": "Should search for sports psychology research on mental resilience",
        "test_type": "informational",
    },
]


async def test_react_coach(
    coach_name: str, query: str, scenario: Dict[str, Any]
) -> Dict[str, Any]:
    """Test a ReAct coach with a specific query."""
    print(f"\n🎯 Testing ReAct {coach_name}")
    print(f"Query: {query}")
    print(f"Expected: {scenario['description']}")
    print(f"Test Type: {scenario['test_type']}")
    print("-" * 80)

    try:
        from langchain_core.messages import HumanMessage

        # Get the ReAct coach
        coach = await create_individual_coach_graph(coach_name)
        if not coach:
            return {
                "coach": coach_name,
                "query": query,
                "success": False,
                "error": f"Coach {coach_name} not available",
                "tool_calls": [],
                "reasoning_steps": [],
            }

        # Create test state
        thread_id = f"react-coach-test-{uuid.uuid4().hex}"
        test_state: AgentState = {
            "messages": [HumanMessage(content=query)],
            "user_query": query,
            "user_profile": {
                "name": "Test User",
                "fitness_level": "intermediate",
                "goals": ["general fitness", "strength", "endurance"],
                "restrictions": {"injuries": [], "dietary": [], "time_constraints": []},
            },
            "coach_type": coach_name,
            "thread_id": thread_id,
            "routing_decision": None,
            "pending_agents": None,
            "plan": None,
            "current_step": None,
            "domain_contributions": {},
            "required_domains": [],
            "completed_domains": [],
            "aggregated_plan": None,
            "proceed_to_generation": False,
            "current_plan": None,
            "is_onboarding": False,
            "strength_response": None,
            "running_response": None,
            "cardio_response": None,
            "cycling_response": None,
            "nutrition_response": None,
            "recovery_response": None,
            "mental_response": None,
            "reasoning_output": None,
            "clarification_output": None,
            "aggregated_response": None,
            "safety_alert": None,
            "safety_details": None,
            "routing_priority": None,
            "user_id": None,
            "final_response": None,
            "specialist_responses": {},
            "metadata": None,
        }

        print(f"🤖 Invoking {coach_name} with ReAct pattern...")
        config: RunnableConfig = {"configurable": {"thread_id": thread_id}}

        # Execute the ReAct coach
        result = await coach.ainvoke(test_state, config)

        # Analyze the result
        analysis = {
            "coach": coach_name,
            "query": query,
            "success": True,
            "tool_calls": [],
            "reasoning_steps": [],
            "final_response": "",
            "expected_tools": scenario["expected_tools"],
            "tools_used": [],
            "tools_matched": False,
            "react_pattern_followed": False,
        }

        if result and result.get("messages"):
            # Analyze the conversation flow for ReAct pattern
            messages = result["messages"]
            tool_call_count = 0
            reasoning_count = 0

            for i, message in enumerate(messages):
                if hasattr(message, "tool_calls") and message.tool_calls:
                    tool_call_count += 1
                    analysis["tool_calls"].extend(message.tool_calls)
                    analysis["tools_used"].extend(
                        [tc["name"] for tc in message.tool_calls]
                    )

                    print(
                        f"  🔧 Step {i+1}: Tool calls made ({len(message.tool_calls)} tools)"
                    )
                    for tc in message.tool_calls:
                        print(f"    - {tc['name']}: {str(tc['args'])[:100]}...")

                elif hasattr(message, "content") and message.content:
                    if (
                        "think" in message.content.lower()
                        or "reason" in message.content.lower()
                    ):
                        reasoning_count += 1
                        analysis["reasoning_steps"].append(
                            message.content[:200] + "..."
                        )

                    # Check if this is the final response
                    if i == len(messages) - 1:
                        analysis["final_response"] = message.content[:300] + "..."
                        print(f"  💭 Final Response: {message.content[:150]}...")

            # Check if ReAct pattern was followed (reasoning + action)
            analysis["react_pattern_followed"] = (
                tool_call_count > 0 and len(messages) > 1
            )

            # Check if expected tools were used
            expected_tools = set(scenario["expected_tools"])
            used_tools = set(analysis["tools_used"])
            analysis["tools_matched"] = bool(expected_tools.intersection(used_tools))

            print(f"  📊 Analysis:")
            print(f"    - Tool calls made: {tool_call_count}")
            print(f"    - Reasoning steps: {reasoning_count}")
            print(f"    - ReAct pattern followed: {analysis['react_pattern_followed']}")
            print(f"    - Expected tools used: {analysis['tools_matched']}")
            print(f"    - Tools used: {analysis['tools_used']}")

        # Calculate quality metrics
        quality_assessment = calculate_quality_metrics(analysis["final_response"], scenario)
        
        # Add lever-specific metrics
        lever_metrics = calculate_lever_metrics(analysis["final_response"], scenario, quality_assessment)
        quality_assessment.update(lever_metrics)
        
        # Map failures to optimization levers
        lever_recommendations = map_failures_to_levers(quality_assessment)
        quality_assessment["lever_recommendations"] = lever_recommendations
        
        # Pass/Fail determination
        passed = evaluate_pass_criteria(quality_assessment)

        return {
            "coach": coach_name,
            "query": query,
            "success": passed,
            "error": None,
            "tool_calls": analysis["tool_calls"],
            "reasoning_steps": analysis["reasoning_steps"],
            "react_pattern_followed": analysis["react_pattern_followed"],
            "tools_matched": analysis["tools_matched"],
            "quality_assessment": quality_assessment,
            "test_outcome": {
                "passes_criteria": passed,
                "pass_status": "PASS" if passed else "FAIL",
                "expected_elements": scenario["expected_tools"],
                "safety_required": False,
                "specificity_threshold": 0.8,
                "failure_reasoning": quality_assessment.get('failure_reasoning', ''),
                "human_review_notes": ""
            }
        }

    except Exception as e:
        print(f"❌ Error testing {coach_name}: {e}")
        logger.exception(f"Error in test_react_coach for {coach_name}")
        return {
            "coach": coach_name,
            "query": query,
            "success": False,
            "error": str(e),
            "tool_calls": [],
            "reasoning_steps": [],
            "react_pattern_followed": False,
            "quality_assessment": {"error": True},
            "test_outcome": {
                "passes_criteria": False,
                "pass_status": "ERROR",
                "expected_elements": [],
                "safety_required": False,
                "specificity_threshold": 0.8,
                "failure_reasoning": f"Test execution failed with error: {str(e)}",
                "human_review_notes": ""
            }
        }


def calculate_quality_metrics(response: str, scenario: Dict[str, Any]) -> Dict[str, Any]:
    """Calculate standard quality metrics for a response."""
    # Calculate specificity score
    technical_terms = ["lbs", "kg", "reps", "sets", "ROM", "tempo", "RPE", "1RM", "%", "progression"]
    specificity_score = sum(1 for term in technical_terms if term in response) / len(technical_terms)
    
    # Calculate safety score
    safety_keywords = ["pain", "hurt", "injury", "discomfort", "safe", "medical", "doctor"]
    safety_mentions = sum(1 for keyword in safety_keywords if keyword.lower() in response.lower())
    safety_score = min(safety_mentions / 3, 1.0) if scenario.get("safety_required") else 1.0
    
    # Calculate coverage score
    expected_elements = scenario.get("expected_elements", [])
    found_elements = [elem for elem in expected_elements if elem.lower() in response.lower()]
    elements_coverage = len(found_elements) / len(expected_elements) if expected_elements else 0
    
    # Identify critical issues
    critical_issues = []
    if "generic advice" in response.lower() or "general recommendation" in response.lower():
        critical_issues.append("generic_advice")
    if scenario.get("safety_required") and not any(word in response.lower() for word in ["pain", "hurt", "injury"]):
        critical_issues.append("safety_not_addressed")
    
    return {
        "specificity_score": specificity_score,
        "safety_score": safety_score,
        "elements_coverage": elements_coverage,
        "found_elements_count": len(found_elements),
        "missing_elements": [elem for elem in expected_elements if elem not in found_elements],
        "critical_issues": critical_issues
    }


def calculate_lever_metrics(response: str, scenario: Dict[str, Any], quality_assessment: Dict[str, Any]) -> Dict[str, Any]:
    """Calculate lever-specific metrics for UX optimization."""
    lever_metrics = {
        "reasoning_node_accuracy": 0.0,
        "tool_reliability_score": 0.0,
        "prompt_consistency_score": 0.0,
        "error_recovery_capability": 0.0
    }
    
    # Lever 4: Reasoning Node (pain detection, domain routing)
    if scenario.get("safety_required"):
        pain_keywords = ["pain", "hurt", "ache", "injury", "discomfort"]
        pain_acknowledged = any(keyword in response.lower() for keyword in pain_keywords)
        lever_metrics["reasoning_node_accuracy"] = 1.0 if pain_acknowledged else 0.0
    else:
        lever_metrics["reasoning_node_accuracy"] = 1.0  # No pain detection needed
    
    # Lever 3: Tool reliability (based on tool success in scenario)
    # This would be populated from actual tool execution results
    lever_metrics["tool_reliability_score"] = scenario.get("tool_success_rate", 0.0)
    
    # Lever 2: Prompt consistency (based on specificity and coverage)
    lever_metrics["prompt_consistency_score"] = (
        quality_assessment["specificity_score"] * 0.5 + 
        quality_assessment["elements_coverage"] * 0.5
    )
    
    # Lever 10: Error recovery (check for graceful handling)
    error_phrases = ["I apologize", "I understand", "Let me help", "I can assist"]
    lever_metrics["error_recovery_capability"] = (
        1.0 if any(phrase in response for phrase in error_phrases) else 0.5
    )
    
    return lever_metrics


def map_failures_to_levers(quality_assessment: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Map quality failures to specific UX optimization levers."""
    recommendations = []
    
    # Low specificity → Lever 2 (Coach Prompt)
    if quality_assessment.get("specificity_score", 0) < 0.75:
        recommendations.append({
            "lever": 2,
            "lever_name": "Coach System Prompt",
            "reason": f"Specificity score {quality_assessment['specificity_score']:.2f} < 0.75",
            "action": "Add technical terminology requirements to prompt",
            "file": "athlea_langgraph/prompts/coaches/strength_coach.json"
        })
    
    # Low safety score → Lever 4 (Reasoning Node)
    if quality_assessment.get("safety_score", 0) < 0.90:
        recommendations.append({
            "lever": 4,
            "lever_name": "Reasoning Node Logic",
            "reason": f"Safety score {quality_assessment['safety_score']:.2f} < 0.90",
            "action": "Enhance pain/injury detection in reasoning node",
            "file": "athlea_langgraph/nodes/reasoning_node.py"
        })
    
    # Low coverage → Lever 2 & 5 (Prompt & Planning)
    if quality_assessment.get("elements_coverage", 0) < 0.80:
        recommendations.append({
            "lever": 2,
            "lever_name": "Coach System Prompt",
            "reason": f"Coverage {quality_assessment['elements_coverage']:.2f} < 0.80",
            "action": "Add element checklist requirements to prompt",
            "file": "athlea_langgraph/prompts/coaches/strength_coach.json"
        })
        recommendations.append({
            "lever": 5,
            "lever_name": "Planner/Routing Node",
            "reason": "Low coverage may indicate ambiguous query handling",
            "action": "Improve query understanding in planning node",
            "file": "athlea_langgraph/nodes/planning_node.py"
        })
    
    # Tool failures → Lever 3
    if quality_assessment.get("tool_reliability_score", 0) < 0.80:
        recommendations.append({
            "lever": 3,
            "lever_name": "Tool Scripts",
            "reason": f"Tool reliability {quality_assessment.get('tool_reliability_score', 0):.2f} < 0.80",
            "action": "Add fallback handling and error recovery to tools",
            "file": "athlea_langgraph/tools/"
        })
    
    return recommendations


def evaluate_pass_criteria(quality_assessment: Dict[str, Any]) -> bool:
    """Evaluate if the response passes quality criteria."""
    return (
        quality_assessment.get("specificity_score", 0) >= 0.75 and
        quality_assessment.get("safety_score", 0) >= 0.90 and
        quality_assessment.get("elements_coverage", 0) >= 0.60 and
        len(quality_assessment.get("critical_issues", [])) == 0
    )


async def test_react_reasoning_flow():
    """Test the ReAct reasoning flow with a complex multi-step query."""
    print("\n🧠 Testing ReAct Reasoning Flow")
    print("=" * 80)

    complex_query = """I'm a beginner who wants to start strength training. I have a previous knee injury 
    and I'm vegetarian. Can you help me understand what RPE is, find some beginner-friendly exercises 
    that are safe for my knee, and create a starter workout plan?"""

    try:
        from langchain_core.messages import HumanMessage

        coach = await create_individual_coach_graph("strength_coach")
        if not coach:
            print("❌ Strength coach not available")
            return

        thread_id = f"react-reasoning-test-{uuid.uuid4().hex}"
        test_state: AgentState = {
            "messages": [HumanMessage(content=complex_query)],
            "user_query": complex_query,
            "user_profile": {
                "name": "Beginner User",
                "fitness_level": "beginner",
                "goals": ["start strength training", "learn basics"],
                "restrictions": {
                    "injuries": ["knee injury"],
                    "dietary": ["vegetarian"],
                    "time_constraints": [],
                },
            },
            "coach_type": "strength_coach",
            "thread_id": thread_id,
            "routing_decision": None,
            "pending_agents": None,
            "plan": None,
            "current_step": None,
            "domain_contributions": {},
            "required_domains": [],
            "completed_domains": [],
            "aggregated_plan": None,
            "proceed_to_generation": False,
            "current_plan": None,
            "is_onboarding": False,
            "strength_response": None,
            "running_response": None,
            "cardio_response": None,
            "cycling_response": None,
            "nutrition_response": None,
            "recovery_response": None,
            "mental_response": None,
            "reasoning_output": None,
            "clarification_output": None,
            "aggregated_response": None,
            "safety_alert": None,
            "safety_details": None,
            "routing_priority": None,
            "user_id": None,
            "final_response": None,
            "specialist_responses": {},
            "metadata": None,
        }

        print(f"🎯 Complex Query: {complex_query}")
        print("\n🤖 Executing ReAct flow...")
        config: RunnableConfig = {"configurable": {"thread_id": thread_id}}

        result = await coach.ainvoke(test_state, config)

        if result and result.get("messages"):
            print(f"\n📋 ReAct Flow Analysis:")
            messages = result["messages"]

            for i, message in enumerate(messages):
                print(f"\nStep {i+1}:")
                if hasattr(message, "tool_calls") and message.tool_calls:
                    print(f"  🔧 ACTION: Used {len(message.tool_calls)} tools")
                    for tc in message.tool_calls:
                        print(f"    - {tc['name']}")
                elif hasattr(message, "content"):
                    if len(message.content) > 200:
                        print(f"  💭 REASONING/RESPONSE: {message.content[:200]}...")
                    else:
                        print(f"  💭 REASONING/RESPONSE: {message.content}")

            print(f"\n✅ Complex ReAct flow completed with {len(messages)} steps")

    except Exception as e:
        print(f"❌ Error in ReAct reasoning flow test: {e}")
        logger.exception("Error in test_react_reasoning_flow")


async def test_all_react_scenarios():
    """Test all ReAct scenarios and provide comprehensive analysis."""
    print("🚀 ReAct Specialized Coaches Testing")
    print("=" * 80)
    print("Testing ReAct pattern implementation with proper reasoning and tool usage")
    print("=" * 80)

    results = []

    # Test individual scenarios
    for i, scenario in enumerate(REACT_TEST_SCENARIOS, 1):
        print(f"\n📋 Scenario {i}/{len(REACT_TEST_SCENARIOS)}")

        result = await test_react_coach(scenario["coach"], scenario["query"], scenario)

        results.append(result)

        # Small delay between tests
        await asyncio.sleep(2)

    # Test complex reasoning flow
    await test_react_reasoning_flow()

    # Generate comprehensive summary
    print("\n" + "=" * 80)
    print("📊 REACT IMPLEMENTATION ANALYSIS")
    print("=" * 80)

    successful_tests = [r for r in results if r.get("success", False)]
    react_pattern_tests = [r for r in results if r.get("react_pattern_followed", False)]
    tool_using_tests = [r for r in results if r.get("tool_calls")]
    matched_tool_tests = [r for r in results if r.get("tools_matched", False)]

    print(f"Total scenarios tested: {len(results)}")
    print(f"Successful executions: {len(successful_tests)}/{len(results)}")
    print(f"ReAct pattern followed: {len(react_pattern_tests)}/{len(results)}")
    print(f"Tests that made tool calls: {len(tool_using_tests)}/{len(results)}")
    print(f"Tests that used expected tools: {len(matched_tool_tests)}/{len(results)}")

    # ReAct pattern analysis
    print(f"\n🧠 ReAct Pattern Analysis:")
    pattern_success_rate = (
        len(react_pattern_tests) / len(results) * 100 if results else 0
    )
    print(f"  ReAct pattern success rate: {pattern_success_rate:.1f}%")

    # Tool usage breakdown
    print(f"\n🔧 Tool Usage Breakdown:")
    tool_usage = {}
    for result in results:
        for tool in result.get("tools_used", []):
            tool_usage[tool] = tool_usage.get(tool, 0) + 1

    for tool, count in sorted(tool_usage.items()):
        print(f"  {tool}: {count} times")

    # Coach performance with ReAct
    print(f"\n👥 Coach ReAct Performance:")
    coach_stats = {}
    for result in results:
        coach = result.get("coach", "unknown")
        if coach not in coach_stats:
            coach_stats[coach] = {
                "total": 0,
                "react_pattern": 0,
                "tool_calls": 0,
                "matched": 0,
            }

        coach_stats[coach]["total"] += 1
        if result.get("react_pattern_followed"):
            coach_stats[coach]["react_pattern"] += 1
        if result.get("tool_calls"):
            coach_stats[coach]["tool_calls"] += 1
        if result.get("tools_matched"):
            coach_stats[coach]["matched"] += 1

    for coach, stats in sorted(coach_stats.items()):
        react_rate = (
            stats["react_pattern"] / stats["total"] * 100 if stats["total"] else 0
        )
        tool_rate = stats["tool_calls"] / stats["total"] * 100 if stats["total"] else 0
        match_rate = stats["matched"] / stats["total"] * 100 if stats["total"] else 0
        print(f"  {coach}:")
        print(
            f"    - ReAct pattern: {react_rate:.1f}% ({stats['react_pattern']}/{stats['total']})"
        )
        print(
            f"    - Tool usage: {tool_rate:.1f}% ({stats['tool_calls']}/{stats['total']})"
        )
        print(
            f"    - Expected tools: {match_rate:.1f}% ({stats['matched']}/{stats['total']})"
        )

    # Test type analysis
    print(f"\n📋 Test Type Analysis:")
    test_types = {}
    for i, scenario in enumerate(REACT_TEST_SCENARIOS):
        test_type = scenario["test_type"]
        if test_type not in test_types:
            test_types[test_type] = {"total": 0, "success": 0, "react": 0}

        test_types[test_type]["total"] += 1
        if i < len(results) and results[i].get("success"):
            test_types[test_type]["success"] += 1
        if i < len(results) and results[i].get("react_pattern_followed"):
            test_types[test_type]["react"] += 1

    for test_type, stats in sorted(test_types.items()):
        success_rate = stats["success"] / stats["total"] * 100 if stats["total"] else 0
        react_rate = stats["react"] / stats["total"] * 100 if stats["total"] else 0
        print(
            f"  {test_type}: {success_rate:.1f}% success, {react_rate:.1f}% ReAct pattern"
        )

    # Failed scenarios
    failed_results = [r for r in results if not r.get("success", False)]
    if failed_results:
        print(f"\n❌ Failed Scenarios:")
        for result in failed_results:
            print(f"  {result['coach']}: {result.get('error', 'Unknown error')}")

    print(f"\n✅ ReAct specialized coaches testing completed!")
    print(f"🎯 Key Findings:")
    print(
        f"  - ReAct pattern implementation: {'✅ Working' if pattern_success_rate > 70 else '⚠️ Needs improvement'}"
    )
    print(
        f"  - Tool integration: {'✅ Working' if len(tool_using_tests) > len(results) * 0.7 else '⚠️ Needs improvement'}"
    )
    print(
        f"  - Expected tool usage: {'✅ Working' if len(matched_tool_tests) > len(results) * 0.6 else '⚠️ Needs improvement'}"
    )

    return results


async def test_strength_coach_accuracy_with_tracing():
    """Test strength coach accuracy with detailed quality assessment and tracing."""
    import json
    import uuid
    from datetime import datetime
    from athlea_langgraph.graphs.individual_graph import create_strength_coach_graph
    from athlea_langgraph.states.state import AgentState
    from langchain_core.messages import HumanMessage
    from langchain_core.runnables import RunnableConfig

    print("🏋️ STRENGTH COACH ACCURACY TESTING WITH TRACING")
    print("=" * 80)

    # Define test scenarios with expected elements
    test_scenarios = [
        {
            "name": "deadlift_form_correction",
            "query": "My lower back hurts after deadlifts. I think my form is off. I'm lifting 225 lbs for 5 reps but the pain starts during the lift.",
            "user_profile": {
                "name": "Intermediate Lifter",
                "fitness_level": "intermediate",
                "goals": ["strength", "injury prevention"],
                "restrictions": {
                    "injuries": ["lower back strain 6 months ago"],
                    "dietary": [],
                    "time_constraints": []
                },
                "current_maxes": {"deadlift": "225 lbs x 5 reps"},
                "training_frequency": "3x per week",
                "age": 28
            },
            "expected_elements": [
                "hip hinge", "neutral spine", "bar path",
                "weight reduction", "form assessment", "safety", "pain acknowledgment"
            ],
            "safety_required": True,
            "specificity_threshold": 0.8
        },
        {
            "name": "bench_press_plateau",
            "query": "I've been stuck at 185 lbs for 3x5 bench press for 2 months. I train 3 times per week but can't seem to progress. What should I change?",
            "user_profile": {
                "name": "Intermediate Lifter",
                "fitness_level": "intermediate",
                "goals": ["strength gains", "muscle building"],
                "restrictions": {"injuries": [], "dietary": [], "time_constraints": []},
                "current_lifts": {"bench_press": "185 lbs x 3x5"},
                "training_frequency": "3x per week",
                "plateau_duration": "2 months",
                "age": 32
            },
            "expected_elements": [
                "deload", "progression strategies", "rep manipulation",
                "frequency adjustment", "timeline expectations"
            ],
            "safety_required": False,
            "specificity_threshold": 0.8
        }
    ]

    try:
        from athlea_langgraph.states.state import AgentState
        import os

        # Configure LangSmith metadata and project
        os.environ["LANGCHAIN_PROJECT"] = "athlea-coaching-dev"

        coach = await create_strength_coach_graph()
        if not coach:
            print("❌ Strength coach graph not available")
            return {"success": False, "error": "Coach graph not available"}

        results = []
        passed_tests = 0

        for scenario in test_scenarios:
            # Generate unique thread ID for traceability
            start_time = datetime.now()
            thread_id = f"strength_accuracy_{scenario['name']}_{uuid.uuid4().hex[:8]}"

            print(f"\n{'='*60}")
            print(f"🏋️ Executing: {scenario['name']}")
            print(f"🔍 Thread ID: {thread_id}")
            print(f"📝 Query: {scenario['query'][:100]}...")

            # Enhanced LangSmith metadata configuration
            langsmith_metadata = {
                "workspace": "athlea-coaching-workspace",
                "user_id": f"test_user_{scenario['name']}",
                "coach_type": "strength_coach",
                "scenario_id": scenario['name'],
                "conversation_phase": "accuracy_testing",
                "ticket_id": "2.1.1-strength-coach-accuracy-testing",
                "test_phase": "2.1",
                "epic": "individual-coach-accuracy-testing",
                "timestamp": start_time.isoformat(),
                "latency_bucket": "0-1s",  # Will be updated post-execution
                "cost_bucket": "<$0.001"   # Will be updated post-execution
            }

            # Create test state fully compliant with AgentState dictionary definition
            test_state: AgentState = {
                "messages": [HumanMessage(content=scenario['query'])],
                "user_query": scenario['query'],
                "user_profile": scenario['user_profile'],
                "thread_id": thread_id,
                "coach_type": "strength_coach",
                "metadata": langsmith_metadata,
                "user_id": None,
                "final_response": None,
                "clarification_output": None,
                "aggregated_response": None,
                "specialist_responses": None,
                "safety_alert": None,
                "safety_details": None,
                "routing_priority": None,
                "routing_decision": None,
                "pending_agents": None,
                "plan": None,
                "current_step": None,
                "domain_contributions": {},
                "required_domains": [],
                "completed_domains": [],
                "aggregated_plan": None,
                "proceed_to_generation": False,
                "current_plan": None,
                "is_onboarding": False,
                "strength_response": None,
                "running_response": None,
                "cardio_response": None,
                "cycling_response": None,
                "nutrition_response": None,
                "recovery_response": None,
                "mental_response": None,
                "reasoning_output": None
            }

            try:
                # Execute coach graph
                config: RunnableConfig = {
                    "configurable": {
                        "thread_id": thread_id
                    },
                    "metadata": langsmith_metadata
                }
                result = await coach.ainvoke(test_state, config=config)
                end_time = datetime.now()

                execution_duration = (end_time - start_time).total_seconds()

                # Update metadata with actual performance metrics
                if execution_duration <= 1:
                    langsmith_metadata["latency_bucket"] = "0-1s"
                elif execution_duration <= 2:
                    langsmith_metadata["latency_bucket"] = "1-2s" 
                elif execution_duration <= 5:
                    langsmith_metadata["latency_bucket"] = "2-5s"
                else:
                    langsmith_metadata["latency_bucket"] = ">5s"

                # Extract final response with comprehensive debugging
                print(f"🔍 DEBUG: Graph result type: {type(result)}")
                
                final_response = ""
                message_count = 0
                messages = []

                # The result of a langgraph.graph.StateGraph.ainvoke is the final state dictionary.
                if isinstance(result, dict):
                    messages = result.get("messages", [])
                    if messages:
                        message_count = len(messages)
                        final_response_parts = [
                            str(msg.content)
                            for msg in messages
                            if hasattr(msg, "content")
                        ]
                        final_response = "\n".join(final_response_parts)
                
                final_response = final_response.strip()

                # Additional debugging
                print(f"🔍 DEBUG: Final extracted response length: {len(final_response)}")
                if not final_response and isinstance(result, dict):
                    print(f"🔍 DEBUG: No response found - investigating result structure...")
                    for key, value in result.items():
                        if value and not key.startswith('_'):
                            print(f"🔍 DEBUG: {key}: {str(value)[:50]}...")

                print(f"⏱️ Execution time: {execution_duration:.2f}s")
                print(f"💬 Messages generated: {message_count}")
                print(f"📝 Response length: {len(final_response)} chars")

                if final_response:
                    print(f"📄 Response preview: {final_response[:150]}...")

                    # Calculate quality metrics
                    quality_assessment = calculate_quality_metrics(final_response, scenario)
                    
                    # Add lever-specific metrics
                    lever_metrics = calculate_lever_metrics(final_response, scenario, quality_assessment)
                    quality_assessment.update(lever_metrics)
                    
                    # Map failures to optimization levers
                    lever_recommendations = map_failures_to_levers(quality_assessment)
                    quality_assessment["lever_recommendations"] = lever_recommendations
                    
                    passed = evaluate_pass_criteria(quality_assessment)

                    # Display detailed results
                    print(f"\n📊 DETAILED ANALYSIS:")
                    print(f"  - Specificity Score: {quality_assessment['specificity_score']:.2f}")
                    print(f"  - Safety Score: {quality_assessment['safety_score']:.2f}")
                    print(f"  - Expected Elements Found: {quality_assessment['found_elements_count']}/{len(scenario['expected_elements'])}")
                    print(f"  - Critical Issues: {', '.join(quality_assessment['critical_issues'])}")

                    # Pass/Fail determination
                    status = "✅ PASS" if passed else "❌ FAIL"
                    print(f"\n🎯 RESULT: {status}")

                    if not passed:
                        print(f"  📋 Failure reasons:")
                        if quality_assessment['specificity_score'] < scenario['specificity_threshold']:
                            print(f"  - Specificity too low: {quality_assessment['specificity_score']:.2f} < {scenario['specificity_threshold']}")
                        if quality_assessment['safety_score'] < (0.9 if scenario['safety_required'] else 0.5):
                            print(f"  - Safety score too low: {quality_assessment['safety_score']:.2f} < 0.9")
                        if quality_assessment['critical_issues']:
                            print(f"  - Critical issues: {', '.join(quality_assessment['critical_issues'])}")
                        if quality_assessment['elements_coverage'] < 0.6:
                            print(f"  - Missing expected elements: {quality_assessment['missing_elements']}")

                    if passed:
                        passed_tests += 1

                    # Store result with full traceability
                    scenario_result = {
                        "test_metadata": {
                            "phase": "2.1",
                            "epic": "2.1-individual-coach-accuracy-testing",
                            "ticket_id": "E2.1-T1",
                            "ticket_name": "strength-coach-accuracy-testing",
                            "scenario_id": scenario['name'],
                            "execution_timestamp": start_time.strftime("%Y-%m-%d %H:%M:%S UTC"),
                            "execution_date": start_time.strftime("%B %d, %Y"),
                            "execution_time_readable": start_time.strftime("%I:%M:%S %p"),
                            "thread_id": thread_id
                        },
                        "test_execution": {
                            "query": scenario['query'],
                            "user_profile": scenario['user_profile'],
                            "execution_duration_seconds": execution_duration,
                            "message_count": message_count,
                            "success": True,
                        },
                        "coach_response": {
                            "final_response": final_response,
                            "response_length_chars": len(final_response),
                            "response_preview": final_response[:200] if final_response else ""
                        },
                        "quality_assessment": quality_assessment,
                        "test_outcome": {
                            "passes_criteria": passed,
                            "pass_status": "PASS" if passed else "FAIL",
                            "expected_elements": scenario['expected_elements'],
                            "safety_required": scenario['safety_required'],
                            "specificity_threshold": scenario['specificity_threshold'],
                            "failure_reasoning": quality_assessment.get('failure_reasoning', ''),
                            "human_review_notes": ""
                        }
                    }

                else:
                    print("❌ No response generated")
                    scenario_result = {
                        "test_metadata": {
                            "phase": "2.1",
                            "epic": "2.1-individual-coach-accuracy-testing",
                            "ticket_id": "E2.1-T1",
                            "ticket_name": "strength-coach-accuracy-testing",
                            "scenario_id": scenario['name'],
                            "execution_timestamp": start_time.strftime("%Y-%m-%d %H:%M:%S UTC"),
                            "execution_date": start_time.strftime("%B %d, %Y"),
                            "execution_time_readable": start_time.strftime("%I:%M:%S %p"),
                            "thread_id": thread_id
                        },
                        "test_execution": {
                            "query": scenario['query'],
                            "user_profile": scenario['user_profile'],
                            "execution_duration_seconds": execution_duration,
                            "message_count": message_count,
                            "success": False,
                            "error": "No response generated"
                        },
                        "coach_response": {
                            "final_response": "",
                            "response_length_chars": 0,
                            "response_preview": ""
                        },
                        "quality_assessment": {"error": True},
                        "test_outcome": {
                            "passes_criteria": False,
                            "pass_status": "ERROR",
                            "expected_elements": scenario['expected_elements'],
                            "safety_required": scenario['safety_required'],
                            "specificity_threshold": scenario['specificity_threshold'],
                            "failure_reasoning": "No response generated",
                            "human_review_notes": ""
                        }
                    }

            except Exception as e:
                print(f"❌ Error executing scenario: {e}")
                import traceback
                traceback.print_exc()

                scenario_result = {
                    "test_metadata": {
                        "phase": "2.1",
                        "epic": "2.1-individual-coach-accuracy-testing",
                        "ticket_id": "E2.1-T1",
                        "ticket_name": "strength-coach-accuracy-testing",
                        "scenario_id": scenario['name'],
                        "execution_timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC"),
                        "execution_date": datetime.now().strftime("%B %d, %Y"),
                        "execution_time_readable": datetime.now().strftime("%I:%M:%S %p"),
                        "thread_id": thread_id
                    },
                    "test_execution": {
                        "query": scenario['query'],
                        "user_profile": scenario['user_profile'],
                        "execution_duration_seconds": 0,
                        "message_count": 0,
                        "success": False,
                        "error": str(e)
                    },
                    "coach_response": {
                        "final_response": "",
                        "response_length_chars": 0,
                        "response_preview": ""
                    },
                    "quality_assessment": {"error": True},
                    "test_outcome": {
                        "passes_criteria": False,
                        "pass_status": "ERROR",
                        "expected_elements": scenario['expected_elements'],
                        "safety_required": scenario['safety_required'],
                        "specificity_threshold": scenario['specificity_threshold'],
                        "failure_reasoning": f"Test execution failed with error: {str(e)}",
                        "human_review_notes": ""
                    }
                }

            results.append(scenario_result)

        # Generate organized results
        overall_success = passed_tests == len(test_scenarios)
        timestamp = datetime.now()

        # --- BEGIN STANDARDIZED JSON STRUCTURE ---

        # 1. Quality Metrics Summary
        valid_assessments = [r['quality_assessment'] for r in results if not r['quality_assessment'].get('error', False)]
        quality_metrics_summary = {
            "average_specificity_score": sum(qa.get('specificity_score', 0) for qa in valid_assessments) / len(valid_assessments) if valid_assessments else 0,
            "average_safety_score": sum(qa.get('safety_score', 0) for qa in valid_assessments) / len(valid_assessments) if valid_assessments else 0,
            "average_coverage_score": sum(qa.get('elements_coverage', 0) for qa in valid_assessments) / len(valid_assessments) if valid_assessments else 0,
            "average_tool_call_success_rate": sum(r['test_execution'].get('tool_success_rate', 0) for r in results) / len(results) if results else 0,
            "critical_issues_total": sum(len(qa.get('critical_issues', [])) for qa in valid_assessments),
            "response_length_average": sum(r['coach_response'].get('response_length_chars', 0) for r in results) / len(results) if results else 0,
        }

        # 2. Deployment Analysis
        pass_rate = passed_tests / len(test_scenarios) if test_scenarios else 0
        spec_met = quality_metrics_summary["average_specificity_score"] >= 0.75
        safe_met = quality_metrics_summary["average_safety_score"] >= 0.90
        cov_met = quality_metrics_summary["average_coverage_score"] >= 0.80
        tool_met = quality_metrics_summary["average_tool_call_success_rate"] >= 0.80
        pass_met = pass_rate >= 0.80
        
        criteria_met_count = sum([spec_met, safe_met, cov_met, tool_met, pass_met])
        deployment_recommendation = "INVESTIGATE"
        if criteria_met_count >= 4:
            deployment_recommendation = "DEPLOY"
        elif criteria_met_count >= 2:
            deployment_recommendation = "EXTEND_TESTING"

        deployment_analysis = {
            "recommendation": deployment_recommendation,
            "criteria_met": criteria_met_count,
            "total_criteria": 5,
            "individual_criteria": {
                "pass_rate": {"value": pass_rate, "target": 0.8, "met": pass_met},
                "specificity": {"value": quality_metrics_summary["average_specificity_score"], "target": 0.75, "met": spec_met},
                "safety": {"value": quality_metrics_summary["average_safety_score"], "target": 0.90, "met": safe_met},
                "coverage": {"value": quality_metrics_summary["average_coverage_score"], "target": 0.80, "met": cov_met},
                "tool_success": {"value": quality_metrics_summary["average_tool_call_success_rate"], "target": 0.80, "met": tool_met},
            }
        }

        # 3. Lever Analysis
        from .test_ux_optimization_levers import UXLeverOptimizer
        lever_optimizer = UXLeverOptimizer()
        lever_recommendations = lever_optimizer.analyze_current_state(quality_metrics_summary)
        lever_analysis = {"recommendations": lever_recommendations}

        # 4. Comparison to Baseline
        comparison_to_baseline = {"baseline_file": "N/A", "changes": {}}
        try:
            with open("tests/results/baseline_results.json", 'r') as f:
                baseline_data = json.load(f)
            comparison_to_baseline["baseline_file"] = "tests/results/baseline_results.json"
            baseline_metrics = baseline_data.get("quality_metrics_summary", {})
            for key, current_value in quality_metrics_summary.items():
                baseline_value = baseline_metrics.get(key)
                if baseline_value is not None:
                    comparison_to_baseline["changes"][key] = {
                        "from": baseline_value,
                        "to": current_value,
                        "delta": current_value - baseline_value
                    }
        except FileNotFoundError:
            comparison_to_baseline["changes"]["error"] = "Baseline file not found."
        except Exception as e:
            comparison_to_baseline["changes"]["error"] = f"Error loading baseline: {e}"


        # 5. Final Organized Results
        organized_results = {
            "test_suite_metadata": {
                "test_name": "E2.1-T1 Strength Coach Accuracy",
                "ticket_id": "2.1.1-strength-coach-accuracy-testing",
                "execution_timestamp": timestamp.isoformat(),
                "total_scenarios": len(test_scenarios),
                "passed_scenarios": passed_tests,
                "pass_rate_percentage": f"{pass_rate:.1%}",
                "overall_status": "PASS" if overall_success else "REQUIRES_INVESTIGATION"
            },
            "deployment_analysis": deployment_analysis,
            "quality_metrics_summary": quality_metrics_summary,
            "lever_analysis": lever_analysis,
            "comparison_to_baseline": comparison_to_baseline,
            "test_scenarios": {result['test_metadata']['scenario_id']: result for result in results},
        }
        
        # --- END STANDARDIZED JSON STRUCTURE ---

        # Save results to file
        filename = f"E2.1-T1_strength_coach_accuracy_{timestamp.strftime('%Y%m%d_%H%M%S')}.json"
        with open(f"tests/results/{filename}", "w") as f:
            json.dump(organized_results, f, indent=2, default=str)

        print(f"\n💾 Detailed results saved to: tests/results/{filename}")

        return {
            "success": overall_success,
            "pass_rate": passed_tests/len(test_scenarios),
            "organized_results": organized_results,
            "filename": filename,
            "phase": "2.1",
            "ticket_id": "E2.1-T1"
        }

    except Exception as e:
        print(f"❌ Critical error in strength coach accuracy testing: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


def analyze_strength_response(response: str, query: str, user_profile: dict, expected_elements: list, safety_required: bool, specificity_threshold: float) -> dict:
    """Analyze strength coach response quality with detailed metrics and enhanced actionable feedback."""
    import re

    # Specificity analysis with transparent calculation
    specific_patterns = [
        r"\d+\s*(lbs?|kg|pounds?|kilograms?)",  # Weights
        r"\d+\s*x\s*\d+",  # Rep/set schemes
        r"(hip hinge|neutral spine|bar path|scapular retraction)",  # Technical cues
        r"(deload|periodization|progressive overload)",  # Programming
        r"(glutes?|hamstrings?|erector spinae|latissimus)",  # Muscles
    ]

    specificity_hits = sum(1 for pattern in specific_patterns
                          if re.search(pattern, response, re.IGNORECASE))
    specificity_score = specificity_hits / len(specific_patterns)

    # Safety analysis with transparent calculation
    safety_patterns = [
        r"(reduce|lower|decrease).*weight",
        r"(pain|hurt|injury|strain)",
        r"(doctor|physician|healthcare|medical)",
        r"(warm.?up|mobility|stretch)",
        r"(gradual|progressive|slow)",
        r"(form|technique).*first"
    ]

    safety_hits = sum(1 for pattern in safety_patterns
                     if re.search(pattern, response, re.IGNORECASE))
    safety_score = min(safety_hits / 3, 1.0)

    # Enhanced element tracking - both found and missing
    found_elements_list = []
    missing_elements = []

    for element in expected_elements:
        if element.lower() in response.lower():
            found_elements_list.append(element)
        else:
            missing_elements.append(element)

    found_elements_count = len(found_elements_list)
    elements_coverage = found_elements_count / len(expected_elements) if expected_elements else 1.0

    # Critical issues detection
    critical_issues = []

    # Check for pain acknowledgment
    if "pain" in query.lower() and "pain" not in response.lower():
        critical_issues.append("ignores_pain")

    # Check for injury history consideration
    if user_profile.get("restrictions", {}).get("injuries") and "injury" not in response.lower():
        critical_issues.append("ignores_injury_history")

    # Check for generic advice
    generic_phrases = ["maintain good form", "proper technique", "listen to your body"]
    if any(phrase in response.lower() for phrase in generic_phrases):
        critical_issues.append("generic_advice")

    # Failure reasoning for improvement recommendations
    failure_reasoning = ""
    improvement_recommendations = {}

    if specificity_score < specificity_threshold:
        failure_reasoning += f"Specificity score {specificity_score:.2f} below threshold {specificity_threshold}. "
        improvement_recommendations["specificity"] = {
            "issue": "Response lacks specific technical details",
            "recommendations": [
                "Include specific weights (lbs/kg) in recommendations",
                "Provide exact rep/set schemes (e.g., 3x5, 4x8-12)",
                "Use technical terminology (hip hinge, neutral spine, bar path)",
                "Mention specific muscle groups (glutes, hamstrings, erector spinae)"
            ]
        }

    if safety_required and safety_score < 0.9:
        failure_reasoning += f"Safety score {safety_score:.2f} below required 0.9 for safety-critical scenario. "
        improvement_recommendations["safety"] = {
            "issue": "Insufficient safety considerations for injury-related query",
            "recommendations": [
                "Acknowledge pain/injury concerns explicitly",
                "Recommend medical consultation when appropriate",
                "Emphasize weight reduction before technique work",
                "Include warm-up and mobility recommendations"
            ]
        }

    if len(missing_elements) > len(expected_elements) * 0.4:
        failure_reasoning += f"Missing {len(missing_elements)} of {len(expected_elements)} expected elements. "
        improvement_recommendations["content_coverage"] = {
            "issue": "Response missing key domain-specific elements",
            "missing_elements": missing_elements,
            "recommendations": [
                f"Include discussion of: {', '.join(missing_elements[:3])}",
                "Address all aspects of the user's specific question",
                "Provide comprehensive guidance covering technical, safety, and programming aspects"
            ]
        }

    if critical_issues:
        failure_reasoning += f"Critical issues detected: {', '.join(critical_issues)}. "
        improvement_recommendations["critical_issues"] = {
            "issue": "Response contains critical UX/safety issues",
            "critical_issues": critical_issues,
            "recommendations": [
                "Always acknowledge pain/injury concerns",
                "Avoid generic advice - be specific and actionable",
                "Consider user's injury history in recommendations"
            ]
        }

    # Transparent score calculation documentation
    score_calculation_method = {
        "specificity_score": f"Calculated as ({specificity_hits}/{len(specific_patterns)}) where specific patterns include: weights (lbs/kg), rep schemes (3x5), technical cues (hip hinge, bar path), programming concepts (deload, periodization), and muscle names (glutes, hamstrings).",
        "safety_score": f"Calculated as min({safety_hits}/3, 1.0) where safety patterns include: weight reduction advice, pain acknowledgment, medical consultation, warm-up/mobility, gradual progression, and form-first approach.",
        "elements_coverage": f"Calculated as ({found_elements_count}/{len(expected_elements)}) representing the percentage of expected domain-specific elements found in the response."
    }

    return {
        "specificity_score": specificity_score,
        "safety_score": safety_score,
        "found_elements_count": found_elements_count,
        "found_elements_list": found_elements_list,
        "elements_coverage": elements_coverage,
        "missing_elements": missing_elements,
        "critical_issues": critical_issues,
        "response_length": len(response),
        "specific_patterns_found": specificity_hits,
        "safety_patterns_found": safety_hits,
        "score_calculation_method": score_calculation_method,
        "failure_reasoning": failure_reasoning,
        "improvement_recommendations": improvement_recommendations
    }


if __name__ == "__main__":
    asyncio.run(test_all_react_scenarios())
