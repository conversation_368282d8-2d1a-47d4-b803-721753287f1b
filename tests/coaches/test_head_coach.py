"""
Tests for Head Coach Agent

This module contains comprehensive tests for the head coach agent,
including routing logic, clarification handling, and coordination tests.
"""

import asyncio
import logging
from typing import Any, Dict, List
from unittest.mock import AsyncMock, Mock, patch

import pytest
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from athlea_langgraph.agents.head_coach import (
    clarification_node,
    head_coach_node,
    head_coach_router,
)
from athlea_langgraph.states.state import AgentState

from ..fixtures.user_profiles import (
    ALL_PROFILES,
    BEGINNER_MUSCLE_GAIN,
    INTERMEDIATE_RUNNER,
    OLDER_ADULT,
    VEGAN_ATHLETE,
)
from ..synthetic.synthetic_coaching_sessions import (
    ALL_SESSIONS,
    CONFLICTING_GOALS,
    MINIMAL_INFORMATION,
    MULTI_AGENT_BEGINNER,
    SIMPLE_NUTRITION_QUERY,
    SIMPLE_STRENGTH_QUERY,
)

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestHeadCoachNode:
    """Test cases for the head_coach_node function."""

    @pytest.mark.asyncio
    async def test_head_coach_with_plan(self):
        """Test head coach execution when a plan exists."""
        state = {
            "user_query": "I want to build muscle",
            "plan": ["strength_coach", "nutrition_coach"],
            "current_step": 0,
            "messages": [HumanMessage(content="I want to build muscle")],
        }

        result = await head_coach_node(state)

        # Should increment current_step
        assert "current_step" in result
        assert result["current_step"] == 1

    @pytest.mark.asyncio
    async def test_head_coach_without_plan(self):
        """Test head coach execution when no plan exists."""
        state = {
            "user_query": "I want to get fit",
            "plan": [],
            "current_step": 0,
            "messages": [HumanMessage(content="I want to get fit")],
        }

        result = await head_coach_node(state)

        # Should return empty dict when no plan
        assert result == {}

    @pytest.mark.asyncio
    async def test_head_coach_plan_completed(self):
        """Test head coach when plan is already completed."""
        state = {
            "user_query": "I want to build muscle",
            "plan": ["strength_coach", "nutrition_coach"],
            "current_step": 2,  # Beyond plan length
            "messages": [HumanMessage(content="I want to build muscle")],
        }

        result = await head_coach_node(state)

        # Should return empty dict when plan is completed
        assert result == {}

    @pytest.mark.asyncio
    async def test_head_coach_partial_plan_execution(self):
        """Test head coach in middle of plan execution."""
        state = {
            "user_query": "I need strength and nutrition help",
            "plan": ["strength_coach", "nutrition_coach", "recovery_coach"],
            "current_step": 1,
            "messages": [HumanMessage(content="I need help")],
        }

        result = await head_coach_node(state)

        # Should increment to step 2
        assert result["current_step"] == 2


class TestHeadCoachRouter:
    """Test cases for the head_coach_router function."""

    def test_router_with_plan_execution(self):
        """Test router when executing a plan."""
        state = {
            "plan": ["strength_coach", "nutrition_coach"],
            "current_step": 0,
            "user_query": "I want to build muscle",
        }

        result = head_coach_router(state)

        # Should route to strength_coach (first in plan)
        assert result == "strength_coach"

    def test_router_plan_step_mapping(self):
        """Test router correctly maps plan steps to node names."""
        test_cases = [
            (["strength_coach"], 0, "strength_coach"),
            (["running_coach"], 0, "cardio_coach"),  # Running maps to cardio
            (["nutrition_coach"], 0, "nutrition_coach"),
            (["recovery_coach"], 0, "recovery_coach"),
            (["mental_coach"], 0, "mental_coach"),
        ]

        for plan, step, expected in test_cases:
            state = {"plan": plan, "current_step": step, "user_query": "Test query"}

            result = head_coach_router(state)
            assert result == expected

    def test_router_with_specialist_responses(self):
        """Test router when specialist responses exist."""
        state = {
            "plan": ["strength_coach"],
            "current_step": 1,  # Plan completed
            "strength_response": "Here's your strength plan...",
            "user_query": "I want to build muscle",
        }

        result = head_coach_router(state)

        # Should route to aggregation
        assert result == "aggregate_responses"

    def test_router_multiple_specialist_responses(self):
        """Test router with multiple specialist responses."""
        state = {
            "plan": ["strength_coach", "nutrition_coach"],
            "current_step": 2,  # Plan completed
            "strength_response": "Strength advice...",
            "nutrition_response": "Nutrition advice...",
            "user_query": "I need help",
        }

        result = head_coach_router(state)

        # Should route to aggregation
        assert result == "aggregate_responses"

    def test_router_clarification_needed(self):
        """Test router when clarification is needed."""
        state = {"plan": ["clarification"], "current_step": 0, "user_query": "help"}

        result = head_coach_router(state)

        # Should route to clarification
        assert result == "clarification"

    def test_router_no_plan_default(self):
        """Test router defaults to clarification when no plan."""
        state = {
            "plan": [],
            "current_step": 0,
            "user_query": "I need help but it's unclear",
        }

        result = head_coach_router(state)

        # Should default to clarification
        assert result == "clarification"

    def test_router_invalid_plan_step(self):
        """Test router with plan step beyond plan length."""
        state = {
            "plan": ["strength_coach"],
            "current_step": 5,  # Way beyond plan
            "user_query": "Test",
        }

        result = head_coach_router(state)

        # Should route to clarification when plan is invalid
        assert result == "clarification"


class TestClarificationNode:
    """Test cases for the clarification_node function."""

    @pytest.mark.asyncio
    async def test_clarification_basic_functionality(self):
        """Test basic clarification node functionality."""
        state = {
            "user_query": "help",
            "reasoning_output": "Request is too vague",
            "messages": [HumanMessage(content="help")],
        }

        with patch(
            "athlea_langgraph.agents.head_coach.create_azure_chat_openai"
        ) as mock_llm_factory:
            # Mock the LLM
            mock_llm = AsyncMock()
            mock_response = Mock()
            mock_response.content = "Could you please provide more details about what type of fitness help you're looking for?"
            mock_llm.ainvoke.return_value = mock_response
            mock_llm_factory.return_value = mock_llm

            result = await clarification_node(state)

            # Should return clarification message
            assert "messages" in result
            assert "clarification_output" in result
            assert len(result["messages"]) == 1
            assert result["messages"][0].name == "head_coach"
            assert "more details" in result["clarification_output"]

    @pytest.mark.asyncio
    async def test_clarification_with_reasoning(self):
        """Test clarification node incorporates reasoning."""
        state = {
            "user_query": "fitness",
            "reasoning_output": "User needs to specify goals and current fitness level",
            "messages": [HumanMessage(content="fitness")],
        }

        with patch(
            "athlea_langgraph.agents.head_coach.create_azure_chat_openai"
        ) as mock_llm_factory:
            mock_llm = AsyncMock()
            mock_response = Mock()
            mock_response.content = "I see you're interested in fitness. To provide the best guidance, could you tell me about your specific goals and current fitness level?"
            mock_llm.ainvoke.return_value = mock_response
            mock_llm_factory.return_value = mock_llm

            result = await clarification_node(state)

            # Verify reasoning was incorporated
            mock_llm.ainvoke.assert_called_once()
            call_args = mock_llm.ainvoke.call_args[0][0]  # Get the messages

            # Should have system and human messages
            assert len(call_args) == 2
            assert isinstance(call_args[0], SystemMessage)
            assert isinstance(call_args[1], HumanMessage)

            # Human message should contain reasoning
            human_content = call_args[1].content
            assert "User needs to specify goals" in human_content

    @pytest.mark.asyncio
    async def test_clarification_error_handling(self):
        """Test clarification node error handling."""
        state = {"user_query": "help", "messages": [HumanMessage(content="help")]}

        with patch(
            "athlea_langgraph.agents.head_coach.create_azure_chat_openai"
        ) as mock_llm_factory:
            # Mock LLM to raise an error
            mock_llm = AsyncMock()
            mock_llm.ainvoke.side_effect = Exception("LLM error")
            mock_llm_factory.return_value = mock_llm

            # Should handle the error gracefully
            try:
                result = await clarification_node(state)
                # If it doesn't raise, that's also acceptable behavior
            except Exception as e:
                # If it raises, it should be the original error
                assert "LLM error" in str(e)


class TestHeadCoachIntegration:
    """Integration tests for head coach with different scenarios."""

    @pytest.mark.asyncio
    async def test_simple_single_agent_routing(self):
        """Test routing for simple single-agent scenarios."""
        # Test strength query
        strength_state = {
            "plan": ["strength_coach"],
            "current_step": 0,
            "user_query": "I want to build muscle",
        }

        result = head_coach_router(strength_state)
        assert result == "strength_coach"

        # Test nutrition query
        nutrition_state = {
            "plan": ["nutrition_coach"],
            "current_step": 0,
            "user_query": "What should I eat?",
        }

        result = head_coach_router(nutrition_state)
        assert result == "nutrition_coach"

    @pytest.mark.asyncio
    async def test_multi_agent_routing_sequence(self):
        """Test routing sequence for multi-agent scenarios."""
        base_state = {
            "plan": ["strength_coach", "nutrition_coach"],
            "user_query": "I need workout and diet help",
        }

        # First step - should route to strength
        state_step_0 = {**base_state, "current_step": 0}
        result = head_coach_router(state_step_0)
        assert result == "strength_coach"

        # Second step - should route to nutrition
        state_step_1 = {**base_state, "current_step": 1}
        result = head_coach_router(state_step_1)
        assert result == "nutrition_coach"

        # After completion with responses - should aggregate
        state_completed = {
            **base_state,
            "current_step": 2,
            "strength_response": "Strength advice",
            "nutrition_response": "Nutrition advice",
        }
        result = head_coach_router(state_completed)
        assert result == "aggregate_responses"

    @pytest.mark.asyncio
    async def test_clarification_scenarios(self):
        """Test various clarification scenarios."""
        scenarios = [
            {
                "name": "No plan",
                "state": {"plan": [], "current_step": 0, "user_query": "help"},
                "expected": "clarification",
            },
            {
                "name": "Clarification plan",
                "state": {
                    "plan": ["clarification"],
                    "current_step": 0,
                    "user_query": "unclear request",
                },
                "expected": "clarification",
            },
            {
                "name": "Plan beyond bounds",
                "state": {
                    "plan": ["strength_coach"],
                    "current_step": 5,
                    "user_query": "test",
                },
                "expected": "clarification",
            },
        ]

        for scenario in scenarios:
            result = head_coach_router(scenario["state"])
            assert (
                result == scenario["expected"]
            ), f"Failed for scenario: {scenario['name']}"


class TestHeadCoachWithSyntheticData:
    """Test head coach with synthetic coaching session data."""

    @pytest.mark.asyncio
    async def test_simple_strength_session_routing(self):
        """Test routing for simple strength session."""
        session = SIMPLE_STRENGTH_QUERY

        state = {
            "plan": ["strength_coach"],
            "current_step": 0,
            "user_query": session["initial_query"],
            "user_profile": session["user_profile"],
        }

        # Test routing
        result = head_coach_router(state)
        assert result == "strength_coach"

        # Test head coach node
        node_result = await head_coach_node(state)
        assert node_result["current_step"] == 1

    @pytest.mark.asyncio
    async def test_multi_agent_session_routing(self):
        """Test routing for multi-agent session."""
        session = MULTI_AGENT_BEGINNER

        state = {
            "plan": session["expected_agents"],
            "current_step": 0,
            "user_query": session["initial_query"],
            "user_profile": session["user_profile"],
        }

        # Should route to first agent in plan
        result = head_coach_router(state)
        assert result == session["expected_agents"][0]

    @pytest.mark.asyncio
    async def test_minimal_information_routing(self):
        """Test routing when minimal information is provided."""
        session = MINIMAL_INFORMATION

        state = {
            "plan": [],  # No plan generated due to lack of info
            "current_step": 0,
            "user_query": session["initial_query"],
            "user_profile": session["user_profile"],
        }

        # Should route to clarification
        result = head_coach_router(state)
        assert result == "clarification"

    @pytest.mark.asyncio
    async def test_conflicting_goals_routing(self):
        """Test routing for conflicting goals scenario."""
        session = CONFLICTING_GOALS

        state = {
            "plan": session["expected_agents"],
            "current_step": 0,
            "user_query": session["initial_query"],
            "user_profile": session["user_profile"],
        }

        # Should route to first agent despite complexity
        result = head_coach_router(state)
        assert result == session["expected_agents"][0]


class TestHeadCoachEdgeCases:
    """Test edge cases and error scenarios for head coach."""

    def test_router_with_missing_fields(self):
        """Test router behavior with missing state fields."""
        # Missing plan
        state = {"current_step": 0, "user_query": "test"}
        result = head_coach_router(state)
        assert result == "clarification"

        # Missing current_step
        state = {"plan": ["strength_coach"], "user_query": "test"}
        result = head_coach_router(state)
        # Should still work, default current_step to 0
        assert result == "strength_coach"

    def test_router_with_empty_plan(self):
        """Test router with explicitly empty plan."""
        state = {"plan": [], "current_step": 0, "user_query": "I need help"}

        result = head_coach_router(state)
        assert result == "clarification"

    def test_router_with_none_plan(self):
        """Test router when plan is None."""
        state = {"plan": None, "current_step": 0, "user_query": "I need help"}

        result = head_coach_router(state)
        assert result == "clarification"

    @pytest.mark.asyncio
    async def test_head_coach_node_with_none_plan(self):
        """Test head coach node when plan is None."""
        state = {"plan": None, "current_step": 0, "user_query": "test"}

        result = await head_coach_node(state)
        assert result == {}

    def test_router_specialist_response_detection(self):
        """Test router correctly detects different specialist responses."""
        response_fields = [
            "strength_response",
            "running_response",
            "cardio_response",
            "cycling_response",
            "nutrition_response",
            "recovery_response",
            "mental_response",
        ]

        for field in response_fields:
            state = {
                "plan": ["test_agent"],
                "current_step": 1,  # Plan completed
                field: "Some response content",
                "user_query": "test",
            }

            result = head_coach_router(state)
            assert result == "aggregate_responses", f"Failed to detect {field}"

    def test_router_unknown_agent_in_plan(self):
        """Test router with unknown agent in plan."""
        state = {"plan": ["unknown_agent"], "current_step": 0, "user_query": "test"}

        # Should return the unknown agent name (will be handled downstream)
        result = head_coach_router(state)
        assert result == "unknown_agent"


class TestHeadCoachPerformance:
    """Performance tests for head coach components."""

    def test_router_performance_multiple_calls(self):
        """Test router performance with multiple rapid calls."""
        import time

        state = {
            "plan": ["strength_coach", "nutrition_coach"],
            "current_step": 0,
            "user_query": "test",
        }

        start_time = time.time()

        # Make 100 rapid calls
        for _ in range(100):
            result = head_coach_router(state)
            assert result == "strength_coach"

        end_time = time.time()

        # Should complete quickly (less than 1 second for 100 calls)
        assert (end_time - start_time) < 1.0

    @pytest.mark.asyncio
    async def test_head_coach_node_performance(self):
        """Test head coach node performance."""
        import time

        state = {"plan": ["strength_coach"], "current_step": 0, "user_query": "test"}

        start_time = time.time()

        # Make multiple async calls
        tasks = [head_coach_node(state) for _ in range(10)]
        results = await asyncio.gather(*tasks)

        end_time = time.time()

        # All should complete successfully
        assert len(results) == 10
        for result in results:
            assert result["current_step"] == 1

        # Should complete quickly
        assert (end_time - start_time) < 2.0


class TestHeadCoachStateConsistency:
    """Test state consistency across head coach operations."""

    @pytest.mark.asyncio
    async def test_state_preservation(self):
        """Test that head coach preserves state correctly."""
        original_state = {
            "plan": ["strength_coach", "nutrition_coach"],
            "current_step": 0,
            "user_query": "I need help",
            "user_profile": BEGINNER_MUSCLE_GAIN,
            "messages": [HumanMessage(content="I need help")],
            "custom_field": "should be preserved",
        }

        result = await head_coach_node(original_state)

        # Should only modify current_step
        assert result == {"current_step": 1}

        # Original state should be unchanged
        assert original_state["current_step"] == 0
        assert original_state["custom_field"] == "should be preserved"

    def test_router_state_immutability(self):
        """Test that router doesn't modify state."""
        original_state = {
            "plan": ["strength_coach"],
            "current_step": 0,
            "user_query": "test",
            "extra_data": {"some": "value"},
        }

        state_copy = original_state.copy()

        result = head_coach_router(original_state)

        # State should be unchanged
        assert original_state == state_copy
        assert result == "strength_coach"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
