#!/usr/bin/env python3
"""
Complete n8n Cloud ↔ LangGraph Integration Test

This script demonstrates the complete end-to-end integration:
1. LangGraph API serving coaching endpoints
2. n8n Cloud webhook triggering workflows
3. Email automation and response processing
"""

import requests
import json
import time
from datetime import datetime

# Configuration
LANGGRAPH_API_BASE = "http://localhost:8001"
N8N_WEBHOOK_TEST = (
    "https://athlea.app.n8n.cloud/webhook-test/0bd1c98d-0d78-49bf-ac94-d98d9a96b3ef"
)
N8N_WEBHOOK_PROD = (
    "https://athlea.app.n8n.cloud/webhook/0bd1c98d-0d78-49bf-ac94-d98d9a96b3ef"
)


def test_langgraph_endpoints():
    """Test all LangGraph API endpoints."""
    print("🚀 Testing LangGraph API Endpoints")
    print("=" * 50)

    endpoints = [
        {"name": "Health Check", "method": "GET", "url": "/health"},
        {"name": "Available Coaches", "method": "GET", "url": "/coaching/coaches"},
        {
            "name": "Coaching Execution",
            "method": "POST",
            "url": "/coaching/execute",
            "data": {
                "message": "I need a beginner strength training plan",
                "user_id": "integration_test",
                "coach_type": "strength",
            },
        },
        {
            "name": "n8n Webhook Handler",
            "method": "POST",
            "url": "/n8n/webhooks/coaching",
            "data": {
                "message": "How should I train for a marathon?",
                "user_id": "n8n_test",
                "coach_type": "running",
            },
        },
    ]

    results = []

    for endpoint in endpoints:
        print(f"\n🔗 Testing: {endpoint['name']}")
        print(f"   {endpoint['method']} {endpoint['url']}")

        try:
            if endpoint["method"] == "GET":
                response = requests.get(f"{LANGGRAPH_API_BASE}{endpoint['url']}")
            else:
                response = requests.post(
                    f"{LANGGRAPH_API_BASE}{endpoint['url']}",
                    json=endpoint.get("data", {}),
                )

            if response.status_code == 200:
                print(f"   ✅ Success ({response.status_code})")
                if endpoint["name"] == "Available Coaches":
                    data = response.json()
                    print(f"   📋 Found {len(data.get('coaches', []))} coaches")
                elif endpoint["name"] == "Coaching Execution":
                    data = response.json()
                    print(f"   💡 Response: {data.get('response', '')[:80]}...")
                    print(f"   ⚙️ Tools: {data.get('tools_used', [])}")
                elif endpoint["name"] == "n8n Webhook Handler":
                    data = response.json()
                    print(f"   📊 Status: {data.get('status')}")
                    print(
                        f"   🎯 Response: {data.get('coaching_response', '')[:80]}..."
                    )
                results.append(True)
            else:
                print(f"   ❌ Failed ({response.status_code})")
                results.append(False)

        except Exception as e:
            print(f"   ❌ Error: {e}")
            results.append(False)

    return results


def test_n8n_webhooks():
    """Test n8n Cloud webhooks."""
    print("\n🌤️ Testing n8n Cloud Webhooks")
    print("=" * 50)

    test_data = {
        "message": "I want to build muscle and lose fat. What's the best approach?",
        "user_id": "complete_test_user",
        "coach_type": "strength",
        "user_email": "<EMAIL>",
        "session_data": {
            "source": "complete_integration_test",
            "timestamp": datetime.now().isoformat(),
        },
    }

    webhooks = [
        {"name": "Test Webhook", "url": N8N_WEBHOOK_TEST},
        {"name": "Production Webhook", "url": N8N_WEBHOOK_PROD},
    ]

    results = []

    for webhook in webhooks:
        print(f"\n🔗 Testing: {webhook['name']}")
        print(f"   URL: {webhook['url']}")

        try:
            response = requests.post(
                webhook["url"],
                json=test_data,
                headers={"Content-Type": "application/json"},
                timeout=30,
            )

            if response.status_code == 200:
                print("   ✅ Success - Webhook responded!")
                try:
                    data = response.json()
                    print(f"   📋 Response: {json.dumps(data, indent=2)}")
                except:
                    print(f"   📋 Response Text: {response.text}")
                results.append(True)
            elif response.status_code == 404:
                print("   ⏸️ Webhook not active (404)")
                if "webhook-test" in webhook["url"]:
                    print("   💡 Click 'Listen for Test Event' in n8n to activate")
                else:
                    print("   💡 Activate the workflow in n8n Cloud to enable")
                results.append(False)
            else:
                print(f"   ❌ Failed ({response.status_code})")
                print(f"   📋 Response: {response.text[:200]}")
                results.append(False)

        except Exception as e:
            print(f"   ❌ Error: {e}")
            results.append(False)

    return results


def demonstrate_complete_flow():
    """Demonstrate the complete coaching flow."""
    print("\n🎯 Complete Integration Flow Demonstration")
    print("=" * 60)

    flow_steps = [
        "1. 👤 User submits coaching question",
        "2. 🌐 n8n Cloud receives webhook request",
        "3. ✅ n8n validates input data",
        "4. 🚀 n8n calls LangGraph API",
        "5. 🤖 LangGraph executes coaching workflow",
        "6. 🧠 AI generates personalized response",
        "7. 📊 n8n processes the results",
        "8. 📧 n8n sends beautiful email summary",
        "9. 📝 n8n returns success response",
        "10. 📈 All execution logs available in n8n Cloud",
    ]

    print("Complete flow architecture:")
    for step in flow_steps:
        print(f"   {step}")
        time.sleep(0.5)  # Dramatic effect!

    print("\n🎨 Email Features:")
    print("   • 🏋️ Branded Athlea header with gradients")
    print("   • 📝 User's original question highlighted")
    print("   • 🎯 Full AI coaching response")
    print("   • 🧠 Reasoning steps breakdown")
    print("   • ⚙️ Session metadata (duration, tools, etc.)")
    print("   • 🚀 Call-to-action for next session")


def main():
    """Run complete integration test suite."""
    print("🌟 Athlea LangGraph ↔ n8n Cloud Integration")
    print("Complete End-to-End Test Suite")
    print("=" * 70)

    # Test LangGraph API
    langgraph_results = test_langgraph_endpoints()

    # Test n8n webhooks
    n8n_results = test_n8n_webhooks()

    # Demonstrate complete flow
    demonstrate_complete_flow()

    # Results summary
    print("\n📊 Integration Test Results")
    print("=" * 40)

    total_tests = len(langgraph_results) + len(n8n_results)
    passed_tests = sum(langgraph_results) + sum(n8n_results)

    print(
        f"LangGraph API Tests: {sum(langgraph_results)}/{len(langgraph_results)} passed"
    )
    print(f"n8n Webhook Tests: {sum(n8n_results)}/{len(n8n_results)} passed")
    print(
        f"Overall: {passed_tests}/{total_tests} tests passed ({passed_tests/total_tests*100:.1f}%)"
    )

    if passed_tests == total_tests:
        print("\n🎉 PERFECT! Complete integration is working!")
    elif passed_tests >= total_tests * 0.8:
        print("\n✅ EXCELLENT! Integration is mostly working!")
    else:
        print("\n⚠️ Some issues detected. Check the output above.")

    print("\n🚀 Next Steps:")
    print("1. Import n8n_production_workflow.json into n8n Cloud")
    print("2. Configure email credentials in n8n (optional)")
    print("3. Activate the workflow to enable production webhook")
    print("4. Update the API URL if deploying to production server")
    print("5. Set up monitoring and scaling as needed")

    print("\n📚 Files Created:")
    print("• n8n_langgraph_integration.py - FastAPI wrapper for LangGraph")
    print("• n8n_production_workflow.json - Production n8n workflow")
    print("• PRODUCTION_SETUP_GUIDE.md - Complete setup instructions")
    print("• test_*.py scripts - Comprehensive testing suite")

    print("\n🎯 Your integration is ready for production! 🚀")


if __name__ == "__main__":
    main()
