#!/usr/bin/env python3
"""
Simple test to isolate Gremlin service issues
"""

import asyncio
from athlea_langgraph.services.gremlin_service import get_gremlin_service
from athlea_langgraph.config.graphrag_config import get_graphrag_config


async def test_gremlin():
    try:
        print("🔄 Testing Gremlin service...")

        # Print configuration details
        config = get_graphrag_config()
        print(f"📋 Configuration:")
        print(f"  Cosmos Endpoint: {config.cosmos_endpoint}")
        print(f"  Cosmos Database: {config.cosmos_database}")
        print(f"  Cosmos Graph: {config.cosmos_graph}")
        print(f"  Cosmos Key: {'SET' if config.cosmos_key else 'NOT SET'}")

        service = get_gremlin_service()
        print(f"✅ Service created: {type(service)}")
        print(f"🔗 Connection string: {service._connection_string}")

        # Test the vertex count query
        query_input = {"query": "g.V().count()"}
        print(f"🔍 Testing query: {query_input}")

        result = await service.execute_query(query_input)
        print(f"📊 Result type: {type(result)}")
        print(f"📊 Success: {result.success}")
        print(f"📊 Message: {result.message}")

        if result.success:
            print(f"📊 Results: {result.results}")
            print(f"📊 Count: {result.result_count}")
        else:
            print(f"❌ Error type: {result.error_type}")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_gremlin())
