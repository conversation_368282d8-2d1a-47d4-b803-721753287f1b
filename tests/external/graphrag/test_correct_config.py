#!/usr/bin/env python3
"""
Test Gremlin Connection with Correct Configuration

Using the exact values provided by the user:
- COSMOS_GREMLIN_ENDPOINT=https://cosmos-gremlin-aml.documents.azure.com:443/
- COSMOS_GREMLIN_KEY=****************************************************************************************
- COSMOS_GREMLIN_DATABASE=graphragdb
- COSMOS_GREMLIN_GRAPH=knowledge-graph
- COSMOS_GREMLIN_PARTITION_KEY=/type
- COSMOS_GREMLIN_USE_SSL=true
"""

import logging
from urllib.parse import urlparse

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def convert_endpoint_to_wss(https_endpoint):
    """Convert HTTPS endpoint to WSS format for Gremlin."""
    parsed = urlparse(https_endpoint)

    # Convert https:// to wss://
    wss_endpoint = f"wss://{parsed.hostname}"

    # Add port if specified
    if parsed.port:
        wss_endpoint += f":{parsed.port}"

    # Add gremlin path
    wss_endpoint += "/gremlin"

    return wss_endpoint


def test_with_correct_config():
    """Test connection using the exact provided configuration."""
    print("🔧 TESTING WITH CORRECT CONFIGURATION")
    print("=" * 60)

    # Exact values from user
    cosmos_endpoint = "https://cosmos-gremlin-aml.documents.azure.com:443/"
    cosmos_key = "****************************************************************************************"
    cosmos_database = "graphragdb"
    cosmos_graph = "knowledge-graph"
    cosmos_partition_key = "/type"
    cosmos_use_ssl = True

    print(f"Original HTTPS endpoint: {cosmos_endpoint}")

    # Convert to WSS format
    wss_endpoint = convert_endpoint_to_wss(cosmos_endpoint)
    print(f"Converted WSS endpoint: {wss_endpoint}")

    # Username format for Cosmos DB Gremlin
    username = f"/dbs/{cosmos_database}/colls/{cosmos_graph}"
    print(f"Username: {username}")
    print(f"Database: {cosmos_database}")
    print(f"Graph: {cosmos_graph}")
    print(f"Partition key: {cosmos_partition_key}")
    print(f"Use SSL: {cosmos_use_ssl}")

    try:
        from gremlin_python.driver import client

        print("\n🚀 Testing connection...")

        # Create client without enable_ssl parameter (it's not supported)
        gremlin_client = client.Client(
            wss_endpoint,
            "g",  # traversal source
            username=username,
            password=cosmos_key,
        )

        print("✓ Gremlin client created successfully")

        # Test basic query
        print("Testing vertex count...")
        result = gremlin_client.submit("g.V().count()")
        count = result.one()

        print(f"✅ SUCCESS! Vertex count: {count}")

        # Test additional queries
        print("\nTesting additional queries...")

        # Edge count
        result = gremlin_client.submit("g.E().count()")
        edge_count = result.one()
        print(f"✓ Edge count: {edge_count}")

        # Test vertex labels
        result = gremlin_client.submit("g.V().label().dedup()")
        labels = result.all().result()
        print(f"✓ Vertex labels found: {labels}")

        # Test edge labels
        result = gremlin_client.submit("g.E().label().dedup()")
        edge_labels = result.all().result()
        print(f"✓ Edge labels found: {edge_labels}")

        return True

    except Exception as e:
        print(f"✗ Connection failed: {e}")
        print(f"Error type: {type(e).__name__}")

        # Additional error details
        if hasattr(e, "status"):
            print(f"Status: {e.status}")
        if hasattr(e, "message"):
            print(f"Message: {e.message}")

        return False


def test_alternative_username_formats():
    """Test different username formats that might work with the partition key."""
    print("\n🔍 TESTING ALTERNATIVE USERNAME FORMATS")
    print("=" * 60)

    cosmos_endpoint = "https://cosmos-gremlin-aml.documents.azure.com:443/"
    cosmos_key = "****************************************************************************************"
    cosmos_database = "graphragdb"
    cosmos_graph = "knowledge-graph"

    wss_endpoint = convert_endpoint_to_wss(cosmos_endpoint)

    # Different username formats to try
    username_formats = [
        f"/dbs/{cosmos_database}/colls/{cosmos_graph}",
        f"/{cosmos_database}",
        f"/dbs/{cosmos_database}",
        cosmos_database,
        f"/dbs/{cosmos_database}/colls/{cosmos_graph}/graphs/g",
    ]

    try:
        from gremlin_python.driver import client

        for i, username in enumerate(username_formats, 1):
            print(f"\nFormat {i}: {username}")

            try:
                test_client = client.Client(
                    wss_endpoint, "g", username=username, password=cosmos_key
                )

                # Try a simple query
                result = test_client.submit("g.V().limit(1).count()")
                count = result.one()
                print(f"  ✅ SUCCESS! Count: {count}")
                return True

            except Exception as e:
                print(f"  ✗ Failed: {e}")
                continue

        print("\n❌ All username formats failed")
        return False

    except ImportError:
        print("✗ gremlinpython not available")
        return False


def main():
    """Run tests with correct configuration."""
    print("🎯 GREMLIN CONNECTION TEST WITH CORRECT CONFIGURATION")
    print("=" * 70)
    print("Using exact values provided by user")
    print("=" * 70)

    # Test with standard format
    success = test_with_correct_config()

    if not success:
        print("\n📋 Standard format failed, trying alternatives...")
        success = test_alternative_username_formats()

    print("\n🎯 FINAL RESULT")
    print("=" * 70)

    if success:
        print("✅ GREMLIN CONNECTION SUCCESSFUL!")
        print("✅ Configuration is correct!")
        print("\n📋 Next steps:")
        print("1. Update GraphRAG service with working configuration")
        print("2. Set Azure Search credentials")
        print("3. Test full GraphRAG integration")
    else:
        print("❌ CONNECTION STILL FAILING")
        print("\n🔧 Additional troubleshooting needed:")
        print("1. Verify Cosmos DB account has Gremlin API enabled")
        print("2. Check firewall/network security rules")
        print("3. Verify access key is still valid")
        print("4. Confirm database and graph exist")


if __name__ == "__main__":
    main()
