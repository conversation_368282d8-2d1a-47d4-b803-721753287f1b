"""
Test Coach Routing Fix

Test that verifies the bug fix where GraphRAG nodes were overriding
Intelligence Hub's primary_coach decision with hardcoded strength_coach defaults.

This test ensures that:
1. Recovery queries route to recovery_coach (not strength_coach)
2. Nutrition queries route to nutrition_coach (not strength_coach)
3. The primary_coach field from Intelligence Hub is preserved
"""

import asyncio
import logging
import pytest
from typing import Dict, Any
from unittest.mock import AsyncMock, patch

# Configure logging for the test
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@pytest.mark.asyncio
async def test_knowledge_assessment_preserves_primary_coach():
    """Test that knowledge_assessment_node preserves Intelligence Hub's primary_coach decision."""

    # Import the function we're testing
    from athlea_langgraph.agents.graphrag_nodes import knowledge_assessment_node

    # Test cases that were failing before the fix
    test_cases = [
        {
            "name": "Recovery Query",
            "primary_coach": "recovery_coach",
            "user_query": "I need help with sleep optimization and injury prevention",
            "expected_domain": "recovery_coach",
        },
        {
            "name": "Nutrition Query",
            "primary_coach": "nutrition_coach",
            "user_query": "What should I eat before and after workouts?",
            "expected_domain": "nutrition_coach",
        },
        {
            "name": "Mental Query",
            "primary_coach": "mental_coach",
            "user_query": "How can I improve my motivation and mental toughness?",
            "expected_domain": "mental_coach",
        },
        {
            "name": "Cardio Query",
            "primary_coach": "cardio_coach",
            "user_query": "How should I train for a marathon?",
            "expected_domain": "cardio_coach",
        },
    ]

    for test_case in test_cases:
        logger.info(f"\n🧪 Testing {test_case['name']}")

        # Create state with Intelligence Hub's decision
        state = {
            "user_query": test_case["user_query"],
            "primary_coach": test_case["primary_coach"],  # This should be preserved!
            "execution_steps": ["intelligence_hub"],
            "messages": [],
        }

        # Mock the GraphRAG retriever to avoid external dependencies
        mock_assessment = {
            "needs_knowledge_retrieval": False,
            "knowledge_confidence": 0.8,
            "knowledge_assessment_reasoning": "Test assessment",
        }

        with patch(
            "athlea_langgraph.agents.graphrag_nodes.get_graphrag_retriever"
        ) as mock_retriever:
            mock_instance = AsyncMock()
            mock_instance.assess_knowledge_needs.return_value = mock_assessment
            mock_retriever.return_value = mock_instance

            # Call the knowledge assessment node
            result = await knowledge_assessment_node(state)

            # Verify the correct coach domain was used
            logger.info(f"✅ Primary coach preserved: {test_case['primary_coach']}")
            logger.info(f"✅ Expected domain: {test_case['expected_domain']}")

            # The key assertion: verify the assessment was called with the correct domain
            mock_instance.assess_knowledge_needs.assert_called_once()
            call_args = mock_instance.assess_knowledge_needs.call_args
            used_domain = call_args[0][1]  # Second argument is coach_domain

            assert used_domain == test_case["expected_domain"], (
                f"❌ {test_case['name']} failed: "
                f"Expected domain '{test_case['expected_domain']}', "
                f"but got '{used_domain}'. "
                f"This means the primary_coach '{test_case['primary_coach']}' was not preserved!"
            )

            logger.info(
                f"✅ {test_case['name']} passed: Correct domain '{used_domain}' used"
            )


@pytest.mark.asyncio
async def test_graphrag_retrieval_preserves_primary_coach():
    """Test that graphrag_retrieval_node preserves Intelligence Hub's primary_coach decision."""

    # Import the function we're testing
    from athlea_langgraph.agents.graphrag_nodes import graphrag_retrieval_node

    # Test case for recovery coach (the bug case from the logs)
    test_case = {
        "name": "Recovery Query via GraphRAG",
        "primary_coach": "recovery_coach",
        "user_query": "I need research on sleep optimization protocols",
        "expected_domain": "recovery_coach",
    }

    logger.info(f"\n🧪 Testing {test_case['name']}")

    # Create state with Intelligence Hub's decision
    state = {
        "user_query": test_case["user_query"],
        "primary_coach": test_case["primary_coach"],  # This should be preserved!
        "needs_knowledge_retrieval": True,  # Trigger GraphRAG
        "execution_steps": ["intelligence_hub", "knowledge_assessment"],
        "messages": [],
    }

    # Mock the GraphRAG retriever methods
    mock_retrieval_results = {
        "vector_results": [],
        "graph_results": [],
        "knowledge_context": "Test knowledge context",
        "knowledge_sources": [],
    }

    with patch(
        "athlea_langgraph.agents.graphrag_nodes.get_graphrag_retriever"
    ) as mock_retriever:
        mock_instance = AsyncMock()
        mock_instance.route_retrieval_type.return_value = "vector"
        mock_instance.decompose_query.return_value = [test_case["user_query"]]
        mock_instance.vector_search.return_value = []
        mock_instance.synthesize_knowledge.return_value = "Test knowledge"
        mock_retriever.return_value = mock_instance

        # Call the GraphRAG retrieval node
        result = await graphrag_retrieval_node(state)

        # Verify the correct coach domain was used in all GraphRAG calls
        logger.info(f"✅ Primary coach preserved: {test_case['primary_coach']}")

        # Check route_retrieval_type call
        mock_instance.route_retrieval_type.assert_called_once()
        route_call_args = mock_instance.route_retrieval_type.call_args
        route_domain = route_call_args[0][1]  # Second argument is coach_domain

        assert route_domain == test_case["expected_domain"], (
            f"❌ route_retrieval_type failed: "
            f"Expected domain '{test_case['expected_domain']}', "
            f"but got '{route_domain}'"
        )

        # Check decompose_query call
        mock_instance.decompose_query.assert_called_once()
        decompose_call_args = mock_instance.decompose_query.call_args
        decompose_domain = decompose_call_args[0][1]

        assert decompose_domain == test_case["expected_domain"], (
            f"❌ decompose_query failed: "
            f"Expected domain '{test_case['expected_domain']}', "
            f"but got '{decompose_domain}'"
        )

        logger.info(
            f"✅ {test_case['name']} passed: Correct domain '{route_domain}' used throughout GraphRAG"
        )


@pytest.mark.asyncio
async def test_fallback_behavior_when_no_primary_coach():
    """Test that fallback behavior works correctly when no primary_coach is set."""

    from athlea_langgraph.agents.graphrag_nodes import knowledge_assessment_node

    logger.info("\n🧪 Testing fallback behavior when primary_coach is missing")

    # State without primary_coach (should fallback to strength_coach)
    state = {
        "user_query": "General fitness question",
        "execution_steps": [],
        "messages": [],
        # Note: No primary_coach field!
    }

    mock_assessment = {
        "needs_knowledge_retrieval": False,
        "knowledge_confidence": 0.5,
        "knowledge_assessment_reasoning": "Fallback assessment",
    }

    with patch(
        "athlea_langgraph.agents.graphrag_nodes.get_graphrag_retriever"
    ) as mock_retriever:
        mock_instance = AsyncMock()
        mock_instance.assess_knowledge_needs.return_value = mock_assessment
        mock_retriever.return_value = mock_instance

        # Call the knowledge assessment node
        result = await knowledge_assessment_node(state)

        # Verify it falls back to strength_coach when no primary_coach is set
        mock_instance.assess_knowledge_needs.assert_called_once()
        call_args = mock_instance.assess_knowledge_needs.call_args
        used_domain = call_args[0][1]

        assert (
            used_domain == "strength_coach"
        ), f"❌ Fallback failed: Expected 'strength_coach', but got '{used_domain}'"

        logger.info(
            f"✅ Fallback behavior correct: Used '{used_domain}' when no primary_coach set"
        )


# Integration test that simulates the bug scenario
@pytest.mark.asyncio
async def test_end_to_end_bug_scenario():
    """Test the exact bug scenario: Intelligence Hub → GraphRAG preserves coach decision."""

    logger.info("\n🧪 Testing End-to-End Bug Scenario")

    # Simulate Intelligence Hub output (what should be preserved)
    intelligence_hub_output = {
        "primary_coach": "nutrition_coach",  # Intelligence Hub correctly identifies nutrition
        "routing_decision": "graphrag_enhanced",
        "required_coaches": ["nutrition"],
        "knowledge_retrieval_needed": True,
        "user_query": "What does the latest research say about protein timing for muscle growth?",
    }

    logger.info(
        f"🧠 Intelligence Hub correctly identifies: {intelligence_hub_output['primary_coach']}"
    )

    # Test knowledge assessment preserves the decision
    from athlea_langgraph.agents.graphrag_nodes import (
        knowledge_assessment_node,
        graphrag_retrieval_node,
    )

    state = {
        **intelligence_hub_output,
        "execution_steps": ["intelligence_hub"],
        "messages": [],
    }

    # Mock GraphRAG to avoid external dependencies
    with patch(
        "athlea_langgraph.agents.graphrag_nodes.get_graphrag_retriever"
    ) as mock_retriever:
        mock_instance = AsyncMock()
        mock_instance.assess_knowledge_needs.return_value = {
            "needs_knowledge_retrieval": True,
            "knowledge_confidence": 0.9,
            "knowledge_assessment_reasoning": "Research question requires latest findings",
        }
        mock_instance.route_retrieval_type.return_value = "vector"
        mock_instance.decompose_query.return_value = [state["user_query"]]
        mock_instance.vector_search.return_value = []
        mock_instance.synthesize_knowledge.return_value = (
            "Latest protein research findings..."
        )
        mock_retriever.return_value = mock_instance

        # Step 1: Knowledge Assessment
        assessment_result = await knowledge_assessment_node(state)

        # Verify knowledge assessment used nutrition domain
        knowledge_call_args = mock_instance.assess_knowledge_needs.call_args
        knowledge_domain = knowledge_call_args[0][1]

        assert knowledge_domain == "nutrition_coach", (
            f"❌ Knowledge assessment bug still exists! "
            f"Expected 'nutrition_coach', got '{knowledge_domain}'"
        )

        logger.info(f"✅ Knowledge assessment preserves: {knowledge_domain}")

        # Step 2: GraphRAG Retrieval
        updated_state = {**state, **assessment_result}
        retrieval_result = await graphrag_retrieval_node(updated_state)

        # Verify GraphRAG retrieval used nutrition domain
        route_call_args = mock_instance.route_retrieval_type.call_args
        retrieval_domain = route_call_args[0][1]

        assert retrieval_domain == "nutrition_coach", (
            f"❌ GraphRAG retrieval bug still exists! "
            f"Expected 'nutrition_coach', got '{retrieval_domain}'"
        )

        logger.info(f"✅ GraphRAG retrieval preserves: {retrieval_domain}")
        logger.info("🎉 End-to-end test passed! Bug is fixed!")


async def run_coach_routing_tests():
    """Run all coach routing tests."""

    print("🚀 Starting Coach Routing Bug Fix Tests\n")

    try:
        await test_knowledge_assessment_preserves_primary_coach()
        print("✅ Knowledge assessment test passed")

        await test_graphrag_retrieval_preserves_primary_coach()
        print("✅ GraphRAG retrieval test passed")

        await test_fallback_behavior_when_no_primary_coach()
        print("✅ Fallback behavior test passed")

        await test_end_to_end_bug_scenario()
        print("✅ End-to-end bug scenario test passed")

        print("\n🎉 ALL TESTS PASSED! The coach routing bug is fixed!")
        print("\nThe fix ensures that:")
        print("  ✅ Recovery queries → recovery_coach (not strength_coach)")
        print("  ✅ Nutrition queries → nutrition_coach (not strength_coach)")
        print("  ✅ Mental queries → mental_coach (not strength_coach)")
        print("  ✅ Intelligence Hub decisions are preserved through GraphRAG")

        return True

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(run_coach_routing_tests())
    exit(0 if success else 1)
