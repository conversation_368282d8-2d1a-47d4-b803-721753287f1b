#!/usr/bin/env python3
"""
Test script for GraphRAG integration in the Comprehensive Coaching Graph.

This script tests the complete flow:
1. Reasoning and Planning
2. Complexity Assessment
3. Knowledge Assessment (NEW)
4. GraphRAG Retrieval (NEW)
5. Coach Execution with Enhanced Context
6. Aggregation and Reflection
"""

import asyncio
import logging
import os
from typing import Dict, Any

from langchain_core.messages import HumanMessage

# Set up logging to see the GraphRAG flow
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

# Import the comprehensive coaching graph
from athlea_langgraph.graphs.archived.comprehensive_coaching_graph import (
    create_comprehensive_coaching_graph,
)


async def test_graphrag_integration():
    """Test the GraphRAG integration with different types of queries."""

    print("🚀 Testing GraphRAG Integration in Comprehensive Coaching Graph")
    print("=" * 70)

    # Create the graph with GraphRAG enabled
    config = {
        "user_id": "test_user",
        "thread_id": "graphrag_test",
        "enable_memory": False,
        "use_react_agents": True,
        "max_iterations": 2,
        "enable_human_feedback": False,
        "enable_reflexion": False,
    }

    print("📊 Creating comprehensive coaching graph with GraphRAG integration...")
    graph = await create_comprehensive_coaching_graph(config)
    print("✅ Graph created successfully!")
    print()

    # Test cases: different types of queries to test GraphRAG routing
    test_cases = [
        {
            "name": "Simple Query (Should skip GraphRAG)",
            "query": "How do I do a proper squat?",
            "expected_knowledge_needed": False,
            "description": "Basic form question - should not require external knowledge",
        },
        {
            "name": "Evidence-Based Query (Should use GraphRAG)",
            "query": "What does the latest research say about periodization for strength training?",
            "expected_knowledge_needed": True,
            "description": "Research-based question - should trigger knowledge retrieval",
        },
        {
            "name": "Complex Program Design (Should use GraphRAG)",
            "query": "Create an evidence-based 12-week strength program for intermediate lifters",
            "expected_knowledge_needed": True,
            "description": "Complex, evidence-based request - should use GraphRAG for research context",
        },
        {
            "name": "Nutrition Research Query (Should use GraphRAG)",
            "query": "[COACH_OVERRIDE:nutrition_coach] What do recent studies show about intermittent fasting for athletes?",
            "expected_knowledge_needed": True,
            "description": "Direct coach override with research request - should use GraphRAG",
        },
    ]

    # Test each case
    for i, test_case in enumerate(test_cases, 1):
        print(f"🧪 Test Case {i}: {test_case['name']}")
        print(f"📝 Query: {test_case['query']}")
        print(
            f"💡 Expected: {'Knowledge needed' if test_case['expected_knowledge_needed'] else 'No knowledge needed'}"
        )
        print(f"📄 Description: {test_case['description']}")
        print("-" * 50)

        # Create initial state
        initial_state = {
            "messages": [HumanMessage(content=test_case["query"])],
            "user_query": test_case["query"],
            "user_profile": {
                "experience_level": "intermediate",
                "goals": ["strength", "muscle_growth"],
                "preferences": {"evidence_based": True},
            },
        }

        try:
            print("🔄 Executing graph...")

            # Execute the graph
            final_state = None
            step_count = 0
            async for state in graph.astream(
                initial_state, {"configurable": {"thread_id": f"test_{i}"}}
            ):
                step_count += 1
                current_nodes = list(state.keys())
                print(f"  Step {step_count}: {current_nodes}")

                # Check for GraphRAG-specific state updates
                for node_name, node_state in state.items():
                    if isinstance(node_state, dict):
                        # Check for knowledge assessment results
                        if "needs_knowledge_retrieval" in node_state:
                            needs_knowledge = node_state["needs_knowledge_retrieval"]
                            confidence = node_state.get("knowledge_confidence", 0.0)
                            print(
                                f"    🧠 Knowledge Assessment: Needs retrieval = {needs_knowledge} (confidence: {confidence:.2f})"
                            )

                        # Check for GraphRAG retrieval results
                        if "retrieval_type" in node_state:
                            retrieval_type = node_state["retrieval_type"]
                            knowledge_context = node_state.get("knowledge_context", "")
                            print(f"    🔍 GraphRAG Retrieval: Type = {retrieval_type}")
                            if knowledge_context and len(knowledge_context) > 50:
                                print(
                                    f"    📚 Knowledge Context: {knowledge_context[:100]}..."
                                )

                        # Check for coach responses
                        if any(key.endswith("_response") for key in node_state.keys()):
                            coach_responses = {
                                k: v
                                for k, v in node_state.items()
                                if k.endswith("_response")
                            }
                            for coach, response in coach_responses.items():
                                if response and len(response) > 50:
                                    print(
                                        f"    🤖 {coach.replace('_response', '').title()}: {response[:100]}..."
                                    )

                final_state = state

            print("✅ Graph execution completed!")

            # Analyze the results
            if final_state:
                last_state = list(final_state.values())[-1]
                if isinstance(last_state, dict):
                    print("\n📋 Final Results Analysis:")

                    # Check if knowledge assessment worked
                    needs_knowledge = last_state.get("needs_knowledge_retrieval")
                    if needs_knowledge is not None:
                        expected = test_case["expected_knowledge_needed"]
                        result = "✅ PASS" if needs_knowledge == expected else "❌ FAIL"
                        print(
                            f"  Knowledge Assessment: {result} (Expected: {expected}, Got: {needs_knowledge})"
                        )

                    # Check if GraphRAG was executed when needed
                    retrieval_type = last_state.get("retrieval_type")
                    if retrieval_type:
                        print(f"  GraphRAG Retrieval Type: {retrieval_type}")

                    # Check if knowledge context was provided to coaches
                    knowledge_context = last_state.get("knowledge_context")
                    coach_responses = last_state.get("coach_responses", {})

                    # Check for evidence-based content in coach responses
                    evidence_based_content = False
                    if coach_responses:
                        for coach_name, response in coach_responses.items():
                            if any(
                                keyword in response.lower()
                                for keyword in [
                                    "research",
                                    "studies",
                                    "evidence",
                                    "according to",
                                    "findings",
                                    "data shows",
                                    "meta-analysis",
                                    "literature",
                                    "scientific",
                                ]
                            ):
                                evidence_based_content = True
                                break

                    if (
                        knowledge_context
                        and knowledge_context != "No external knowledge required"
                        and len(knowledge_context) > 100
                    ):
                        print(
                            f"  Knowledge Context Provided: ✅ ({len(knowledge_context)} chars)"
                        )
                        # Show preview of context
                        context_preview = (
                            knowledge_context[:150] + "..."
                            if len(knowledge_context) > 150
                            else knowledge_context
                        )
                        print(f"    📖 Preview: {context_preview}")
                    elif evidence_based_content:
                        print(
                            f"  Knowledge Context Provided: ✅ (Evidence-based response detected)"
                        )
                    elif coach_responses:
                        print(
                            f"  Knowledge Context Provided: ⚠️ (Coach responded but no external context detected)"
                        )
                        print(f"    🤖 Coach responses: {len(coach_responses)} found")
                    else:
                        print(f"  Knowledge Context Provided: ❌ (None or basic)")

                    # Check final response
                    final_response = last_state.get("final_response")
                    if final_response:
                        print(f"  Final Response Length: {len(final_response)} chars")
                        if (
                            "research" in final_response.lower()
                            or "studies" in final_response.lower()
                        ):
                            print(
                                f"  Evidence-Based Content: ✅ (Contains research references)"
                            )
                        else:
                            print(
                                f"  Evidence-Based Content: ❓ (No clear research references)"
                            )

        except Exception as e:
            print(f"❌ Error during test execution: {e}")
            import traceback

            traceback.print_exc()

        print("\n" + "=" * 70 + "\n")

    print("🎯 GraphRAG Integration Testing Complete!")
    print("\n📊 Summary:")
    print("- Knowledge Assessment Node: Tests whether queries need external knowledge")
    print("- GraphRAG Retrieval Node: Performs vector/graph search and synthesis")
    print(
        "- Enhanced Coach Context: Coaches receive knowledge context for evidence-based responses"
    )
    print("- Routing Logic: Simple queries skip GraphRAG, complex queries use it")


async def test_basic_flow():
    """Test a basic flow to ensure GraphRAG doesn't break existing functionality."""

    print("🔧 Testing Basic Flow (No GraphRAG needed)")
    print("=" * 50)

    config = {
        "user_id": "test_user",
        "thread_id": "basic_test",
        "enable_memory": False,
        "use_react_agents": True,
        "max_iterations": 1,
        "enable_human_feedback": False,
    }

    graph = await create_comprehensive_coaching_graph(config)

    # Simple greeting that should not trigger GraphRAG
    initial_state = {
        "messages": [
            HumanMessage(content="Hello, I'm new to fitness. Can you help me?")
        ],
        "user_query": "Hello, I'm new to fitness. Can you help me?",
    }

    try:
        final_state = None
        async for state in graph.astream(
            initial_state, {"configurable": {"thread_id": "basic_test"}}
        ):
            final_state = state
            current_nodes = list(state.keys())
            print(f"Executed: {current_nodes}")

        print("✅ Basic flow completed successfully!")

        # Check that it didn't break the normal flow
        if final_state:
            last_state = list(final_state.values())[-1]
            if isinstance(last_state, dict) and last_state.get("final_response"):
                print(
                    f"📝 Final response received: {len(last_state['final_response'])} chars"
                )

    except Exception as e:
        print(f"❌ Error in basic flow: {e}")
        raise


if __name__ == "__main__":

    async def main():
        # Test basic flow first
        await test_basic_flow()
        print("\n")

        # Then test GraphRAG integration
        await test_graphrag_integration()

    asyncio.run(main())
