#!/usr/bin/env python3
"""
Interactive GraphRAG Testing Script

This script allows you to input queries and see exactly what the GraphRAG system produces:
- Knowledge assessment results
- Vector search results
- Graph search results
- Knowledge synthesis
- Enhanced coach responses
"""

import asyncio
import logging
import sys
from typing import Dict, Any

from langchain_core.messages import HumanMessage

# Set up logging to see detailed GraphRAG flow
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

# Import the comprehensive coaching graph
from athlea_langgraph.graphs.archived.comprehensive_coaching_graph import (
    create_comprehensive_coaching_graph,
)


def print_header(title: str):
    """Print a formatted header."""
    print("\n" + "=" * 70)
    print(f"🔬 {title}")
    print("=" * 70)


def print_section(title: str):
    """Print a formatted section."""
    print(f"\n📋 {title}")
    print("-" * 50)


async def test_query(graph, query: str, test_number: int):
    """Test a single query and show detailed GraphRAG results."""

    print_header(f"Test Query #{test_number}")
    print(f"📝 Query: {query}")

    # Create initial state
    initial_state = {
        "messages": [HumanMessage(content=query)],
        "user_query": query,
        "user_profile": {
            "experience_level": "intermediate",
            "goals": ["strength", "muscle_growth"],
            "preferences": {"evidence_based": True},
        },
    }

    # Track GraphRAG-specific results
    knowledge_assessment = None
    retrieval_results = None
    final_coach_response = None
    knowledge_context = None

    try:
        print("\n🔄 Executing graph...")

        step_count = 0
        try:
            async for state in graph.astream(
                initial_state,
                {"configurable": {"thread_id": f"interactive_test_{test_number}"}},
            ):
                step_count += 1
                current_nodes = list(state.keys())

                # Extract GraphRAG information from each step
                for node_name, node_state in state.items():
                    if isinstance(node_state, dict):

                        # 1. Knowledge Assessment Results
                        if "needs_knowledge_retrieval" in node_state:
                            knowledge_assessment = {
                                "needs_knowledge": node_state[
                                    "needs_knowledge_retrieval"
                                ],
                                "confidence": node_state.get(
                                    "knowledge_confidence", 0.0
                                ),
                                "reasoning": node_state.get(
                                    "knowledge_assessment_reasoning", ""
                                ),
                            }

                        # 2. GraphRAG Retrieval Results
                        if "retrieval_type" in node_state:
                            retrieval_results = {
                                "type": node_state["retrieval_type"],
                                "vector_results": node_state.get("vector_results", []),
                                "graph_results": node_state.get("graph_results", []),
                                "knowledge_context": node_state.get(
                                    "knowledge_context", ""
                                ),
                                "decomposed_queries": node_state.get(
                                    "decomposed_queries", []
                                ),
                            }
                            knowledge_context = node_state.get("knowledge_context", "")

                        # 3. Coach Responses
                        if (
                            "coach_responses" in node_state
                            and node_state["coach_responses"]
                        ):
                            final_coach_response = node_state["coach_responses"]

        except Exception as e:
            # Handle LangGraph interrupts gracefully
            if "GraphInterrupt" in str(type(e)) or "Interrupt" in str(e):
                print(
                    "ℹ️  Graph execution paused for human feedback (this is expected behavior)"
                )
                print(
                    "✅ GraphRAG pipeline completed successfully - results extracted above"
                )
                # The interrupt contains the final response, let's extract it
                if hasattr(e, "args") and e.args and isinstance(e.args[0], tuple):
                    interrupt_data = e.args[0]
                    if hasattr(interrupt_data, "value") and isinstance(
                        interrupt_data.value, dict
                    ):
                        final_response = interrupt_data.value.get("content", "")
                        if final_response:
                            print("\n📋 Final Response from GraphInterrupt:")
                            print("--------------------------------------------------")
                            print(
                                f"📝 Response Preview:\n{final_response[:200]}{'...' if len(final_response) > 200 else ''}"
                            )
                            print(
                                f"📊 Response Length: {len(final_response)} characters"
                            )
            else:
                print(f"❌ Unexpected error during graph execution: {e}")
                raise

        # Display detailed results
        print_section("1. Knowledge Assessment Results")
        if knowledge_assessment:
            print(
                f"🧠 Needs External Knowledge: {knowledge_assessment['needs_knowledge']}"
            )
            print(f"🎯 Confidence: {knowledge_assessment['confidence']:.2f}")
            print(f"💭 Reasoning: {knowledge_assessment['reasoning'][:200]}...")
        else:
            print("❌ No knowledge assessment found")

        print_section("2. GraphRAG Retrieval Results")
        if retrieval_results:
            print(f"🔍 Retrieval Type: {retrieval_results['type']}")

            if retrieval_results["decomposed_queries"]:
                print(
                    f"🧩 Decomposed Queries ({len(retrieval_results['decomposed_queries'])}):"
                )
                for i, q in enumerate(retrieval_results["decomposed_queries"][:3], 1):
                    print(f"   {i}. {q}")

            if retrieval_results["vector_results"]:
                print(
                    f"📄 Vector Search Results: {len(retrieval_results['vector_results'])} documents"
                )
                # Show top 2 results
                for i, result in enumerate(retrieval_results["vector_results"][:2], 1):
                    if isinstance(result, dict):
                        content = result.get("content", "No content")[:150]
                        source = result.get("source", "Unknown source")
                        print(f"   📖 Result {i}: {content}... (Source: {source})")

            if retrieval_results["graph_results"]:
                print(
                    f"🕸️ Graph Search Results: {len(retrieval_results['graph_results'])} relationships"
                )
                for i, result in enumerate(retrieval_results["graph_results"][:2], 1):
                    if isinstance(result, dict):
                        content = str(result)[:100]
                        print(f"   🔗 Relationship {i}: {content}...")

            if retrieval_results["knowledge_context"]:
                context_len = len(retrieval_results["knowledge_context"])
                print(f"📚 Synthesized Knowledge Context: {context_len} characters")
                if context_len > 0:
                    preview = retrieval_results["knowledge_context"][:300]
                    print(f"   📖 Preview: {preview}...")

        else:
            print("❌ No GraphRAG retrieval performed (skipped)")

        print_section("3. Knowledge Context for Coach")
        if knowledge_context and len(knowledge_context) > 50:
            print(f"✅ Enhanced Context Provided: {len(knowledge_context)} characters")
            print(f"📖 Context Preview:\n{knowledge_context[:400]}...")
        else:
            print("❌ No enhanced context provided to coach")

        print_section("4. Final Coach Response")
        if final_coach_response:
            for coach_name, response in final_coach_response.items():
                print(
                    f"🤖 {coach_name.replace('_', ' ').title()}: {len(response)} characters"
                )
                print(f"📝 Response Preview:\n{response[:300]}...")

                # Check for evidence-based content
                evidence_keywords = [
                    "research",
                    "studies",
                    "evidence",
                    "according to",
                    "findings",
                    "data shows",
                    "meta-analysis",
                    "literature",
                    "scientific",
                ]
                has_evidence = any(
                    keyword in response.lower() for keyword in evidence_keywords
                )
                if has_evidence:
                    print("✅ Evidence-based content detected")
                else:
                    print("ℹ️ Standard coaching response")
        else:
            print("❌ No coach response found")

        # Summary
        print_section("Summary")
        if knowledge_assessment and knowledge_assessment["needs_knowledge"]:
            if retrieval_results and retrieval_results["type"] != "none":
                print("✅ GraphRAG Pipeline: Complete")
                print(
                    f"   - Knowledge needed: {knowledge_assessment['needs_knowledge']}"
                )
                print(f"   - Retrieval performed: {retrieval_results['type']}")
                print(
                    f"   - Context provided: {bool(knowledge_context and len(knowledge_context) > 50)}"
                )
                print(f"   - Coach enhanced: {bool(final_coach_response)}")
            else:
                print(
                    "⚠️ GraphRAG Pipeline: Knowledge needed but no retrieval performed"
                )
        else:
            print("✅ Simple Query Pipeline: Knowledge retrieval skipped as expected")

    except Exception as e:
        print(f"❌ Error during test execution: {e}")
        import traceback

        traceback.print_exc()


async def interactive_graphrag_tester():
    """Main interactive testing function."""

    print("🚀 Interactive GraphRAG Testing Tool")
    print("=" * 70)
    print("This tool lets you test queries and see exactly what GraphRAG produces.")
    print("Enter queries to see:")
    print("  - Knowledge assessment (does it need external knowledge?)")
    print("  - Vector search results (Azure Cognitive Search)")
    print("  - Graph search results (Cosmos DB Gremlin)")
    print("  - Knowledge synthesis (combined context)")
    print("  - Enhanced coach responses")
    print("\nType 'quit' or 'exit' to stop.")
    print("=" * 70)

    # Create the graph
    print("\n📊 Creating comprehensive coaching graph with GraphRAG...")
    config = {
        "user_id": "interactive_user",
        "thread_id": "interactive_test",
        "enable_memory": False,
        "use_react_agents": True,
        "max_iterations": 2,
        "enable_human_feedback": False,
        "enable_reflexion": False,
    }

    print(
        "🔧 Note: Some LangGraph interrupts for human feedback may appear as 'errors' but are expected behavior."
    )

    try:
        graph = await create_comprehensive_coaching_graph(config)
        print("✅ Graph created successfully!")
    except Exception as e:
        print(f"❌ Error creating graph: {e}")
        return

    test_number = 1

    while True:
        try:
            print(f"\n{'='*30} Query #{test_number} {'='*30}")

            # Get user input
            query = input("\n🎯 Enter your query (or 'quit' to exit): ").strip()

            if query.lower() in ["quit", "exit", "q"]:
                print("\n👋 Thanks for testing GraphRAG! Goodbye!")
                break

            if not query:
                print("❌ Please enter a query.")
                continue

            # Test the query
            await test_query(graph, query, test_number)
            test_number += 1

            # Ask if they want to continue
            continue_choice = input("\n🔄 Test another query? (y/n): ").strip().lower()
            if continue_choice in ["n", "no"]:
                print("\n👋 Thanks for testing GraphRAG! Goodbye!")
                break

        except KeyboardInterrupt:
            print("\n\n👋 Interrupted by user. Goodbye!")
            break
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            import traceback

            traceback.print_exc()


# Example queries to suggest
EXAMPLE_QUERIES = [
    "How do I do a proper squat?",  # Should skip GraphRAG
    "What does the latest research say about periodization for strength training?",  # Should use GraphRAG
    "Create an evidence-based 12-week strength program for intermediate lifters",  # Should use GraphRAG
    "[COACH_OVERRIDE:nutrition_coach] What do recent studies show about intermittent fasting for athletes?",  # Should use GraphRAG
    "Hello, I'm new to fitness",  # Should skip GraphRAG
    "What are the best exercises for muscle hypertrophy according to recent research?",  # Should use GraphRAG
]


if __name__ == "__main__":
    print("\n💡 Suggested test queries:")
    for i, query in enumerate(EXAMPLE_QUERIES, 1):
        print(f"   {i}. {query}")

    asyncio.run(interactive_graphrag_tester())
