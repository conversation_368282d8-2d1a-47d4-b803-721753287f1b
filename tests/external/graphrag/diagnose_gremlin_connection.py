#!/usr/bin/env python3
"""
Comprehensive Gremlin Connection Diagnostics

This script systematically tests different connection configurations
to identify the exact issue with Azure Cosmos DB Gremlin API.
"""

import logging
import time
from urllib.parse import urlparse

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def test_endpoint_parsing():
    """Test if we're parsing the endpoint correctly."""
    print("🔍 STEP 1: Testing Endpoint Parsing")
    print("=" * 50)

    endpoint = "https://cosmos-gremlin-aml.documents.azure.com:443/"
    parsed = urlparse(endpoint)

    print(f"Original endpoint: {endpoint}")
    print(f"Parsed hostname: {parsed.hostname}")
    print(f"Parsed port: {parsed.port}")
    print(f"Parsed scheme: {parsed.scheme}")

    # Test different URL formats
    test_urls = [
        "wss://cosmos-gremlin-aml.documents.azure.com:443/gremlin",
        "wss://cosmos-gremlin-aml.documents.azure.com/gremlin",
        "ws://cosmos-gremlin-aml.documents.azure.com:443/gremlin",
    ]

    for url in test_urls:
        print(f"Test URL: {url}")
        parsed_test = urlparse(url)
        print(f"  - Hostname: {parsed_test.hostname}")
        print(f"  - Port: {parsed_test.port}")
        print(f"  - Path: {parsed_test.path}")

    print("✓ Endpoint parsing complete\n")


def test_authentication_format():
    """Test different authentication string formats."""
    print("🔍 STEP 2: Testing Authentication Format")
    print("=" * 50)

    # Test credentials
    cosmos_key = "****************************************************************************************"
    cosmos_database = "graphragdb"

    print(f"Database: {cosmos_database}")
    print(f"Key length: {len(cosmos_key)} characters")
    print(f"Key starts with: {cosmos_key[:10]}...")
    print(f"Key ends with: ...{cosmos_key[-10:]}")

    # Test different username formats
    username_formats = [
        f"/dbs/{cosmos_database}/colls/knowledge-graph",
        f"graphragdb",
        f"/dbs/graphragdb/colls/knowledge-graph/docs",
    ]

    for username in username_formats:
        print(f"Username format: {username}")

    print("✓ Authentication format test complete\n")


def test_basic_connection():
    """Test basic connection without queries."""
    print("🔍 STEP 3: Testing Basic Connection")
    print("=" * 50)

    try:
        from gremlin_python.driver import client

        # Connection parameters
        endpoint = "cosmos-gremlin-aml.documents.azure.com"
        port = 443
        username = "/dbs/graphragdb/colls/knowledge-graph"
        password = "****************************************************************************************"

        print(f"Connecting to: {endpoint}:{port}")
        print(f"Username: {username}")
        print(f"Password: [REDACTED - {len(password)} chars]")

        # Create client
        gremlin_client = client.Client(
            f"wss://{endpoint}:{port}/gremlin",
            "g",
            username=username,
            password=password,
            enable_ssl=True,
        )

        print("✓ Gremlin client created successfully")

        # Try to submit a very simple query
        print("Attempting simple count query...")

        result = gremlin_client.submit("g.V().count()")
        count = result.one()

        print(f"✓ Connection successful! Vertex count: {count}")
        return True

    except Exception as e:
        print(f"✗ Connection failed: {e}")
        print(f"Error type: {type(e).__name__}")
        return False


def test_alternative_connection_parameters():
    """Test alternative connection parameters."""
    print("\n🔍 STEP 4: Testing Alternative Connection Parameters")
    print("=" * 50)

    try:
        from gremlin_python.driver import client

        # Alternative parameter sets
        configs = [
            {
                "name": "Standard SSL",
                "url": "wss://cosmos-gremlin-aml.documents.azure.com:443/gremlin",
                "username": "/dbs/graphragdb/colls/knowledge-graph",
                "enable_ssl": True,
            },
            {
                "name": "Without explicit SSL",
                "url": "wss://cosmos-gremlin-aml.documents.azure.com:443/gremlin",
                "username": "/dbs/graphragdb/colls/knowledge-graph",
                "enable_ssl": None,  # Don't set explicitly
            },
            {
                "name": "Alternative username format",
                "url": "wss://cosmos-gremlin-aml.documents.azure.com:443/gremlin",
                "username": "graphragdb",
                "enable_ssl": True,
            },
        ]

        password = "****************************************************************************************"

        for config in configs:
            print(f"\nTesting: {config['name']}")
            print(f"URL: {config['url']}")
            print(f"Username: {config['username']}")

            try:
                client_params = {
                    "traversal_source": "g",
                    "username": config["username"],
                    "password": password,
                }

                if config["enable_ssl"] is not None:
                    client_params["enable_ssl"] = config["enable_ssl"]

                test_client = client.Client(config["url"], **client_params)

                print("  ✓ Client created")

                # Try a simple query
                result = test_client.submit("g.V().limit(1).count()")
                count = result.one()
                print(f"  ✓ Query successful! Limited count: {count}")

            except Exception as e:
                print(f"  ✗ Failed: {e}")
                print(f"  Error type: {type(e).__name__}")

    except ImportError:
        print("✗ gremlinpython not available")


def check_azure_documentation_format():
    """Check if we're following Azure Cosmos DB Gremlin documentation."""
    print("\n🔍 STEP 5: Azure Documentation Format Check")
    print("=" * 50)

    print("According to Azure Cosmos DB Gremlin documentation:")
    print("- Endpoint format: wss://{account-name}.gremlin.cosmos.azure.com:443/")
    print("- Our endpoint: wss://cosmos-gremlin-aml.documents.azure.com:443/gremlin")
    print()
    print("⚠️ POTENTIAL ISSUE FOUND:")
    print("   Azure documentation suggests endpoint should be:")
    print("   wss://cosmos-gremlin-aml.gremlin.cosmos.azure.com:443/")
    print("   But we're using:")
    print("   wss://cosmos-gremlin-aml.documents.azure.com:443/gremlin")
    print()
    print("Let's test the documented format...")

    try:
        from gremlin_python.driver import client

        # Try the documented format
        documented_url = "wss://cosmos-gremlin-aml.gremlin.cosmos.azure.com:443/"
        username = "/dbs/graphragdb/colls/knowledge-graph"
        password = "****************************************************************************************"

        print(f"Testing documented URL: {documented_url}")

        test_client = client.Client(
            documented_url, "g", username=username, password=password, enable_ssl=True
        )

        print("✓ Client created with documented format")

        result = test_client.submit("g.V().limit(1).count()")
        count = result.one()
        print(f"✓ Documented format works! Count: {count}")

    except Exception as e:
        print(f"✗ Documented format failed: {e}")
        print(f"Error type: {type(e).__name__}")


def main():
    """Run all diagnostic tests."""
    print("🚨 COSMOS DB GREMLIN CONNECTION DIAGNOSTICS")
    print("=" * 60)
    print()

    # Run all diagnostic steps
    test_endpoint_parsing()
    test_authentication_format()

    success = test_basic_connection()

    if not success:
        test_alternative_connection_parameters()
        check_azure_documentation_format()

    print("\n🎯 DIAGNOSIS COMPLETE")
    print("=" * 60)

    if success:
        print("✅ CONNECTION SUCCESSFUL - GraphRAG integration ready!")
    else:
        print("❌ CONNECTION FAILED")
        print("\n🔧 RECOMMENDED FIXES:")
        print("1. Verify the Cosmos DB endpoint URL format")
        print("2. Check if the Gremlin API is enabled for this Cosmos DB")
        print("3. Verify the access key is still valid")
        print("4. Ensure the database/collection names are correct")
        print("5. Check Azure Cosmos DB firewall settings")


if __name__ == "__main__":
    main()
